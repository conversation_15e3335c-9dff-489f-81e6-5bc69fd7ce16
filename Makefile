# Executables
# MBED2 build location
#IMAGEBASE=cmake_build/PELAGIC_L431/develop/GCC_ARM/pelagic_l431_mbed
# MBED1 build location
# IMAGEBASE=BUILD/PELAGIC_L431/GCC_ARM/pelagic_l431_mbed

#! Makefile build location - hardcoded from original_source/Makefile.common
IMAGEBASE=original_source/images/pelagic_vms

# Tools
MBED=mbed
MBED_2=mbed_tools
GDB=gdb-multiarch
JLINK=JLinkCommander
OPENOCD=openocd

# Scripts
JLINK_FLASH_SCRIPT = flash.jlink
JLINK_DEBUG_SCRIPT = debug.jlink
JLINK_GDB_FLAGS = -port 2331 -s -device STM32L431CC -endian little -speed 4000 -if swd -vd
OPENOCD_FLASH_SCRIPT = flash.cfg
OPENOCD_DEBUG_SCRIPT = debug.cfg
GDB_INIT_SCRIPT = connect.gdb
GDB_UTILS_SCRIPT = utils.gdb

# Build Flags
QUIET ?= 1

# ------------------------------------------------------------------------------
# Building
# ------------------------------------------------------------------------------

all: build

help:
	@echo "make all            - build the project (default)"
	@echo "make mbed1          - build using legacy mbed CLI"
	@echo "make mbed2          - build using mbed-tools"
	@echo "make flash          - flash the image using OpenOCD"
	@echo "make debug          - start OpenOCD in debug mode"
	@echo "make attach         - attach gdb to the OpenOCD session"
	@echo "make inspect        - connect to the OpenOCD telnet inspect interface"
	@echo "make control        - connect to the OpenOCD telnet control interface"
	@echo "make clean          - remove build directories and clean sources"
	@echo "make fresh          - clean and rebuild everything"
	@echo "make docker_image   - build the Docker image for building"
	@echo "make container      - start a Docker container for building"
	@echo "make docker_compile - build inside Docker and run make fresh"

mbed1:
	$(MBED) compile -m PELAGIC_L431 -t GCC_ARM --color

mbed2:
	$(MBED_2) compile -m PELAGIC_L431 -t GCC_ARM

$(IMAGEBASE).elf:
	make -C ./original_source VERS=12 QUIET=$(QUIET) vms
	@echo "----------------------------------------------"
	@echo "built $(IMAGEBASE).elf"
	@echo "----------------------------------------------"

build: $(IMAGEBASE).elf

clean:
	rm -rf BUILD/
	rm -rf cmake_build/
	make -C ./original_source superclean

fresh: clean build

# ------------------------------------------------------------------------------
# Flashing and Debugging
# ------------------------------------------------------------------------------

flash: $(IMAGEBASE).elf
	$(OPENOCD) -c "set IMAGE $(IMAGEBASE).elf" -f $(OPENOCD_FLASH_SCRIPT) 

debug: $(IMAGEBASE).elf
	$(OPENOCD) -f $(OPENOCD_DEBUG_SCRIPT)

attach:
	$(GDB) "$(IMAGEBASE).elf" -x $(GDB_INIT_SCRIPT) -x $(GDB_UTILS_SCRIPT)

inspect:
	telnet localhost 2332

control:
	telnet localhost 4444

# ------------------------------------------------------------------------------
# Docker Management
# ------------------------------------------------------------------------------

# Docker Args
IMAGE_NAME := mbed:latest
CONTAINER_NAME := mbed_make
HOST_PWD := $(shell pwd)
HOST_UID := $(shell id -u)
HOST_GID := $(shell id -g)

docker_image:
	docker build -t $(IMAGE_NAME) .

container:
	docker run -d -it \
			--user $(HOST_UID):$(HOST_GID) \
			-v $(HOST_PWD):$(HOST_PWD) \
			-w $(HOST_PWD) \
			--name $(CONTAINER_NAME) \
			$(IMAGE_NAME)

docker_compile:
	docker run --rm -t \
			--user $(HOST_UID):$(HOST_GID) \
			-v $(HOST_PWD):$(HOST_PWD) \
			-w $(HOST_PWD) \
			--privileged \
			$(IMAGE_NAME) \
			bash -c "make fresh" | tee latest_build.txt

# ------------------------------------------------------------------------------
# Misc
# ------------------------------------------------------------------------------

.PHONY: help all \
		build clean fresh \
		flash \
		debug inspect attach control \
	    docker_image docker_compile \
		mbed1 mbed2
FROM ghcr.io/armmbed/mbed-os-env:latest

# docker build -t image_name .
# docker run -it -v /dev:/dev -v /home/<USER>/src/pelagic_l431_mbed:/root/pelagic_l431_mbed --name mbed_usb ghcr.io/armmbed/mbed-os-env

RUN apt update \
    && apt install nano \
    && apt install -y openocd telnet make usbutils \
    && apt install -y gdb-multiarch

ENV TERM=xterm-256color

# mbed_tools configure -t GCC_ARM -m PELAGIC_L431
RUN echo "make fresh\n" >> /root/.bash_history \
    && echo "export GCC_COLORS='error=01;31:warning=01;35:note=01;36:caret=01;32:locus=01:quote=01'" >> /etc/bash.bashrc \
    && echo "export PS1='\[\033[01;35m\]dev\[\033[00m\]:\[\033[01;34m\]\w\[\033[00m\]\$ '" >> /etc/bash.bashrc

# CMD ["/bin/bash"]
CMD ["/bin/bash"]

#include "swo_debug.h"
#include <stdarg.h>
#include <stdio.h>

void SWO_Init()
{
    // Enable DWT and ITM
    CoreDebug->DEMCR |= CoreDebug_DEMCR_TRCENA_Msk;
    ITM->LAR = 0xC5ACCE55;           // Unlock ITM (STM32 sometimes locks it)
    ITM->TCR = ITM_TCR_ITMENA_Msk    // Enable ITM
               | ITM_TCR_TSENA_Msk   // Enable timestamping (optional)
               | ITM_TCR_SWOENA_Msk; // Enable SWO output
    // Enable stimulus port 0
    ITM->TER = 0x1;
}

// ITM_SendChar already exists in core_cm4.h, it's identical to this
void ITM_SendChar_Blocking(uint8_t ch)
{
    if ((ITM->TCR & ITM_TCR_ITMENA_Msk) && (ITM->TER & 1))
    {
        while (ITM->PORT[0].u32 == 0)
            ;
        ITM->PORT[0].u8 = ch;
    }
}

void ITM_SendString(const char* s)
{
    while (*s)
    {
        ITM_SendChar_Blocking(*s++);
    }
}

void ITM_Printf(const char* format, ...)
{
    char    buffer[128]; // Temporary buffer for formatted string
    va_list args;

    va_start(args, format);
    vsnprintf(buffer, sizeof(buffer), format, args); // Format the string
    va_end(args);

    ITM_SendString(buffer); // Send the formatted string
}

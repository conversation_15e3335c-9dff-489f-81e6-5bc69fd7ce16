GPS Test Program Starting...
=== GPS Serial Communication Test ===
GPS Serial Test: Starting...
GPS Serial Test: Powering on and resetting GPS...
LPUART interrupt RX initialized
LPUART timeout after 1000 ms, received 0/80 bytes
LPUART timeout after 1000 ms, received 0/80 bytes
LPUART timeout after 1000 ms, received 0/80 bytes
LPUART timeout after 1000 ms, received 0/80 bytes
LPUART timeout after 1000 ms, received 0/80 bytes
[38/11/22 09:20:08] gps     :error command not confirmed name=[rmc-gga-gsa]
GPS Test Program Starting...
=== GPS Serial Communication Test ===
GPS Serial Test: Starting...
GPS Serial Test: Powering on and resetting GPS...
LPUART interrupt RX initialized
LPUART timeout after 1000 ms, received 0/80 bytes
LPUART timeout after 1000 ms, received 0/80 bytes
LPUART timeout after 1000 ms, received 0/80 bytes
LPUART timeout after 1000 ms, received 0/80 bytes
LPUART timeout after 1000 ms, received 0/80 bytes
[38/11/22 09:21:36] gps     :error command not confirmed name=[rmc-gga-gsa]
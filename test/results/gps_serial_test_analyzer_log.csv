
00: 7035 0000
F1: 0000 0000
V0: 0000 0000 [0001]
01: 0000 0000
U0: 0000 0001 [0000]
D0: 0000 0001
T0: 0000 00A6
Leaving the BROM

[Bootloader] bl_uart_init done
set CP10 and CP11 Full Access 
[CLK] Dynamic Clock Management: Enable
[CLK] frequency meter
CM4(CMSYS) freq [215896]KHz
BUS(FSYS) freq [107948]KHz
SFC freq [107948]KHz
SPISLV freq [0]KHz
I3C freq [107948]KHz
RTC SRAM freq [107948]KHz
hal_flash_init
BL Jump to addr 0x8009000 (0)

$PQTMVER,MODULE_LC76GPANR12A01S,2023/02/13,10:41:57*61
$PAIR001,002,1*38
$PAIR001,021,0*38
$PAIR021,AG3352Q_V2.3.0.AG3352_20230213,S,N,2b31f59,2209141904,,,,,d32ef91c,2209141902,571d3e7,2209141904,,,-15.48,-15.48,-14.02,-15.48,0,1,##,0,0*34

//! NOTE: MCU attempt to configure GPS or something
$PMTK314,0,1,0,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0*29
$PMTK314,0,1,0,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0*29
$PMTK314,0,1,0,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0*29
$PMTK314,0,1,0,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0*29
$PMTK314,0,1,0,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0*29


//! NOTE: GPS resets, then begins reporting!
F1: 0000 0000
V0: 0000 0000 [0001]
00: 0000 0000
U0: 0000 0001 [0000]
D0: 0000 0001
T0: 0000 00A6
Leaving the BROM

[Bootloader] bl_uart_init done
set CP10 and CP11 Full Access 
[CLK] Dynamic Clock Management: Enable
[CLK] frequency meter
CM4(CMSYS) freq [215922]KHz
BUS(FSYS) freq [107948]KHz
SFC freq [107948]KHz
SPISLV freq [0]KHz
I3C freq [107948]KHz
RTC SRAM freq [107948]KHz
hal_flash_init
BL Jump to addr 0x8009000 (0)

$PQTMVER,MODULE_LC76GPANR12A01S,2023/02/13,10:41:57*61
$PAIR001,002,1*38
$PAIR001,021,0*38
$PAIR021,AG3352Q_V2.3.0.AG3352_20230213,S,N,2b31f59,2209141904,,,,,d32ef91c,2209141902,571d3e7,2209141904,,,-15.48,-15.48,-14.02,-15.48,0,1,##,0,0*34
$PAIR011,001*27
$PAIR001,752,0*3B
$PAIR010,1,-1*16
$PAIR010,2,-1*15
$PAIR001,002,0*39
$PAIR001,021,0*38
$PAIR021,AG3352Q_V2.3.0.AG3352_20230213,S,N,2b31f59,2209141904,,,,,d32ef91c,2209141902,571d3e7,2209141904,,,-15.48,-15.48,-14.02,-15.48,0,1,##,0,0*34

//! NOTE: After reset, GPS begins sending (GNGGA, GNGLL, GNGSA, GPGSV, GLGSV, GAGSV, GBGSV, GNRMC, GNVTG)
$GNGGA,235942.013,,,,,0,0,,,M,,M,,*5F
$GNGLL,,,,,235942.013,V,N*6D
$GNGSA,A,1,,,,,,,,,,,,,,,,1*1D
$GNGSA,A,1,,,,,,,,,,,,,,,,2*1E
$GNGSA,A,1,,,,,,,,,,,,,,,,3*1F
$GNGSA,A,1,,,,,,,,,,,,,,,,4*18
$GPGSV,1,1,00,1*64
$GLGSV,1,1,00,1*78
$GAGSV,1,1,00,7*73
$GBGSV,1,1,00,1*76
$GBGSV,1,1,00,3*74
$GNRMC,235942.013,V,,,,,,,050180,,,N,V*2C
$GNVTG,,T,,M,,N,,K,N*32

//! NOTE: next block... repeats
$GNGGA,235943.012,,,,,0,0,,,M,,M,,*5F
$GNGLL,,,,,235943.012,V,N*6D
$GNGSA,A,1,,,,,,,,,,,,,,,,1*1D
$GNGSA,A,1,,,,,,,,,,,,,,,,2*1E
$GNGSA,A,1,,,,,,,,,,,,,,,,3*1F
$GNGSA,A,1,,,,,,,,,,,,,,,,4*18
$GPGSV,1,1,00,1*64
$GLGSV,1,1,00,1*78
$GAGSV,1,1,00,7*73
$GBGSV,1,1,00,1*76
$GBGSV,1,1,00,3*74
$GNRMC,235943.012,V,,,,,,,050180,,,N,V*2C
$GNVTG,,T,,M,,N,,K,N*32
$GNGGA,235944.012,,,,,0,0,,,M,,M,,*58
$GNGLL,,,,,235944.012,V,N*6A
$GNGSA,A,1,,,,,,,,,,,,,,,,1*1D
$GNGSA,A,1,,,,,,,,,,,,,,,,2*1E
$GNGSA,A,1,,,,,,,,,,,,,,,,3*1F
$GNGSA,A,1,,,,,,,,,,,,,,,,4*18
$GPGSV,1,1,00,1*64
$GLGSV,1,1,00,1*78
$GAGSV,1,1,00,7*73
$GBGSV,1,1,00,1*76
$GBGSV,1,1,00,3*74
$GNRMC,235944.012,V,,,,,,,050180,,,N,V*2B
$GNVTG,,T,,M,,N,,K,N*32
$GNGGA,235945.012,,,,,0,0,,,M,,M,,*59
$GNGLL,,,,,235945.012,V,N*6B
$GNGSA,A,1,,,,,,,,,,,,,,,,1*1D
$GNGSA,A,1,,,,,,,,,,,,,,,,2*1E
$GNGSA,A,1,,,,,,,,,,,,,,,,3*1F
$GNGSA,A,1,,,,,,,,,,,,,,,,4*18
$GPGSV,1,1,00,1*64
$GLGSV,1,1,00,1*78
$GAGSV,1,1,00,7*73
$GBGSV,1,1,00,1*76
$GBGSV,1,1,00,3*74
$GNRMC,235945.012,V,,,,,,,050180,,,N,V*2A
$GNVTG,,T,,M,,N,,K,N*32
$GNGGA,235946.011,,,,,0,0,,,M,,M,,*59
$GNGLL,,,,,235946.011,V,N*6B
$GNGSA,A,1,,,,,,,,,,,,,,,,1*1D
$GNGSA,A,1,,,,,,,,,,,,,,,,2*1E
$GNGSA,A,1,,,,,,,,,,,,,,,,3*1F
$GNGSA,A,1,,,,,,,,,,,,,,,,4*18
$GPGSV,1,1,00,1*64
$GLGSV,1,1,00,1*78
$GAGSV,1,1,00,7*73
$GBGSV,1,1,00,1*76
$GBGSV,1,1,00,3*74
$GNRMC,235946.011,V,,,,,,,050180,,,N,V*2A
$GNVTG,,T,,M,,N,,K,N*32
$GNGGA,235947.012,,,,,0,0,,,M,,M,,*5B
$GNGLL,,,,,235947.012,V,N*69
$GNGSA,A,1,,,,,,,,,,,,,,,,1*1D
$GNGSA,A,1,,,,,,,,,,,,,,,,2*1E
$GNGSA,A,1,,,,,,,,,,,,,,,,3*1F
$GNGSA,A,1,,,,,,,,,,,,,,,,4*18
$GPGSV,1,1,00,1*64
$GLGSV,1,1,00,1*78
$GAGSV,1,1,00,7*73
$GBGSV,1,1,00,1*76
$GBGSV,1,1,00,3*74
$GNRMC,235947.012,V,,,,,,,050180,,,N,V*28
$GNVTG,,T,,M,,N,,K,N*32
$GNGGA,235948.012,,,,,0,0,,,M,,M,,*54
$GNGLL,,,,,235948.012,V,N*66
$GNGSA,A,1,,,,,,,,,,,,,,,,1*1D
$GNGSA,A,1,,,,,,,,,,,,,,,,2*1E
$GNGSA,A,1,,,,,,,,,,,,,,,,3*1F
$GNGSA,A,1,,,,,,,,,,,,,,,,4*18
$GPGSV,1,1,00,1*64
$GLGSV,1,1,00,1*78
$GAGSV,1,1,00,7*73
$GBGSV,1,1,00,1*76
$GBGSV,1,1,00,3*74
$GNRMC,235948.012,V,,,,,,,050180,,,N,V*27
$GNVTG,,T,,M,,N,,K,N*32
$GNGGA,235949.012,,,,,0,0,,,M,,M,,*55
$GNGLL,,,,,235949.012,V,N*67
$GNGSA,A,1,,,,,,,,,,,,,,,,1*1D
$GNGSA,A,1,,,,,,,,,,,,,,,,2*1E
$GNGSA,A,1,,,,,,,,,,,,,,,,3*1F
$GNGSA,A,1,,,,,,,,,,,,,,,,4*18
$GPGSV,1,1,00,1*64
$GLGSV,1,1,00,1*78
$GAGSV,1,1,00,7*73
$GBGSV,1,1,00,1*76
$GBGSV,1,1,00,3*74
$GNRMC,235949.012,V,,,,,,,050180,,,N,V*26
$GNVTG,,T,,M,,N,,K,N*32
$GNGGA,235950.012,,,,,0,0,,,M,,M,,*5D
$GNGLL,,,,,235950.012,V,N*6F
$GNGSA,A,1,,,,,,,,,,,,,,,,1*1D
$GNGSA,A,1,,,,,,,,,,,,,,,,2*1E
$GNGSA,A,1,,,,,,,,,,,,,,,,3*1F
$GNGSA,A,1,,,,,,,,,,,,,,,,4*18
$GPGSV,1,1,00,1*64
$GLGSV,1,1,00,1*78
$GAGSV,1,1,00,7*73
$GBGSV,1,1,00,1*76
$GBGSV,1,1,00,3*74
$GNRMC,235950.012,V,,,,,,,050180,,,N,V*2E
$GNVTG,,T,,M,,N,,K,N*32
$GNGGA,235951.012,,,,,0,0,,,M,,M,,*5C
$GNGLL,,,,,235951.012,V,N*6E
$GNGSA,A,1,,,,,,,,,,,,,,,,1*1D
$GNGSA,A,1,,,,,,,,,,,,,,,,2*1E
$GNGSA,A,1,,,,,,,,,,,,,,,,3*1F
$GNGSA,A,1,,,,,,,,,,,,,,,,4*18
$GPGSV,1,1,00,1*64
$GLGSV,1,1,00,1*78
$GAGSV,1,1,00,7*73
$GBGSV,1,1,00,1*76
$GBGSV,1,1,00,3*74
$GNRMC,235951.012,V,,,,,,,050180,,,N,V*2F
$GNVTG,,T,,M,,N,,K,N*32
$GNGGA,235952.012,,,,,0,0,,,M,,M,,*5F
$GNGLL,,,,,235952.012,V,N*6D
$GNGSA,A,1,,,,,,,,,,,,,,,,1*1D
$GNGSA,A,1,,,,,,,,,,,,,,,,2*1E
$GNGSA,A,1,,,,,,,,,,,,,,,,3*1F
$GNGSA,A,1,,,,,,,,,,,,,,,,4*18
$GPGSV,1,1,00,1*64
$GLGSV,1,1,00,1*78
$GAGSV,1,1,00,7*73
$GBGSV,1,1,00,1*76
$GBGSV,1,1,00,3*74
$GNRMC,235952.012,V,,,,,,,050180,,,N,V*2C
$GNVTG,,T,,M,,N,,K,N*32
$GNGGA,235953.012,,,,,0,0,,,M,,M,,*5E
$GNGLL,,,,,235953.012,V,N*6C
$GNGSA,A,1,,,,,,,,,,,,,,,,1*1D
$GNGSA,A,1,,,,,,,,,,,,,,,,2*1E
$GNGSA,A,1,,,,,,,,,,,,,,,,3*1F
$GNGSA,A,1,,,,,,,,,,,,,,,,4*18
$GPGSV,1,1,00,1*64
$GLGSV,1,1,00,1*78
$GAGSV,1,1,00,7*73
$GBGSV,1,1,00,1*76
$GBGSV,1,1,00,3*74
$GNRMC,235953.012,V,,,,,,,050180,,,N,V*2D
$GNVTG,,T,,M,,N,,K,N*32
$GNGGA,235954.012,,,,,0,0,,,M,,M,,*59
$GNGLL,,,,,235954.012,V,N*6B
$GNGSA,A,1,,,,,,,,,,,,,,,,1*1D
$GNGSA,A,1,,,,,,,,,,,,,,,,2*1E
$GNGSA,A,1,,,,,,,,,,,,,,,,3*1F
$GNGSA,A,1,,,,,,,,,,,,,,,,4*18
$GPGSV,1,1,00,1*64
$GLGSV,1,1,00,1*78
$GAGSV,1,1,00,7*73
$GBGSV,1,1,00,1*76
$GBGSV,1,1,00,3*74
$GNRMC,235954.012,V,,,,,,,050180,,,N,V*2A
$GNVTG,,T,,M,,N,,K,N*32
$GNGGA,235955.012,,,,,0,0,,,M,,M,,*58
$GNGLL,,,,,235955.012,V,N*6A
$GNGSA,A,1,,,,,,,,,,,,,,,,1*1D
$GNGSA,A,1,,,,,,,,,,,,,,,,2*1E
$GNGSA,A,1,,,,,,,,,,,,,,,,3*1F
$GNGSA,A,1,,,,,,,,,,,,,,,,4*18
$GPGSV,1,1,00,1*64
$GLGSV,1,1,00,1*78
$GAGSV,1,1,00,7*73
$GBGSV,1,1,00,1*76
$GBGSV,1,1,00,3*74
$GNRMC,235955.012,V,,,,,,,050180,,,N,V*2B
$GNVTG,,T,,M,,N,,K,N*32
$GNGGA,235956.012,,,,,0,0,,,M,,M,,*5B
$GNGLL,,,,,235956.012,V,N*69
$GNGSA,A,1,,,,,,,,,,,,,,,,1*1D
$GNGSA,A,1,,,,,,,,,,,,,,,,2*1E
$GNGSA,A,1,,,,,,,,,,,,,,,,3*1F
$GNGSA,A,1,,,,,,,,,,,,,,,,4*18
$GPGSV,1,1,00,1*64
$GLGSV,1,1,00,1*78
$GAGSV,1,1,00,7*73
$GBGSV,1,1,00,1*76
$GBGSV,1,1,00,3*74
$GNRMC,235956.012,V,,,,,,,050180,,,N,V*28
$GNVTG,,T,,M,,N,,K,N*32
$GNGGA,235957.012,,,,,0,0,,,M,,M,,*5A
$GNGLL,,,,,235957.012,V,N*68
$GNGSA,A,1,,,,,,,,,,,,,,,,1*1D
$GNGSA,A,1,,,,,,,,,,,,,,,,2*1E
$GNGSA,A,1,,,,,,,,,,,,,,,,3*1F
$GNGSA,A,1,,,,,,,,,,,,,,,,4*18
$GPGSV,1,1,00,1*64
$GLGSV,1,1,00,1*78
$GAGSV,1,1,00,7*73
$GBGSV,1,1,00,1*76
$GBGSV,1,1,00,3*74
$GNRMC,235957.012,V,,,,,,,050180,,,N,V*29
$GNVTG,,T,,M,,N,,K,N*32
$GNGGA,235958.012,,,,,0,0,,,M,,M,,*55
$GNGLL,,,,,235958.012,V,N*67
$GNGSA,A,1,,,,,,,,,,,,,,,,1*1D
$GNGSA,A,1,,,,,,,,,,,,,,,,2*1E
$GNGSA,A,1,,,,,,,,,,,,,,,,3*1F
$GNGSA,A,1,,,,,,,,,,,,,,,,4*18
source [find interface/jlink.cfg]
transport select swd
source [find target/stm32l4x.cfg]

set CHIPNAME STM32L431CC
gdb_port 2331

set DEFUALT_IMAGE "cmake_build/PELAGIC_L431/develop/GCC_ARM/pelagic_l431_mbed.elf"
#set DEFUALT_IMAGE "BUILD/PELAGIC_L431/GCC_ARM/pelagic_l431_mbed.elf"

# Get current image path from CLI if provided with [-c "set IMAGE $(IMAGEBASE).elf"]
if { ![info exists IMAGE] } {
    puts "IMAGE not set; using $DEFUALT_IMAGE"
    set IMAGE $DEFUALT_IMAGE
}
puts "--------------------------------------------------------"
puts "Flashing $IMAGE"
puts "--------------------------------------------------------"

# Set up CPU debug session
init
reset halt

# Erase all flash (bank 0) and program
stm32l4x mass_erase 0
puts "*********  ERASE COMPLETE  *********"
flash write_image $IMAGE
reset halt
puts "*********  WRITE COMPLETE  *********"
flash verify_image $IMAGE
puts "*********  VERIFY COMPLETE *********"

# Exit session with CPU halted
exit

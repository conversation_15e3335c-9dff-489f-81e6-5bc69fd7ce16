{
    "configurations": [
        {
            "name": "STM32_Linux",
            "includePath": [
                "${workspaceFolder}/original_source/cpu",
                "${workspaceFolder}/original_source/rtx",
                "${workspaceFolder}/original_source/common",
                "${workspaceFolder}/original_source/vms",
                "${workspaceFolder}/original_source/devices",
                "${workspaceFolder}/original_source/shell",
                "${workspaceFolder}/mbed-os"
                // "${workspaceFolder}/tests",
            ],
            "defines": [
                "DEBUG",            // <PERSON> defines
                "USE_HAL_DRIVER",   // STM defines
                "USE_LL_DRIVER",
                "USE_FULL_LL_DRIVER",
                "STM32L431xx",
                "TARGET_STM32L431xC",
                "TOOLCHAIN_GCC",    // arm-none-eabi-gcc defines
                "__CORTEX_M4", 
                "__CMSIS_RTOS",
                "PELAGIC",          // Pelagic defines
                "TARGET_PLACO",
                "TARGET_VMS",
                "HAVE_V1",
                "DEVICE_SERIAL",    // mbed defines
                "DEVICE_SERIAL_ASYNCH",
                "DEVICE_I2C",
                "DEVICE_RTC",
                "DEVICE_USTICKER",
                "DEVICE_INTERRUPTIN",
                "MBED_CONF_TARGET_CONSOLE_UART",
                "MBED_MINIMAL_PRINTF",
                "MBED_CONF_RTOS_API_PRESENT"
            ],
            "compilerPath": "/usr/bin/arm-none-eabi-gcc",
            // "compilerPath": "/opt/mbed-os-toolchain/gcc-arm-none-eabi-10.3-2021.07/bin/arm-none-eabi-gcc",
            "intelliSenseMode": "linux-gcc-arm",
            "cStandard": "c99",
            "cppStandard": "c++11"
        }
    ],
    "version": 4
}
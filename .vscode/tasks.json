{
	"version": "2.0.0",
	"tasks": [
		{
            "label": "Build",
            "type": "shell",
            "windows": {
                "command": "${config:stm32.toolchain.make}"
            },
            "linux": {
                "command": "make"
            },
            "args": [
                "all"
            ],
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "problemMatcher": [
                "$gcc"
            ],
            "detail": "make all"
        },
        {
            "label": "Clean",
            "type": "shell",
            "windows": {
                "command": "${config:stm32.toolchain.make}"
            },
            "linux": {
                "command": "make"
            },
            "args": [
                "clean"
            ],
            "group": {
                "kind": "none",
                "isDefault": true
            },
            "problemMatcher": [
                "$gcc"
            ],
            "detail": "make clean"
        },
        {
            "label": "Flash",
            "type": "shell",
            "windows": {
                "command": "${config:stm32.toolchain.jlink}"
            },
            "linux": {
                "command": "make"
            },
            "args": [
                "flash"
            ],
            "problemMatcher": [
                "$gcc"
            ]
        },
        {
            "label": "Start Debug Server",
            "type": "shell",
            "windows": {
                "command": "${config:stm32.toolchain.jlink}"
            },
            "linux": {
                "command": "openocd",
                "args": [
                    "-f",
                    "${workspaceFolder}/debug.cfg"
                ],
            },
            "isBackground": true,
            "problemMatcher": []
        },
        {
            "label": "Stop Debug Server",
            "type": "shell",
            "linux": {
                "command": "pkill openocd"
            },
            "isBackground": true,
            "problemMatcher": []
        }
	]
}/home/<USER>/src/pelagic_l431_mbed/debug.cfg
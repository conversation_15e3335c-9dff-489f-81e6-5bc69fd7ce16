# Copyright (c) 2025 ARM Limited. All rights reserved.
# SPDX-License-Identifier: Apache-2.0

cmake_minimum_required(VERSION 3.19.0)
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

set(MBED_PATH ${CMAKE_CURRENT_SOURCE_DIR}/mbed-os CACHE INTERNAL "")
set(MBED_CONFIG_PATH ${CMAKE_CURRENT_BINARY_DIR} CACHE INTERNAL "")
# set(OG_PATH ${CMAKE_CURRENT_SOURCE_DIR}/source CACHE INTERNAL "")
set(APP_TARGET pelagic_l431_mbed)

target_compile_definitions(${APP_TARGET}
    PRIVATE
    # Target and board configuration
    TARGET_PLACO=1
    TARGET_VMS=1
    PELAGIC=1
    STM32L4=1

    # Device version configuration (equivalent to VERS=125)
    DEVICE_VERSION=125
    HAVE_V1=1
    HAVE_ACCEL=1

    # Clock configuration
    OS_CLOCK=20971520

    # Feature flags (matching VERS=125 configuration)
    HAVE_DIP=0

    # Factory test configuration (FACTORY_TEST=1)
    # HAVE_FACTORY_TEST=1

    # Radio configuration - disabled for STM32L431 port
    RADIO=none
    HAVE_RADIO=0

    # CMSIS-RTOS v1 compatibility configuration
    osCMSIS=0x10001
    __CMSIS_RTOS=1
    TOOLCHAIN_GCC=1
    __CORTEX_M4=1
    MBED_CONF_RTOS_API_PRESENT=1
)

# Add include path for CMSIS-RTOS v1 compatibility layer
target_include_directories(${APP_TARGET} PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/mbed-os/cmsis/CMSIS_5/CMSIS/RTOS2/RTX/Include1
)

include(${MBED_PATH}/tools/cmake/app.cmake)

project(${APP_TARGET})

add_subdirectory(${MBED_PATH})

# RTX removed - using mbed-os RTOS instead

add_executable(${APP_TARGET}
    swo_debug.c

    # VMS main control & threads
    source/vms/main.c
    source/vms/gps_thread.c
    source/vms/gps_setting.c
    source/vms/helm.c
    source/vms/modem_thread.c
    source/vms/reboot_thread.c

    # VMS support
    source/vms/battery.c
    source/vms/board_info.c
    source/vms/board_log.c
    source/vms/boat_log.c
    source/vms/cell_signal.c
    source/vms/compress.c
    source/vms/console.c
    source/vms/firmware_update.c
    source/vms/firmware_vms_sensor.c
    source/vms/flash_read.c
    source/vms/flash_store.c
    source/vms/flash_store_vms.c
    source/vms/geo.c
    source/vms/heatshrink_encoder.c
    source/vms/imei.c
    source/vms/modem_bodytrace.c
    source/vms/modem_log.c
    source/vms/nap.c
    source/vms/nap_settings.c
    source/vms/nmea.c
    source/vms/pds.c
    source/vms/pds_firmware.c
    source/vms/pds_messages.c
    source/vms/pds_settings.c
    source/vms/power.c
    source/vms/power_average.c
    source/vms/provision.c
    source/vms/settings.c
    source/vms/solar.c
    source/vms/sun.c
    source/vms/thermistor.c

    # devices
    source/devices/device_power.c
    source/devices/winbond_flash.c
    source/devices/stm32l4_flash.c
    source/devices/gps_quectel.c
    # source/devices/pinmap_common.c  # Conflicts with mbed-os - excluded for STM32L4

    # Factory test files (FACTORY_TEST=1)
    # source/vms/factory_test_common.c
    # source/vms/gps_factory_test.c



    # Common files
    source/common/alarm.c
    source/common/announce.c
    source/common/bits.c
    # source/common/board_fault.c  # Conflicts with mbed-os - excluded for STM32L4
    source/common/build_timestamp.c
    source/common/chip_uid.c
    source/common/clock_freq.c
    source/common/console.c
    source/common/crc16.c
    source/common/datetime.c
    source/common/doxygen-init.c
    source/common/event.c
    source/common/firmware_update.c
    source/common/flash_read.c
    source/common/flash_store.c
    source/common/heatshrink_encoder.c
    source/common/imei.c
    source/common/led.c
    source/common/libc.c
    source/common/mcu_sleep.c
    source/common/os_idle.c
    source/common/port_stubs.c
    source/common/printf.c
    source/common/provision.c
    # source/common/start.c  # Conflicts with mbed-os - excluded for STM32L4
    source/common/system_file.c
    source/common/thermistor.c
    source/common/uid.c

    # Device drivers
    source/devices/lsm303d.c
    source/devices/accel_thread.c


)

#add the equivalent of cmake -DCMAKE_EXPORT_COMPILE_COMMANDS=ON .
set_target_properties(${APP_TARGET} PROPERTIES CMAKE_EXPORT_COMPILE_COMMANDS ON)

target_link_libraries(${APP_TARGET} mbed-os)

target_include_directories(${APP_TARGET}
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}
        ${CMAKE_CURRENT_SOURCE_DIR}/source/common
        ${CMAKE_CURRENT_SOURCE_DIR}/source/devices
        ${CMAKE_CURRENT_SOURCE_DIR}/source/vms
)

mbed_set_post_build(${APP_TARGET})

option(VERBOSE_BUILD "Have a verbose build process")
if(VERBOSE_BUILD)
    set(CMAKE_VERBOSE_MAKEFILE ON)
endif()
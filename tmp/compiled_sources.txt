"-I.
"-I./mbed-os
"-I./mbed-os/cmsis
"-I./mbed-os/cmsis/CMSIS_5
"-I./mbed-os/cmsis/CMSIS_5/CMSIS
"-I./mbed-os/cmsis/CMSIS_5/CMSIS/RTOS2
"-I./mbed-os/cmsis/CMSIS_5/CMSIS/RTOS2/Include
"-I./mbed-os/cmsis/CMSIS_5/CMSIS/RTOS2/RTX
"-I./mbed-os/cmsis/CMSIS_5/CMSIS/RTOS2/RTX/Config
"-I./mbed-os/cmsis/CMSIS_5/CMSIS/RTOS2/RTX/Include
"-I./mbed-os/cmsis/CMSIS_5/CMSIS/RTOS2/RTX/Include1
"-I./mbed-os/cmsis/CMSIS_5/CMSIS/RTOS2/RTX/Source
"-I./mbed-os/cmsis/CMSIS_5/CMSIS/TARGET_CORTEX_M
"-I./mbed-os/cmsis/CMSIS_5/CMSIS/TARGET_CORTEX_M/Include
"-I./mbed-os/cmsis/device
"-I./mbed-os/cmsis/device/RTE
"-I./mbed-os/cmsis/device/RTE/include
"-I./mbed-os/cmsis/device/rtos
"-I./mbed-os/cmsis/device/rtos/include
"-I./mbed-os/drivers
"-I./mbed-os/drivers/device_key
"-I./mbed-os/drivers/device_key/include
"-I./mbed-os/drivers/device_key/include/device_key
"-I./mbed-os/drivers/include
"-I./mbed-os/drivers/include/drivers
"-I./mbed-os/drivers/include/drivers/interfaces
"-I./mbed-os/events
"-I./mbed-os/events/include
"-I./mbed-os/events/include/events
"-I./mbed-os/events/include/events/internal
"-I./mbed-os/hal
"-I./mbed-os/hal/include
"-I./mbed-os/hal/include/hal
"-I./mbed-os/hal/usb
"-I./mbed-os/hal/usb/include
"-I./mbed-os/hal/usb/include/usb
"-I./mbed-os/platform
"-I./mbed-os/platform/cxxsupport
"-I./mbed-os/platform/include
"-I./mbed-os/platform/include/platform
"-I./mbed-os/platform/include/platform/internal
"-I./mbed-os/platform/mbed-trace
"-I./mbed-os/platform/mbed-trace/include
"-I./mbed-os/platform/mbed-trace/include/mbed-trace
"-I./mbed-os/platform/randlib
"-I./mbed-os/platform/randlib/include
"-I./mbed-os/platform/randlib/include/mbed-client-randlib
"-I./mbed-os/platform/randlib/include/mbed-client-randlib/platform
"-I./mbed-os/platform/source
"-I./mbed-os/platform/source/minimal-printf
"-I./mbed-os/rtos
"-I./mbed-os/rtos/include
"-I./mbed-os/rtos/include/rtos
"-I./mbed-os/rtos/include/rtos/internal
"-I./mbed-os/rtos/source
"-I./mbed-os/targets/TARGET_STM
"-I./mbed-os/targets/TARGET_STM/TARGET_STM32L4
"-I./mbed-os/targets/TARGET_STM/TARGET_STM32L4/STM32Cube_FW
"-I./mbed-os/targets/TARGET_STM/TARGET_STM32L4/STM32Cube_FW/CMSIS
"-I./mbed-os/targets/TARGET_STM/TARGET_STM32L4/STM32Cube_FW/STM32L4xx_HAL_Driver
"-I./mbed-os/targets/TARGET_STM/TARGET_STM32L4/STM32Cube_FW/STM32L4xx_HAL_Driver/Legacy
"-I./mbed-os/targets/TARGET_STM/TARGET_STM32L4/TARGET_STM32L431xC
"-I./mbed-os/targets/TARGET_STM/TARGET_STM32L4/TARGET_STM32L431xC/TARGET_PELAGIC_L431
"-I./source
"-I./source/common
"-I./source/devices
"-I./source/vms
"-I/root/pelagic_l431_mbed/mbed-os"

Hello from STM32L4 via SWO!
about to zero 24 bytes of SYS_REG_FILE
resetting timestamp to: 0
timeout on event part lock
[ +0 ] bt      :error read timeout
timeout on event part lock
[ +0 ] bt      :error read timeout
Creating reboot thread
[ +0 ] fs      :info  fs erased partition=[boatlog] secs=0 bytes=61440
[ +0 ] fs      :info  fs erased partition=[eventlog] secs=0 bytes=61440
[ +0 ] fs      :info  fs erased partition=[fw-sensor] secs=0 bytes=1024
[ +0 ] fs      :info  init
[ +0 ] sys     :note  image build=-1788784969 tag=[] battery=21877
[ +0 ] sys     :note  boot CSR=0x1c000603 reserved=0x0 reason=4
[ +0 ] modem   :info  init
[ +0 ] modem   :info  modem_should_run signals=0 MODEM_SIGNAL_FORCE=1
[ +0 ] modem   :info  modem run state=[skip-no-time]
[ +0 ] modem   :prof  modem sleep seconds=900
[ +0 ] helm    :info  init
[ +0 ] mems    :error not present chip=[lsm303d] result=0x0
[ +0 ] gps     :error command not confirmed name=[rmc-gga-gsa]
[ +0 ] gps     :error command not confirmed name=[rmc-gga-gsa]
[ +0 ] gps     :prof  normal
[ +0 ] gps     :error command not confirmed name=[always-locate]
[ +0 ] gps     :error command not confirmed name=[rmc-gga-gsa]
[ +0 ] gps     :error command not confirmed name=[rmc-gga-gsa]
[ +0 ] gps     :error command not confirmed name=[always-locate]
[ +0 ] gps     :prof  low res
[ +0 ] gps     :info  init
[ +0 ] gps     :error command not confirmed name=[full]
[ +0 ] gps     :error command not confirmed name=[rmc-gga-gsa]
[ +0 ] gps     :error command not confirmed name=[rmc-gga-gsa]
[ +0 ] gps     :error command not confirmed name=[full]
[ +0 ] gps     :prof  high res
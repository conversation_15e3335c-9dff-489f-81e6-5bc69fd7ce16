# Serial Timeout Function Fix

## Problem Description

The `serial_read_timeout()` function in `original_source/devices/serial_api_stubs.c` was hanging indefinitely due to the use of `us_ticker_read()`, which was broken and always returned 0. This caused the timeout mechanism to fail, leading to infinite loops in serial communication code.

## Root Cause

From the timing function tests, we identified that several timing functions were not working:

**FAILED Functions:**
- `us_ticker_read()` - Always returned 0 (should increment in microseconds)
- `wait_us()` - Depends on `us_ticker_read()`
- `wait_ms()` - Calls `wait_us()` internally
- `port_delay_ms()` - Called `wait_ms()` internally

**WORKING Functions:**
- `HAL_GetTick()` - Returns milliseconds, working correctly
- `osDelay()` - RTX delay function that yields control
- `rt_time_get()` - RTX system tick counter
- `os_time` - RTX global time counter

## Solution

### 1. Fixed `serial_read_timeout()` Function

**File:** `original_source/devices/serial_api_stubs.c`

**Changes:**
- Replaced `us_ticker_read()` with `HAL_GetTick()` for timeout calculations
- Replaced `port_delay_ms(1)` with `osDelay(1)` for yielding control
- Added proper includes for `stm32l4xx_hal.h` and `cmsis_os.h`

**Key improvements:**
- **Non-blocking behavior**: `osDelay(1)` yields control to other threads instead of busy waiting
- **Reliable timing**: `HAL_GetTick()` provides accurate millisecond timing
- **Proper timeout handling**: Function now correctly times out instead of hanging

### 2. Fixed `port_delay_ms()` Function

**File:** `original_source/devices/port_stubs.c`

**Changes:**
- Replaced `wait_ms()` with `osDelay()` 
- Added proper includes for `stm32l4xx_hal.h` and `cmsis_os.h`
- Updated comments to explain the fix

**Benefits:**
- Uses working RTX timing mechanisms
- Yields control to other threads
- No longer hangs on broken `us_ticker_read()`

### 3. Fixed Printf Declarations

**File:** `original_source/common/pelagic.h`

**Changes:**
- Uncommented printf and related function declarations that were needed for compilation

## Code Changes Summary

### Before (Broken):
```c
// In serial_read_timeout() for LPUART instances:
uint32_t start_us = us_ticker_read();  // Always returned 0!
uint32_t timeout_us = milliseconds * 1000;

while (bytes_received < bytes) {
    if ((us_ticker_read() - start_us) >= timeout_us) {  // Never true!
        return bytes_received;
    }
    // ... check for data ...
    port_delay_ms(1);  // Called broken wait_ms() -> wait_us() -> us_ticker_read()
}
```

### After (Fixed):
```c
// In serial_read_timeout() for LPUART instances:
uint32_t start_ms = HAL_GetTick();  // Working millisecond timer

while (bytes_received < bytes) {
    if ((HAL_GetTick() - start_ms) >= milliseconds) {  // Works correctly!
        return bytes_received;
    }
    // ... check for data ...
    osDelay(1);  // Yields control, uses working RTX timing
}
```

## Testing

Created `test/serial_timeout_test.c` to verify the fixes:

**Test Coverage:**
- Verifies `HAL_GetTick()` is working correctly
- Verifies `osDelay()` is working correctly  
- Tests `serial_read_timeout()` doesn't hang
- Tests timeout behavior is correct

**Build Command:**
```bash
cd test
make TEST_NAME=serial_timeout_test fresh
```

## Impact

### Positive Effects:
1. **No more hanging**: Serial communication functions now properly timeout
2. **Better performance**: Functions yield control instead of busy waiting
3. **Thread-friendly**: Other threads can run while waiting for serial data
4. **Reliable timing**: Uses proven working timer mechanisms

### Functions Affected:
- `serial_read_timeout()` - Primary fix
- `serial_read()` - Calls `serial_read_timeout()` internally
- `serial_read_signal()` - Calls `serial_read_timeout()` internally  
- `serial_read_timeout_signal()` - Calls `serial_read_timeout()` internally
- `port_delay_ms()` - Now uses working timing mechanism

## Technical Details

### Timer Function Status:
- **HAL_GetTick()**: ✅ Working - Returns milliseconds since boot
- **osDelay()**: ✅ Working - RTX delay function, yields control
- **rt_time_get()**: ✅ Working - RTX system tick counter
- **os_time**: ✅ Working - RTX global time counter
- **us_ticker_read()**: ❌ Broken - Always returns 0
- **wait_us()/wait_ms()**: ❌ Broken - Depend on us_ticker_read()

### RTOS Integration:
- Uses RTX (Real-Time Executive) timing mechanisms
- Properly yields control with `osDelay()` instead of blocking
- Compatible with multi-threaded environment
- Maintains timing accuracy for low-power applications

## Future Considerations

1. **us_ticker_read() Fix**: The root cause of `us_ticker_read()` returning 0 should be investigated and fixed
2. **DMA Implementation**: Consider implementing DMA-based serial I/O for better performance
3. **Interrupt-driven I/O**: Implement interrupt-based serial reading to eliminate polling
4. **Error Handling**: Add more robust error handling for serial communication failures

## Files Modified

1. `original_source/devices/serial_api_stubs.c` - Main fix
2. `original_source/devices/port_stubs.c` - Supporting fix  
3. `original_source/common/pelagic.h` - Printf declarations
4. `original_source/vms/main.c` - Printf include fix
5. `test/serial_timeout_test.c` - New test file
6. `test/Makefile` - Added test target

## Verification

The fix has been verified to:
- ✅ Compile successfully
- ✅ Use working timer mechanisms
- ✅ Provide non-blocking behavior
- ✅ Maintain compatibility with existing code
- ✅ Follow project coding standards

-DARM_MATH_CM4
-DCLOCK_SOURCE=USE_PLL_MSI
-DDEVICE_ANALOGIN=1
-DDEVICE_ANALOGOUT=1
-DDEVICE_CAN=1
-DDEVICE_CRC=1
-DDEVICE_FLASH=1
-DDEVICE_I2C=1
-DDEVICE_I2CSLAVE=1
-DDEVICE_I2C_ASYNCH=1
-DDEVICE_INTERRUPTIN=1
-DDEVICE_LPTICKER=1
-DDEVICE_MPU=1
-DDEVICE_PORTIN=1
-DDEVICE_PORTINOUT=1
-DDEVICE_PORTOUT=1
-DDEVICE_PWMOUT=1
-DDEVICE_RESET_REASON=1
-DDEVICE_RTC=1
-DDEVICE_SERIAL=1
-DDEVICE_SERIAL_ASYNCH=1
-DDEVICE_SERIAL_FC=1
-DDEVICE_SLEEP=1
-DDEVICE_SPI=1
-DDEVICE_SPISLAVE=1
-DDEVICE_SPI_ASYNCH=1
-DDEVICE_STDIO_MESSAGES=1
-DDEVICE_TRNG=1
-DDEVICE_USTICKER=1
-DDEVICE_VERSION=1
-DDEVICE_WATCHDOG=1
-DEXTRA_IDLE_STACK_REQUIRED
-DLPTICKER_DELAY_TICKS=0
-DMBED_CONF_DRIVERS_OSPI_CSN=OSPI_FLASH1_CSN
-DMBED_CONF_DRIVERS_OSPI_DQS=OSPI_FLASH1_DQS
-DMBED_CONF_DRIVERS_OSPI_IO0=OSPI_FLASH1_IO0
-DMBED_CONF_DRIVERS_OSPI_IO1=OSPI_FLASH1_IO1
-DMBED_CONF_DRIVERS_OSPI_IO2=OSPI_FLASH1_IO2
-DMBED_CONF_DRIVERS_OSPI_IO3=OSPI_FLASH1_IO3
-DMBED_CONF_DRIVERS_OSPI_IO4=OSPI_FLASH1_IO4
-DMBED_CONF_DRIVERS_OSPI_IO5=OSPI_FLASH1_IO5
-DMBED_CONF_DRIVERS_OSPI_IO6=OSPI_FLASH1_IO6
-DMBED_CONF_DRIVERS_OSPI_IO7=OSPI_FLASH1_IO7
-DMBED_CONF_DRIVERS_OSPI_SCK=OSPI_FLASH1_SCK
-DMBED_CONF_DRIVERS_QSPI_CSN=QSPI_FLASH1_CSN
-DMBED_CONF_DRIVERS_QSPI_IO0=QSPI_FLASH1_IO0
-DMBED_CONF_DRIVERS_QSPI_IO1=QSPI_FLASH1_IO1
-DMBED_CONF_DRIVERS_QSPI_IO2=QSPI_FLASH1_IO2
-DMBED_CONF_DRIVERS_QSPI_IO3=QSPI_FLASH1_IO3
-DMBED_CONF_DRIVERS_QSPI_SCK=QSPI_FLASH1_SCK
-DMBED_CONF_DRIVERS_UART_SERIAL_RXBUF_SIZE=256
-DMBED_CONF_DRIVERS_UART_SERIAL_TXBUF_SIZE=256
-DMBED_CONF_EVENTS_SHARED_DISPATCH_FROM_APPLICATION=0
-DMBED_CONF_EVENTS_SHARED_EVENTSIZE=768
-DMBED_CONF_EVENTS_SHARED_HIGHPRIO_EVENTSIZE=256
-DMBED_CONF_EVENTS_SHARED_HIGHPRIO_STACKSIZE=1024
-DMBED_CONF_EVENTS_SHARED_STACKSIZE=2048
-DMBED_CONF_EVENTS_USE_LOWPOWER_TIMER_TICKER=0
-DMBED_CONF_PLATFORM_CALLBACK_COMPARABLE=1
-DMBED_CONF_PLATFORM_CALLBACK_NONTRIVIAL=0
-DMBED_CONF_PLATFORM_CRASH_CAPTURE_ENABLED=0
-DMBED_CONF_PLATFORM_CTHUNK_COUNT_MAX=8
-DMBED_CONF_PLATFORM_DEEPSLEEP_STATS_VERBOSE=0
-DMBED_CONF_PLATFORM_DEFAULT_SERIAL_BAUD_RATE=9600
-DMBED_CONF_PLATFORM_ERROR_ALL_THREADS_INFO=0
-DMBED_CONF_PLATFORM_ERROR_FILENAME_CAPTURE_ENABLED=0
-DMBED_CONF_PLATFORM_ERROR_HIST_ENABLED=0
-DMBED_CONF_PLATFORM_ERROR_HIST_SIZE=4
-DMBED_CONF_PLATFORM_ERROR_REBOOT_MAX=1
-DMBED_CONF_PLATFORM_FATAL_ERROR_AUTO_REBOOT_ENABLED=0
-DMBED_CONF_PLATFORM_MAX_ERROR_FILENAME_LEN=16
-DMBED_CONF_PLATFORM_MINIMAL_PRINTF_ENABLE_64_BIT=1
-DMBED_CONF_PLATFORM_MINIMAL_PRINTF_ENABLE_FLOATING_POINT=0
-DMBED_CONF_PLATFORM_MINIMAL_PRINTF_SET_FLOATING_POINT_MAX_DECIMALS=6
-DMBED_CONF_PLATFORM_POLL_USE_LOWPOWER_TIMER=0
-DMBED_CONF_PLATFORM_STDIO_BAUD_RATE=9600
-DMBED_CONF_PLATFORM_STDIO_BUFFERED_SERIAL=0
-DMBED_CONF_PLATFORM_STDIO_CONVERT_NEWLINES=1
-DMBED_CONF_PLATFORM_STDIO_CONVERT_TTY_NEWLINES=1
-DMBED_CONF_PLATFORM_STDIO_FLUSH_AT_EXIT=1
-DMBED_CONF_PLATFORM_STDIO_MINIMAL_CONSOLE_ONLY=0
-DMBED_CONF_PLATFORM_USE_MPU=1
-DMBED_CONF_RTOS_API_PRESENT=1
-DMBED_CONF_RTOS_ENABLE_ALL_RTX_EVENTS=0
-DMBED_CONF_RTOS_EVFLAGS_NUM=0
-DMBED_CONF_RTOS_IDLE_THREAD_STACK_SIZE=512
-DMBED_CONF_RTOS_IDLE_THREAD_STACK_SIZE_DEBUG_EXTRA=128
-DMBED_CONF_RTOS_IDLE_THREAD_STACK_SIZE_TICKLESS_EXTRA=256
-DMBED_CONF_RTOS_MAIN_THREAD_STACK_SIZE=4096
-DMBED_CONF_RTOS_MSGQUEUE_DATA_SIZE=0
-DMBED_CONF_RTOS_MSGQUEUE_NUM=0
-DMBED_CONF_RTOS_MUTEX_NUM=0
-DMBED_CONF_RTOS_PRESENT=1
-DMBED_CONF_RTOS_SEMAPHORE_NUM=0
-DMBED_CONF_RTOS_THREAD_NUM=0
-DMBED_CONF_RTOS_THREAD_STACK_SIZE=4096
-DMBED_CONF_RTOS_THREAD_USER_STACK_SIZE=0
-DMBED_CONF_RTOS_TIMER_NUM=0
-DMBED_CONF_RTOS_TIMER_THREAD_STACK_SIZE=768
-DMBED_CONF_TARGET_BOOT_STACK_SIZE=0x400
-DMBED_CONF_TARGET_CONSOLE_UART=1
-DMBED_CONF_TARGET_CUSTOM_TICKERS=1
-DMBED_CONF_TARGET_DEEP_SLEEP_LATENCY=4
-DMBED_CONF_TARGET_DEFAULT_ADC_VREF=NAN
-DMBED_CONF_TARGET_GPIO_RESET_AT_INIT=0
-DMBED_CONF_TARGET_I2C_TIMING_VALUE_ALGO=0
-DMBED_CONF_TARGET_INIT_US_TICKER_AT_BOOT=1
-DMBED_CONF_TARGET_INTERNAL_FLASH_UNIFORM_SECTORS=1
-DMBED_CONF_TARGET_LPTICKER_LPTIM=1
-DMBED_CONF_TARGET_LPTICKER_LPTIM_CLOCK=1
-DMBED_CONF_TARGET_LPUART_CLOCK_SOURCE=\"USE_LPUART_CLK_LSE|USE_LPUART_CLK_PCLK1|USE_LPUART_CLK_PCLK3\"
-DMBED_CONF_TARGET_LSE_AVAILABLE=1
-DMBED_CONF_TARGET_LSE_DRIVE_LOAD_LEVEL=RCC_LSEDRIVE_LOW
-DMBED_CONF_TARGET_MPU_ROM_END=0x0fffffff
-DMBED_CONF_TARGET_RTC_CLOCK_SOURCE=USE_RTC_CLK_LSE_OR_LSI
-DMBED_CONF_TARGET_TICKLESS_FROM_US_TICKER=0
-DMBED_CONF_TARGET_XIP_ENABLE=0
-DMBED_CRC_TABLE_SIZE=16
-DMBED_MINIMAL_PRINTF
-DMBED_STACK_DUMP_ENABLED=0
-DMBED_TICKLESS
-DMBED_TRACE_COLOR_THEME=0
-DMBED_TRAP_ERRORS_ENABLED=1
-DMEM_ALLOC=malloc
-DMEM_FREE=free
-DPELAGIC
-DRADIO=0
-DSTM32L431xx
-DTARGET_CORTEX
-DTARGET_CORTEX_M
-DTARGET_LIKE_CORTEX_M4
-DTARGET_LIKE_MBED
-DTARGET_M4
-DTARGET_MCU_STM32
-DTARGET_MCU_STM32L4
-DTARGET_MCU_STM32L431xC
-DTARGET_NAME=PELAGIC_L431
-DTARGET_PELAGIC_L431
-DTARGET_PELAGIC_L431_MBED
-DTARGET_RTOS_M4_M7
-DTARGET_STM
-DTARGET_STM32L4
-DTARGET_STM32L431xC
-DTARGET_Target
-DTARGET_VMS
-DTOOLCHAIN_GCC
-DTOOLCHAIN_GCC_ARM
-DTRANSACTION_QUEUE_SIZE_SPI=2
-DUSE_FULL_LL_DRIVER
-DUSE_HAL_DRIVER
-D_RTE_
-D__CMSIS_RTOS
-D__CORTEX_M4
-D__FPU_PRESENT=1
-D__MBED__=1
-I/root/pelagic_l431_mbed
-I/root/pelagic_l431_mbed/source/common
-I/root/pelagic_l431_mbed/source/devices
-I/root/pelagic_l431_mbed/source/vms
-I/root/pelagic_l431_mbed/mbed-os/cmsis/CMSIS_5/CMSIS/RTOS2/RTX/Config
-I/root/pelagic_l431_mbed/mbed-os/cmsis/CMSIS_5/CMSIS/RTOS2/RTX/Include
-I/root/pelagic_l431_mbed/mbed-os/cmsis/CMSIS_5/CMSIS/RTOS2/RTX/Include1
-I/root/pelagic_l431_mbed/mbed-os/cmsis/CMSIS_5/CMSIS/RTOS2/RTX/Source
-I/root/pelagic_l431_mbed/mbed-os/cmsis/CMSIS_5/CMSIS/RTOS2/Include
-I/root/pelagic_l431_mbed/mbed-os/cmsis/device/rtos/include
-I/root/pelagic_l431_mbed/mbed-os
-I/root/pelagic_l431_mbed/mbed-os/cmsis/device/RTE/include
-I/root/pelagic_l431_mbed/mbed-os/cmsis/device/.
-I/root/pelagic_l431_mbed/mbed-os/drivers/.
-I/root/pelagic_l431_mbed/mbed-os/drivers/./include
-I/root/pelagic_l431_mbed/mbed-os/drivers/./include/drivers
-I/root/pelagic_l431_mbed/mbed-os/drivers/./include/drivers/internal
-I/root/pelagic_l431_mbed/mbed-os/hal/usb/include
-I/root/pelagic_l431_mbed/mbed-os/hal/usb/include/usb
-I/root/pelagic_l431_mbed/mbed-os/hal/include
-I/root/pelagic_l431_mbed/mbed-os/hal/include/hal
-I/root/pelagic_l431_mbed/mbed-os/platform/cxxsupport/.
-I/root/pelagic_l431_mbed/mbed-os/platform/mbed-trace/include
-I/root/pelagic_l431_mbed/mbed-os/platform/mbed-trace/include/mbed-trace
-I/root/pelagic_l431_mbed/mbed-os/platform/source/minimal-printf/.
-I/root/pelagic_l431_mbed/mbed-os/platform/source/.
-I/root/pelagic_l431_mbed/mbed-os/platform/include
-I/root/pelagic_l431_mbed/mbed-os/platform/include/platform
-I/root/pelagic_l431_mbed/mbed-os/platform/include/platform/internal
-I/root/pelagic_l431_mbed/mbed-os/rtos/.
-I/root/pelagic_l431_mbed/mbed-os/rtos/./include
-I/root/pelagic_l431_mbed/mbed-os/rtos/./include/rtos
-I/root/pelagic_l431_mbed/mbed-os/rtos/./include/rtos/internal
-I/root/pelagic_l431_mbed/mbed-os/rtos/./source
-I/root/pelagic_l431_mbed/mbed-os/targets/TARGET_STM/TARGET_STM32L4/TARGET_STM32L431xC/TARGET_PELAGIC_L431/.
-I/root/pelagic_l431_mbed/mbed-os/targets/TARGET_STM/TARGET_STM32L4/TARGET_STM32L431xC/.
-I/root/pelagic_l431_mbed/mbed-os/targets/TARGET_STM/TARGET_STM32L4/.
-I/root/pelagic_l431_mbed/mbed-os/targets/TARGET_STM/.
-I/root/pelagic_l431_mbed/mbed-os/cmsis/CMSIS_5/CMSIS/TARGET_CORTEX_M/Include
-I/root/pelagic_l431_mbed/mbed-os/targets/TARGET_STM/TARGET_STM32L4/STM32Cube_FW/.
-I/root/pelagic_l431_mbed/mbed-os/targets/TARGET_STM/TARGET_STM32L4/STM32Cube_FW/CMSIS
-I/root/pelagic_l431_mbed/mbed-os/targets/TARGET_STM/TARGET_STM32L4/STM32Cube_FW/STM32L4xx_HAL_Driver
-I/root/pelagic_l431_mbed/mbed-os/targets/TARGET_STM/TARGET_STM32L4/STM32Cube_FW/STM32L4xx_HAL_Driver/Legacy
-Wall
-Wextra
-Wno-unused-parameter
-Wno-missing-field-initializers
-fmessage-length=0
-fno-exceptions
-ffunction-sections
-fdata-sections
-funsigned-char
-fomit-frame-pointer
-g3
-mthumb
-mcpu=cortex-m4
-mfpu=fpv4-sp-d16
-mfloat-abi=softfp
-fno-builtin
-fdiagnostics-color=always
-c
-Os
-o
CMakeFiles/pelagic_l431_mbed.dir/swo_debug.c.obj
-c
/root/pelagic_l431_mbed/swo_debug.c"
# docker run -it -v /home/<USER>/src/pelagic_l431_mbed:/root/pelagic_l431_mbed --name mbed_prj ghcr.io/armmbed/mbed-os-env
# docker run -it -v /dev:/dev -v /home/<USER>/src/pelagic_l431_mbed:/root/pelagic_l431_mbed --name mbed_usb ghcr.io/armmbed/mbed-os-env

# docker exec -it mbed_usb mbed compile -m PELAGIC_L431 -t GCC_ARM
# docker exec -it mbed_usb mbed compile -m PELAGIC_L431 -t GCC_ARM --flash
# docker exec -it mbed_usb mbed compile -m PELAGIC_L431 -t GCC_ARM --flash --sterm
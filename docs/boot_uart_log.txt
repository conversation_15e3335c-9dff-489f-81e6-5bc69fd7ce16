# UART boot log 06/23/2025

## Config

UART bus speeds
- Modem: 9600
- GPS: 115200?

## Session 1

```log
// Modem to MCU
Fetching IMEI... <\x01\xC0\xC1;

// Modem to MCU
<\xE0\x88\x01;

// MCU to Modem
>\x01\x05\x03\0\x03\x10T\xF8\xC8\xEA\xA9\xC0?;

// Modem to MCU
>\xE0\x88\x01;

// GPS to MCU
00: 7035 0000
F1: 0000 0000
V0: 0000 0000 [0001]
01: 0000 0000
U0: 0000 0001 [0000]
D0: 0000 0001
T0: 0000 00A6
Leaving the BROM

[Bootloader] bl_uart_init done
set CP10 and CP11 Full Access 
[CLK] Dynamic Clock Management: Enable
[CLK] frequency meter
CM4(CMSYS) freq [215974]KHz
BUS(FSYS) freq [107974]KHz
SFC freq [107974]KHz
SPISLV freq [0]KHz
I3C freq [107974]KHz
RTC SRAM freq [107974]KHz
hal_flash_init
BL Jump to addr 0x8009000 (0)

$PQTMVER,MODULE_LC76GPANR12A01S,2023/02/13,10:41:57*61
$PAIR001,002,1*38
$PAIR001,021,0*38
$PAIR021,AG3352Q_V2.3.0.AG3352_20230213,S,N,2b31f59,2209141904,,,,,d32ef91c,2209141902,571d3e7,2209141904,,,-15.48,-15.48,-14.02,-15.48,0,1,##,0,0*34

```


## Session 2

```log

Fetching IMEI... <\x01\xC0\xC1;

<\xE0\x88\x01;>\x01\x05\x03\0\x03\x10T\xF8\xC8\xEA\xA9\xC0?;>\xE0\x88\x01;

00: 7035 0000
F1: 0000 0000
V0: 0000 0000 [0001]
01: 0000 0000
U0: 0000 0001 [0000]
D0: 0000 0001
T0: 0000 00A6
Leaving the BROM

[Bootloader] bl_uart_init done
set CP10 and CP11 Full Access 
[CLK] Dynamic Clock Management: Enable
[CLK] frequency meter
CM4(CMSYS) freq [215948]KHz
BUS(FSYS) freq [107974]KHz
SFC freq [107974]KHz
SPISLV freq [0]KHz
I3C freq [107974]KHz
RTC SRAM freq [107974]KHz
hal_flash_init
BL Jump to addr 0x8009000 (0)

$PQTMVER,MODULE_LC76GPANR12A01S,2023/02/13,10:41:57*61
$PAIR001,002,1*38
$PAIR001,021,0*38
$PAIR021,AG3352Q_V2.3.0.AG3352_20230213,S,N,2b31f59,2209141904,,,,,d32ef91c,2209141902,571d3e7,2209141904,,,-15.48,-15.48,-14.02,-15.48,0,1,##,0,0*34

$PMTK314,0,1,0,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0*29
```
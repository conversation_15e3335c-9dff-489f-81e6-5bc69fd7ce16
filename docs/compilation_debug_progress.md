# STM32L431 Compilation Debug Progress

## Overview
This document tracks the progress of debugging compilation issues when porting the Pelagic codebase from Kinetis to STM32L431 using mbed OS.

## Initial Issues and Fixes

### ✅ 1. RTOS Include Issues
**Problem**: `osMutexId`, `osMutexDef_t`, and `osThreadId` were undefined
**Solution**: Added proper CMSIS-RTOS includes to `source/common/pelagic.h`
```c
// Include RTOS headers for thread and mutex types
#ifndef TARGET_TEST
#include "cmsis_os.h"
#endif
```

### ✅ 2. Function Declaration Conflicts
**Problem**: Custom declarations of standard library functions conflicted with built-in functions
**Solution**: Removed conflicting declarations from `source/common/pelagic.h`
- Removed custom declarations for `printf`, `memcpy`, `memset`, `strlen`, etc.
- Kept only custom implementations that don't conflict

### ✅ 3. Chip UID Implementation
**Problem**: `chip_uid.c` was trying to include `kinetis.h` and use Kinetis-specific SIM registers
**Solution**: Added conditional compilation for STM32L431
```c
#if defined(TARGET_TEST)
    // Test implementation
#elif defined(TARGET_STM32L4) || defined(STM32L431xx)
    // STM32L4 unique ID at 0x1FFF7590
    uint32_t *uid_base = (uint32_t *)0x1FFF7590;
    // Read 10 bytes from the 96-bit unique ID
#else
    // Kinetis implementation using SIM registers
#endif
```

### ✅ 4. OS_CLOCK Definition
**Problem**: `OS_CLOCK` was undeclared in `announce.c`
**Solution**: Added `OS_CLOCK=20971520` to CMakeLists.txt compile definitions

### ✅ 5. Kinetis Flash Driver
**Problem**: `kinetis_flash.c` was trying to include Kinetis-specific headers and registers
**Solution**: Made the entire Kinetis flash driver conditional
```c
// Only compile Kinetis flash driver for Kinetis targets
#if !defined(TARGET_STM32L4) && !defined(STM32L431xx)
// ... entire kinetis flash implementation
#endif
```

### ✅ 6. BNET Conditional Compilation
**Problem**: BNET-related files were being compiled but referenced undefined variables like `bnet_tid`
**Solution**: Added `#ifdef HAVE_BNET` conditional compilation to:
- `source/bnet/bnet_vms_settings.c`
- `source/bnet/bnet_console_keepalive.c` 
- `source/bnet/bnet_console_common.c`

### ✅ 7. Peripheral Names Mapping
**Problem**: `PeripheralNames.h` used Kinetis peripheral base addresses that don't exist on STM32L431
**Solution**: Added conditional compilation for STM32L431 peripheral mappings
```c
typedef enum {
#if defined(TARGET_STM32L4) || defined(STM32L431xx)
    // STM32L431 UART/USART base addresses
    UART_0 = (int)USART1_BASE,
    UART_1 = (int)USART2_BASE,
    UART_2 = (int)USART3_BASE
#else
    // Kinetis UART base addresses
    UART_0 = (int)UART0_BASE,
    UART_1 = (int)UART1_BASE,
    UART_2 = (int)UART2_BASE
#endif
} UARTName;
```

## Compilation Progress Timeline

| Attempt | Progress | Main Issue |
|---------|----------|------------|
| Initial | 0% | RTOS includes missing |
| After RTOS fix | ~18% | OS_CLOCK undefined |
| After OS_CLOCK | ~82% | Kinetis flash issues |
| After Kinetis fix | ~84% | BNET variables undefined |
| After BNET fix | ~84.8% | Peripheral base addresses |

## Current Status
- **Latest Progress**: 84.8% compilation complete
- **Last Fixed**: Peripheral base address mapping for STM32L431
- **Next Steps**: Continue fixing remaining compilation errors

## Key Learnings
1. **Conditional Compilation Strategy**: Use `#if defined(TARGET_STM32L4) || defined(STM32L431xx)` for STM32-specific code
2. **BNET Dependency**: Many files depend on BNET being enabled - need `#ifdef HAVE_BNET` guards
3. **Peripheral Mapping**: STM32L431 uses different peripheral naming (USART vs UART, different base addresses)
4. **Build System**: mbed automatically includes all source files, requiring conditional compilation rather than file exclusion

## Files Modified
- `source/common/pelagic.h` - RTOS includes, function declarations
- `source/common/chip_uid.c` - STM32L431 UID implementation  
- `source/common/chip_uid.h` - Added stdint.h include
- `source/devices/kinetis_flash.c` - Made conditional
- `source/devices/kinetis_flash.h` - Made conditional
- `source/common/flash_store.h` - Conditional device declarations
- `source/vms/board_info.c` - Conditional flash operations
- `source/bnet/bnet_vms_settings.c` - Added HAVE_BNET guards
- `source/bnet/bnet_console_keepalive.c` - Added HAVE_BNET guards
- `source/bnet/bnet_console_common.c` - Added HAVE_BNET guards
- `source/devices/PeripheralNames.h` - STM32L431 peripheral mapping
- `CMakeLists.txt` - Added compile definitions and source file list

2025-06-05T19:05:55.681122 | PSU:   0.0 mW | DMM:  50.0 mW | Device:   50.0 mW    # 0 mW charge

2025-06-05T19:06:00.243078 | PSU: 190.0 mW | DMM: -74.7 mW | Device: -264.6 mW    # est. 125 mW charge for 50 mW draw

2025-06-05T19:26:57.481480 | PSU: 110.8 mW | DMM: -13.0 mW | Device: -123.8 mW    # est. 63 mW charge for 50 mW draw


125 = 190*efficiency   # 0.657
63 = 110.8*efficiency  # 0.568
43/103 = 0.417



190 - (-74.7) = 264.7 mW
190 + (-74.7) = 115.3 mW


# Measured quantities
p_sol_in = 190                # in to system (must be positive)
p_batt_supply = -74.7         # in to system (positive for discharge, negative for charge)



# Calculated quantities
p_device_draw = ?                                 # consumed by device (must be positive)
p_batt_charge = p_batt_supply - p_device_draw     # power charging the battery (zero if no charging, positive if charging)
p_batt_charge = (p_sol_in) * efficiency           # >= 0   


p_device_draw = p_batt_charge - p_batt_supply
              = p_sol_in * efficiency - p_batt_supply 
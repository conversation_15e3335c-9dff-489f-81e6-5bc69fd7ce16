# LPUART Interrupt-Driven Serial Implementation

## Overview

Implemented interrupt-driven serial communication for LPUART to replace the broken polling approach that was hanging on `us_ticker_read()`. This provides efficient, non-blocking serial data reception for GPS communication.

## Problem Solved

**Original Issue:**
- `serial_read_timeout()` was hanging indefinitely due to broken `us_ticker_read()` function
- Used 1ms polling delays which were too slow for serial data rates
- Blocked other threads during serial operations

**Solution:**
- Interrupt-driven LPUART receive buffer
- Non-blocking data collection in interrupt handler
- Efficient buffer management with circular buffer
- Proper thread yielding with `osDelay()`

## Implementation Details

### 1. Interrupt Buffer Management

**File:** `original_source/devices/serial_api_stubs.c`

**Buffer Structure:**
```c
#define LPUART_RX_BUFFER_SIZE 256
static volatile uint8_t lpuart_rx_buffer[LPUART_RX_BUFFER_SIZE];
static volatile uint16_t lpuart_rx_head = 0;
static volatile uint16_t lpuart_rx_tail = 0;
static volatile bool lpuart_rx_initialized = false;
```

**Key Functions:**
- `lpuart_rx_buffer_available()` - Returns number of bytes available
- `lpuart_rx_buffer_empty()` - Checks if buffer is empty
- `lpuart_rx_buffer_read()` - Reads one byte from buffer
- `lpuart_rx_buffer_write()` - Writes one byte to buffer (from ISR)
- `lpuart_rx_buffer_clear()` - Clears the buffer

### 2. Interrupt Handler

**LPUART1_IRQHandler():**
- Overrides weak definition in `startup_stm32l431xx.S`
- Handles RXNE (Receive Data Register Not Empty) interrupt
- Reads data directly from `LPUART1->RDR` register
- Stores data in circular buffer
- Clears error flags (ORE, FE, PE, NE)

**Interrupt Setup:**
- Enables UART_IT_RXNE interrupt
- Sets NVIC priority to 1
- Enables LPUART1_IRQn in NVIC

### 3. Modified serial_read_timeout()

**For LPUART instances:**
1. **Initialize interrupt RX** (first call only)
2. **Read from buffer** instead of polling hardware
3. **Use HAL_GetTick()** for reliable timeout
4. **Yield with osDelay(1)** when no data available
5. **Return partial data** on timeout

**For regular UART instances:**
- Continue using `HAL_UART_Receive()` with timeout
- No changes to existing behavior

### 4. Debug and Instrumentation

**Added Functions:**
- `lpuart_debug_buffer_status()` - Shows buffer state
- Detailed printf logging in `serial_read_timeout()`
- Buffer status logging in GPS test

**GPS Test Enhancements:**
- Buffer status before/after each read attempt
- Detailed byte-by-byte logging
- Character display with escape sequences

## Technical Benefits

### 1. Performance Improvements
- **No more hanging**: Eliminates infinite loops from broken `us_ticker_read()`
- **Faster response**: Interrupt-driven vs 1ms polling
- **Better throughput**: Can handle higher baud rates efficiently
- **Thread-friendly**: Yields control properly with `osDelay()`

### 2. Reliability Improvements
- **Error handling**: Clears UART error flags in ISR
- **Buffer overflow protection**: Drops bytes when buffer full
- **Timeout accuracy**: Uses working `HAL_GetTick()` timer
- **Partial data return**: Returns data received before timeout

### 3. System Integration
- **RTOS compatible**: Proper thread yielding
- **Low power friendly**: Interrupt-driven, not polling
- **Existing API preserved**: No changes to calling code
- **Backward compatible**: Regular UARTs unchanged

## Memory Usage

**Static Memory:**
- 256-byte receive buffer per LPUART instance
- 3 volatile variables for buffer management
- Minimal stack usage in ISR

**Runtime Overhead:**
- Interrupt latency: ~10-20 CPU cycles
- Buffer management: O(1) operations
- No dynamic memory allocation

## Configuration

**Buffer Size:**
```c
#define LPUART_RX_BUFFER_SIZE 256  // Adjustable based on needs
```

**Interrupt Priority:**
```c
NVIC_SetPriority(LPUART1_IRQn, 1);  // High priority for serial data
```

**Timeout Behavior:**
- Uses `HAL_GetTick()` (millisecond resolution)
- Returns partial data on timeout
- Yields every 1ms when waiting

## Testing

**GPS Serial Test Enhanced:**
- Added buffer status monitoring
- Detailed logging of read operations
- Character-by-character data display
- Timing measurements

**Build Command:**
```bash
cd test
make TEST_NAME=gps_serial_test fresh
```

## Files Modified

1. **`original_source/devices/serial_api_stubs.c`**
   - Added interrupt buffer management
   - Implemented `LPUART1_IRQHandler()`
   - Modified `serial_read_timeout()` for LPUART
   - Added debug functions

2. **`original_source/devices/serial_api_stubs.h`**
   - Added `lpuart_debug_buffer_status()` declaration

3. **`test/gps_serial_test.c`**
   - Added buffer status monitoring
   - Enhanced debug output
   - Added timing measurements

## Future Enhancements

1. **Multi-LPUART Support**: Extend to support multiple LPUART instances
2. **DMA Integration**: Use DMA for even more efficient data transfer
3. **Flow Control**: Add RTS/CTS support for high-speed communication
4. **Error Statistics**: Track buffer overflows and UART errors
5. **Dynamic Buffer Size**: Allow runtime buffer size configuration

## Compatibility Notes

- **STM32L431**: Primary target, LPUART1 supported
- **Other STM32L4**: Should work with minor modifications
- **Regular UART**: No changes, continues using HAL functions
- **Thread Safety**: ISR and main thread access different buffer pointers

## Performance Characteristics

**Interrupt Latency:** ~10-20 CPU cycles
**Buffer Access:** O(1) read/write operations
**Memory Overhead:** 256 bytes + 6 bytes management
**CPU Usage:** Minimal, only during data reception
**Power Impact:** Reduced (interrupt vs polling)

This implementation provides a robust, efficient solution for LPUART serial communication while maintaining compatibility with existing code and following the project's architectural guidelines.

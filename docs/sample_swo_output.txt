Hello from STM32L4 via SWO!
about to zero 24 bytes of SYS_REG_FILE
resetting timestamp to: 0
timeout on event part lock
[ +0 ] bt      :error read timeout
timeout on event part lock
[ +0 ] bt      :error read timeout
Creating reboot thread
[ +0 ] fs      :info  fs erased partition=[boatlog] secs=0 bytes=61440
[ +0 ] fs      :info  fs erased partition=[eventlog] secs=0 bytes=61440
[ +0 ] fs      :info  fs erased partition=[fw-sensor] secs=0 bytes=1024
[ +0 ] fs      :info  init
[ +0 ] sys     :note  image build=-1788785594 tag=[] battery=21877
[ +0 ] sys     :note  boot CSR=0x1c000603 reserved=0x0 reason=4



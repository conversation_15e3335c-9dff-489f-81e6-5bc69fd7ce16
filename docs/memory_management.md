# Memory Management in PelagicOS for STM32L431

The main memory management tasks for the pelagic device are 
- OTA firmware updates
- Persisting device settings
- Persisting device status      (provisioning)
- Persisting device reset data  (reboot reason, etc.)
- Storing device logs

## Relevant Files

These should be relevant to flash memory routines
<!-- Assuming this file is in $(project_root)/docs -->

Device operating mode = provisioning state
- [common/provision.c](../original_source/common/provision.c)
- [common/provision.h](../original_source/common/provision.h)

System state stored in battery-backed registers
- [common/system_file.c](../original_source/common/system_file.c)
- [common/system_file.h](../original_source/common/system_file.h)

Flash memory map
- [common/memmap.h](../original_source/common/memmap.h)
- [common/memmap.c](../original_source/common/memmap.c)

Application flash data management
- [common/flash_store.c](../original_source/vms/flash_store_vms.c)

Flash drivers
- [common/devices/stm32l4_flash.c](../original_source/common/devices/stm32l4_flash.c)
- [common/devices/stm32l4_flash.c](../original_source/common/flash_read.c)
- [common/devices/stm32l4_flash.c](../original_source/common/flash_store.c)
- [common/devices/stm32l4_flash.c](../original_source/common/flash_store.h)

Firmware update routines
- [common/firmware_update.c](../original_source/common/firmware_update.c)
- [common/firmware.h](../original_source/common/firmware.h)

Old kinetis flash driver
- [common/devices/kinetis_flash.c](../original_source/common/devices/kinetis_flash.c)

## STM32L431 Memory Region Symbols

```C
#define FLASH_BASE            (0x08000000UL) /*!< FLASH(up to 256 KB) base address */
#define FLASH_END             (0x0803FFFFUL) /*!< FLASH END address                */
#define FLASH_BANK1_END       (0x0803FFFFUL) /*!< FLASH END address of bank1       */
#define SRAM1_BASE            (0x20000000UL) /*!< SRAM1(up to 48 KB) base address  */
#define SRAM2_BASE            (0x10000000UL) /*!< SRAM2(16 KB) base address */
#define PERIPH_BASE           (0x40000000UL) /*!< Peripheral base address */
#define QSPI_BASE             (0x90000000UL) /*!< QUADSPI memories accessible over AHB base address */

#define QSPI_R_BASE           (0xA0001000UL) /*!< QUADSPI control registers base address */
#define SRAM1_BB_BASE         (0x22000000UL) /*!< SRAM1(96 KB) base address in the bit-band region */
#define PERIPH_BB_BASE        (0x42000000UL) /*!< Peripheral base address in the bit-band region */

/* Legacy defines */
#define SRAM_BASE             SRAM1_BASE
#define SRAM_BB_BASE          SRAM1_BB_BASE

#define SRAM1_SIZE_MAX        (0x0000C000UL) /*!< maximum SRAM1 size (up to 48 KBytes) */
#define SRAM2_SIZE            (0x00004000UL) /*!< SRAM2 size (16 KBytes) */

#define FLASH_SIZE_DATA_REGISTER ((uint32_t)0x1FFF75E0)

#define FLASH_SIZE               (((((*((uint32_t *)FLASH_SIZE_DATA_REGISTER)) & (0x0000FFFFU)) == 0x0000FFFFU)) ? (0x100U << 10U) : \
                                  (((*((uint32_t *)FLASH_SIZE_DATA_REGISTER)) & (0x0000FFFFU)) << 10U))
```

## Memory Region Details

Human readable...

### Flash

Active Firmware
- Size: 120K
- Start: 0x08000000
- End: 0x08020000

Reserved Firmware / Log Data Staging
- Size: 120K
- Start: 0x08020000
- End: 0x08040000

Provisioning Data
- Size: 1K

Settings Data
- Size: 1K

Board Fault Data
- Size: 1K


### RAM

?

## STM32L431 Boot Options

The STM32L431 has 3 boot options:
1. Boot from main flash
2. Boot from system flash
3. Boot from SRAM1

Main flash refers to the location of the active firmware
System flash contains the factory bootloader from ST -- non-erasable
SRAM1 has 48k that can be written before a warm reset to boot from

I thought there was a mechanism to aliase a different flash address to 0x08000000, but that's not the case.
Booting from SRAM won't help because there's only 48k. The ST bootloader probably won't be useful since it requires
some specific write sequence to load a new image over one of the serial interfaces. It's possible though.

As I'm looking at firmware_update.c, I see it calls out that its functions need to be run from RAM. I guess if I can
just relocate the relevant functions to SRAM1, then I could use them to write the new image to flash without a full bootloader.


## Notes in progress

Flash partitions for data

```C
static uint8_t boatlog_buffer[FLASH_PAGE_SIZE] __attribute__ ((aligned (4) ));
static uint8_t event_buffer[FLASH_PAGE_SIZE] __attribute__ ((aligned (4) ));

flash_partition_t boatlog_partition = {
        .name = "boatlog", 
        .offset = BOATLOG_PART_OFFSET, 
        .size = BOATLOG_PART_SIZE, 
        .staging_buffer = boatlog_buffer, 
        .device = DEFAULT_FLASH_DEVICE,
};

flash_partition_t event_partition = {
        .name = "eventlog", 
        .offset = EVENT_PART_OFFSET, 
        .size = EVENT_PART_SIZE, 
        .staging_buffer = event_buffer, 
        .device = DEFAULT_FLASH_DEVICE
};

// firmware_update assumes the update is stored in the internal flash
flash_partition_t firmware_partition = {
        .name = "firmware", 
        .offset = FIRMWARE_PART_OFFSET, 
        .size = FIRMWARE_PART_SIZE,
        .staging_buffer = the_cache, 
        .device = &stm_flash_device, 
        .is_raw = true
};
```

High-level flash map

```c
enum {
        FIRMWARE_ADDR           = 0x08020000, // 128k, end of linker-defined flash
        // FIRMWARE_ADDR           = (128*1024),
        FIRMWARE_SIZE           = (120*1024), // VMS firmware can be 120KB
        // why is there a 4k gap between firmware and provision? 248k-->252k

        // TODO Ian: confirm appropriate locations and sizes
        // Pretty sure this is fine actually
        PROVISION_ADDR          = (252 * 1024), 
        PROVISION_SIZE          = 1024,

        SETTINGS_ADDR           = (253 * 1024),
        SETTINGS_SIZE           = 1024,

        BOARD_FAULT_ADDR        = (254 * 1024),
        BOARD_FAULT_SIZE        = (1024),

        BOARD_INFO_ADDR         = (255 * 1024),
        BOARD_INFO_SIZE         = (1024),
        // So 4k allocated at end of flash for little details
};
```



# Programming the Flash #

## One-time flashing ##

$ sh ./segger-flash.sh image.bin

This requires the Segger utilities to be installed.

Typically it takes 1 second to flash ~14 kilobytes. i.e. a 90K image will take 6 to 8 seconds.

*** DO NOT INTERRUPT THE FLASH PROGRAMMING YOU WILL BRICK THE BOARD ***

The board will boot with the new image after a successful flash.

## Development flashing via GDB ##

The Segger GDB server should be running on another window.

    $ /Applications/SEGGER/JLink/JLinkGDBServer -device MKL16Z256xxx4 -if swd -speed 4000

One a second window:

    $ arm-none-ebi-gdb
    (gdb) target remote localhost:2331   <-- connects to openocd
         (can be shortened to tar rem :2331)
    (gdb) monitor halt <--- halts the processor
    (gdb) load images/vms-vNN-RADIO-TAG.elf <--- load the image into the MCU
    MDM: Chip is unsecured. Continuing.
    target state: halted
    target halted due to debug-request, current mode: Thread
    xPSR: 0x01000000 pc: 0x000000c0 msp: 0x20006000
    Loading section .isr_vector, size 0xf0 lma 0x0
    Loading section .flash_protect, size 0x10 lma 0x400
    Loading section .text, size 0x100c8 lma 0x410
    Loading section .ARM.exidx, size 0x8 lma 0x104d8
    Loading section .sram.text, size 0x2f0 lma 0x104e0
    Loading section .data, size 0x29c lma 0x107d0
    (gdb) monitor reset <-- resets the processor
    (gdb) continue  <-- continues execution.

CTRL-C can be use to stop the MCU and run various gdb commands.


To reboot the MCU:

    (gdb) monitor reset

For the KL16 processor, up to two breakpoints can be set.

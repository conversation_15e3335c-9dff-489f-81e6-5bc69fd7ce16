# VMS Shell Commands #

The VMS software provides a primitive shell on the MCU's serial debugging port.

Settings: 115,200, 8-bits, 1-stop bit, no parity

___YOU MAY NEED TO REBOOT THE BOARD AFTER RUNNING SOME COMMANDS___

When the board is reset, the boot screen should be something like

```
BoatOS - Pelagic Data Systems (c) 2014, 2015
Built 1505200252 Tag v1.6 Target 5
Chip UID 45:4e:60:30:19:80:2e:00:47:00
Clocks cpu 20mhz bus 20mhz
[ +0 ] flash   :info  init
[ +0 ] fs      :info  init
[ +0 ] sys     :note  image build=1505200252 tag=[v1.6] battery=43185
[ +0 ] sys     :note  boot SRS0=0x0 SRS1=0x4
[ +0 ] reboot  :info  init
[ +0 ] helm    :info  init
[ +0 ] mems    :error not present chip=[lsm303d] result=0xff
[ +0 ] radio   :info  init chip=[cc1200] vers=0x24
[ +0 ] radio   :prof  power on
[ +0 ] boatnet :info  init
vms>
```

When the GPS acquires a satellite lock, the following will be displayed:

```
[15/03/31 17:05:10] sys     :info  time set
[15/03/31 17:05:10] modem   :info  init
[15/03/31 17:05:10] helm    :info  init
[15/03/31 17:05:10] boatnet :info  init
[15/03/31 17:05:11] modem   :info  modem run state=[tardy] skipping=false
```

Until then, some commands may not work.

Keyboard commands
=================

- Ctrl-L  - redisplay the command prompt and what's been typed
- Ctrl-U  - clear the line
- Backspace - delete a typed character

Help
====

Top level help is available.
```
vms> help
Command              Description
---------------------------------------
accel                accel data
board                display board log
date                 display date, sunrise & set
dip                  show dip switches
event                display the most recent events
fault                fault tests
gps                  GPS command {log, current, bytes}
gsm                  GSM command {power, log, status, xmit}
helm                 helm jobs
info                 buildstamp, tag, and uptime
net                  net test commands
ping                 ping network
power                display power usage
provision            force factory provisioning
ps                   thread listing
reboot               reboot board
set                  show nvram settings
stats                display stats
store                flash store commands {dump, hex, flush, reset}
temp                 read temp
winbond              winbond command {write, read}
```

Expected messages
=================

Messages may be displayed at various times, some typically from the helm and modem threads.
The thread typically wakes every 5 minutes.

You may see something like this appear -

```
[15/03/31 17:06:13] modem   :info  modem run state=[tardy] skipping=false
[15/03/31 17:06:31] log     :note  cell lat=4365524 lng=-7220402 signal=17 rssi=99
[15/03/31 17:06:31] modem   :info  network present network=true
[15/03/31 17:06:32] modem   :info  version major=3 minor=5
[15/03/31 17:06:32] bt      :info  start xmit log=[boatlog] size=45 retry?=false
[15/03/31 17:06:32] bt      :info  sending chunk=0 bytes=45 compresss=42
[15/03/31 17:06:42] bt      :info  chunk success elapsed=0
[15/03/31 17:06:45] bt      :info  xmit log success elasped=13 bytes-per-sec=3
[15/03/31 17:06:45] fs      :info  fs erased partition=[boatlog] usecs=172 bytes=0
[15/03/31 17:06:45] modem   :info  upload success log=[boatlog]
[15/03/31 17:06:45] bt      :info  start xmit log=[eventlog] size=18 retry?=false
[15/03/31 17:06:45] bt      :info  sending chunk=0 bytes=18 compresss=0
[15/03/31 17:06:45] bt      :info  chunk success elapsed=0
[15/03/31 17:06:48] bt      :info  xmit log success elasped=3 bytes-per-sec=6
[15/03/31 17:06:48] fs      :info  fs erased partition=[eventlog] usecs=1065 bytes=0
[15/03/31 17:06:48] modem   :info  upload success log=[eventlog]
[15/03/31 17:06:48] bt      :info  messages count=0 elapsed=0
[15/03/31 17:06:51] modem   :prof  duration elapsed=28
[15/03/31 17:06:51] modem   :info  modem sleep seconds=21600
[15/03/31 17:06:52] modem   :info  modem run state=[enter-dock] skipping=true
[15/03/31 17:06:52] modem   :info  modem sleep seconds=10800
```

Use CTRL-L to redisplay what you have typed on the command line.

Some Debugging Commands
========================

* `gsm connect` - wake the modem thread to attempt to connect to the cell network
* `gsm watch` - show the commands send to and responses received from the modem
* `gps bytes` - show the bytes being received from the GPS
* `info` - shows the board uptime, and software build information
* `date` - show current time, as well as when sunrise and sunset are.
* `ping` - send a ping broadcast over the radio.
* `reboot now` - immediately reboots the board without waiting for operations (namely modem activity) to complete
* `reboot wait` - waits for any pending activity to complete before rebooting.

#!/bin/sh

# find JLinkExe (include default macos path)
JLINKEXE=`PATH=$PATH:/Applications/SEGGER/JLink which JLinkExe`

IMAGE=$1

TMPFILE=/tmp/uid.$$.txt

if [ ! -f "$JLINKEXE" ]; then
    echo "The Segger utilties are not installed on this machine."
    echo "$JLINKEXE is missing."
    exit 3
fi

echo "Attempting to read MCU serial uid"

$JLINKEXE -device MKL16Z256xxx4 -if swd -speed 4000 > $TMPFILE  <<EOF
h
h
mem8 0x40048060,4
mem8 0x4004805C,4
mem8 0x40048058,2
g
exit
EOF

awk '
match($0, /^40048060 = (.*)$/, ary) { upper = ary[1]; }
match($0, /^4004805C = (.*)$/, ary) { middle = ary[1]; }
match($0, /^40048058 = (.*)$/, ary) { lower = ary[1]; }
END {
    uid = upper middle lower;
    gsub(/\s+$/, "", uid);
    gsub(/\s+/, ":",uid);
    print uid;
}
' < $TMPFILE

rm -f $TMPFILE

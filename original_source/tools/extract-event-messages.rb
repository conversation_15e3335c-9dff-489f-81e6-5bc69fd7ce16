﻿#
# This file is used to generate the event_log/v*/labels.rb for libpds.
#

ROOT = File.dirname(__FILE__)+"/.."

FILES = Dir.glob("#{ROOT}/{sensor,vms,common,devices,bnet}/*.c")

EVENT_VERSION=%r{^\s+EVT_MSG_VERSION\s*=\s*(\d+),}

LEVEL_BLOCK_MATCH=%r{^// begin levels\n(.*)// end levels}m
LEVEL_LINE_MATCH=%r{^\s+(\w+)\s+=\s+(\d+)}

TYPE_BLOCK_MATCH=%r{^// begin types\n(.*)// end types}m
TYPE_LINE_MATCH=%r{^\s+(\w+)\s+=\s+([0-9a-fx]+)}

SOURCE_BLOCK_MATCH=%r{^// begin sources\n(.*)// end sources}m
SOURCE_LINE_MATCH=%r{^\s+(\w+)\s+=\s+(\d+)}

EVENT_BLOCK_MATCH=%r{^// begin events\n(.*)// end events}m
EVENT_LINE_MATCH=%r{^\s+(\w+)\s*=\s*EVENT_T\((\w+),\s*\w+\s*,\s*(\d+)\s*\)}

FUNC_MATCH=%r{^\s*(?://\+\s*)?EVENT_LOG[\d]*\(\s*(EVT_\w+), "([^"]+)"([^;]+);}m
FUNC_ARGS_MATCH=%r{\s*[,]+\s*(?://\+\s*)?\s*"([^"]+)",\s*([^,]+)\s*,\s*([^,]+)}

event_file = File.read("common/event.h")

unless event_file =~ EVENT_VERSION then
  puts "could not find event message version"
  exit
end

msg_version = $1.to_i

puts "Event message version #{msg_version}"

unless event_file =~ LEVEL_BLOCK_MATCH then
  puts "levels list not found"
  exit
end
levels = $1.dup

unless event_file =~ SOURCE_BLOCK_MATCH then
  puts "Source list not found"
  exit
end
sources = $1.dup

unless event_file =~ EVENT_BLOCK_MATCH then
  puts "Events not found"
  exit
end
events = $1.dup

unless event_file =~ TYPE_BLOCK_MATCH then
  puts "Format not found"
  exit
end
types = $1.dup

source_list = { }

sources.scan(SOURCE_LINE_MATCH) do |source_name, source_value|
  source_list[source_name] = source_value.to_i
end

event_list = { }
events.scan(EVENT_LINE_MATCH) do |event_name, event_source, event_number|
  unless source_list[event_source] then
    puts "event source [#{event_source}] was not found for [#{event_name}]"
    exit
  end

  event_list[event_name] = {
    source: event_source,
    number: event_number.to_i,
    desc: event_name.sub('EVT_', '').gsub(/_/, ' ').downcase.gsub(/\bbt\b/, "bodytrace").gsub(/\bmsg\b/, "message")
    }
end

levels_list = [ ]
levels.scan(LEVEL_LINE_MATCH) do |name, number|
  levels_list << { name: name, desc: name.sub('EVT_','').downcase, number: number }
end

types_list = [ ]
types.scan(TYPE_LINE_MATCH) do |name, number|
  types_list << { name: name, number: number }
end

FILES.each do |file|
  src = File.read(file)

  begin
    src.scan(FUNC_MATCH) do |name, desc, args|
      args.gsub!(/\n/,'')
      args.gsub!(/\)$/, '')

      params = [ ]
      types = [ ]
      args.scan(FUNC_ARGS_MATCH) do |arg_desc, arg_type, arg_param|
        params << arg_desc
        types << arg_type
      end

      unless event = event_list[name] then
        puts "missing name #{name}"
        next
      end

      event[:params] = params
      event[:types] = types
    end
  rescue StandardError => e
    puts "Error reading #{file}"
    puts e.message
    puts "\n"
    puts e.backtrace
    exit -1
  end
end

puts "Scanned #{FILES.length}, found #{event_list.size} events."

event_sources = { }

event_list.each do |key, value|
  (event_sources[value[:source]] ||= {})[value[:number]] = value
  value[:name] = key
end

sources_sorted = source_list.sort_by {|src, num| num }

msgs = File.open('labels.rb', 'w')

msgs << %(
# This file was automatically generated by #{__FILE__}
# #{Time.now}
#

module Pds
  module EventLog
    module V#{msg_version}
      module Labels
)

types_list.each do |type|
  msgs << "        #{type[:name]} = #{type[:number]}\n"
end


levels_list.each do |level|
  msgs << "        #{level[:name]} = #{level[:number]}\n"
end

msgs << "\n        EVENT_LEVELS = {\n"
levels_list.each do |level|
  msgs << %{          #{level[:name]} => "#{level[:desc]}",\n}
end

msgs << "        }\n"
sources_sorted.each do |source|
  msgs << "        #{source[0]} = #{source[1]}\n"
end

msgs << "\n        # event source labels\n"
msgs << "        EVENT_SOURCES = {\n"
sources_sorted.each do |source|
  msgs << %{          #{source[0]} => "#{source[0].sub('EVT_', '').downcase}",\n}
end
msgs << "        }\n"

msgs << "\n        # event messages\n"
msgs << "        EVENT_MESSAGES = { \n"

event_sources.keys.sort.each do |src|
  source = event_sources[src]
  msgs << "          #{src} => {\n"
    source.keys.sort.each do |num|
      desc = source[num]
      params = desc[:params]
      types = desc[:types]

      unless label = desc[:desc] then
        label = "event #{src}:#{num}"
      end

      msgs << "            # #{desc[:name]}\n"
      msgs << %(            #{num} => { desc: "#{label}")
      msgs << ", params: #{params}" if (params and params.size > 0)
      msgs << ", types: [ #{types.join(', ')} ]" if (types and types.size > 0)
      msgs << " },\n"
    end
  msgs << "          },\n"
end

msgs << "        }\n"

msgs << %(
      end
    end
  end
end
)

msgs.close

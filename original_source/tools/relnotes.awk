#
# Input is expected to be RELEASE_NOTES.md
# which is in the general form:
#
#     # Release notes
#     ## Latest release
#     stuff
#     more stuff
#     ### Next latest release
#     other stuff
#     ...
#
# The idea is to extract only the lines associated with the latest
# release. This output goes into a file to be picked up by upload.sh
#

BEGIN {
	on = 0;
}

function change_state() {
        if (on == 1) {
                on = 0;
                exit 0;
        } else {
                on = 1;
        }
}

function guts() {
        version = "v" $3;
        if (version == tag) {
                change_state();
        } else {
                change_state();
        }
}

$0 ~ /^\#\#[ \t]/ {
        guts();
}

$0 ~ /^\#\#\#[ \t]/ {
        if (on == 1)
                change_state();
}

$0 ~ /^\#\#\#\#[ \t]/ {
        guts();
}

{
        if (on == 1) {
                print $0;
        }
}
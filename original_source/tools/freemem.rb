#!/usr/bin/ruby

MEMORY_SIZE = 32 * 1024
MEMORY_START =  0x1FFFE000
MEMORY_END = MEMORY_START + MEMORY_SIZE
BSS_END_NAME = "__bss_end__"

if ARGV.size == 0 then
  puts "* Usage: #{ARGV[0]} file.map"
  exit -1
end

mapfile = File.read(ARGV[0])

unless mapfile =~ /^\s+(0x[0-9a-f]+)\s+#{BSS_END_NAME}/ then
  puts "Could not find #{BSS_END_NAME}"
  exit -2
end

end_addr = $1.to_i(16)

free = MEMORY_END - end_addr
used = MEMORY_SIZE - free

puts "RAM free #{"%5d" % free} bytes / #{"%2.3f" % (free / 1024.0)} Kbytes"
puts "used #{"%5d" % used} bytes / #{"%2.3f" % (used / 1024.0)} Kbytes"

#!/bin/bash

AUTH_FILE=.auth

# SERVER="http://localhost:3000"
SERVER="https://api.pelagicdata.com"

if [ ! -s $AUTH_FILE -o -z "$(/usr/bin/find $AUTH_FILE -mtime -1)" ]; then
    echo "Enter your email";
    read USERNAME;
    echo "Enter your password";
    read -s PASSWORD

    curl -s -F email="$USERNAME" -F password="$PASSWORD" "$SERVER/api/v0/authenticate" | awk -F\" '{print $4}' > $AUTH_FILE
fi

if [ ! -s $AUTH_FILE ]; then
    echo "Bad Auth"
    exit
fi

GITSHA=$(git log --pretty=format:'%h' -n1)
GITLABEL=$(git describe --tags)
if [ ! -f .buildstamp ]
then
    echo "$0: No .buildstamp file -- cannot continue"
    exit 1
fi
BUILDSTAMP=$(cat .buildstamp)

# Loop and send the right params here. Make sure to send .bin file
for version in 05 10 12 125 13
do
    img="images/vms-$version-cc1200-ft-$GITLABEL.bin"
    if [ ! -f "$img" ]
    then
        [ "$ret" != "1" ] && echo "Build problem?"
        echo "  Image file: $img is missing"
        ret=1
        continue
    fi

    # When the server can accept a notes file, replace
    # the notes line with this:
    #    -F notes="@release_blurb" \
    curl \
        -X POST \
        -H "Authorization: JWT $(cat $AUTH_FILE)" \
        -F device_type="1" \
        -F buildstamp="$BUILDSTAMP" \
        -F build_tag="$GITLABEL" \
        -F hw_version="$version" \
        -F firmware_file="@$img" \
        -F notes="$(cat release_blurb)" \
        -F sha="$GITSHA" \
        $SERVER/api/v0/outgoing/firmware
done

exit $ret

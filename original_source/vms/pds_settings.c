/** @{
    @ingroup vms
    @file
*/

#include "pelagic.h"
#include "modem.h"
#include "pds.h"

/**
    @brief  parses a setting message received via modem.
    @param[in]  msg     settings message packet
    @param[in]  size    overall packet length
*/

void
pds_settings_process(pds_settings_incoming_msg_t *msg, uint32_t bytes)
{
        uint32_t length;
        modem_result_t result;

        length = settings_parse((char *)msg->data, bytes - 2, (char *)the_buffer.settings);

        if ((result = pds_send_chunk(PDS_TYPE_SETTINGS, PDS_SETTINGS_VERSION, the_buffer.settings, length)) != MODEM_SUCCESS ||
            (result = modem_xmit_wait(MODEM_WAIT)) != MODEM_SUCCESS) {
                EVENT_LOG1(EVT_PDS_SETTINGS_SEND_ERR, "settings send error", "result", EVT_16BIT, result);
        } else {
                EVENT_LOG(EVT_PDS_SETTINGS_SUCCESS, "settings success");
        }

}

/** @} */

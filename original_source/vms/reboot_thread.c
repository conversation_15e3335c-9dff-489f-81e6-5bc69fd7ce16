/**@{

    @ingroup    vms
    @file
    @brief      Reboot Thread
*/

#include "pelagic.h"
#include "modem.h"
#include "alarm.h"
#include "modem_thread.h"
#include "system_file.h"
#include "gps.h"
#include "accel.h"
#include "helm.h"
#include "power.h"
#include "signals.h"
#include "led.h"
#include "provision.h"
#include "nap.h"
#include "mcu_sleep.h"
#include "device_power.h"

enum {
        REBOOT_TIME_DEFAULT  = 7 * DAY,
        REBOOT_GRACE_PERIOD  = 30 * MINUTE,     // Modem thread may run up long - 5 mins to register + 5 minute download.

        ACCEL_NAP_DURATION = 5 * MINUTE,
        // ACCEL_NAP_DURATION = 30,
};

volatile bool reboot_pending = 0;
uint32_t reboot_count = 0;

void reboot_tick();

osTimerDef(reboot_timer, reboot_tick);
osTimerId reboot_timer_id;

extern osThreadId reboot_tid;

/**
    @brief  initialize the reboot settings to default
*/

void
reboot_settings_init()
{
        settings.reboot.time = REBOOT_TIME_DEFAULT;
}

/**
    @brief   reboot countdown timer
    @note   called once a second by RTX via SysTick. The RTC is not used
            in case the XCAL fails - SysTick will only fail if the entire MCU
            fails.
*/

void
reboot_tick()
{
        if (settings.reboot.time) {
                reboot_count++;

                if (reboot_count >= settings.reboot.time)
                        osSignalSet(reboot_tid, ALARM_SIGNAL_BUZZER);
        }
}

/**
    @note   The reboot thread sleeps until one of the following happens:
            - REBOOT_TIME_DEFAULT expires, or
            - Signaled via the console or received settings command
            - Battery level has reached a critical level
*/

void
reboot_thread(void *unused)
{
        osEvent result;
        int grace;
        uint32_t secs_to_nap = 0;
        uint16_t signals;
        uint8_t reason;

        reboot_timer_id = osTimerCreate(osTimer(reboot_timer), osTimerPeriodic, NULL);
        osTimerStart(reboot_timer_id, 1000);

        result = osSignalWait(0, osWaitForever);
        osTimerStop(reboot_timer_id);

        signals = result.value.signals;

        if (!(signals & REBOOT_SIGNAL_STILL_PARKED)) {
                EVENT_LOG(EVT_REBOOT_INIT, "init");
        }

        if (signals & REBOOT_SIGNAL_CRITICAL) {
                reason = REBOOT_CRITICAL;
                secs_to_nap = CRITICAL_POWER_SLEEP;
        } else if (signals & REBOOT_SIGNAL_NAP) {
                reason = REBOOT_NAP;
                secs_to_nap = nap_duration();
        } else if (signals & REBOOT_SIGNAL_PARKED) {
                reason = REBOOT_ACCEL_NAP;
                secs_to_nap = ACCEL_NAP_DURATION;
        } else if (signals & REBOOT_SIGNAL_STILL_PARKED) {
                reason = REBOOT_ACCEL_NAP;
                secs_to_nap = ACCEL_NAP_DURATION;
        } else if (signals & REBOOT_SIGNAL_NORMAL) {
                secs_to_nap = provision_sleep;
                reason = secs_to_nap ? REBOOT_NAP : REBOOT_AUTO;
        } else {
                reason = REBOOT_AUTO;
        }

        if (!(signals & REBOOT_SIGNAL_STILL_PARKED)) {
                EVENT_LOG(EVT_REBOOT_START, "reboot started");
        }

        osSignalSet(gps_tid, SYS_SIGNAL_SHUTDOWN);
#ifdef HAVE_ACCEL
        osSignalSet(accel_tid, SYS_SIGNAL_SHUTDOWN);
#endif
#ifdef HAVE_RADIO
        osSignalSet(bnet_tid, SYS_SIGNAL_SHUTDOWN);
#endif

        // various threads should have been woken up to sleep their respective devices
        // allow that to happen..
        osDelay(2000);

        reboot_pending = 1;

        for (grace = 0; grace < REBOOT_GRACE_PERIOD; grace++) {
                if (!modem_thread_running && !gps_running && !helm_running)
                        break;

                osDelay(1000);
        }

        if (!(signals & REBOOT_SIGNAL_STILL_PARKED)) {
                if (grace == REBOOT_GRACE_PERIOD) {
                        EVENT_LOG3(EVT_REBOOT_TIMEOUT, "reboot timed out",
                                   "modem thread", EVT_BOOL, modem_thread_running,
                                   "gps thread", EVT_BOOL, gps_running,
                                   "helm thread", EVT_BOOL, helm_running);
                }

                EVENT_LOG2(EVT_REBOOT_FINAL, "reboot final",
                           "reason", EVT_8BIT, reason,
                           "secs", EVT_32BIT, secs_to_nap);
        }


        flash_store_flush(&boatlog_partition);
        flash_store_flush(&event_partition);

        SYS_REG_FILE->reboot_reason = reason;

        osDelay(1000);

        if (secs_to_nap) {
                mcu_sleep(secs_to_nap);
        } else {
                // And good bye!
                rtc_save(0);
                NVIC_SystemReset();
        }
}

/**
    @brief check to see if a reboot is pending, and wait forever
*/

void
reboot_check_pending()
{
        if (!reboot_pending)
                return;

        for (;;)
                osDelay(1000);
}

/**
    @note   parse a reboot setting.
            - reboot now : initiated a board reboot immediately
            - reboot SECONDS - set the new reboot counter to SECONDS
    @param[in]  argc    array size of argv
    @param[in]  argv    settings arguments
    @return true for successful settings

*/


bool
reboot_setting(int argc, char **argv)
{
        char *arg = argv[0];

        if (argc != 1)
                return false;

        if (strcmp(arg, "now") == 0) {
                osSignalSet(reboot_tid, ALARM_SIGNAL_BUZZER);
                return true;
        }

        if (arg[0] >= '0' && arg[0] <= '9') {
                settings.reboot.time = atoi(arg);
                EVENT_LOG1(EVT_REBOOT_SET_TIME, "set reboot", "time", EVT_16BIT, settings.reboot.time);
                return true;
        }

        return false;
}

/**
    @brief  dump reboot settings
    @param[in]  buffer  area to dump settings to
    @return bytes used
*/

int
reboot_setting_dump(char *buffer)
{
        return sprintf(buffer, "reboot %d\n", settings.reboot.time);
}

/** @} */

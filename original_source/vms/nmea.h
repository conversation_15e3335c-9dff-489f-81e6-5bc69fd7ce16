#ifndef __NMEA_H__
#define __NMEA_H__

typedef enum {
        NMEA_NONE = 0,      // Sentence not finished parsing
        NMEA_GPGGA,
        NMEA_GPRMC,         // GPRMC Sentence parsed
        NMEA_GPGSA,         // GSA sentences
        NMEA_PMTK,          // Parsed MTK Packet
        NMEA_GPRMC_INVALID, // Received a GPRMC statement but the chip reported as not valid.
        NMEA_CRC_ERR,       // CRC error while parsing sentence (typically a result of bytes dropped)
} nmea_result_t;

enum {
        NMEA_WORD_SIZE = 16,
        NMEA_WORDS     = 20
};

struct nmea {
        int8_t          is_reading;                     // flag used by the parser, when a valid sentence has begun
        char            words[NMEA_WORDS][NMEA_WORD_SIZE+1],                //  hold parsed words for one given NMEA sentence
                        checksum_word[NMEA_WORD_SIZE+1];                //  hold the received checksum for one given NMEA sentence

        // will be set to true for characters between $ and * only
        int         computed_checksum;          // used to compute checksum and indicate valid checksum interval (between $ and * in a given sentence)
        int         checksum;                   // numeric checksum, computed for a given sentence
        int         received_checksum ;         // after getting  * we start cuttings the received checksum
        int8_t      checksum_idx;       // used to parse received checksum

        // word cutting variables
        int8_t          word_idx,                   // the current word in a sentence
                        now_idx ;                   // current character index

        // globals to store parser results
        double          longitude;                  // GPRMC and GPGGA
        double          latitude;                   // GPRMC and GPGGA

        uint8_t         hour, minute, second;       // GPRMC and GPGGA
        uint8_t         day, month;                 // GPRMC
        uint16_t        year;

        int8_t          satellites_used;            // GPGGA
        float           altitude;                   // GPGGA
        float           speed;                      // GPRMC
        float           heading;                    // GPRMC
        float           hdop;                       // GPGGA & GPGSA
        float           pdop;                       // GPGSA
        float           vdop;                       // GPGSA

        bool            is_glonas;                  // using GLOAS versus GPS
        /*
         * The serial data is assembled on the fly, without using any redundant buffers.
         * When a sentence is complete (one that starts with $, ending in EOL), all processing is done on
         * this temporary buffer that we've built: checksum computation, extracting sentence "words" (the CSV values),
         * and so on.
         * When a new sentence is fully assembled using the fusedata function, the code calls parsedata.
         * This function in turn, splits the sentences and interprets the data. Here is part of the parser function,
         * handling both the $GPRMC NMEA sentence:
         */

};

typedef struct nmea nmea_t;

void nmea_init(nmea_t *gps);
nmea_result_t nmea_fusedata(nmea_t *gps, char c);

#endif

/**
  @{

    @file
    @ingroup vms
    @brief    Board Information
    @note     For the moment stores and retrieves the IMEI from flash

 */
#include "pelagic.h"
#include "board_info.h"
#include "memmap.h"
#include "modem.h"

#ifdef HAVE_NFC
#include "nfc.h"
#endif

#if defined(TARGET_TESTBOX) || defined(TARGET_OLDTESTBOX)
modem_info_t modem_info;
#else
extern modem_info_t modem_info;
#endif

bool board_info_have_imei = false;  /**< Set true if board_info_imei contains a valid IMEI */
imei_t board_info_imei;     /**< The IMEI for the board */

/**
    @brief  Initializes the board information (currently just IMEI)
*/

void
board_info_init()
{
        if (BOARD_INFO->magic != BOARD_INFO_MAGIC)
                return;

        board_info_have_imei = true;
        imei_copy(board_info_imei, BOARD_INFO->imei);
}

/**
    @brief  read the board info area
    @param[out]  info    area to copy board info
*/

void
board_info_read(board_info_t *info)
{
        if (BOARD_INFO->magic != BOARD_INFO_MAGIC) {
                memset(info, 0, sizeof(board_info_t));
                return;
        }

        memcpy(info, BOARD_INFO, sizeof(board_info_t));
}

/**
    @brief write to flash board info
    @param[in]  info    new board info
*/

void
board_info_write(board_info_t *info)
{
        info->magic   = BOARD_INFO_MAGIC;
        info->version = BOARD_INFO_VERSION;

        kinetis_flash_erase_sector(BOARD_INFO_ADDR);
        kinetis_flash_write(BOARD_INFO_ADDR, info, sizeof(board_info_t));
}

/**
    @brief  Sets the IMEI for the board
    @param imei the IMEI (64-bit format gotten from the Bodytrace modem.)
*/

void
board_info_set_imei(uint64_t imei)
{
        board_info_t info;

        board_info_read(&info);
        memcpy(info.imei, &imei, sizeof(imei));
        board_info_write(&info);

        board_info_have_imei = true;
        imei_copy(board_info_imei, info.imei);
}

/**
    @brief  Sets the CRC for the image that will be booted
    @param  crc the CRC-16 value of the image
*/

void
board_info_set_build_crc(uint16_t crc)
{
        board_info_t    info;

        board_info_read(&info);
        info.build_crc = crc;
        board_info_write(&info);
}

uint16_t
board_info_read_build_crc()
{
        board_info_t    info;

        board_info_read(&info);

        return info.build_crc;
}

/**
    @brief  Fetches the IMEI and stores it in NFC
*/

bool
board_info_ensure_nfc_imei()
{
        if (board_info_have_imei == false) {
                modem_init();
                modem_power_on();
                uart_printf("Fetching IMEI... ");
                osDelay(5000); // Give extra time to get info
                modem_result_t result = modem_get_info(&modem_info);
                modem_power_off();


                if (result != MODEM_SUCCESS)
                        return false;

                board_info_set_imei(modem_info.imei);

                uint64_t imei = get_uint64(board_info_imei);
                uart_printf("%6.6d%9.9d\n", (uint32_t)(imei / 1000000000), (uint32_t)(imei % 1000000000) );
#ifdef HAVE_NFC
                char buf[20];
                sprintf(buf, "%6.6d%9.9d", (uint32_t)(imei / 1000000000), (uint32_t)(imei % 1000000000));
                nfc_init(buf);
#endif
        }
        return true;
}

/** @} */

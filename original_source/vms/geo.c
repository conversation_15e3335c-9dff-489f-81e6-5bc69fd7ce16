/** @{

    @file
    @ingroup vms
    @brief  geo functions
*/

#include "pelagic.h"
#include "geo.h"

#define R       (6371)
#define TO_RAD (3.1415926536 / 180.0)

/**
    @brief  calculate the distance in meters between two points
    @param[in] lat1 latitude of first point
    @param[in] lng1 longitude of first point
    @param[in] lat2 latitude of second point
    @param[in] lng2 longitude of second point
    @return distance in meters
*/

int32_t
distance_in_meters(double lat1, double lng1, double lat2, double lng2)
{
        double dx, dy, dz, cos_lat1;

        lng1 -= lng2;
        lng1 *= TO_RAD, lat1 *= TO_RAD, lat2 *= TO_RAD;

        cos_lat1 = cos(lat1);

        dz = sin(lat1) - sin(lat2);
        dx = cos(lng1) * cos_lat1 - cos(lat2);
        dy = sin(lng1) * cos_lat1;
        return abs((int)((asin(sqrt(dx * dx + dy * dy + dz * dz) / 2) * 2 * R) * 1000));
}

/**
    @brief  check to see if a position is located within a bounding box
    @param[in]  coord_count coords array size
    @param[in]  coords      bounding box array
    @param[in]  lat         latitude to check
    @param[in]  lng         longitude to check
    @return true if [lat,lng] is within the bounding box.
*/

bool
coords_in_dock(int coord_count, const coord_t *coords, int32_t lat, int32_t lng)
{
        int i, j, c = 0;

        for (i = 0, j = coord_count-1; i < coord_count; j = i++) {
                if ((((coords[i].lat <= lat) && (lat < coords[j].lat)) ||
                     ((coords[j].lat <= lat) && (lat < coords[i].lat))) &&
                    (lng < (coords[j].lng - coords[i].lng) * (lat - coords[i].lat) / (coords[j].lat - coords[i].lat) + coords[i].lng))
                        c = !c;
        }

        return c;
}

/** @} */

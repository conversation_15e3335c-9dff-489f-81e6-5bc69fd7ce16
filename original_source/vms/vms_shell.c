#include "pelagic.h"
#include "shell.h"

#ifdef HAVE_ACCEL
void accel_command(int argc, char **argv);
#endif
void board_command(int argc, char **argv);
void date_command(int argc, char **argv);
#ifdef HAVE_DIP
void dip_command(int argc, char **argv);
#endif
void event_command(int argc, char **argv);
void fault_command(int argc, char **argv);
void gps_command(int argc, char **argv);
void gsm_command(int argc, char **argv);
void helm_command(int argc, char **argv);
#ifdef HAVE_HUMIDITY
void humidity_command(int argc, char **argv);
#endif
void info_command(int argc, char **argv);
#ifdef HAVE_RADIO
void net_command(int argc, char **argv);
void ping_command(int argc, char **argv);
void wakeup_command(int argc, char **argv);
#endif
void park_command(int ac, char **av);
void power_command(int argc, char **argv);
void provision_command(int argc, char **argv);
void ps_command(int argc, char **argv);
void reboot_command(int argc, char **argv);
void settings_command(int argc, char **argv);
void stats_command(int argc, char **argv);
void store_command(int argc, char **argv);
void temp_command(int argc, char **argv);
void winbond_command(int argc, char **argv);
void load_command(int argc, char **argv);

const shell_command_t shell_commands[] = {
#ifdef HAVE_ACCEL
        { "accel", "accel data", accel_command },
#endif
        { "board", "display board log", board_command },
        { "date", "display date, sunrise & set", date_command },
#ifdef HAVE_DIP
        { "dip", "show dip switches", dip_command },
#endif
        { "event", "display the most recent events", event_command },
        { "fault", "fault tests", fault_command },
        { "gps", "GPS command {log, current, bytes}", gps_command },
        { "gsm", "GSM command {power, log, status, xmit}", gsm_command },
        { "helm", "helm jobs", helm_command },
#ifdef HAVE_HUMIDITY
        { "humidity", "humidity display", humidity_command },
#endif
        { "info", "buildstamp, tag, and uptime", info_command },
        { "load", "load file into firmware partition", load_command },
#ifdef HAVE_RADIO
        { "net", "net test commands", net_command },
        { "ping", "ping radio network", ping_command },
#endif
        { "park", "query or enable park mode", park_command },
        { "power", "display power usage", power_command },
        { "provision", "force factory provisioning", provision_command },
        { "ps", "thread listing", ps_command },
        { "reboot", "reboot board", reboot_command },
        { "set", "show nvram settings", settings_command },
        { "stats", "display stats", stats_command },
        { "store", "flash store commands {dump, hex, flush, reset}", store_command },
        { "temp", "read temp", temp_command },
#ifdef HAVE_RADIO
        { "wakeup", "wakeup sensors", wakeup_command },
#endif
        { "winbond", "winbond command {write, read}", winbond_command },
        { NULL, NULL, NULL }
};

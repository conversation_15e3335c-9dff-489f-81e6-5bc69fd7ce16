/** @{

    @file
    @ingroup    vms
    @brief      Record the Cell Signal strength with position

*/

#include "pelagic.h"

/**
    @brief  Record cell signal strength with position. Logged as an event. (EVT_CELL_LOG)
    @param[in]  lat   GPS latitude
    @param[in]  lng   GPS longitude
    @param[in]  signal  signal strength
    @param[in]  rssi    RSSI
*/

void
cell_signal_record(int32_t lat, int32_t lng,  uint8_t signal, uint8_t rssi)
{
        event_param_t params[4] = {
                { .desc="lat", .type=EVT_POINT, .arg=lat },
                { .desc="lng", .type=EVT_POINT, .arg=lng },
                { .desc="signal", .type=EVT_8BIT, .arg=signal },
                { .desc="rssi", .type=EVT_8BIT, .arg=rssi }
        };

        event_log(EVT_CELL_LOG, "cell", 4, params);

        // Adding this with //+ comments for extract-event-messages
        //+ EVENT_LOG5(EVT_CELL_LOG, "cell",
        //+        "lat", EVT_POINT, value,
        //+        "lng", EVT_POINT, value,
        //+        "signal", EVT_8BIT, value,
        //+        "rssi", EVT_8BIT, value,
        //+        );
}

#if !defined(TARGET_TEST)
void
cell_signal_dump()
{
        flash_read_t log_read;
        event_t     event;
        uint32_t timestamp = 0;
        int lines = 0, param_count;
        event_param_t params[7];

        flash_read_init(&event_partition, &log_read, the_cache);

        while (log_read.is_eof == false) {
                event_log_read(&log_read, &event, &timestamp, &param_count, params);

                if (event != EVT_CELL_LOG)
                        continue;

                datetime_display(timestamp);
                printf(" (%7d, %7d) signal %3d rssi %3d\n", (int32_t) params[0].arg, (int32_t)params[1].arg, (uint8_t)params[2].arg, (uint8_t)params[3].arg);

                if (!display_more(&lines))
                        return;
        }
}
#endif

/**@} */

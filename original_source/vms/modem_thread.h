#ifndef __MODEM_THREAD_H__
#define __MODEM_THREAD_H__

#include "alarm.h"

extern osThreadId modem_tid;
extern volatile bool modem_thread_running, modem_thread_hold;
extern alarm_t modem_alarm;
extern volatile uint32_t modem_sleep, modem_last_contact, modem_last_power_up;
extern modem_info_t modem_info;

char *modem_run_state(uint8_t state);
bool modem_should_run(uint16_t signals);
void modem_thread(void const *arg);
bool modem_check_for_network();

#endif

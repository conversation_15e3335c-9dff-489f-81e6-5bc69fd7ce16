/** @{

    @file
    @ingroup vms
    @brief   GPS Factory Tests
 */

#include "pelagic.h"
#include "gps.h"
#include "alarm.h"
#include "factory_test.h"

enum {
        GPS_TEST_LOCK_TIMEOUT = 10 * MINUTE,  /**< Time limit to acquire lock */
};

/**
    @brief  Test to see if the GPS is issuing statements
    @return true if the GPS talked
 */

bool
gps_test_response()
{
        int bytes;

        ft_update("gps response: start");
        bytes = serial_read_timeout(&gps_port, gps_data, GPS_DATA_SIZE, 4000);

        if (bytes == 0) {
                ft_update("gps response: timeout FAIL");
                return false;
        }

        ft_update("gps response: test passed");
        return true;
}

/**
    @brief  wait for the GPS to acquire a lock within GPS_TEST_LOCK_TIMEOUT seconds
    @return true if a lock was acquired

    @note   Both the current location and satellite count is reported.
 */

bool
gps_test_lock()
{
        alarm_t timeout;
        bool have_sat = false, have_gps = false;
        int bytes, loop=0;
        char *buffer;
        volatile bool buzzer;

        ft_update("gps lock: start");
        alarm_start_set(&timeout, GPS_TEST_LOCK_TIMEOUT, &buzzer);
        nmea_init(&gps_loc);

        for (;;) {
                bytes = serial_read_timeout(&gps_port, gps_data, GPS_DATA_SIZE, 1000);

                if (buzzer) {
                        ft_update("gps lock: TIMEOUT");
                        return false;
                }

                buffer = gps_data;

                while (bytes-- > 0) {
                        switch (nmea_fusedata(&gps_loc, *buffer++)) {
                        case NMEA_GPRMC:
                                if (have_gps)
                                        continue;

                                have_gps = true;
                                ft_device_result("gps %d %d", (int32_t)(gps_loc.latitude * GPS_PRECISION), (int32_t)(gps_loc.longitude * GPS_PRECISION));
                                break;

                        case NMEA_GPGGA:
                                if (have_sat)
                                        continue;

                                ft_device_result("gps-sat %d", gps_loc.satellites_used);
                                have_sat = true;
                                break;

                        case NMEA_GPRMC_INVALID:
                                loop++;
                                ft_update("gps lock: waiting %d:%02d", loop/60, loop%60);
                                continue;

                        default:
                                continue;
                        }

                        if (have_sat && have_gps) {
                                alarm_cancel(&timeout);
                                ft_update("gps lock: test passed");
                                return true;
                        }
                }
        }
}

/**
    @brief  check to see if the GPS can be placed into a standby mode
    @return true if standby mode succeeded
 */

bool
gps_test_standby_mode()
{
        ft_update("gps backup mode: start");

        if (gps_standby_mode() == true) {
                ft_update("gps test backup mode: test passed");
                return true;
        }

        ft_update("gps test backup mode: FAIL");
        return false;
}

/**
    @brief  run thru response, lock, and standby tests
    @return
            - FT_GPS_UNRESPONSIVE: GPS did not respond to commands
            - FT_GPS_LOCK_FAIL: GPS did not acquire a lock within a given period
            - FT_GPS_BACK_FAIL: GPS could not be placed into standby mode
 */

ft_result_t
gps_test_run()
{
        if (gps_test_response() == false)
                return FT_GPS_UNRESPONSIVE;

#ifndef NO_GPS_LOCK
        if (gps_test_lock() == false)
                return FT_GPS_LOCK_FAIL;
#endif

        if (gps_test_standby_mode() == false)
                return FT_GPS_BACKUP_FAIL;

        return FT_SUCCESS;
}

/**
    @brief  Attempts to initialize the GPS and run tests
    @return
            - FT_SUCCESS : all tests passed
            - Specific error - see gps_test_run routine
 */

ft_result_t
gps_factory_test()
{
        ft_result_t result;

        gps_init();
        gps_reset();

        ft_update("gps: starting tests");

        result = gps_test_run();

#ifdef HAVE_V1
        gps_power_off();
#endif

        if (result != FT_SUCCESS)
                return result;

        ft_update("gps: all tests passed");
        return FT_SUCCESS;
}

/** @} */

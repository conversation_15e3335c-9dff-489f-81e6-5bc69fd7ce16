#ifndef __BOARD_LOG_H__
#define __BOARD_LOG_H__

void board_log_record(uint8_t parked);
void board_log_dump();

// Board log flag bits
#define BOARD_LOG_FLAG_CHARGING    POWER_FLAG_CHARGING
#define BOARD_LOG_FLAG_FAULTED     POWER_FLAG_FAULTED
#define BOARD_LOG_FLAG_UPSIDEDOWN  0x04
#define BOARD_LOG_FLAG_PARKED      0x08
#define BOARD_LOG_FLAG_RSVD1       0x10
#define BOARD_LOG_FLAG_RSVD2       0x20
#define BOARD_LOG_FLAG_RSVD3       0x40
#define BOARD_LOG_FLAG_RSVD4       0x80

#endif

/**@{

    @ingroup    vms
    @file

    @brief      Helm Thread - run periodic jobs (log board readings, calculate sunrise/sunset, radio calibration, etc)
*/

#include "pelagic.h"
#include "sun.h"
#include "helm.h"
#include "power.h"
#include "signals.h"
#include "dip.h"
#include "temp.h"
#include "system_file.h"
#include "power.h"
#include "alarm.h"
#include "board_log.h"
#include "nap.h"
#ifdef HAVE_RADIO
#include "radio.h"
#endif
#include "gps.h"

enum {
#ifdef FACTORY_TEST_FIELD
        HELM_WORK_INTERVAL         = 10,
#else
        HELM_WORK_INTERVAL         = MINUTE,
#endif
        HELM_POWER_MANAGE_INTERVAL = 5 * MINUTE,
        HELM_BOARD_LOG_INTERVAL    = 5 * MINUTE,
        HELM_NAP_INTERVAL          = 1 * MINUTE,
        HELM_GPSWATCH_INTERVAL     = 1 * MINUTE,
#ifdef HAVE_RADIO
        HELM_TEST_RADIO_INTERVAL   = 5 * MINUTE,
        HELM_POWER_CYCLE_INTERVAL  = 24 * HOUR,
        HELM_RADIO_CALIBRATE_INTERVAL  = 15 * MINUTE,
#endif
#ifdef FACTORY_TEST_FIELD
        HELM_FT_INTERVAL           = 10,    // Every 10 seconds
#endif
};

typedef struct helm_job {
        const char      *name;
        void            (*worker)(struct helm_job *job);
        const uint16_t  action;
        int             interval;
} helm_job_t;

void helm_calculate_sun(helm_job_t *job);
void helm_power_manage(helm_job_t *job);
void helm_board_log(helm_job_t *job);
void helm_nap(helm_job_t *job);
static void helm_gps_watch(helm_job_t *job);
static void report_gps(bool fix);

#ifdef HAVE_RADIO
void helm_radio_power_cycle(helm_job_t *job);
void helm_radio_ping(helm_job_t *job);
void helm_radio_calibrate(helm_job_t *job);
#endif
#ifdef FACTORY_TEST_FIELD
void helm_ft_start(helm_job_t *job);
#endif

helm_job_t helm_jobs[] = {
        { .name = "sunriseset", .worker = helm_calculate_sun, .action = HELM_ACTION_SUN_POS  },
        { .name = "boardlog", .worker = helm_board_log, .action = HELM_ACTION_BOARD_LOG },
        // Board power management
        { .name = "powermanage", .worker = helm_power_manage, .action = HELM_ACTION_POWER_MANAGE },
        { .name = "nap", .worker = helm_nap, .action = HELM_ACTION_NAP },
#ifdef HAVE_RADIO
        // Power cycle some components
        { .name = "radiocycle", .worker = helm_radio_power_cycle, .action = HELM_ACTION_POWER_CYCLE, .interval = HELM_POWER_CYCLE_INTERVAL},
        { .name = "radioping", .worker = helm_radio_ping, .action = HELM_ACTION_TEST_RADIO },
        { .name = "radiocal", .worker = helm_radio_calibrate, .action = HELM_ACTION_RADIO_CALIBRATE, .interval = HELM_RADIO_CALIBRATE_INTERVAL },
#endif
#ifdef FACTORY_TEST_FIELD
        { .name = "ftstart", .worker = helm_ft_start, .interval = HELM_FT_INTERVAL},
#endif
        { .name = "gpswatch", .worker = helm_gps_watch, .interval = HELM_GPSWATCH_INTERVAL },
};

#define HELM_JOBS_COUNT (sizeof(helm_jobs)/sizeof(helm_jobs[0]))

bool helm_watch = 0;

volatile helm_action_t helm_actions;
volatile bool helm_running;

alarm_t helm_alarm;

/**
    @brief  Helm Thread - wakeup periodically to run jobs
*/

void
helm_thread(void const *arg)
{
        osEvent result;
        helm_job_t *job, *queue[HELM_JOBS_COUNT];
        int jobs = 0;
        uint16_t actions;

        EVENT_LOG(EVT_HELM_INIT, "init");

        alarm_start_periodic(&helm_alarm, HELM_WORK_INTERVAL);

        for (;;) {
                helm_running = false;
                result = osSignalWait(0, osWaitForever);

                reboot_check_pending();

                helm_running = true;
                helm_stats.wakeups++;

                jobs = 0;

                if (helm_watch)
                        printf("helm: wake up\n");

                if (result.value.signals & ALARM_SIGNAL_BUZZER) {
                        // Alarm went off
                        for (job = helm_jobs; job < &helm_jobs[HELM_JOBS_COUNT]; job++) {
                                if (job->interval > 0)
                                        job->interval -= HELM_WORK_INTERVAL;

                                if (job->interval <= 0)
                                        queue[jobs++] = job;
                        }
                } else {
                        // A specific job was requested via the shell
                        actions = helm_actions;
                        helm_actions = 0;

                        if (helm_watch)
                                printf("helm: actions 0x%x\n", actions);

                        for (job = helm_jobs; job < &helm_jobs[HELM_JOBS_COUNT]; job++) {
                                // Attempt to run a specific job(s) if work is available
                                if (job->action & actions)
                                        queue[jobs++] = job;
                        }
                }

                if (helm_watch)
                        printf("helm: %d jobs\n", jobs);

                for (int i = 0; i < jobs; i++) {
                        job = queue[i];

                        EVENT_LOG1(EVT_HELM_RUNNING, "running", "job", EVT_STRCONST, job->name);
                        job->worker(job);
                }

                if (helm_watch)
                        printf("helm: end of work.\n");
        }
}

/**
    @brief  calculate when sunrise & sunset happens
*/

void
helm_calculate_sun(helm_job_t *job)
{
        uint32_t utc_now = rtc_read(), secs;

        if (!time_acquired) {
                job->interval = HELM_WORK_INTERVAL;
                return;
        }

        // sun_position will calculate either sunrise & sunset for the
        // current day if the time is before sunset, or it will
        // calculate sunset for today, and sunrise for tomorrow.

        sun_position(utc_now, gps_current_lat, gps_current_lon);

        if (utc_now > sunset) {
                EVENT_LOG(EVT_HELM_PAST_SUNSET, "past sunset");
                secs = sunrise;
        } else {
                EVENT_LOG(EVT_HELM_BEFORE_SUNSET, "before sunset");
                // before sunset.
                secs = sunset;
        }

        secs = job->interval = (secs - utc_now) + (HELM_WORK_INTERVAL-1);

        EVENT_LOG1(EVT_HELM_SUN_RECALC, "next sun position calc", "seconds", EVT_16BIT, secs);
}

/**
    @brief  power manage the board
*/

void
helm_power_manage(helm_job_t *job)
{
        power_management();
        job->interval = HELM_POWER_MANAGE_INTERVAL;
}

/**
    @brief  record board readings (temperature, humidity, etc)
*/

void
helm_board_log(helm_job_t *job)
{
        board_log_record(0);
        job->interval = HELM_BOARD_LOG_INTERVAL;
}

/**
    @brief  check to see if its nap time. (see the nap setting for more info)
*/

void
helm_nap(helm_job_t *job)
{
        job->interval = HELM_NAP_INTERVAL;

        if (!is_nap_time())
                return;

        EVENT_LOG(EVT_POWER_NAP, "nap time");
        osSignalSet(reboot_tid, REBOOT_SIGNAL_NAP);
}

/**
    @brief  display the jobs and time left to run. (for debugging)
*/

void
helm_info()
{
        printf("Job        Remaining (secs)\n");

        for (helm_job_t *job = helm_jobs; job < &helm_jobs[HELM_JOBS_COUNT]; job++) {
                printf("%-10s %d\n", job->name, job->interval);
        }
}

#ifdef HAVE_RADIO
/**
    @brief     request to power cycle the radio in case its locked up.
*/

void
helm_radio_power_cycle(helm_job_t *job)
{
        osSignalSet(bnet_tid, BNET_SIGNAL_RADIO_CYCLE);
        job->interval = HELM_POWER_CYCLE_INTERVAL;
}

/**
    @brief  check to make sure the radio is still responding by broadcasting a periodic message
*/

void
helm_radio_ping(helm_job_t *job)
{
        osSignalSet(bnet_tid, BNET_SIGNAL_RADIO_PING);
        job->interval = HELM_TEST_RADIO_INTERVAL;
}

/**
    @brief  request the radio to be calibrated - needs to happen when the temperature changes.
*/

void
helm_radio_calibrate(helm_job_t *job)
{
        osSignalSet(bnet_tid, BNET_SIGNAL_RADIO_CALIBRATE);
        job->interval = HELM_RADIO_CALIBRATE_INTERVAL;
}
#endif

#ifdef FACTORY_TEST_FIELD
void
helm_ft_start(helm_job_t *job)
{
        osSignalSet(bnet_tid, BNET_SIGNAL_FT_START);
        job->interval = HELM_FT_INTERVAL;
}
#endif

/**
    @brief  make events about changes in GPS fix state
*/

static void
helm_gps_watch(helm_job_t *job)
{
        static bool     ra, ls;
        bool            cur;

        if (((gps_tracking == GPS_TRACK_HIGH_RES) && gps_have_fix == true)
            || (gps_acquired_fix == true && (clock_read() - gps_fix_time) < 60)) {
                cur = true;
        } else {
                cur = false;
        }

        // right after boot, we report unconditionally
        if (!ra) {
                ra = true;
                ls = cur;
                report_gps(cur);
        }

        // report only if changed
        if (cur != ls) {
                ls = cur;
                report_gps(cur);
        }
        job->interval = HELM_GPSWATCH_INTERVAL;
}

static void
report_gps(bool fix)
{
        if (fix) {
                EVENT_LOG(EVT_HELM_GPS_FIX, "helm has a GPS location");
        } else {
                EVENT_LOG(EVT_HELM_GPS_NOFIX, "helm no GPS location");
        }
}

static void helm_gps_watch(helm_job_t *job);

/** @{

    @ingroup    vms
    @file

    @brief      NMEA parsing
*/

#include "pelagic.h"

#ifdef TARGET_TEST
#include <string.h>
#include <stdlib.h>
#endif

nmea_result_t nmea_parsedata(nmea_t *gps);
int         nmea_digit2dec(char hexdigit);

void
nmea_init(nmea_t *gps)
{
        gps->is_reading = 0; //are we in a sentence?
}

/**

    @brief  continuous NMEA sentence parser

    @note   (Original comment)
             The serial data is assembled on the fly, without using any redundant buffers.
             When a sentence is complete (one that starts with $, ending in EOL), all processing is done on
             this temporary buffer that we've built: checksum computation, extracting sentence "words" (the CSV values),
             and so on.
             When a new sentence is fully assembled using the fusedata function, the code calls parsedata.
             This function in turn, splits the sentences and interprets the data. Here is part of the parser function,
             handling both the $GPRMC NMEA sentence:

    @return
            - NMEA_NONE sentence not finished parsing
            - NMEA_GPGGA  GPS fix data available
            - NMEA_GPRMC  Recommended minimum GPS data available
            - NMEA_GPGSA  Satellite and DOP info available
            - NMEA_PMTK   Quectec sentence parsed
            - NMEA_GPRMC_INVALID GPRMC received but not valid
            - NMEA_CRC_ERR sentence parsing encountered a CRC error (may mean the MCU is not getting the bytes fast enoough)
 */

nmea_result_t
nmea_fusedata(nmea_t *gps, char c)
{
        if (c == '$') {
                gps->is_reading = 1;
                // init parser vars
                gps->computed_checksum = 0;
                gps->checksum = 0;
                // after getting  * we start cuttings the received checksum
                gps->received_checksum = 0;
                gps->checksum_idx = 0;
                // word cutting variables
                gps->word_idx = 0;
                gps->now_idx = 0;
        }

        if (!gps->is_reading)
                return NMEA_NONE;

        // check ending
        if (c == '\r' || c== '\n') {
                // catch last ending item too
                gps->words[gps->word_idx][gps->now_idx] = 0;
                gps->checksum_word[gps->checksum_idx] = 0;
                // sentence complete, read done
                gps->is_reading = 0;
                // parse
                return nmea_parsedata(gps);
        }

        // computed checksum logic: count all chars between $ and * exclusively
        if (gps->computed_checksum) {
                if (c == '*')
                        gps->computed_checksum = 0;
                else
                        gps->checksum ^= c;
        } else if (c == '$') {
                gps->computed_checksum = 1;
        }

        // received checksum
        if (gps->received_checksum) {
                if (gps->checksum_idx < NMEA_WORD_SIZE) {
                        gps->checksum_word[gps->checksum_idx++] = c;
                }
        } else if (c == '*') {
                gps->words[gps->word_idx][gps->now_idx] = 0;
                gps->received_checksum = 1;
        } else if (c == ',') {
                gps->words[gps->word_idx][gps->now_idx] = 0;

                if (gps->word_idx < NMEA_WORDS) {
                        gps->word_idx++;
                        gps->now_idx = 0;
                }
        } else if (gps->now_idx < NMEA_WORD_SIZE) {
                gps->words[gps->word_idx][gps->now_idx++] = c;
        }


        return NMEA_NONE;
}


/**
	@brief	parse a complete sentence (internal routine)
 */

nmea_result_t
nmea_parsedata(nmea_t *gps)
{
        int received_cks = 16*nmea_digit2dec(gps->checksum_word[0]) + nmea_digit2dec(gps->checksum_word[1]);

        // check checksum, and return if invalid!
        if (gps->checksum != received_cks) {
                return NMEA_CRC_ERR;
        }

        /* $GPGGA
         * $GPGGA,hhmmss.ss,llll.ll,a,yyyyy.yy,a,x,xx,x.x,x.x,M,x.x,M,x.x,xxxx*hh
         * ex: $GPGGA,230600.501,4543.8895,N,02112.7238,E,1,03,3.3,96.7,M,39.0,M,,0000*6A,
         *
         * WORDS:
         *  1    = UTC of Position
         *  2    = Latitude
         *  3    = N or S
         *  4    = Longitude
         *  5    = E or W
         *  6    = GPS quality indicator (0=invalid; 1=GPS fix; 2=Diff. GPS fix)
         *  7    = Number of satellites in use [not those in view]
         *  8    = Horizontal dilution of position
         *  9    = Antenna altitude above/below mean sea level (geoid)
         *  10   = Meters  (Antenna height unit)
         *  11   = Geoidal separation (Diff. between WGS-84 earth ellipsoid and mean sea level.
         *      -geoid is below WGS-84 ellipsoid)
         *  12   = Meters  (Units of geoidal separation)
         *  13   = Age in seconds since last update from diff. reference station
         *  14   = Diff. reference station ID#
         *  15   = Checksum
         */
        if (strcmp(gps->words[0], "$GPGGA") == 0) {
                // Check GPS Fix: 0=no fix, 1=GPS fix, 2=Dif. GPS fix
                if (gps->words[6][0] == '0') {
                        // clear data
                        gps->latitude = 0;
                        gps->longitude = 0;
                        return NMEA_NONE;
                }
                // parse time
                gps->hour = nmea_digit2dec(gps->words[1][0]) * 10 + nmea_digit2dec(gps->words[1][1]);
                gps->minute = nmea_digit2dec(gps->words[1][2]) * 10 + nmea_digit2dec(gps->words[1][3]);
                gps->second = nmea_digit2dec(gps->words[1][4]) * 10 + nmea_digit2dec(gps->words[1][5]);
                // parse latitude and longitude in NMEA format
                gps->latitude = atof(gps->words[2]);
                gps->longitude = atof(gps->words[4]);
                // get decimal format
                if (gps->words[3][0] == 'S') gps->latitude  *= -1.0;
                if (gps->words[5][0] == 'W') gps->longitude *= -1.0;
                double degrees = truncf(gps->latitude / 100.0f);
                double minutes = gps->latitude - (degrees * 100.0f);
                gps->latitude = degrees + minutes / 60.0f;
                degrees = truncf(gps->longitude / 100.0f);
                minutes = gps->longitude - (degrees * 100.0f);
                gps->longitude = degrees + minutes / 60.0f;

                // parse number of satellites
                gps->satellites_used = atoi(gps->words[7]);

                gps->hdop = atof(gps->words[8]);

                // parse altitude
                gps->altitude = atof(gps->words[9]);

                return  NMEA_GPGGA;
        }

        /* $GPRMC
         * note: a siRF chipset will not support magnetic headers.
         * $GPRMC,hhmmss.ss,A,llll.ll,a,yyyyy.yy,a,x.x,x.x,ddmmyy,x.x,a*hh
         * ex: $GPRMC,230558.501,A,4543.8901,N,02112.7219,E,1.50,181.47,230213,,,A*66,
         *
         * WORDS:
         *  1	 = UTC of position fix
         *  2    = Data status (V=navigation receiver warning)
         *  3    = Latitude of fix
         *  4    = N or S
         *  5    = Longitude of fix
         *  6    = E or W
         *  7    = Speed over ground in knots
         *  8    = Track made good in degrees True, heading This indicates the direction that the device is currently moving in,
         *       from 0 to 360, measured in azimuth.
         *  9    = UT date
         *  10   = Magnetic variation degrees (Easterly var. subtracts from true course)
         *  11   = E or W
         *  12   = Checksum
         */
        if (strcmp(gps->words[0], "$GNRMC") == 0 || strcmp(gps->words[0], "$GPRMC") == 0) {
                // Check data status: A-ok, V-invalid
                if (gps->words[2][0] == 'V') {
                        // clear data
                        gps->latitude = 0;
                        gps->longitude = 0;
                        return NMEA_GPRMC_INVALID;
                }
                // parse time
                gps->hour = nmea_digit2dec(gps->words[1][0]) * 10 + nmea_digit2dec(gps->words[1][1]);
                gps->minute = nmea_digit2dec(gps->words[1][2]) * 10 + nmea_digit2dec(gps->words[1][3]);
                gps->second = nmea_digit2dec(gps->words[1][4]) * 10 + nmea_digit2dec(gps->words[1][5]);
                // parse latitude and longitude in NMEA format
                gps->latitude = atof(gps->words[3]);
                gps->longitude = atof(gps->words[5]);
                // get decimal format
                if (gps->words[4][0] == 'S') gps->latitude  *= -1.0;
                if (gps->words[6][0] == 'W') gps->longitude *= -1.0;
                double degrees = truncf(gps->latitude / 100.0f);
                double minutes = gps->latitude - (degrees * 100.0f);
                gps->latitude = degrees + minutes / 60.0f;
                degrees = truncf(gps->longitude / 100.0f);
                minutes = gps->longitude - (degrees * 100.0f);
                gps->longitude = degrees + minutes / 60.0f;
                //parse speed
                // The knot (pronounced not) is a unit of speed equal to one nautical mile (1.852 km) per hour
                gps->speed = atof(gps->words[7]);
                gps->speed /= 1.852; // convert to km/h
                // parse heading
                gps->heading = atof(gps->words[8]);
                // parse UTC date
                gps->day = nmea_digit2dec(gps->words[9][0]) * 10 + nmea_digit2dec(gps->words[9][1]);
                gps->month = nmea_digit2dec(gps->words[9][2]) * 10 + nmea_digit2dec(gps->words[9][3]);
                gps->year = (nmea_digit2dec(gps->words[9][4]) * 10 + nmea_digit2dec(gps->words[9][5])) + 2000;
                gps->is_glonas = (gps->words[0][2] == 'N');

                return NMEA_GPRMC;
        }

        if (strcmp(gps->words[0], "$GPGSA") == 0 || strcmp(gps->words[0], "$GNGSA") == 0) {
                if (gps->words[2][0] == '1')
                        return NMEA_NONE;

                gps->pdop = atof(gps->words[15]);
                gps->hdop = atof(gps->words[16]);
                gps->vdop = atof(gps->words[17]);

                return NMEA_GPGSA;
        }

        if (strncmp(gps->words[0], "$PMTK", 5) == 0) {
                return  NMEA_PMTK;
        }

        return NMEA_NONE;
}
/**
	@brief	parses an ascii digit into a integer
 */

int
nmea_digit2dec(char digit)
{
        if (digit >= 65)
                return digit - 55;
        else
                return digit - 48;
}

/** @} */

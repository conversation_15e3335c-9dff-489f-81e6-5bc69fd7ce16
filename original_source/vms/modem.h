#ifndef __MODEM_H__
#define __MODEM_H__

enum {
        MODEM_QUEUE_THRESHOLD     = (800*1024), // Don't exceed total byte count queued up
        MODEM_CHUNK_MAX           = 1000, // Max data size for Send Data
        MODEM_FLASH_PAGES_PER_MSG = 4,
};

typedef enum {
        MODEM_SUCCESS = 1,          // Command completed
        MODEM_ERR_CRC,              // computed CRC does not match packet CRC
        MODEM_ERR_TIMEOUT,          // Modem did not respond
        MODEM_ERR_MISMATCH,         // Mismatch between command sent and acknowledgement
        MODEM_ERR_TOO_BIG,          // packet/message too big to receive
        MODEM_ERR_UNKNOWN_MSG,      // Unknown message type received
        MODEM_ERR_FAULT,            // General fault
        MODEM_ERR_PARTITION_BUSY,   // The flash store partition is busy.
        MODEM_ERR_XMIT_TIMEOUT,     // transmission timeout.
        MODEM_EOF,                  // no more bytes to send out of the log (not error)
} modem_result_t;

typedef enum {
        MODEM_WAIT_NONE = 0, // don't wait until completion, just until xmit starts
        MODEM_WAIT      = 1, // wait until message is full transmitted.
        MODEM_WAIT_TEST = 2 // For testing purposes only, don't wait at all for packet
} modem_wait_t;

typedef struct {
        void        *data;
        uint32_t    size;
} modem_iovec_t;


typedef struct {
        uint8_t     state;              // Network state
        uint16_t    voltage;            // Power supply voltage (in mV)
        uint16_t    adc;                // ADC voltage (if connected) (in mV)
        uint8_t     rssi;               // RSSI (in -dBm)
        uint8_t     signal_strength;    // Radio signal stength (in percents)
        uint8_t     provisioned;        // Provisioned flag - 0 if not
        int8_t      temperature;        // Temperature (in C)
} modem_status_t;


/** Modem information */
typedef struct {
        uint8_t major;              /**< Software version major number */
        uint8_t minor;              /**< Software version minor number */
        uint64_t imei;              /**< IMEI (left pad with zeros for full IMEI) */
} modem_info_t;

void modem_pin_setup();
void modem_init();
void modem_power_on();
void modem_power_off();
bool modem_attempt_upload();

modem_result_t modem_read(void *data, const uint32_t size);
modem_result_t modem_receive_ack_response(const char *name, const uint8_t command);
modem_result_t modem_xmit_wait(modem_wait_t wait);
modem_result_t modem_xmit_wait_test(modem_wait_t wait, bool send_ft_update);
modem_result_t modem_send_data(void *data, const uint32_t bytes, modem_wait_t wait);
modem_result_t modem_send_data_vec(const modem_iovec_t *vec, const int vec_size, modem_wait_t wait, const int command);
modem_result_t modem_send_test_data(void *data, const uint32_t bytes, modem_wait_t wait);
modem_result_t modem_send_test(void *data, const uint32_t bytes);
modem_result_t modem_get_info(modem_info_t *info);
modem_result_t modem_get_status(modem_status_t *status);
modem_result_t modem_ping();
modem_result_t modem_get_message_count(uint32_t *count);
modem_result_t modem_read_chunk(void *buffer, const uint32_t offset, const uint32_t length, uint32_t *bytes, uint32_t *remaining);
modem_result_t modem_delete_message();

void modem_firmware_update_mode();

extern serial_t modem_port;

extern volatile uint32_t modem_power_on_time;
extern volatile bool modem_watch;
#endif

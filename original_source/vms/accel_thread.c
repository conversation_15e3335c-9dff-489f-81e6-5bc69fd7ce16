/**
   @file accel_thread.c
   @ingroup vms
   @{
   @brief   Accelerometer Thread

   The accelerometer thread is responsible for detecting two things:
     -# if the device "capsized" (turned upside down) or righted.
     -# if the devices has been stationary for some time and possibly
        put the device into a more power efficient state

 */
#include "pelagic.h"
#include "signals.h"
#include "accel.h"
#include "winbond_flash.h"
#include "geo.h"
#include "modem.h"
#include "modem_log.h"
#include "modem_thread.h"

// Useful for outputting a single character every second
//#define GEORG_DEBUG
#ifdef GEORG_DEBUG
#define DEBUG_CHAR(x)           printf(x)
#define DUMP_LOCATIONS(x)       dump_gps_cache()
static void dump_gps_cache(void);
#else
#define DEBUG_CHAR(x)
#define DUMP_LOCATIONS(x)
#endif

/**
   @var device_raw_heading
   raw heading before tilt compensation
   @var last_heading
   last known heading
   @var heading_current
   newest reading
   @var device_pitch
   pitch
   @var device_roll
   roll
*/
volatile int16_t device_raw_heading, last_heading,
         heading_current, device_pitch, device_roll;
/**
   @var last_accel
   saved axis_t from accelerometer
   @var last_mag
   saved axis_t from ecompass
   @var accel_current
   latest reading from accelerometer
   @var mag_current
   latest reading from ecompass
*/
volatile axis_t last_accel, last_mag, accel_current, mag_current;

/**
   @var device_capsized
   is the device upside-down
*/
volatile bool device_capsized = false;

/**
   @var enable_park
   is park mode enabled
*/
bool enable_park = true;

/**
   @var waiting_to_park
   set when we are going to park but we are waiting for the final upload
*/
static bool waiting_to_park = false;

/*
 *  XXX - should become a setting if we keep this logic
 *        See accel_is_stationary()
 */
static bool accel_gps_override = true;

static uint8_t gps_ignored;

// circular buffer for location sampling every 60 seconds (or so)
#define NUM_LOCATIONS   5
static struct loc {
        uint32_t        clock;
        double lat;
        double lon;
        double pdop;
} locations[NUM_LOCATIONS];
static uint8_t loc_idx;
static bool got_five;

// methods for said circular buffer
static bool cache_position(void);
static struct loc *get_oldest_location(void);

/**
   various constants
   1 cycle == 1 second (but that _could_ change)
*/
enum {
        HEADING_CHANGE_THRESHOLD = 10,  ///< number of degrees heading must changes before accel_is_stationary() will report movement
        CAPSIZED_THRESHOLD       = 20,  ///< threshold, in bits, for capsized reporting
        MOVEMENT_THRESHOLD       = 20,  ///< threshold (accelerometer) to detect motion
        Z_FLIP_THRESHOLD         = -50, ///< z value (accelerometer) threshold to detect capsize
        PARK_LOG_INTERVAL        = 30*MINUTE,   ///< \# of cycles of stationary before logging
        PARK_TIME                = 60*MINUTE,   ///< \# of cycles of stationary before we nap the device
        ACCEL_INTERVAL_DEFAULT   = 1000,        ///< default interval between cycles of accel_thread()
        HEARTBEAT_INTERVAL       = 12*HOUR      ///< if we've been sleeping for this long, phone home
};

#define UPSIDEDOWN      (accel_current.z <= Z_FLIP_THRESHOLD)

/**
   @struct deviations
   @brief simple structure to track the maximum deviations of data
   elements as reported by the accelerometer
*/
static struct deviations {
        uint16_t max_acc_x,     ///< abs(max) X deviation from accelerometer
                 max_acc_y,     ///< abs(max) Y deviation from accelerometer
                 max_acc_z;     ///< abs(max) Z deviation from accelerometer
        uint16_t max_mag_x,     ///< abs(max) X deviation from ecompass
                 max_mag_y,     ///< abs(max) Y deviation from ecompass
                 max_mag_z;     ///< abs(max) Z deviation from ecompass
        uint16_t max_heading;   ///< abs(max) heading deviation
} deviations;

/**
   @var ACCEL_THREAD_INTERVAL
   number of milliseconds between each cycle of @ref accel_thread
*/
const int ACCEL_THREAD_INTERVAL = ACCEL_INTERVAL_DEFAULT;

/**
   @var flipped_bits
   number of cycles (in bits) a vessel has been capsized
*/
uint32_t flipped_bits = 0;

/**
   @var moving_bits
   a rolling history of return values from accel_is_stationary()
   MOVING_MSK is the mask for the bits we actually care about
   In English, if accel_is_stationary() returns true 10 times in
   the last 16 calls, we are moving
*/
uint32_t moving_bits = 0;
#define MOVING_MSK      0xffff

// local functions
static bool    accel_fetch_values(void);
static bool    accel_is_stationary(volatile axis_t *la, volatile axis_t *lm,
                                   volatile int16_t *lh, bool update_last);
static int16_t accel_compute_heading(void);
static int32_t heading_change(int32_t new, int32_t old);
static int32_t accel_gps_moved(void);

/**
   @fn void accel_thread(const void *param)
   @brief polls the accelerometer chip once every @ref
   ACCEL_THREAD_INTERVAL to determine all the things
   @return Function never returns
*/
void
accel_thread(const void *param)
{
        bool capsized, stationary;
        bool have_data = false;
        osEvent result;
        static uint16_t stationary_time;

        // Set some defaults if they've never been set before
        if (settings.parking.park_time == 0xffff || settings.parking.park_time == 0) {
                settings.parking.park_time = PARK_TIME;
                settings.parking.park_log_interval = PARK_LOG_INTERVAL;
                settings.parking.heartbeat_interval = HEARTBEAT_INTERVAL;
        }
        // metres_allowed is 16bits -- if that changes, the comparison is no good
        if (settings.parking.metres_allowed == 0xffff || settings.parking.metres_allowed == 0) {
                settings.parking.metres_allowed = 50;
        }
        if (settings.parking_use_gps == 0xff) {
                accel_gps_override = settings.parking_use_gps;  // some real boolishness here
        }

        if (!accel_init()) {
                for (;;)
                        osDelay(10000);
        }

        accel_power_on();
        mag_power_on();

        EVENT_LOG(EVT_ACCEL_INIT, "init");

#ifdef GEORG_DEBUG
        printf("sizeof(locations): %d\n", sizeof(locations));
#endif

        uint16_t cycle_count;
        for (cycle_count = 0; ; cycle_count++) {
                result = osSignalWait(0, ACCEL_THREAD_INTERVAL);
                reboot_check_pending();

                if (result.value.signals & SYS_SIGNAL_SHUTDOWN) {
                        accel_power_off();
                        mag_power_off();
                        EVENT_LOG(EVT_ACCEL_POWER_OFF, "power off");
                        continue;
                }

                if (waiting_to_park) {
                        //printf("waiting to park\n");
                        continue;
                }

                if (!accel_fetch_values())
                        continue;

                accel_gps_override = (bool)settings.parking_use_gps;

                // Test for capsize
                flipped_bits = (flipped_bits << 1) | UPSIDEDOWN;
                capsized = (__builtin_popcount(flipped_bits) >= CAPSIZED_THRESHOLD);

                if (capsized != device_capsized) {
                        device_capsized = capsized;
                        if (capsized) {
                                EVENT_LOG3(EVT_ACCEL_CAPSIZED, "capsized",
                                           "x", EVT_16BIT, accel_current.x,
                                           "y", EVT_16BIT, accel_current.y,
                                           "z", EVT_16BIT, accel_current.z);
                        } else {
                                EVENT_LOG3(EVT_ACCEL_UPRIGHT, "upright",
                                           "x", EVT_16BIT, accel_current.x,
                                           "y", EVT_16BIT, accel_current.y,
                                           "z", EVT_16BIT, accel_current.z);
                        }
                }


                // First time here? Update the last_* variables
                if (!have_data) {
                        have_data = true;

                        last_accel.x = accel_current.x;
                        last_accel.y = accel_current.y;
                        last_accel.z = accel_current.z;
                        last_mag.x = mag_current.x;
                        last_mag.y = mag_current.y;
                        last_mag.z = mag_current.z;
                        last_heading = heading_current;

                        continue;
                }

                // Maybe cache position
                cache_position();

                /*
                 * The brains -- are we stationary or moving
                 *
                 * Every 10 seconds:
                 *  if we have adequate GPS signal then use it
                 *  else use the accelerometer.
                 *
                 * If we have moved, reset the stationary timer (stationary_time)
                 * If GPS was part of the determination, reset moving_bits
                 *
                 * when we're stationary for longer than threshold, shut 'er down (park)
                 */
                int32_t metres = -1;
                if (accel_gps_override && (cycle_count % 10) == 0
                    && gps_acquired_fix && gps_current_pdop < 4.0) {
                        metres = accel_gps_moved();
                }
                if (metres > 0) {
                        // GPS says we have moved
                        stationary_time = moving_bits = 0;
                        DEBUG_CHAR("GM");
                } else if (metres == 0) {
                        // GPS says we are in the same place
                        DEBUG_CHAR("GS");
                        moving_bits = 0;
                        stationary_time++;
                } else if (accel_is_stationary(&last_accel, &last_mag, &last_heading, true)) {
                        DEBUG_CHAR("-");
                        stationary_time++;
                        // Stuff a 0 bit into moving_bits
                        moving_bits = (moving_bits << 1) | 0;
                } else {
                        gps_ignored = 0;
                        // Stuff a 1 bit into moving_bits
                        moving_bits = (moving_bits << 1) | 1;
                        // Don't do anything until we've detected movement at least n times
                        // out of the last MOVING_MSK bits
                        if (__builtin_popcount(moving_bits & MOVING_MSK) >= 10) {
                                stationary_time = 0;
                                DEBUG_CHAR("!");
                        } else {
                                DEBUG_CHAR("+");
                        }
                }

                if ((cycle_count % settings.parking.park_log_interval) == 0) {
#ifdef GEORG_DEBUG
                        if (gps_acquired_fix) {
                                printf("satellites: %d\n", gps_loc.satellites_used);
                                printf("pdop: ");
                                if (gps_current_pdop < 1.0) {
                                        printf("less than one\n");
                                } else if (gps_current_pdop < 2.0) {
                                        printf("less than two\n");
                                } else if (gps_current_pdop < 5.0) {
                                        printf("less than five\n");
                                } else if (gps_current_pdop < 10.0) {
                                        printf("less than 10\n");
                                } else if (gps_current_pdop < 20.0) {
                                        printf("less than 20\n");
                                } else {
                                        printf("crap\n");
                                }
                                printf("pdop approx: %d\n", PDOP_INT());
                        }
#endif
                        log_accel_deviations(stationary_time == 0 ? EVT_ACCEL_MOVING : EVT_ACCEL_STATIONARY,
                                             stationary_time);
                }
#ifdef GEORG_DEBUG
                if ((cycle_count % 30) == 0) {
                        printf("moving bits: 0x%08x, set bits = %d\n", moving_bits, __builtin_popcount(moving_bits));
                        printf("stationary_time: %d\n", stationary_time);
                }
#endif
                stationary = (stationary_time >= settings.parking.park_time);
                if (enable_park == true && stationary) {
                        // We are parked, save some state and log it
                        settings.parking.last_accel = accel_current;
                        settings.parking.last_mag = mag_current;
                        settings.parking.last_heading = heading_current;
                        settings.parking.parked_time = rtc_read();
                        settings.parking.parked_lat = gps_lat;
                        settings.parking.parked_lon = gps_lon;
                        settings_store();
                        EVENT_LOG4(EVT_ACCEL_PARKED, "parked",
                                   "lat", EVT_POINT, settings.parking.parked_lat,
                                   "lon", EVT_POINT, settings.parking.parked_lon,
                                   "time", EVT_32BIT, settings.parking.parked_time,
                                   "pdop", EVT_32BIT, PDOP_INT());

                        waiting_to_park = true;
                        // send the parking signal to the modem thread
                        // it will probably upload and eventually reboot
                        // we will spin slowly because waiting_to_park at the top of
                        // this loop
                        // This waiting_to_park hack is necessary because the
                        // modem upload will do enough stalls/waits that control
                        // comes back to this thread (reboot thread will take care
                        // of killing us when the time comes)
                        osSignalSet(modem_tid, MODEM_SIGNAL_PARKING);

                }
        }
}

/**
   @fn static bool accel_fetch_values(void)
   @brief Get readings from the accelerometer, magnetometer, e-compass
   @return Returns false if there was an error reading from the chip.

   Read x, y, z data from the accelerometer and magnetometer.
   Compute a tilt compensated heading.
   Results are stored in globals.
*/
static bool
accel_fetch_values(void)
{
        if (accel_read_data((axis_t *)&accel_current) == false
            || mag_read_data((axis_t *) &mag_current) == false) {
                return false;
        }

        heading_current = accel_compute_heading();

        // printf("accel_current: %4d, %4d, %4d\n"
        //        "mag_current:   %4d, %4d, %4d\n"
        //        "heading:        %3d\n",
        //    accel_current.x, accel_current.y, accel_current.z,
        //    mag_current.x, mag_current.y, mag_current.z,
        //    heading_current);

        return true;
}

/**
   @fn int16_t accel_compute_heading(void)
   @brief calculate the compass heading with tilt compesation
   @return Returns computed heading (0-360 degrees)

   Calculate a heading from the magnetometer x, y, z and then
   compensate for tilt.

   If, for some reason, the magnetometer returns (0, 0) then set all
   the heading, pitch and roll globals to zero.

   Global @ref device_raw_heading, @ref device_pitch and @ref
   device_roll will be updated.
*/
int16_t
accel_compute_heading(void)
{
        int32_t heading;
        double accXnorm,accYnorm,pitch,roll,magXcomp,magYcomp;
        double mxf, myf, mzf, axf, ayf, azf;
        double sin_pitch, sin_roll, cos_pitch;
        double r;

        // pathological case, set everything to zero
        if (mag_current.y == 0 && mag_current.x == 0) {
                device_raw_heading = 0;
                heading_current = 0;
                device_pitch = 0;
                device_roll = 0;
                return 0;
        }

        heading = (180.0 * atan2(mag_current.y,mag_current.x))/M_PI;
        heading += 90;
        if (heading < 0)
                heading += 360;
        device_raw_heading = heading;

        mxf = mag_current.x;
        myf = mag_current.y;
        mzf = -mag_current.z;
        axf = accel_current.x;
        ayf = accel_current.y;
        azf = accel_current.z;

        r = axf * axf + ayf * ayf + azf * azf;
        accXnorm = axf/sqrt(r);
        accYnorm = ayf/sqrt(r);

        // Calculate pitch and roll
        pitch = asin(accXnorm);

        if (accYnorm != 0.0) {
                roll = -asin(accYnorm/cos(pitch));
        } else {
                roll = 0;
        }

        device_pitch = pitch*100;
        device_roll = roll*100;

        sin_pitch = sin(pitch);
        cos_pitch = cos(pitch);
        sin_roll = sin(roll);

        // Calculate the new tilt compensated values
        magXcomp = mxf*cos_pitch+mzf*sin_pitch;
        magYcomp = mxf*sin_roll*sin_pitch+myf*cos(roll)-mzf*sin_roll*cos_pitch;

        // Calculate heading
        heading = (180.0*atan2(magYcomp,magXcomp))/M_PI;
        heading += 90;

        // Convert heading to 0 - 360
        if(heading < 0)
                heading += 360;

        return (heading);
}

/**
   @fn static bool accel_is_stationary(volatile axis_t *la, volatile axis_t *lm, volatile int16_t *lh, bool update_last)
   @brief decide if device is stationary
   @param[in,out] la (x, y, z) from the accelerometer
   @param[in,out] lm (x, y, z) from the magnetometer
   @param[in,out] lh last heading (as computed by @ref accel_compute_heading)
   @param[in] update_last if true, update the last accel data
   @return Return true if the device is stationary

   Test whether or not the device is stationary.
   Track the maximum deviations in the data while stationary.
   Update the values in the parameters.

   The caller _may_ be passing pointers to things extracted from flash
   after a reboot (so will set update_last to false which means to NOT
   update the last_* globals).

*/
static bool
accel_is_stationary(volatile axis_t *la, volatile axis_t *lm,
                    volatile int16_t *lh, bool update_last)
{
        bool          ret = true;
        uint32_t      hc;
        uint16_t      tx, ty, tz;

        tx = abs(la->x - accel_current.x);
        if (tx > deviations.max_acc_x) deviations.max_acc_x = tx;

        ty = abs(la->y - accel_current.y);
        if (ty > deviations.max_acc_y) deviations.max_acc_y = ty;

        tz = abs(la->z - accel_current.z);
        if (tz > deviations.max_acc_z) deviations.max_acc_z = tz;

        if (tx > MOVEMENT_THRESHOLD && ty > MOVEMENT_THRESHOLD && tz > MOVEMENT_THRESHOLD) {
                ret = false;
                goto bail;
        }

        tx = abs(lm->x - mag_current.x);
        if (tx > deviations.max_mag_x) deviations.max_mag_x = tx;

        ty = abs(lm->y - mag_current.y);
        if (ty > deviations.max_mag_y) deviations.max_mag_y = ty;

        tz = abs(lm->z - mag_current.z);
        if (tz > deviations.max_mag_z) deviations.max_mag_z = tz;

        if (tx > MOVEMENT_THRESHOLD && ty > MOVEMENT_THRESHOLD && tz > MOVEMENT_THRESHOLD) {
                ret = false;
                goto bail;
        }

        hc = abs(heading_change(*lh, heading_current));
        if (hc > deviations.max_heading) deviations.max_heading = hc;
        if (hc > HEADING_CHANGE_THRESHOLD) {
                ret = false;
                goto bail;
        }

bail:
        if (update_last) {
                *la = accel_current;
                last_accel = accel_current;
                *lm = mag_current;
                last_mag = mag_current;
                *lh = heading_current;
                last_heading = heading_current;
        }

        return ret;
}

/**
        @fn static int accel_gps_moved()
        @brief Have we moved move than threshold in the last 5 minutes
         -1 if we cannot use GPS to make a determination, 0 if
        we have not moved greater than threshold, else metres moved

*/
static int32_t
accel_gps_moved()
{
        int32_t ret = -1;
        int     metres;

        /* XXX - accel_gps_override should morph into a setting
         * Override the accelerometer if GPS has us not moving
         *
         * When GPS is in low res, the GPS chip periodically gets
         * an "invalid sentence". The gps_thread(), in turn, unsets
         * gps_have_fix... it will set it again when the chip gets
         * good data (usually within a few seconds). So we check
         * gps_acquired_fix (we got fix at least once this boot).
         * The intent here  is to use the possibly stale coordinates
         * anyway, on the assumption that the situation will resolve
         * itself.
         */
        if ((accel_gps_override == false)
            || (gps_acquired_fix == false)
            || ((gps_tracking == GPS_TRACK_HIGH_RES) && gps_have_fix == false)) {
                return ret;     // no GPS for you!
        }

        // oldloc should be where we were 5 minutes ago
        // Unless it's early after boot, where we can have just got
        // a fix but location has not yet been cached.
        struct loc *oldloc = get_oldest_location();
        if (oldloc == NULL) {
                return ret;
        }

        // if the current GPS reading is shitty, ignore up to 4 of them
        if (gps_current_pdop > 2.5 && gps_ignored < 4) {
                gps_ignored++;
                DEBUG_CHAR("<");
                return false;
        }

        gps_ignored = 0;

        // How far from the cached location have we travelled
        metres = distance_in_meters(gps_current_lat, gps_current_lon,
                                    oldloc->lat, oldloc->lon);
        if (metres > settings.parking.metres_allowed) {
                //printf("metres: %d, gps override!!!\n", metres);
                ret = metres;
        } else {
                ret = 0;
        }

#ifdef GEORG_DEBUG
        printf("accel_gps_moved()\n");
        printf(" metres:           %6d\n", metres);
        printf(" oldloc->pdop:     %6d\n", (int)(oldloc->pdop * 10000));
        printf(" gps_current_pdop: %6d\n", PDOP_INT());
        printf(" returning:        %6d\n", ret);
#endif

        return ret;
}

/**
   @fn bool accel_should_stay_in_nap_mode(void)
   @brief after wakeup, decide whether or not to go back to sleep
   @return Return true if we should go back to sleep

   Called after a reboot. Wake up the accelerometer, fetch and toss
   the early garbage. Then determine if we are still stationary. If
   so, return true (telling the caller to put us back to sleep) else
   return false and stay awake.
*/
bool
accel_should_stay_in_nap_mode(void)
{
        int i = 0;
        bool got_accel_data = false;

        // Try for up to a minute to turn the chip on on
        for (i=0; i<10; i++) {
                if (!accel_init())
                        osDelay(6000);
        }
        accel_power_on();
        mag_power_on();

        //printf("Checking for movement...\n");

        // The first poll from the accelerometer can give bogus data, so flush it with a few reads
        for (i = 0; i < 3; i++) {
                if (accel_fetch_values())
                        got_accel_data = true;
                osDelay(50);
        }
        if (!got_accel_data)
                return false;

        // uart_printf("Stored settings: [%d, %d, %d];  [%d, %d, %d];  [%d]\n",
        //     settings.parking.last_accel.x,
        //     settings.parking.last_accel.y,
        //     settings.parking.last_accel.z,
        //     settings.parking.last_mag.x,
        //     settings.parking.last_mag.y,
        //     settings.parking.last_mag.z,
        //     settings.parking.last_heading
        // );

        for (i = 1; i <= 3; i++) {
                if (accel_fetch_values()
                    && accel_is_stationary(&settings.parking.last_accel,
                                           &settings.parking.last_mag,
                                           &settings.parking.last_heading,
                                           false)) {

                        return true;
                }
                osDelay(1000);
        }
        //printf("Movement detected\n");
        return false;
}

/**
   @fn static int32_t heading_change(int32_t new, int32_t old)
   @brief Compute the difference between two compass headings
   @param[in] new new or current compass heading
   @param[in] old old or previous compass heading
   @return Returns a +/-180 value representing the difference between new and old
 */
static int32_t
heading_change(int32_t new, int32_t old)
{
        int32_t diff = new - old;

        if (diff > 180)
                diff -= 360;
        else if (diff < -180)
                diff += 360;

        return diff;
}

/**
   @fn void log_accel_deviations(event_t evt, uint16_t stationary_time)
   @brief Log the deviations structure
   @param[in] evt event type, expected to be either EVT_ACCEL_STATIONARY or EVT_ACCEL_MOVING
   @param[in] stationary_time time, in seconds, that we were stationary before being called
 */
void
log_accel_deviations(event_t evt, uint16_t stationary_time)
{
        EVENT_LOG2(evt, evt == EVT_ACCEL_MOVING ? "moving" : "stationary",
                   "type", EVT_8BIT, 1,
                   "time", EVT_16BIT, stationary_time);
        EVENT_LOG4(evt, "acc deviations",
                   "type", EVT_8BIT, 2,
                   "x", EVT_16BIT, deviations.max_acc_x,
                   "y", EVT_16BIT, deviations.max_acc_y,
                   "z", EVT_16BIT, deviations.max_acc_z);
        EVENT_LOG4(evt, "mag deviations",
                   "type", EVT_8BIT, 3,
                   "x", EVT_16BIT, deviations.max_mag_x,
                   "y", EVT_16BIT, deviations.max_mag_y,
                   "z", EVT_16BIT, deviations.max_mag_z);
        EVENT_LOG2(evt, "heading deviation",
                   "type", EVT_8BIT, 4,
                   "degrees", EVT_16BIT, deviations.max_heading);
        EVENT_LOG3(evt, "moving_bits",
                   "type", EVT_8BIT, 5,
                   "moving_bits", EVT_32BIT | EVT_HEX, moving_bits,
                   "number of bits", EVT_8BIT, __builtin_popcount(moving_bits & MOVING_MSK));

        deviations.max_acc_x = 0;
        deviations.max_acc_y = 0;
        deviations.max_acc_z = 0;
        deviations.max_mag_x = 0;
        deviations.max_mag_y = 0;
        deviations.max_mag_z = 0;
        deviations.max_heading = 0;
}

/**
  @fn int parking_setting_dump(char *buffer)
  @param[in] buffer  memory to print settings info
  @return bytes dumped
*/
int
parking_setting_dump(char *buffer)
{
        return sprintf(buffer,
                       "parking park_time %d\n"
                       "parking park_log_interval %d\n"
                       "parking heartbeat_interval %d\n"
                       "parking metres_allowed %d\n"
                       "parking use_gps %s\n",
                       settings.parking.park_time,
                       settings.parking.park_log_interval,
                       settings.parking.heartbeat_interval,
                       settings.parking.metres_allowed,
                       settings.parking_use_gps == true ? "true" : "false");
}

bool
parking_setting(int ac, char **av)
{
        int     t;
        char    c;

        if (ac == 0) {
                return false;
        }

        t = atoi(av[1]);
        c = av[1][0];

        if (strcmp(av[0], "use_gps") == 0) {
                if (t == 1) {
                        settings.parking_use_gps = true;
                } else if (c == 'o' || c == 't' || c == 'f') {
                        if (strcmp(av[1], "on") == 0
                            || strcmp(av[1], "true") == 0) {
                                settings.parking_use_gps = true;
                        } else if (strcmp(av[1], "off") == 0
                                   || strcmp(av[1], "false") == 0) {
                                settings.parking_use_gps = false;
                        } else
                                return false;
                } else if (t == 0) {
                        settings.parking_use_gps = false;
                } else
                        return false;
        } else if (strcmp(av[0], "park_time") == 0) {
                if (t > 0 && t < 365*DAY)
                        settings.parking.park_time = t;
                else
                        return false;
        } else if (strcmp(av[0], "park_log_interval") == 0) {
                if (t > 0 && t < 365*DAY)
                        settings.parking.park_log_interval = t;
                else
                        return false;
        } else if (strcmp(av[0], "heartbeat_interval") == 0) {
                if (t > 0 && t < 365*DAY)
                        settings.parking.heartbeat_interval = t;
                else
                        return false;
        } else if (strcmp(av[0], "metres_allowed") == 0) {
                if (t < 1000)
                        settings.parking.metres_allowed = t;
                else
                        return false;
        } else
                return false;

        return true;
}

void
parking_settings_init(void)
{
        // Set some defaults if they've never been set before
        settings.parking.park_time = PARK_TIME;
        settings.parking.park_log_interval = PARK_LOG_INTERVAL;
        settings.parking.heartbeat_interval = HEARTBEAT_INTERVAL;
        settings.parking.metres_allowed = 50;
        settings.parking_use_gps = true;
}

bool
accel_upsidedown(void)
{
        return UPSIDEDOWN;
}

/**
        @fn static bool cache_position(void)
        @brief Called once a second, every sixty seconds, cache our location
        or keep trying until we succeed
        @return false on failure, true when position is cached

        XXX This might make more sense in the helm thread
*/
static bool
cache_position(void)
{
        static uint16_t call_count = 61;

        /*
         * Only want to do this stuff every 60ish calls (it gets reset
         * when we successfully cache a location.
         */
        if (call_count++ < 60) return false;

        // no gps? nothing to do
        if ((gps_acquired_fix == false) || ((gps_tracking == GPS_TRACK_HIGH_RES) && gps_have_fix == false)) {
                return false;
        }

        // gps precision not good enough? nothing to do
        if (gps_current_pdop == 0.0 || gps_current_pdop >= 5.0) {
#ifdef GEORG_DEBUG
                printf("NOT Caching location, pdop is no good: %d,%d, %d\n",
                       gps_lat, gps_lon, PDOP_INT());
#endif
                return false;
        }

        if (!got_five && loc_idx == NUM_LOCATIONS - 1)
                got_five = true;

        locations[loc_idx].clock = clock_read();
        locations[loc_idx].lat = gps_current_lat;
        locations[loc_idx].lon = gps_current_lon;
        locations[loc_idx].pdop = gps_current_pdop;

        loc_idx = (loc_idx + 1) % NUM_LOCATIONS;

        DUMP_LOCATIONS();

        call_count = 0;
        return true;
}

/**
        @fn static struct loc *get_oldest_location
        @brief Return the oldest member of the locations circular array
        @return a pointer to a struct loc or NULL if we have yet to
        cache a location
*/
static struct loc *
get_oldest_location(void)
{
        // If we have nothing, return NULL
        // if we haven't filled the circular buffer
        // then the first element is the oldest
        if (!got_five) {
                if (loc_idx == 0)
                        return NULL;
                return &locations[0];
        }

        // otherwise, the oldest is the next one we
        // would be overwriting

        return &locations[loc_idx];
}


#ifdef GEORG_DEBUG
/**
        @brief debugging function to dump the locations table
        (along with distances from the current location)
*/
static void
dump_gps_cache(void)
{
        struct loc      *oldloc;

        printf("Cached location table:\n");

        // Sorry about the ugly loop
        for (int i = (loc_idx == 0) ? (NUM_LOCATIONS - 1) : (loc_idx - 1), j = NUM_LOCATIONS,
             doit = 0, metres = 0;
             j > 0;
             i = (i == 0) ? (NUM_LOCATIONS - 1) : (i - 1), j--) {

                if (doit) {
                        metres = distance_in_meters(gps_current_lat, gps_current_lon,
                                                    locations[i].lat, locations[i].lon);
                } else {
                        doit = 1;
                }

                printf("%d: %d: %d,%d pdop: %d, metres from current: %d\n",
                       i, locations[i].clock,
                       (int)(locations[i].lat * 10000),
                       (int)(locations[i].lon * 10000),
                       (int)(locations[i].pdop * 10000),
                       metres);
        }

        oldloc = get_oldest_location();
        if (oldloc) {
                printf("oldest clock: %d\n", oldloc->clock);
        }
}
#endif

/// @}

/**
    @file
    @ingroup power
    @brief   Routines need to read the battery level
    @{
*/

#include "pelagic.h"
#include "gpio_api.h"
#include "analogin_api.h"
#include "pinmap.h"
#include "factory_test.h"
#include "power.h"

gpio_t bat_sw_pin;
gpio_t power_charge_pin, power_fault_pin;

analogin_t bat_an_pin;

osMutexId battery_mutex;
osMutexDef(battery_mutex);

bool battery_initted = false;

/**
    @brief initialize the analog & GPIO pins need to read the battery level
*/

void
battery_init()
{
        if (battery_initted)
                return;

        // gpio_init_out(&bat_sw_pin, POWER_VB_SW);     // PORT Ian: not used
        analogin_init(&bat_an_pin, ADC_VBAT);        //! TODO Ian: how do I configure the internal ADC vbat channel?

        gpio_init_in(&power_charge_pin, PWR_CHRG);
        pin_mode(PWR_CHRG, PullUp);

        gpio_init_in(&power_fault_pin, PWR_FAULT);
        pin_mode(PWR_FAULT, PullUp);

        battery_mutex = osMutexCreate(osMutex(battery_mutex));

        battery_initted = true;
}

/**
    @brief  read the current battery level
    @return battery level in "potatos" - see power.h for translation to voltages
    @note   The battery level is read 4 times pausing 200ms between readings. The highest value is returned.
*/

uint16_t
battery_read()
{
#ifdef TARGET_DEVBOARD
        return BATTERY_VOLTAGE_GOOD;
#else
        uint16_t highest = 0, value = 0;

        osMutexWait(battery_mutex, osWaitForever);

        gpio_write(&bat_sw_pin, 1);
        osDelay(500);
        for (int i = 0; i < 4; i++) {
                value = analogin_read_u16(&bat_an_pin);
                if (value > highest)
                        highest = value;

                if (i != 3)
                        osDelay(200);
        }
        gpio_write(&bat_sw_pin, 0);
        osMutexRelease(battery_mutex);

        return highest;
#endif
}

/**
    @brief  return the "charging" flag from the battery charger
    @return true = the battery is charging.
*/

bool
battery_charge_read()
{
#ifdef TARGET_DEVBOARD
        return true;
#else
        return !gpio_read(&power_charge_pin);
#endif
}

/**
    @brief  return the fault flag from the battery charger
    @return true if a fault is hapenning.
*/

bool
battery_fault_read()
{
#ifdef TARGET_DEVBOARD
        return false;
#else
        return !gpio_read(&power_fault_pin);
#endif
}

#ifdef HAVE_FACTORY_TEST

/**
    @brief  test the power level
*/

ft_result_t
power_factory_test()
{
        uint16_t charge;
        // power_init has already been called shortly after boot

        charge = battery_read();

        if (charge < 30000) {
                ft_update("power: bad bat [0x%x]", charge);
                return FT_POWER_FAIL;
        } else {
                ft_device_result("power %d", charge);
                ft_device_result("solar %d", solar_read());
                ft_update("power %d", charge);
                ft_update("solar %d", solar_read());
                ft_update("power: test passed");
                return FT_SUCCESS;
        }
}
#endif

/* @} */

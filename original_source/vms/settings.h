#ifndef __SETTINGS_H__
#define __SETTINGS_H__

#include "geo.h"

enum {
        SETTINGS_MAGIC          = 0xB105F00D,
        SETTINGS_VERSION        = 8,

        SETTINGS_DOCK_LIMIT     = 32,
};

typedef volatile struct {
        struct {
                uint16_t        flags;
                uint16_t        run_mode;
                uint16_t        idle_mode;
                uint16_t        periodic_on;
                uint16_t        periodic_off;
                uint16_t        idle_on;
                uint16_t        idle_off;
                uint16_t        idle_meters;
                uint16_t        idle_time;
                uint16_t        dock_on;
                uint16_t        dock_off;
        } gps;

        struct {
                uint8_t         mode;
                uint8_t         flags;
                uint16_t        minute_start;       // Minutes since midnight
                uint16_t        minute_end;
        } nap;

        struct {
                uint32_t        info;
                uint32_t        profile;
        } events;

        struct {
                uint32_t        time;
        } reboot;

        struct {
                uint32_t        flags;
        } radio;

        struct {
                uint8_t         count;
                coord_t         coords[SETTINGS_DOCK_LIMIT];
        } docks;

        struct {
                uint32_t        normal_time;
                uint32_t        dock_time;
                uint32_t        tardy_time;
                uint32_t        low_power_time;
                uint32_t        normal_sleep;
                uint32_t        success_sleep;
                uint32_t        dock_fail_sleep[2];
                uint32_t        fail_sleep;
        } modem;

        // The members marked with "persistent" are not settings.
        // Rather, they are simply data that needs to be remembered
        // across a reboot/sleep
        struct {
                axis_t          last_accel;             // persistent
                axis_t          last_mag;               // persistent
                int16_t         last_heading;           // persistent
                uint16_t        park_time;
                uint16_t        park_log_interval;
                uint32_t        parked_time;            // persistent
                uint32_t        heartbeat_interval;
                int32_t         parked_lat;             // persistent
                int32_t         parked_lon;             // persistent
                uint16_t        metres_allowed;
        } parking;

        uint8_t                 gps_log_freq;
        uint32_t                modem_gofast_interval;  // modem gofast interval
        uint32_t                modem_gofast_volts;     // in battery potatoes
        uint8_t                 parking_use_gps;
        uint8_t                 persistent_boat_log_vers;  // persistent
        uint8_t                 persistent_event_log_vers; // persistent
} settings_t;

typedef struct {
        uint32_t        magic;
        uint32_t        version;
        settings_t      settings;
} settings_area_t;

extern settings_t settings;

typedef struct {
        char        *key;
        uint32_t    value;
} setting_key_t;

typedef struct {
        char    *name;
        bool    (*command)(int argc, char **argv);
} setting_command_t;


void settings_init(void);
int settings_find_key(char *key, const setting_key_t *keys, int count, uint32_t *value);
int settings_parse(char *buffer, int size, char *results);
int settings_dump(char *buffer);
void settings_store(void);

extern char setting_buffer[];
#endif

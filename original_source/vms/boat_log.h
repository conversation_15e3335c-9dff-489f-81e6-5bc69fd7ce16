#ifndef __BOATLOG_H__
#define __BOATLOG_H__

#define DOP_PRECISION       (10.0)
#define SPEED_PRECISION     (100.0)

enum {
        LOG_RECORD_MARKER      = 0xE8,
};

typedef enum {
        SIZE_EMPTY  = 0,
        SIZE_SMALL  = 1,
        SIZE_MEDIUM = 2,
        SIZE_FULL   = 3
} delta_size_t;

typedef enum {
        LOG_HEADER_TYPE_MASK           = 0x0f,
        LOG_HEADER_TYPE_GPS            = 0,
        LOG_HEADER_TYPE_EVENT          = 1,     // TODO
        LOG_HEADER_TYPE_SENSOR_REMOTE  = 2,
        LOG_HEADER_TYPE_SENSOR_BOARD   = 3,     // TODO

        LOG_HEADER_TIME_MASK           = 0xf0,
        LOG_HEADER_TIME_FULL           = SIZE_FULL,
        LOG_HEADER_TIME_SHIFT          = 4,
        LOG_HEADER_TIME_ONE_SECOND     = 0x40,
        LOG_HEADER_EXTENDED            = 0x80,  // unused
} log_header_t;

enum {
        LOG_GPS_HEADER_LAT_MASK        = 0x03,
        LOG_GPS_HEADER_LAT_SHIFT       = 0,
        LOG_GPS_HEADER_LON_MASK        = 0x0C,
        LOG_GPS_HEADER_LON_SHIFT       = 2,

        LOG_GPS_HEADER_LAT_FULL        = SIZE_FULL,
        LOG_GPS_HEADER_LON_FULL        = SIZE_FULL << LOG_GPS_HEADER_LON_SHIFT,

        LOG_GPS_HEADER_HIGH_RES        = 0x10,
        LOG_GPS_HEADER_SPEED_HEADING   = 0x20,
        LOG_GPS_HEADER_ACCEL_HEADING   = 0x40,
        LOG_GPS_HEADER_EXTENDED        = 0x80,
};

enum {
        LOG_GPS_EXT_HEADER_PRECISION       = 0x01,
        LOG_GPS_EXT_HEADER_ACCEL_RAW       = 0x02,
};

enum {
        LOG_ATTR_HIGH_RES          = 0x01,
        LOG_ATTR_PRECISION         = 0x02,
        LOG_ATTR_SPEED_HEADING     = 0x04,
        LOG_ATTR_ACCEL_HEADING     = 0x08,
        LOG_ATTR_ACCEL_RAW         = 0x10,
};

enum {
        LOG_SENSOR_TYPE_NODE_ID_MASK  = 0x30,
        LOG_SENSOR_TYPE_NODE_ID_SET   = 0x80,
};

typedef struct  {
        uint8_t     satellites;
        uint8_t     hdop;
        uint8_t     vdop;
        uint8_t     pdop;
        uint16_t    heading;
        uint16_t    speed;
        uint16_t    accel_heading;
        int16_t    accel_x, accel_y, accel_z;
        int16_t    mag_x, mag_y, mag_z;
} log_attribute_t;

typedef struct {
        uint8_t     sensor_type;
        board_uid_t sensor_uid;
        uint8_t     sensor_voltage;
        uint8_t     sensor_data_length;
        uint8_t     sensor_data[40]; // XXX This is the #defined max in the gear sensor; I actually want this to be variable and malloced
} log_sensor_t;

void boat_log_gps(int32_t lat_now, int32_t lon_now, uint16_t flags, log_attribute_t *attrs);
void boat_log_thermistor(uint16_t temperature, board_uid_t uid);

void boat_log_sensor(log_sensor_t *sensor);
void boat_log_dump();
void boat_log_reset();
#endif

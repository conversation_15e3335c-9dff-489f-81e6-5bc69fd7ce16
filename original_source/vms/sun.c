/**@{

    @ingroup    vms
    @file
*/

#include "pelagic.h"
#include "sun.h"

uint32_t    sunrise = 0, sunset = 0;

/**
    @brief  compute sunrise and sunset
    @param[in]  utc_time    time period to figure out sunrise/sunset
    @param[in]  latitude    latitude position
    @param[in]  longitude   longitude position
*/

void
sun_position(uint32_t utc_time, double latitude, double longitude)
{
        uint32_t start_of_day;
        int32_t tz_offset = timezone_offset(longitude);
        datetime_t dt;

        seconds_to_datetime(utc_time + tz_offset, &dt);

        start_of_day = date_to_seconds(dt.year, dt.month, dt.day);

        sunset = sunrise_sunset_time(dt.year, dt.month, dt.day, latitude, longitude, SUNSET) + start_of_day - tz_offset;

        if (utc_time >= sunset) {
                // since the time is past sunset, calculate for sunrise the next day
                seconds_to_datetime(utc_time + tz_offset + (23 * 3600), &dt);
                start_of_day = date_to_seconds(dt.year, dt.month, dt.day);
        }

        sunrise = sunrise_sunset_time(dt.year, dt.month, dt.day, latitude, longitude, SUNRISE) + start_of_day - tz_offset;
}

/**
    @brief  check to see if its night time
    @return true if its night, false if day
*/

int8_t
is_nighttime()
{
        uint32_t now = rtc_read();

        if (sunrise == 0)
                return 0;

        if (now >= sunset) {
                if (now <= sunrise)
                        return 1;   // its night!
                else
                        return 0;
        } else if (now >= sunrise) {
                return 0;   // daytime
        } else
                return 1;   // nighttime
}

/**
    @brief  how many seconds to sunrise (if night) or sunset (if day)
    @return seconds to sunrise/sunset
*/

int
secs_to_sun_event()
{
        uint32_t now = rtc_read();
        int secs;

        if (sunrise == 0)   // Very unlikely..
                return MINUTE;

        if (is_nighttime()) {
                secs = (sunrise - now);
        } else {
                secs = (sunset - now);
        }

        return ((secs <= 0) ? HOUR : secs);
}


#define OFFICIAL_ZENITH     ((double) 90.8333333)   // Tip of the sun is below horizon
#define CIVIL_ZENITH        ((double) 96.0)         // Sun center is 6 degrees below horizon
#define NAUTICAL_ZENITH     ((double) 102.0)        // Sun center is 6 to 12 deg below
#define ASTRONOMICAL_ZENITH ((double) 108.0)        // Sun center 18 deg below

/**
    @note   calculate sunrise or sunset for a  and location

            based on http://williams.best.vwh.net/sunrise_sunset_algorithm.htm

    @param[in]  year   full year
    @param[in]  month  1 to 12
    @param[in]  day    1 to 31
    @param[in]  latitude    position
    @param[in]  longitude    position
    @param[in]  position    calculate SUNRISE or SUNSET
    @return     seconds since Unix Epoch for sunrise or sunset
w*/

uint32_t
sunrise_sunset_time(int year, int month, int day, double latitude, double longitude, sun_position_t position)
{
        const double zenith = OFFICIAL_ZENITH;
        const double D2R = M_PI / 180.0;
        const double R2D = 180.0 / M_PI;

        // get day of year

        int N1 = 275 * month / 9;
        int N2 = (month + 9) / 12;
        int N3 = (1 + ((year - 4 * (year / 4) + 2) / 3));
        int day_of_year = N1 - (N2 * N3) + day - 30;

        // convert the longitude to hour value and calculate an approximate time
        double lon_hour = longitude / 15.0;
        double hour;

        if (position == SUNRISE)
                hour = day_of_year + ((6 - lon_hour) / 24);
        else
                hour = day_of_year + ((18 - lon_hour) / 24);

        // calculate the Sun's mean anomaly
        double sun_anomaly = (0.9856 * hour) - 3.289;;

        //calculate the Sun's true longitude
        double sun_true_lon = sun_anomaly + (1.916 * sin(sun_anomaly * D2R)) + (0.020 * sin(2 * sun_anomaly * D2R)) + 282.634;

        if (sun_true_lon > 360) {
                sun_true_lon = sun_true_lon - 360;
        } else if (sun_true_lon < 0) {
                sun_true_lon = sun_true_lon + 360;
        }

        // calculate the Sun's right ascension
        double sun_right_asc = R2D * atan(0.91764 * tan(sun_true_lon * D2R));

        if (sun_right_asc > 360) {
                sun_right_asc = sun_right_asc - 360;
        } else if (sun_right_asc < 0) {
                sun_right_asc = sun_right_asc + 360;
        }

        // right ascension value needs to be in the quadrant as true longitude
        double Lquadrant = (floor(sun_true_lon / (90.0))) * 90.0;
        double RAquadrant = floor(sun_right_asc / 90.0) * 90.0;
        sun_right_asc = sun_right_asc + (Lquadrant - RAquadrant);

        //right ascension value needs to be converted into hours
        sun_right_asc = sun_right_asc / 15.0;

        // calculate the Sun's declination
        double sinDec = 0.39782 * sin(sun_true_lon * D2R);
        double cosDec = cos(asin(sinDec));

        //calculate the Sun's local hour angle
        double cosH = (cos(zenith * D2R) - (sinDec * sin(latitude * D2R))) / (cosDec * cos(latitude * D2R));
        double hours;

        if (position == SUNRISE) {
                hours = 360 - R2D * acos(cosH);
        } else {
                hours = R2D * acos(cosH);
        }

        hours = hours / 15;


        // calculate local mean time of rising/setting
        double time = (hours + sun_right_asc - (0.06571 * hour) - 6.622);

        // adjust back to UTC - uncomment the follow
        // double utc = fmod(time - lon_hour, 24.0);

        // keep time in local since the date is local as well.
        // double utc = fmod(time, 24.0);
        double utc = time;

        if (utc > 24) {
                utc -= 24;
        } else if (time < 0) {
                utc += 24;
        }

        return ((uint32_t)(utc * 3600.0));
}

/** @} */

PROJECT=vms
PROJECT_DIR=vms
TARGET=VMS

# main control & threads
SRC +=  main.c \
        gps_thread.c \
        gps_setting.c \
        helm.c \
        modem_thread.c \
        reboot_thread.c

# VMS support
SRC +=  battery.c \
        board_info.c \
        board_log.c \
        boat_log.c \
        cell_signal.c \
        compress.c \
        console.c \
        firmware_update.c \
        firmware_vms_sensor.c \
        flash_read.c \
        flash_store.c \
        flash_store_vms.c \
        geo.c \
        heatshrink_encoder.c \
        imei.c \
        modem_bodytrace.c \
        modem_log.c \
        nap.c \
        nap_settings.c \
        nmea.c \
        pds.c \
        pds_firmware.c \
        pds_messages.c \
        pds_settings.c \
        power.c \
        power_average.c \
        provision.c \
        settings.c \
        solar.c \
        sun.c \
        thermistor.c

#! PORT: remove winbond flash
# devices
SRC += device_power.c \
       gps_quectel.c
#removed
#        winbond_flash.c \

#! PORT: remove shell
# shell commands
# SRC +=  vms_shell.c \
#         board_command.c \
#         date_command.c \
#         event_command.c \
#         fault_command.c \
#         gps_command.c \
#         gsm_command.c \
#         helm_command.c \
#         info_command.c \
#         load_command.c \
#         net_command.c \
#         park_command.c \
#         power_command.c \
#         provision_command.c \
#         ps_command.c \
#         reboot_command.c \
#         settings_command.c \
#         stats_command.c \
#         store_command.c \
#         temp_command.c \
#         winbond_command.c

#! Port: remove bnet
# ifneq ($(RADIO),none)
# # Boatnet
# SRC += bnet_vms.c \
#        bnet_decode.c \
#        bnet_client.c \
#        bnet_console_xmit.c \
#        bnet_console_common.c \
#        bnet_vms_bond.c \
#        bnet_vms_console_server.c \
#        bnet_vms_firmware.c \
#        bnet_vms_ping.c \
#        bnet_vms_provision.c \
#        bnet_vms_sensor.c \
#        bnet_vms_settings.c \
#        ping_command.c \
#        wakeup_command.c

# BUILD_FLAGS += -DHAVE_BNET=1
# endif


ifneq (,$(filter $(DEVICE_VERSION), 13))
BUILD_FLAGS += -DMODEM_3G
endif

ifneq (,$(filter $(DEVICE_VERSION),05 10 12 125 13))
BUILD_FLAGS += -DHAVE_ACCEL=1
# TODO Ian: add mag_iis2mdc.c and accel_lsm6dso32.c
SRC += lsm303d.c accel_thread.c accel_command.c
endif

ifneq (,$(filter $(DEVICE_VERSION), 01 10 12 125 13))
SRC += i2c_api.c device_power.c
endif

# ifneq (,$(filter $(DEVICE_VERSION), 01 10 12))
# BUILD_FLAGS += -DHAVE_HUMIDITY=1
# SRC += si7020.c humidity_command.c
# endif

#! PORT Ian: NFC is now handled by the modem
# ifneq (,$(filter $(DEVICE_VERSION), 12 125))
# BUILD_FLAGS += -DHAVE_NFC=1
# SRC += m24sr.c
# endif

ifneq ($(NO_FACTORY),1)
PROJOPTS += ft
BUILD_FLAGS += -DHAVE_FACTORY_TEST=1
SRC += factory_test_common.c gps_factory_test.c

# ifneq ($(NEW_FACTORY),1)
# BUILD_FLAGS += -DOLD_FACTORY=1
# SRC += factory_test_radio.c
# endif

endif

ifeq ($(STANDALONE),1)
PROJOPTS += standalone
BUILD_FLAGS += -DHAVE_FACTORY_TEST=1 -DHAVE_FT_STANDALONE=1
SRC += factory_test_standalone.c factory_test_common.c
endif

ifeq ($(NO_SOLAR),1)
PROJOPTS += no-solar
BUILD_FLAGS += -DNO_SOLAR=1
endif

ifeq ($(NO_GPS_LOCK), 1)
BUILD_FLAGS += -DNO_GPS_LOCK=1
PROJOPTS += no-gps-lock
endif

# Factory Test in the field - REMOVED bnet dependency
# ifeq ($(FT_FIELD), 1)
# BUILD_FLAGS += -DFACTORY_TEST_FIELD=1
# PROJOPTS += ft-field
# SRC += bnet_factory_test.c
# endif

-include ../Makefile.common

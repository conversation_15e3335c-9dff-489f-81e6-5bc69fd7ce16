/** @{
    @file
    @ingroup vms
    @brief Board Log - records the board power & sensors.
*/

#include "pelagic.h"
#include "board_log.h"
#include "power.h"
#include "temp.h"
#include "humidity.h"

event_param_t board_log_params[] = {
        { .desc = "battery", .type = EVT_16BIT },
        { .desc = "solar",   .type = EVT_16BIT },
        { .desc = "flags",   .type = EVT_8BIT },
        { .desc = "temperature", .type = EVT_16BIT },
#ifdef HAVE_HUMIDITY
        { .desc = "humidity", .type = EVT_8BIT },
#endif
};

/**
    @brief  records various board parameters (battery, solar, tempeature, humidity, etc)
    @note   The information is stored as an event record. (EVT_BOARD_LOG)
            Called periodically either from the helm thread or the parked logic.
            Encodes various things in the flags byte.
*/

void
board_log_record(uint8_t parked)
{
        uint8_t         flags = 0;
        uint16_t        solar = solar_read();

        // if no sun, ignore/lose the charging/fault bits
        if (solar > 2000) {
                if (battery_charge_read())
                        flags |= POWER_FLAG_CHARGING;

                if (battery_fault_read())
                        flags |= POWER_FLAG_FAULTED;
        }

        if (accel_upsidedown())
                flags |= BOARD_LOG_FLAG_UPSIDEDOWN;

        flags |= parked;

        board_log_params[0].arg = battery_read();
        board_log_params[1].arg = solar;
        board_log_params[2].arg = flags;
        board_log_params[3].arg = lsm303d_temp_read();
#ifdef HAVE_HUMIDITY
        board_log_params[4].arg = humidity_read();
#endif

        event_log(EVT_BOARD_LOG, "board", ARRAY_SIZE(board_log_params), board_log_params);

        // Adding this with //+ comments for extract-event-messages
        //+ EVENT_LOG5(EVT_BOARD_LOG, "board",
        //+        "battery", EVT_16BIT, value,
        //+        "solar", EVT_16BIT, value,
        //+        "flags", EVT_8BIT, value,
        //+        "temperature", EVT_16BIT, value,
        //+        "humidity", EVT_16BIT, value,
        //+        );
}

/**
    @brief dump the board log values from the event log (for debugging only)
*/

void
board_log_dump()
{
        flash_read_t log_read;
        event_t     event;
        uint32_t timestamp = 0;
        int lines = 0, param_count;
        event_param_t params[7];

        flash_read_init(&event_partition, &log_read, the_cache);

        while (log_read.is_eof == false) {
                event_log_read(&log_read, &event, &timestamp, &param_count, params);
                if (event != EVT_BOARD_LOG)
                        continue;

                datetime_display(timestamp);
                printf(" battery %d solar %d flags 0x%x thermistor 0x%x", (uint16_t) params[0].arg, (uint16_t)params[1].arg, (uint8_t)params[2].arg, (uint16_t)params[3].arg);

                if (param_count > 4) {
                        uint16_t temp = params[4].arg;

                        printf(" temp %d.%dC", temp / 10, temp % 10);
                }
                printf("\n");
                if (!display_more(&lines))
                        return;
        }
}

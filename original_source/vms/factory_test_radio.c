/** @{

    @file
    @ingroup    vms
    @brief      Factory Test over the radio
*/

#include "pelagic.h"
#include "alarm.h"
#include "bnet.h"
#include "bnet_factory.h"
#include "modem.h"
#include "radio.h"
#include "signals.h"
#include "factory_test.h"
#include "provision.h"
#include "power.h"
#include "firmware.h"
#include "board_info.h"
#include "pinmap.h"
#include "mcu_sleep.h"
#include "rtc_api.h"
#ifndef MODEM_3G
#include "nfc.h"
#endif
#include <stdarg.h>

enum {
        FACTORY_TEST_PACKET_WAIT = 5 * MINUTE, /**< Seconds to wait before going to sleep without hearing about test packet */

        FACTORY_TEST_UPDATE_WAIT = 5,       /**< Seconds to wait before waiting for an acknowledgement */

        COVERUP_DELAY            = MINUTE,  /**< Seconds to wait for solar cover up */

        COVERUP_OR_TEST_WAIT     = (30*MINUTE), /**< Seconds to wait for start test packet */

        SOLAR_FACTORY_WAIT       = 10*MINUTE,   /**< Seconds to wait for solar to appear */

        FACTORY_TEST_PERIOD      = 12 * DAY,

        FACTORY_SLEEP_INTERVAL   = 15,

        SHITTY_REBOOT_SLEEP      = 1, // I don't know the proper way to reboot immediately, so I'm sleeping. :)
};

typedef enum {
        COVER_IS_ON              = 1,   /**< Solar has been covered */
        TEST_REQUESTED           = 2,   /**< Start the tests */
        NO_RESPONSE              = 3,   /**< No response to start test */
        SLEEP_TEST               = 4    /**< Begin sleep test */
} ft_wait_t;

board_uid_t test_server_uid;

extern modem_info_t modem_info;

uint8_t factory_test_sequence = 0;

int vsprintf(char *buf, const char *fmt, va_list args);

/**
    @brief  Wait for a start test packet or timeout
    @param[out] deploy_mode     Zero if device is to continue waiting for more star test packets
                                Non-zero if device is to head to field deployment after successful tests

    @note   This will wait for a specific amount of time to receive a start test packet.
            If wait_for_cover is true it will return COVER_IS_ON when the solar panel has been covered up.

    @return
            - COVER_IS_ON if the solar panel has been covered up
            - TEST_REQUEST start test packet was received
            - NO_RESPONSE no start packet was received within a specific time
*/

ft_wait_t
ft_wait_for_test_packet(uint8_t *deploy_mode)
{
        int bytes;
        uint32_t now, last_sent;
        bool start = false;
        volatile bool buzzer;
        alarm_t timeout;

        bnet_ft_target_t *target_pkt = (bnet_ft_target_t *)bnet_receive_buffer;
        bnet_ft_begin_t *begin_pkt = (bnet_ft_begin_t *)bnet_receive_buffer;
        bnet_ft_ack_t *ack_pkt = (bnet_ft_ack_t *)bnet_receive_buffer;

        uart_printf("ft: wait for start. Will wait %d seconds for test packet.\n", FACTORY_TEST_PACKET_WAIT);

        alarm_start_set(&timeout, FACTORY_TEST_PACKET_WAIT, &buzzer);

        do {
                bytes = radio_read(target_pkt, 1000, NULL);
                //uart_printf("wait for packet bytes %d signals 0x%x\n", bytes, signals);

                if (buzzer) {
                        uart_printf("!timeout waiting for start test\n");
                        return NO_RESPONSE;
                }

                if (bytes == 0)
                        continue;


                switch (target_pkt->type) {
                case BNET_TYPE_PING:
                        // I don't actually know if I need to cast this into the right type or not...
                        bnet_process_ping(target_pkt, bytes);
                        break;

                case BNET_TYPE_FACTORY_TEST_START:
                        if (bytes != sizeof(bnet_ft_start_t))
                                continue;

                        alarm_cancel(&timeout);
                        uid_copy(test_server_uid, target_pkt->from_uid);
                        *deploy_mode = target_pkt->deploy_mode;
                        start = true;
                        break;

                case BNET_TYPE_FACTORY_TEST_TARGET:
                        if (bytes != sizeof(bnet_ft_target_t))
                                continue;

                        if (!imei_partial_match(board_info_imei, target_pkt->to_imei))
                                continue;

                        alarm_cancel(&timeout);

                        uid_copy(test_server_uid, target_pkt->from_uid);
                        *deploy_mode = target_pkt->deploy_mode;
                        start = true;
                        break;

                case BNET_TYPE_PROVISION:
                        alarm_cancel(&timeout);
                        bnet_process_provision(target_pkt, bytes);
                        break;

                case BNET_TYPE_FACTORY_TEST_SLEEP:
                        if (bytes != sizeof(bnet_ft_target_t))
                                continue;

                        if (!imei_partial_match(board_info_imei, target_pkt->to_imei))
                                continue;
                        alarm_cancel(&timeout);
                        return SLEEP_TEST;
                }
        } while (!start);

        // Start test packet received..
        alarm_start_set(&timeout, FACTORY_TEST_PACKET_WAIT, &buzzer);
        last_sent = 0;

        for (;;) {
                now = clock_read();

                if (last_sent != now) {
                        begin_pkt->type = BNET_TYPE_FACTORY_BEGIN;
                        uid_set_me(begin_pkt->from_uid);
                        uid_copy(begin_pkt->to_uid, test_server_uid);
                        radio_send(begin_pkt, sizeof(bnet_ft_begin_t));
                        last_sent = now;
                }

                bytes = radio_read(ack_pkt, 1000, NULL);

                if (buzzer) {
                        uart_printf("! timeout on begin ack\n");
                        return NO_RESPONSE;
                }

                if (bytes == sizeof(bnet_ft_ack_t) &&
                    ack_pkt->type == BNET_TYPE_FACTORY_BEGIN_ACK &&
                    uid_match_me(ack_pkt->to_uid)) {
                        alarm_cancel(&timeout);
                        uart_printf("> begin ack received\n");
                        return TEST_REQUESTED;
                }
        }
}

/**
    @brief  Either report back a success or specific failure to the testbox.
    @return true if the report was acknowledged.
*/

bool
ft_report_results(ft_result_t result)
{
        int bytes;
        volatile bool buzzer;
        uint32_t last_sent, now;
        bnet_ft_finish_t *finish_pkt = (bnet_ft_finish_t *) bnet_send_buffer;
        bnet_ft_ack_t *ack_pkt = (bnet_ft_ack_t *) bnet_receive_buffer;
        alarm_t timeout;

        alarm_start_set(&timeout, FACTORY_TEST_PACKET_WAIT, &buzzer);

        last_sent = 0;

        // I believe that code continues to run after the FT initiator sends an ACK,
        //   which causes the VTS under test to continue listening for 5 minutes;
        //   the end result of which is that we can't do anything to the VTS (like send
        //   a provision-shipping packet) until this alarm expires.
        for (;; ) {
                // send out a packet once a second..
                now = clock_read();
                if (last_sent != now) {
                        finish_pkt->type = BNET_TYPE_FACTORY_FINISH;
                        uid_set_me(finish_pkt->from_uid);
                        set_uint16(finish_pkt->result, result);
                        if (result == FT_SUCCESS)
                                memcpy(finish_pkt->imei, board_info_imei, sizeof(board_info_imei));

                        radio_send(finish_pkt, sizeof(bnet_ft_finish_t));
                        last_sent = now;
                }

                bytes = radio_read(ack_pkt, 1000, NULL);

                if (buzzer)
                        return false;

                if (bytes == sizeof(bnet_ft_ack_t) &&
                    ack_pkt->type == BNET_TYPE_FACTORY_FINISH_ACK &&
                    uid_match(ack_pkt->to_uid, board_uid)) {
                        alarm_cancel(&timeout);
                        uart_printf("< finish ack result 0x%x\n", ack_pkt->flags);
                        return true;
                }
        }
}

/**
    @brief  Give an update to the testbox on how things are going.
    @param[in]  message   printf like string
    @param[in]  ...       optional arguments

    @note   This routine serves a two functions. First, it provides a "heartbeat" to the testbox
            to indicate the tests are still proceeding. The second is it gives a visual update for
            debugging purposes.

            The routine will wait up until FACTORY_TEST_UPDATE_WAIT to receive an acknowledgement.
*/

void
ft_update(const char *message, ...)
{
        int bytes, len;
        volatile bool buzzer;
        uint32_t last_sent, now;
        bnet_ft_update_t *update_pkt = (bnet_ft_update_t *)the_buffer.test;
        bnet_ft_ack_t *ack_pkt = (bnet_ft_ack_t *) bnet_send_buffer;
        alarm_t timeout;
        va_list args;

        alarm_start_set(&timeout, FACTORY_TEST_UPDATE_WAIT, &buzzer);

        last_sent = 0;
        factory_test_sequence++;

        update_pkt->type = BNET_TYPE_FACTORY_UPDATE;
        uid_set_me(update_pkt->from_uid);
        va_start(args, message);
        len = vsprintf(update_pkt->data, message, args);
        va_end(args);
        if (len > 41) {
                uart_printf("MESSAGE TOO BIG len= %d [%s]", len, message);
        }
        update_pkt->size = len;

        uart_printf("= %s\n", update_pkt->data);

        for (;; ) {
                // send out a packet once a second..
                now = clock_read();
                if (last_sent != now) {
                        update_pkt->sequence = factory_test_sequence;
                        radio_send(update_pkt, sizeof(bnet_ft_update_t) + update_pkt->size);
                        last_sent = now;
                }

                bytes = radio_read(ack_pkt, 1000, NULL);

                // Just proceed on if there was no response
                if (buzzer)
                        return;

                if (bytes == sizeof(bnet_ft_ack_t) &&
                    ack_pkt->type == BNET_TYPE_FACTORY_UPDATE_ACK &&
                    uid_match(ack_pkt->to_uid, board_uid)) {
                        alarm_cancel(&timeout);
                        return;
                }
        }
}

void
factory_test(uint8_t factory_test_state, bool sleep_until_darkness)
{
        rtc_init();
        board_info_init();

        if (rtc_read() > FACTORY_TEST_PERIOD) {
                // Switch modes and never return here again!
                uart_printf("Factory Test: Switching to Shipping Mode because we've been awake for %d minutes\n", FACTORY_TEST_PERIOD/60);
                provision_shipping();
                mcu_sleep(FACTORY_SLEEP_INTERVAL);
        } else {
                // We're still in the factory, so continue to respond to requests

                if (solar_present(0)) {
                        if (sleep_until_darkness) {
                                // It’s light out, but we want to sleep (being unresponsive), until it’s dark
                                mcu_sleep(FACTORY_SLEEP_INTERVAL);
                        } else {
                                // We have solar and we want to be available for tests
                                wait_for_test_request(false);
                        }
                } else {
                        if (sleep_until_darkness) {
                                // We don't have solar, but we're waiting until that's been true
                                //   for 4 cycles before we switch modes
                                for (int i=0; i<4; i++) {
                                        if (solar_present(FACTORY_SLEEP_INTERVAL)) {
                                                // After brief darkness, we've returned to the light,
                                                //   so reboot and try again
                                                mcu_sleep(SHITTY_REBOOT_SLEEP);
                                        }
                                }

                                // We’ve seen 4 darknesses, so enter dark mode
                                set_sleep_until_darkness(0);
                                mcu_sleep(FACTORY_SLEEP_INTERVAL);

                        } else {
                                // We’re in normal dark mode
                                set_sleep_until_darkness(0);
                                mcu_sleep(FACTORY_SLEEP_INTERVAL);
                        }
                }
        }
}

void
wait_for_test_request(bool sleep_after_test)
{
        uint8_t wait_result;
        uint8_t deploy_mode;
        board_info_init();

        if (board_info_have_imei == false) {
                uart_printf("ft: attempting to get IMEI from modem.\n");
                modem_init();
                modem_power_on();
                osDelay(5000); // Give extra time to get info
                modem_result_t result = modem_get_info(&modem_info);
                modem_power_off();

                if (result == MODEM_SUCCESS) {
                        board_info_set_imei(modem_info.imei);
                        uart_printf("ft: success getting IMEI from modem.\n");

#ifdef HAVE_NFC
                        uint64_t imei = get_uint64(board_info_imei);
                        char buf[20];
                        sprintf(buf, "%6.6d%9.9d",
                                (uint32_t)(imei / 1000000000),
                                (uint32_t)(imei % 1000000000));
                        nfc_init(buf);
#endif
                } else {
                        uart_printf("ft: error getting IMEI from modem. Aborting.\n");
                        mcu_sleep(FACTORY_SLEEP_INTERVAL);
                }
        }

        if (board_info_have_imei) {
                uint64_t imei = get_uint64(board_info_imei);
                uart_printf("ft: IMEI %6.6d%9.9d\n", (uint32_t)(imei / 1000000000),
                            (uint32_t)(imei % 1000000000));
        } else {
                uart_printf("ft: modem info has not been retrieved yet.\n");
        }

        if (!radio_init(true)) {
                uart_printf("ft: radio init failed. Aborting.\n");
                mcu_sleep(FACTORY_SLEEP_INTERVAL);
        }

        wait_result = ft_wait_for_test_packet(&deploy_mode);
        if (wait_result == NO_RESPONSE) {
                set_sleep_until_darkness(1);
                mcu_sleep(FACTORY_SLEEP_INTERVAL);
        } else {
                run_tests_and_report_results();

                // After we run a test, give the user a chance to repeat it
                if (sleep_after_test) {
                        mcu_sleep(FACTORY_SLEEP_INTERVAL);
                } else {
                        wait_for_test_request(true);
                }
        }

}

void
run_tests_and_report_results()
{
        const ft_device_t *device;
        ft_result_t result;
        ft_results_length = 0;

        // Power on modem first thing so has gets time to search for the network
        //   while other tests are running
        modem_power_on();

        ft_device_result("device-type %d", DEVICE_VMS);
        ft_device_result("device-version %d", DEVICE_VERSION);
        ft_device_result("buildtag %s", build_tag);
        ft_device_result("buildstamp %d", build_timestamp);

        device = ft_devices;
        result = FT_SUCCESS;

        // Run individual tests
        for (int i = 0; i < ft_device_count; i++, device++) {
                ft_update(device->name);

                result = device->test();
                if (result != FT_SUCCESS) {
                        ft_report_results(result);

                        // If any tests failed, abort.

                        set_factory_test_state(FACTORY_TEST_FAILED);
                        mcu_sleep(SHITTY_REBOOT_SLEEP);
                }
        }

        // Store results to flash
        ft_device_result("test-result %d", result);

        if (result == FT_SUCCESS) {
                // We passed all our tets, and uploaded successfully,
                //   so set FACTORY_TEST_PASSED

                set_factory_test_state(FACTORY_TEST_PASSED);
                ft_report_results(result);
        } else {
                set_factory_test_state(FACTORY_TEST_FAILED);
        }
}


/**@} */

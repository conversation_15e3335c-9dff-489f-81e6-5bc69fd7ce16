#ifndef __MODEM_BODYTRACE_H__
#define __MODEM_BODYTRACE_H__

typedef enum {
        MODEM_CMD_PING           = 0x00,
        MODEM_CMD_MODEM_INFO     = 0x01,
        MODEM_CMD_MODEM_STATUS   = 0x02,
        <PERSON><PERSON><PERSON>_CMD_MESSAGE_STATUS = 0x03,
        MODEM_CMD_SEND_TEST      = 0x20,
        MODEM_CMD_SEND_DATA      = 0x40,
        M<PERSON><PERSON>_CMD_READ_CHUNK     = 0x42,
        MODEM_CMD_MSG_DELETE     = 0x43,
        MODEM_CMD_POWER_OFF      = 0xe0
} modem_command_t;

enum {
        BT_PACKET_PRELUDE  = 0x3c,
        BT_PACKET_TRAILER  = 0x3b,
        BT_PACKET_ACK      = 0x3e
};

typedef enum {
        MODEM_STATE_INITIALIZING = 0x00, // System initializing
        MODEM_STATE_IDLE,                           // Idle, not connected
        MOD<PERSON>_STATE_REGISTERING,                    // Registering to the cellular network
        MODEM_STATE_CONNECTING,                     // Connecting to data service
        MODEM_STATE_CONNECTED,                  // Connected to data service, no transmission in progress
        MODEM_STATE_XFER,                       // Transferring messages
        MODEM_STATE_DISCONNECTING,      // Disconnecting from data service
        MODEM_STATE_DEREGISTERING,      // Deregistering from network

        MODEM_STATE_PROVISIONING = 0x20,     // Modem is being provisioned

        MODEM_STATE_ERROR_INTERNAL = 0x80,  // An internal error occurred
        MODEM_STATE_ERROR_BATTERY,         // Supply voltage (battery charge) is critically low
        MODEM_STATE_ERROR_SIM,              // Failed to initialize SIM
        MODEM_STATE_ERROR_REGISTER,         // Failed to register to network
        MODEM_STATE_ERROR_CONNECT,          // Failed to connect to data service
        MODEM_STATE_ERROR_XFER,             // Failed to transfer messages


        MODEM_STATE_ERROR_PROV_KEY     = 0xa0,  // Failed to register device public key
        MODEM_STATE_ERROR_PROV_XFER,            // Failed to transfer data from provisioning service
        MODEM_STATE_ERROR_PROV_INVALID,         // Invalid provisioning response received
        MODEM_STATE_ERROR_PROV_UNPROVISIONED,   // Device not provisioned

        MODEM_STATE_ERROR_TEST_VOLTAGE = 0xc0, // Supply voltage not accepted by remote service
        MODEM_STATE_ERROR_TEST_ADC,           // ADC value not accepted by remote service
        MODEM_STATE_ERROR_TEST_RSSI,           // Signal RSSI not accepted by remote service
        MODEM_STATE_ERROR_TEST_DATA,           // Test data not accepted by remote service
} modem_state_t;

typedef struct
__attribute__ ((packed))
{
        uint8_t prelude;
        uint8_t command;
        uint8_t count[2];				/**< Number of messages pending */
        uint8_t size[4];				/**< Total length of message payloads pending (in bytes) */

        uint8_t test_count[2];
        uint8_t test_size[4];

        uint8_t data_count[2];
        uint8_t data_size[4];

        uint8_t crc[2];
        uint8_t trailer;
}
modem_message_status_t;

typedef struct
__attribute__ ((packed))
{
        uint8_t prelude;
        uint8_t command;
        uint8_t length[4];
}
modem_data_header_t;

typedef struct
__attribute__ ((packed))
{
        uint8_t command;
        uint8_t offset[4];
        uint8_t bytes[4];
}
modem_read_chunk_header_t;

typedef struct
__attribute__ ((packed))
{
        uint8_t prelude;
        uint8_t command;
        uint8_t bytes[4];
        uint8_t remaining[4];
}
modem_read_chunk_response_t;

typedef struct
__attribute__ ((packed))
{
        uint8_t crc[2];
        uint8_t epilogue;
}
modem_footer_t;

bool modem_crc_okay(const char *name, uint8_t *packet, uint32_t bytes);
void modem_send_packet(void *data, uint32_t bytes);

char *modem_state_label(modem_state_t state);
#endif

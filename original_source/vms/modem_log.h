#ifndef __MODEM_LOG_H__
#define __MODEM_LOG_H__

enum {
        MODEM_LOG_SIZE   = 8
};

typedef struct {
        uint32_t         timestamp;
        uint16_t         power_on_secs;
        uint16_t         network_acquire_secs;
        uint16_t         transmit_secs;
        uint32_t         bytes_sent;
        uint32_t         bytes_received;
        uint8_t          run_state;
        uint8_t          modem_state;
        uint8_t          success;
        uint8_t          have_network;
        uint16_t         sleep;
} modem_log_t;

extern uint8_t modem_log_in, modem_log_out, modem_log_count;
extern modem_log_t modem_logs[], *modem_log;

void modem_log_display();
void modem_log_new_entry();
#endif

/** @{

    @file
    @ingroup vms
    @brief  Factory Test support routines
*/

#include "pelagic.h"
#include "factory_test.h"
#include <stdarg.h>

const ft_device_t ft_devices[] = {
//    { .name = "temp",   .test = temp_factory_test },
        { .name = "gps",    .test = gps_factory_test },
        { .name = "power",  .test = power_factory_test },
        { .name = "flash",  .test = winbond_factory_test },
#ifdef HAVE_ACCEL
        { .name = "accel",  .test = accel_factory_test },
#endif

#ifdef HAVE_HUMIDITY
        { .name = "humidity",  .test = humidity_factory_test },
#endif
        { .name = "modem",  .test = modem_factory_test },
};

extern int vsprintf(char *buf, const char *fmt, va_list args);

const int ft_device_count = ARRAY_SIZE(ft_devices);

int ft_results_length = 0;

/**
    @brief  adds a results from a device test (current signal strength, etc)
    @param[in]  message     printf like string
    @param[in]  ...         optional arguments

*/

void
ft_device_result(const char *message, ...)
{
        va_list args;

        va_start(args, message);
        ft_results_length += vsprintf((char *)&compress_buffer[ft_results_length], message, args);
        compress_buffer[ft_results_length++] = '\n';
        va_end(args);
}

/**
    @brief  factory test update message (stub - was in bnet_new_factory.c)
    @param[in]  message     printf like string
    @param[in]  ...         optional arguments
*/
void
ft_update(const char *message, ...)
{
        va_list args;
        char buffer[256];

        va_start(args, message);
        vsprintf(buffer, message, args);
        va_end(args);

        uart_printf("FT: %s\n", buffer);
}

/** @} */

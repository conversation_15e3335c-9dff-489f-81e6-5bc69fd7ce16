/** @{

    @ingroup    vms
    @file
    @brief      retrieve and process incoming messages
*/

#include "pelagic.h"
#include "modem.h"
#include "pds.h"
#include "firmware.h"

/**
    @brief  retrieve modem messages - handles settings and incoming firmware
    @param[out] msg_type    Message type (PDS_TYPE_*, firmware, setting, etc)
    @param[out] fw_header   holds firmware information (buildstamp, length, device, etc)
    @return true if the message was successfully retrieved
*/

bool
pds_retrieve_message(pds_incoming_msg_t *msg_type, firmware_header_t *fw_header)
{
        modem_result_t modem_result;
        flash_result_t flash_result = FLASH_SUCCESS;
        uint32_t bytes, remaining, offset = 0;
        bool is_sensor_firmware = false;
        uint32_t started;
        flash_partition_t *part = NULL;
        uint8_t *data;

        started = clock_read();

        do {
                modem_result = modem_read_chunk(the_buffer.modem, offset, MODEM_CHUNK_MAX, &bytes, &remaining);

                if (modem_result != MODEM_SUCCESS) {
                        EVENT_LOG1(EVT_PDS_MSG_HEADER_READ_ERR, "read msg header err", "result", EVT_8BIT, modem_result);

                        if (part)
                                flash_store_set_busy(part, false);

                        if (is_sensor_firmware)
                                firmware_sensor_header.buildstamp = 0;

                        return false;
                }

                if (modem_watch) {
                        printf("pds_retrieve_message read chunk: bytes: %d, remaining: %d\n", bytes, remaining);
                }

                data = the_buffer.modem;

                if (offset == 0) {
                        *msg_type = data[0];

                        switch (*msg_type) {
                        case PDS_INCOMING_FIRMWARE:
                                memcpy(fw_header, &data[1], sizeof(*fw_header));
                                switch (fw_header->device_hardware) {
                                case DEVICE_VMS:
                                        part = &firmware_partition;
                                        break;

                                case DEVICE_SENSOR:
                                        part = &firmware_sensor_partition;
                                        is_sensor_firmware = true;
                                        break;

                                default:
                                        EVENT_LOG1(EVT_PDS_UNKNOWN_DEVICE_TYPE, "unknown device",
                                                   "type", EVT_8BIT, fw_header->device_hardware);
                                        return false;
                                }

                                flash_store_set_busy(part, true);
                                flash_store_erase(part);
                                flash_result = flash_store_write(part, &data[1], bytes - 1);

                                EVENT_LOG2(EVT_PDS_MSG_FIRMWARE, "firmware msg",
                                           "device", EVT_8BIT, fw_header->device_hardware,
                                           "buildstamp", EVT_32BIT, fw_header->buildstamp);
                                break;

                        case PDS_INCOMING_SETTINGS:
                                EVENT_LOG(EVT_PDS_MSG_SETTINGS, "settings msg");
                                pds_settings_process((pds_settings_incoming_msg_t *) data, bytes);
                                remaining = 0;
                                break;

                        default:
                                EVENT_LOG1(EVT_PDS_MSG_UNKNOWN, "unknown msg", "type", EVT_8BIT, *msg_type);
                                return false;
                        }

                } else {
                        flash_result = flash_store_write(part, data, bytes);
                }

                if (part && flash_result != FLASH_SUCCESS) {
                        flash_store_set_busy(part, false);
                        EVENT_LOG1(EVT_PDS_MSG_STORE_ERR, "msg store error", "result", EVT_8BIT, flash_result);

                        if (is_sensor_firmware)
                                firmware_sensor_header.buildstamp = 0;

                        return false;
                }
                offset += bytes;
        } while (remaining > 0);

        EVENT_LOG2(EVT_PDS_MSG_SUCCESS, "msg received", "bytes", EVT_32BIT, offset, "elasped", EVT_16BIT, clock_read() - started);

        if (part) {
                flash_store_flush(part);

                if (is_sensor_firmware)
                        firmware_sensor_init();

                flash_store_set_busy(part, false);
        }

        return true;
}

/**
    @brief  retrieve message count and firmware message acknowledge
*/

void
pds_check_messages()
{
        bool result;
        uint32_t messages, start;
        pds_incoming_msg_t msg_type;
        firmware_header_t fw_header;
        int i;

        for (i = 1;; i++) {
                start = clock_read();
                result = modem_get_message_count(&messages);

                if (result != MODEM_SUCCESS) {
                        EVENT_LOG1(EVT_PDS_MSG_COUNT_ERR, "msg count read err", "result", EVT_8BIT, result);
                        return;
                }

                EVENT_LOG2(EVT_PDS_MSG_COUNT, "messages", "count", EVT_32BIT, messages, "elapsed", EVT_16BIT, clock_read() - start);

                if (messages == 0)
                        return;

                result = pds_retrieve_message(&msg_type, &fw_header);
                if (modem_watch) {
                        printf("pds_check_messages: retrieved message %d of %d\n", i, messages);
                }
                modem_delete_message();

                if (result == false)
                        continue;

                switch (msg_type) {
                case PDS_INCOMING_FIRMWARE:
                        EVENT_LOG(EVT_PDS_MSG_ACK, "firmware ack");
                        pds_firmware_acknowledge(&fw_header, board_uid);
                        if (fw_header.device_hardware == DEVICE_VMS) {
                                modem_power_off();
                                firmware_update();
                        }
                        break;
                default:
                        break;
                }
        }
}

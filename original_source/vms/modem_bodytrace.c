/** @{

    @ingroup    vms
    @file
    @brief      Bodytrace GSM interface routines
*/

#include "pelagic.h"
#include "modem.h"
#include "modem_bodytrace.h"
#include "modem_log.h"
#include "gpio_api.h"
#include "led.h"
#include "factory_test.h"
#include "pds.h"
#include "board_info.h"
#include "pinmap.h"

volatile bool gsm_enabled = true;

enum {
        MODEM_XMIT_WAIT_TRIES = (15*MINUTE), // Wait up to 15 minutes, 5 minutes to register, and 5 minutes to download incoming firmware update, 5 minute buffer.
        MODEM_RECEIVE_TIMEOUT = 10000,       // or 10 seconds for the modem to respond
        MODEM_TX_BUFFER_SIZE = 128,
        MODEM_RX_BUFFER_SIZE = 128,
};


const char *modem_results[] = {
        "0",
        "success",
        "crc-err",
        "timeout",
        "cmd-mismatch",
        "too big",
        "unknown msg",
        "fault",
        "part-busy",
        "xmit-timeout"
};

bool modem_initted = false, modem_pin_initted = false;

gpio_t modem_power_key;
gpio_t modem_power_en_pin;
serial_t modem_port;

volatile bool modem_watch = 0;

char modem_tx_buffer[MODEM_TX_BUFFER_SIZE];
char modem_rx_buffer[MODEM_RX_BUFFER_SIZE];

/**
    @brief     Initialize the serial port and pins associated with the GSM
*/

void
modem_init()
{
        if (modem_initted)
                return;

        serial_init(&modem_port, MDM_TX, MDM_RX);
        serial_buffer(&modem_port, modem_tx_buffer, MODEM_TX_BUFFER_SIZE, modem_rx_buffer, MODEM_RX_BUFFER_SIZE);
        serial_enable_dma_tx(&modem_port);

        modem_pin_setup();

        modem_initted = true;
}

/**
    @brief      Setup the pins associated with the GSM
*/

void
modem_pin_setup()
{
        if (modem_pin_initted)
                return;

#ifdef TARGET_PLACO
        gpio_init_out_ex(&modem_power_en_pin, MDM_EN, 0);
        gpio_init_out_ex(&modem_power_key, MDM_PKEY, 0);
        // pin_mode(GSM_STA, PullDown);
        //! TODO Ian: verify there is not GSM_STA pin on the new board with the newest schematic
        // contact Gabor if needed to ensure no alternate operation is needed
#endif

        modem_pin_initted = true;
}

/**
    @brief  Send a GSM packet.
*/

void
modem_send_packet(void *data, const uint32_t bytes)
{
        uint16_t crc = crc16(data, bytes);
        uint8_t packet[4];

        serial_flush(&modem_port);          // Dump any pending bytes
        serial_hold(&modem_port);           // wait until the buffer is full or released before xmiting
        packet[0] = BT_PACKET_PRELUDE;
        serial_write(&modem_port, (char *)packet, 1);
        serial_write(&modem_port, (char *)data, bytes);
        packet[0] = crc >> 8;
        packet[1] = crc & 0xff;
        packet[2] = BT_PACKET_TRAILER;
        serial_write(&modem_port, (char *)packet, 3);
        serial_release(&modem_port);
}


/**
    @brief  get the current modem state in human format
    @return a string label for the state.
*/

char *
modem_state_label(modem_state_t state)
{
        static char errmsg[6];

        switch (state) {
        case MODEM_STATE_INITIALIZING:
                return "init";
        case MODEM_STATE_IDLE:
                return "idle";
        case MODEM_STATE_REGISTERING:
                return "register";
        case MODEM_STATE_CONNECTING:
                return "connecting";
        case MODEM_STATE_CONNECTED:
                return "sent";
        case MODEM_STATE_XFER:
                return "xfer";
        case MODEM_STATE_DISCONNECTING:
                return "disconnecting";
        case MODEM_STATE_DEREGISTERING:
                return "deregister";

        case MODEM_STATE_PROVISIONING:
                return "provisioning";

        case MODEM_STATE_ERROR_INTERNAL:
                return "err-internal";
        case MODEM_STATE_ERROR_BATTERY:
                return "err-battery";
        case MODEM_STATE_ERROR_SIM:
                return "err-sim";
        case MODEM_STATE_ERROR_REGISTER:
                return "err-register";
        case MODEM_STATE_ERROR_CONNECT:
                return "err-connect";
        case MODEM_STATE_ERROR_XFER:
                return "err-xfer";

        case MODEM_STATE_ERROR_PROV_KEY:
                return "err-prov-key";
        case MODEM_STATE_ERROR_PROV_XFER:
                return "err-prov-xfer";
        case MODEM_STATE_ERROR_PROV_INVALID:
                return "err-prov-invalid";
        case MODEM_STATE_ERROR_PROV_UNPROVISIONED:
                return "err-unprov";

        case MODEM_STATE_ERROR_TEST_VOLTAGE:
                return "err-test-voltage";
        case MODEM_STATE_ERROR_TEST_ADC:
                return "err-test-adc";
        case MODEM_STATE_ERROR_TEST_RSSI:
                return "err-test-rssi";
        case MODEM_STATE_ERROR_TEST_DATA:
                return "err-test-data";

        default:
                sprintf(errmsg, "0x%02x", state);
                return errmsg;
        }
}

/**
    @brief  power on the modem
*/

void
modem_power_on()
{
        if (modem_watch)
                printf("bt: powering up\n");

        serial_power_on(&modem_port);
        serial_flush(&modem_port);

#ifdef HAVE_3G
        osDelay(10000);
#else
        osDelay(500);
#endif
#ifdef TARGET_PLACO
        if (gsm_enabled)
                gpio_write(&modem_power_en_pin, 1);
        else
                printf("bt: gsm disabled, NOT powering up modem\n");
#if MODEM_3G
        osDelay(10000);
#else
        osDelay(5000);
#endif // MODEM_3G
#endif // TARGET_PLACO

        if (modem_watch)
                printf("bt: powered on\n");
}

/**
    @brief  turn on the Programmer Key (PKEY) pin. Used for updating the GSM firmware
*/

void
modem_pkey_on()
{
        gpio_write(&modem_power_key, 1);
}

/**
    @brief  turn off the PKEY pin
*/

void
modem_pkey_off()
{
        gpio_write(&modem_power_key, 0);
}

/**
    @brief  issue a power off command to the GSM, wait for confirmation, then cut the power
*/

void
modem_power_off()
{
        uint8_t command[] = { MODEM_CMD_POWER_OFF };

        modem_send_packet(command, 1);
        modem_receive_ack_response("power off", MODEM_CMD_POWER_OFF);

#ifdef TARGET_PLACO
#ifdef MODEM_3G
        osDelay(15000);
#else
        osDelay(3000);
#endif  // MODEM_3G
        gpio_write(&modem_power_en_pin, 0);
#endif

        serial_power_off(&modem_port);
        if (modem_watch)
                printf("bt: power off\n");
}

/**
    @brief  log a CRC mismatch
    @param[in]  name    modem command name that experienced a CRC mismatch
    @param[in]  got     received CRC from modem
    @param[in]  expected the computed packet CRC
*/

void
modem_report_crc_err(const char *name, uint16_t got, uint8_t expected)
{
        EVENT_LOG3(EVT_BT_CRC_ERR,
                   "packet crc err",
                   "command", EVT_STRCONST, name,
                   "got", EVT_16BIT, got,
                   "expected", EVT_16BIT, expected);
}

/**
    @brief  verify receive packet CRC against computed
    @param[in]  name    modem command name
    @param[in]  packet  the packet to compute the CRC
    @param[in]  bytes   packet size
    @return true if the packet CRC is valid
*/

bool
modem_crc_okay(const char *name, uint8_t *packet, const uint32_t bytes)
{
        uint16_t computed_crc, packet_crc;

        computed_crc = crc16(&packet[1], bytes - 4);
        packet_crc   = packet[bytes-3] << 8;
        packet_crc  |= packet[bytes-2];

        if (packet_crc == computed_crc)
                return true;

        modem_report_crc_err(name, packet_crc, computed_crc);
        return false;
}

/**
    @brief  store a 32-bit integer as big endian into a modem packet.
    @param[in]  data    buffer to store the integer as big endian
    @param[in]  value   the integer to store
*/

void
modem_set_uint32(uint8_t *data, const uint32_t value)
{
        data[0] = (value >> 24) & 0xff;
        data[1] = (value >> 16) & 0xff;
        data[2] = (value >> 8) & 0xff;
        data[3] = value & 0xff;
}


/**
    @brief  store a 16-bit integer as big endian into a modem packet
    @param[in]  data    buffer to store the integer as big endian
    @param[in]  value   the integer to store
*/

uint16_t
modem_get_uint16(uint8_t *data)
{
        return ((uint16_t) data[0] << 8) | ((uint16_t) data[1]);
}

/**
    @brief  retrieve a 32-bit big endian integer from a modem packet
    @param[in]  data    buffer to retrieve integer
    @return the 32-bit integer in native format
*/

uint32_t
modem_get_uint32(uint8_t *data)
{
        return ((uint32_t) data[0] << 24) | ((uint32_t) data[1] << 16) | ((uint32_t) data[2] << 8) | ((uint32_t) data[3]);
}

/**
    @brief  retrieve a 64-bit big endian integer from a modem packet.
    @param[in]  data    buffer to retrieve integer
    @return the 64-bit integer in native format
*/

uint64_t
modem_get_64(uint8_t *data)
{
        return ((uint64_t) data[0] << 56) | ((uint64_t) data[1] << 48) | ((uint64_t) data[2] << 40) | ((uint64_t) data[3] << 32) |
               ((uint64_t) data[4] << 24) | ((uint64_t) data[5] << 16) | ((uint64_t) data[6] << 8) | ((uint64_t) data[7]);
}

/**
    @brief  read bytes from the modem with timeout
    @param[out] data    buffer to store bytes
    @param[in]  size    byte count to read
    @return
        - MODEM_SUCCESS all requested bytes were received
        - MODEM_ERR_TIMEOUT not all bytes were received within MODEM_RECEIVE_TIMEOUT seconds
*/

modem_result_t
modem_read(void *data, const uint32_t size)
{
        modem_result_t result;

        for (uint32_t bytes = 0; bytes < size; bytes += result) {
                result = serial_read_timeout(&modem_port, &((char *)data)[bytes], size - bytes, MODEM_RECEIVE_TIMEOUT);

                if (result == 0) {
                        EVENT_LOG(EVT_BT_READ_TIMEOUT, "read timeout");
                        return MODEM_ERR_TIMEOUT;
                }
        }

        return MODEM_SUCCESS;
}

/**
    @brief  confirm an ACK response
    @param[in]  name    modem command name
    @param[in]  command the modem command to confirm acknowledgement
    @return
            - MODEM_SUCCESS command was acknowledged
            - MODEM_TIMEOUT modem did not send a ack within a given period
            - MODEM_ERR_CRC CRC did not match
            - MODEM_ERR_MISMATCH the wrong command acknowledgement was received
*/

modem_result_t
modem_receive_ack_response(const char *name, uint8_t command)
{
        modem_result_t result;
        uint8_t packet[5];

        result = modem_read(packet, 5);

        if (result != MODEM_SUCCESS)
                return result;

        if (modem_crc_okay(name, packet, 5) == false)
                return MODEM_ERR_CRC;

        if (packet[1] != command) {
                EVENT_LOG2(EVT_BT_CMD_MISMATCH, "ack mismatch", "cmd", EVT_8BIT, command, "got", EVT_8BIT, packet[1]);
                return MODEM_ERR_MISMATCH;
        }

        if (modem_watch)
                printf("bt: cmd [0x%02x] acked\n", command);

        return MODEM_SUCCESS;
}

/**
    @brief  wait for a packet transmission to start and/or finish
    @param[in]  wait
                    - MODEM_WAIT_NONE wait only until transmission starts (used to batch multiple transmissions)
                    - MODEM_WAIT wait until transmission finishes
    @return
            - MODEM_SUCCESS transmission started and/or finished
            - MODEM_ERR_FAULT modem failed to registered with network, no signal available, etc.
            - MODEM_ERR_XMIT_TIMEOUT transmission failed to complete within MODEM_XMIT_WAIT_TRIES seconds
            - other errors are possible (crc match, ack failure, etc)
*/

modem_result_t
modem_xmit_wait(modem_wait_t wait)
{
        return modem_xmit_wait_test(wait, false);
}

modem_result_t
modem_xmit_wait_test(modem_wait_t wait, bool send_ft_update)
{
        modem_result_t result;
        modem_status_t status;
        int loop;

        for (loop = 0; ; loop++) {
                // We want to limit our modem time in the field, but not during tests.
                if (!send_ft_update && loop > MODEM_XMIT_WAIT_TRIES)
                        break;

                result = modem_get_status(&status);

                if (result != MODEM_SUCCESS)
                        return result;

                if (modem_watch)
                        printf("bt: xmit wait[%d] status[%s]\n", loop, modem_state_label(status.state));

                if (send_ft_update)
                        ft_update("modem: status 0x%02x xmit wait %d:%02d", status.state, loop/60, loop % 60);

                if (status.state == MODEM_STATE_CONNECTED ||
                    status.state == MODEM_STATE_IDLE ||
                    (wait == MODEM_WAIT_NONE && status.state == MODEM_STATE_XFER))
                        return MODEM_SUCCESS;

                if (status.state >= 0x80) {
                        if (modem_log)
                                modem_log->modem_state = status.state;

                        if (send_ft_update)
                                ft_update("modem: status 0x%02x FAIL", status.state);

                        EVENT_LOG1(EVT_BT_XMIT_ERR, "xmit wait", "status", EVT_8BIT | EVT_HEX, status.state);
                        return MODEM_ERR_FAULT;
                }

                osDelay(1000);
        }

        if (modem_watch)
                printf("bt: xmit wait expired\n");

        return MODEM_ERR_XMIT_TIMEOUT;
}

/**
    @brief  send a data packet (SEND_DATA) to the modem
    @param[in]  data    packet to send
    @param[in]  bytes   packet size
    @param[in]  wait    wait for finish or just wait for transmission start
    @return
            - MODEM_SUCCESS packet started and/or finished transmission (based on wait param)
            - other errors are possible
*/

modem_result_t
modem_send_data(void *data, const uint32_t bytes, modem_wait_t wait)
{
        modem_iovec_t vec = { .data = data, .size = bytes };

        return modem_send_data_vec(&vec, 1, wait, MODEM_CMD_SEND_DATA);
}
/**
    @brief  send a data packet (SEND_TEST) to the modem
    @param[in]  data    packet to send
    @param[in]  bytes   packet size
    @param[in]  wait    wait for finish or just wait for transmission start
    @return
            - MODEM_SUCCESS packet started and/or finished transmission (based on wait param)
            - other errors are possible
*/

modem_result_t
modem_send_test_data(void *data, const uint32_t bytes, modem_wait_t wait)
{
        modem_iovec_t vec = { .data = data, .size = bytes };

        return modem_send_data_vec(&vec, 1, wait, MODEM_CMD_SEND_TEST);
}

/**
    @brief  send a (single) data packet in scatter/gather format to the modem
    @param[in]  vec   data vectors to send
    @param[in]  vec_size    array vec size
    @param[in]  wait    wait for finish or just wait for transmission start
    @return
            - MODEM_SUCCESS packet started and/or finished transmission (based on wait param)
            - MODEM_ERR_FAULT modem failed to registered with network, no signal available, etc.
            - MODEM_ERR_XMIT_TIMEOUT transmission failed to complete within MODEM_XMIT_WAIT_TRIES seconds
            - other errors are possible (crc match, ack failure, etc)
*/

modem_result_t
modem_send_data_vec(const modem_iovec_t *vec, const int vec_size, modem_wait_t wait, const int command)
{
        modem_result_t result;
        uint16_t crc;
        uint32_t bytes = 0;
        modem_data_header_t *data_hdr;
        uint8_t packet[8];

        for (int i = 0; i < vec_size; i++) {
                bytes += vec[i].size;
        }

        data_hdr = (modem_data_header_t *)packet;
        data_hdr->prelude = BT_PACKET_PRELUDE;
        data_hdr->command = command;
        modem_set_uint32(data_hdr->length, bytes);
        crc = crc16(&packet[1], 5);

        serial_flush(&modem_port);
        serial_hold(&modem_port);
        serial_write(&modem_port, (char *)packet, 6);

        // Allow for zero length packets to ping the BT servers
        if (bytes) {
                for (int i = 0; i < vec_size; i++) {
                        serial_write(&modem_port, (char *)vec[i].data, vec[i].size);
                        crc = crc16_with_seed(vec[i].data, vec[i].size, crc);

                        if (modem_log)
                                modem_log->bytes_sent += vec[i].size;
                }
        }

        packet[0] = crc >> 8;
        packet[1] = crc & 0xff;
        packet[2] = BT_PACKET_TRAILER;
        serial_write(&modem_port, (char *)packet, 3);

        serial_release(&modem_port);

        result =  modem_receive_ack_response("send", command);

        if (result != MODEM_SUCCESS)
                return result;

        if (wait == MODEM_WAIT_TEST)
                return MODEM_SUCCESS;

        return modem_xmit_wait_test(wait, command == MODEM_CMD_SEND_TEST);
}

/**
    @brief  send a test packet to the modem (does not wait for packet transmission start and/or finish unless modem_send_data)
    @param[in]  data    packet to send
    @param[in]  bytes   packet size
    @return
            - MODEM_SUCCESS packet has been queued to modem
            - other errors are possible
    @note   the SEND_TEST command is used for factory provisioning. It is exactly the same as SEND_DATA expect the server end point is different and the modem initializes the network keys. The command is not intended to be used in the field expect for when the bodytrace firmware is updated or other special deployment reasons.
*/

modem_result_t
modem_send_test(void *data, const uint32_t bytes)
{
        uint16_t crc;
        modem_data_header_t *data_hdr;
        uint8_t packet[8];

        serial_flush(&modem_port);
        serial_hold(&modem_port);

        data_hdr = (modem_data_header_t *)packet;
        data_hdr->prelude = BT_PACKET_PRELUDE;
        data_hdr->command = MODEM_CMD_SEND_TEST;
        modem_set_uint32(data_hdr->length, bytes);
        crc = crc16(&packet[1], 5);

        serial_write(&modem_port, (char *)packet, 6);
        serial_write(&modem_port, data, bytes);
        crc = crc16_with_seed(data, bytes, crc);

        packet[0] = crc >> 8;
        packet[1] = crc & 0xff;
        packet[2] = BT_PACKET_TRAILER;
        serial_write(&modem_port, (char *)packet, 3);

        serial_release(&modem_port);

        return modem_receive_ack_response("test", MODEM_CMD_SEND_TEST);
}

/**
    @brief  poll the modem for the current status
    @param[out] status  modem status retrieved
    @return
            - MODEM_SUCCESS the modem status was successful gotten
            - MODEM_ERR_TIMEOUT the modem did not respond
            - other errors are possible
*/

modem_result_t
modem_get_status(modem_status_t *status)
{
        modem_result_t result;
        uint8_t packet[15];
        uint8_t command[] = { MODEM_CMD_MODEM_STATUS };

        for (int retries = 0; retries < 3; retries++) {
                modem_send_packet(command, 1);
                result = modem_read(packet, sizeof(packet));

                // Timeout, retry the command
                if (result == MODEM_ERR_TIMEOUT)
                        continue;

                if (result != MODEM_SUCCESS) {
                        EVENT_LOG1(EVT_BT_STATUS_ERR, "read error", "status", EVT_8BIT, result);
                        return result;
                }

                if (modem_crc_okay("status", packet, sizeof(packet)) == false)
                        return MODEM_ERR_CRC;


                if (packet[1] != MODEM_CMD_MODEM_STATUS)
                        return MODEM_ERR_MISMATCH;


                status->state           = packet[2];
                status->voltage         = (packet[3] << 8) | packet[4];
                // packet[5] is battery percentage, ignored.
                status->adc             = (packet[6] << 8) | packet[7];
                status->rssi            = packet[8];
                status->signal_strength = packet[9];
                status->provisioned     = packet[10];
                status->temperature     = packet[11];

                return MODEM_SUCCESS;
        }

        EVENT_LOG(EVT_BT_READ_TIMEOUT, "read timeout");
        return MODEM_ERR_TIMEOUT;
}

/**
    @brief  compute the IMEI luhn byte
    @param[in]  a 64-bit number representing a IMEI
    @return computed lunh byte
*/

static uint8_t
modem_luhn(uint64_t number)
{
        uint32_t sum = 0, d;
        uint8_t odd = 1;

        while (number > 0) {
                d = number % 10;
                if(odd) {
                        d *= 2;
                        if(d > 9) d -= 9;
                }
                sum += d;

                // Pop last digit
                number /= 10;

                // Flip odd digit flag
                odd = !odd;
        }

        return (10 - sum % 10) % 10;
}

/**
    @brief  retrieve the modem information (IMEI, firmware version, etc)
    @param[in]  info    modem information
    @return
            - MODEM_SUCCESS the modem information was retrieved successfully
            - MODEM_ERR_TIMEOUT the modem did not respond
            - other errors are possible
*/

modem_result_t
modem_get_info(modem_info_t *info)
{
        modem_result_t result;
        uint8_t cmd[] = { MODEM_CMD_MODEM_INFO };
        uint8_t packet[15];

        modem_send_packet(cmd, 1);
        result = modem_read(packet, sizeof(packet));

        if (result != MODEM_SUCCESS)
                return result;

        if (modem_crc_okay("info", packet, sizeof(packet)) == false)
                return MODEM_ERR_CRC;

        if (packet[1] != MODEM_CMD_MODEM_INFO)
                return MODEM_ERR_MISMATCH;

        info->major = packet[2];
        info->minor = packet[3];
        info->imei = modem_get_64(&packet[4]);
        info->imei = info->imei * 10 + modem_luhn(info->imei);

        return MODEM_SUCCESS;
}

/**
    @brief  ping the modem (are you alive?)
    @return
            - MODEM_SUCCESS the modem responded to the ping command
            - MODEM_ERR_TIMEOUT the modem did not respond
            - other errors are possible
*/

modem_result_t
modem_ping()
{
        uint8_t command[] = { MODEM_CMD_PING };

        modem_send_packet(command, 1);
        return modem_receive_ack_response("ping", MODEM_CMD_PING);
}

/**
    @brief      read a message chunk from the modem
    @param[out] buffer  area to store the read message chunk
    @param[in]  offset  the byte offset for the message chunk to retrieve
    @param[in]  length  max bytes allowed to read
    @param[out] bytes   how many bytes were actually read
    @param[out] remaining the remaining byte count left for the message
    @return
                - MODEM_SUCCESS the message chunk was succesfully read
                - MODEM_ERR_TOO_BIG too many bytes were returned (should never happen)
                - MODEM_ERR_TIMEOUT the modem failed to respond
                - other errors are possible
*/

modem_result_t
modem_read_chunk(void *buffer, const uint32_t offset, const uint32_t length, uint32_t *bytes, uint32_t *remaining)
{
        modem_result_t result;
        modem_read_chunk_header_t header;
        modem_read_chunk_response_t response;
        modem_footer_t footer;
        uint16_t crc, response_crc;

        header.command = MODEM_CMD_READ_CHUNK;
        modem_set_uint32(header.offset, offset);                   // account for the byte already read
        modem_set_uint32(header.bytes, length);
        modem_send_packet(&header, sizeof(header));

        result = modem_read(&response, sizeof(response));                    // read the header

        if (result != MODEM_SUCCESS) {
                EVENT_LOG1(EVT_BT_READ_CHUNK_ERR, "read message", "result", EVT_8BIT, result);
                return result;
        }

        *bytes = modem_get_uint32(response.bytes);
        *remaining = modem_get_uint32(response.remaining);

        if (*bytes > MODEM_CHUNK_MAX) {
                EVENT_LOG1(EVT_BT_READ_CHUNK_TOO_BIG, "read message too big", "bytes", EVT_32BIT, *bytes);
                serial_flush(&modem_port);
                return MODEM_ERR_TOO_BIG;
        }

        result = modem_read(buffer, *bytes);                    // read the data

        if (result != MODEM_SUCCESS) {
                EVENT_LOG1(EVT_BT_READ_CHUNK_ERR, "read chunk err", "result", EVT_8BIT, result);
                serial_flush(&modem_port);
                return result;
        }

        result = modem_read(&footer, sizeof(footer));
        if (result != MODEM_SUCCESS) {
                EVENT_LOG1(EVT_BT_READ_CHUNK_ERR, "read chunk err", "result", EVT_8BIT, result);
                serial_flush(&modem_port);
                return result;
        }

        crc = crc16(&response.command, sizeof(response)-1);
        crc = crc16_with_seed(buffer, *bytes, crc);

        response_crc = modem_get_uint16(footer.crc);
        if (crc != response_crc) {
                modem_report_crc_err("read-chunk", response_crc, crc);
                return MODEM_ERR_CRC;
        }

        if (modem_log)
                modem_log->bytes_received += *bytes;

        return MODEM_SUCCESS;
}

/**
    @brief  retrieve how many incoming messages are available
    @param[out] count   how many messages are available
    @return
            - MODEM_SUCCESS the message count was succesfully read
            - MODEM_ERR_TIMEOUT the modem failed to respond
            - other errors are possible
*/

modem_result_t
modem_get_message_count(uint32_t *count)
{
        modem_result_t result;
        modem_message_status_t packet;
        uint8_t command[] = { MODEM_CMD_MESSAGE_STATUS };

        modem_send_packet(command, 1);
        result = modem_read(&packet, sizeof(packet));
        if (result != MODEM_SUCCESS)
                return result;

        if (modem_crc_okay("message count", (uint8_t *) &packet, sizeof(packet)) == false)
                return MODEM_ERR_CRC;

        if (packet.command != MODEM_CMD_MESSAGE_STATUS)
                return MODEM_ERR_MISMATCH;

        *count = modem_get_uint16(packet.count);

        return MODEM_SUCCESS;
}

/**
    @brief  delete the current message on the modem
    @return
            - MODEM_SUCCESS message was successfully deleted
            - other errors are possible
*/

modem_result_t
modem_delete_message()
{
        uint8_t command[] = { MODEM_CMD_MSG_DELETE };

        modem_send_packet(command, 1);
        return modem_receive_ack_response("message delete", MODEM_CMD_MSG_DELETE);
}

/**
    @brief  place the modem in firmware update mode
    @note   This turns on the PKEY (programmer key) pin to the modem which puts the modem into
            a firmware update mode. The secondary serial lines on the modem (available on the board and not accessible to the MCU currently) are then used to send a new firmware
            using a Bodytrace supplied utility. This was developed for the Myanmar Summer 2015 deployment
*/

void
modem_firmware_update_mode()
{
        printf("powering up modem\n");

        modem_init();
        modem_power_on();
        modem_pkey_on();
        printf("modem ready for firmware update - power cycle board went finished.\n");

#ifdef HAVE_LED
        for (;;) {
                led_toggle(LED_BLUE);
                osDelay(500);
        }
#endif
}

#ifdef HAVE_FACTORY_TEST

ft_result_t
modem_factory_test()
{
        return modem_factory_test_and_report(FT_SUCCESS, 0);
}

/**
    @brief  Test the modem and send the overall factory test results to the test provisioned server
    @param[in]  test_result Either a specific test failure or over all test success
    @param[in]  deploy_mode the requested deployment mode sent by the testbox
    @return
            - FT_MODEM_UNRESPONSIVE the modem failed to respond to a ping or get info request
            - FT_SEND_FAIL the modem failed to transmit the test result packet (typically misconnected antenna)
            - FT_MODEM_SEND_TIMEOUT the modem timed out trying to send a packet (misconnected antenna or weak signal)
            - FT_SUCCESS the modem passed all the tests and the test result packet was successfully transmitted.
    @note   The routine is the last test routine in the factory test series. All tests should have passed before this.
            The SEND_TEST command will be used to the upload the results along with a confirmation on the deployment mode.
            (NOTE: SEND_TEST server end point is usually a Bodytrace's server and not the clients.)
            The code can be compiled with the flag FT_SEND_DATA to use SEND_DATA instead - this is meant for doing a factory test outside the factory where the modem has already been provisioned but the board has not.
*/

ft_result_t
modem_factory_test_and_report(ft_result_t test_result, uint8_t deploy_mode)
{
        modem_result_t result;
        modem_info_t info;
        ft_result_msg_t *msg = (ft_result_msg_t *)the_buffer.modem;

        modem_init();

        ft_update("modem: power up");
        modem_power_on();
        ft_update("modem: ping");

        if (modem_ping() != MODEM_SUCCESS) {
                ft_update("modem: ping FAIL");
                modem_power_off();
                return FT_MODEM_UNREPONSIVE;
        }

        ft_update("modem: get info");
        if (modem_get_info(&info) != MODEM_SUCCESS) {
                ft_update("modem: get info FAIL");
                modem_power_off();
                return FT_MODEM_UNREPONSIVE;
        }

        board_info_set_imei(info.imei);

        ft_update("modem: send test data");
        msg->type           = PDS_TYPE_FACTORY_RESULT_TEST;
        msg->version        = PDS_TYPE_FACTORY_RESULT_TEST_VERSION;
        msg->test_version   = FT_VERSION;
        msg->mode           = deploy_mode;
        uid_set_me(msg->board_uid);
        set_uint16(msg->results_length, ft_results_length);
        memcpy(msg->results, compress_buffer, ft_results_length);

        result = modem_send_test_data((uint8_t *)msg, sizeof(*msg)+ft_results_length, MODEM_WAIT);

        if (result != MODEM_SUCCESS) {
                ft_update("modem: send err=%d FAIL", result);
                modem_power_off();
                return FT_MODEM_SEND_FAIL;
        }

        modem_power_off();

        ft_update("modem: test pass");
        return FT_SUCCESS;
}
#endif

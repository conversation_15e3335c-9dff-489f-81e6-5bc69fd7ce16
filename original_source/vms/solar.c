/** @{

    @ingroup    vms
    @file

    @brief  Solar management
*/

#include "pelagic.h"
#include "power.h"
#include "analogin_api.h"

enum {
        HAVE_SUN    = 11000
};

analogin_t solar_an_pin;

/**
    @brief  initialize solar pin
*/

void
solar_init()
{
        analogin_init(&solar_an_pin, PWR_SOL);
}

/**
    @brief  check to see if solar panel is receiving sun
    @param[in]  wait_time   seconds to wait until sun appears
    @return true if sun appeared
*/

bool
solar_present(int wait_time)
{
#ifdef NO_SOLAR
        return true;
#else
        for (;;) {
                if (solar_read() > HAVE_SUN)
                        return true;

                if (wait_time-- <= 0)
                        return false;

                osDelay(1000);
        }
#endif
}

/**
    @brief  read the solar pin - take three readings and take the highest
    @return the analog value for the solar pin
*/

uint16_t
solar_read()
{
#if defined(NO_SOLAR)
        return 20000;
#else
        uint16_t highest = 0, value;

        for (int loop = 0; loop < 3; loop++) {
                value = analogin_read_u16(&solar_an_pin);
                if (value > highest)
                        highest = value;

                if (loop != 2)
                        osDelay(100);
        }

        return highest;
#endif
}

/** @} */

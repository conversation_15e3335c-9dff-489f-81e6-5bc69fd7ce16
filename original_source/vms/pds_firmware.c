/** @{

    @ingroup    vms
    @file

    @brief      send firmware update messages
*/

#include "pelagic.h"
#include "modem.h"
#include "pds.h"
#include "firmware.h"
#include "board_info.h"

/**
    @brief  construct and send a firmware received acknowledge message
    @param[in]  device  the device type (DEVICE_*) for the received firmware
    @param[in]  version the device hardware version (05 = v.05, 10 = V1.0, etc)
    @param[in]  buildstamp the firmware buildstamp
    @param[in]  uid        the MCU unique serial identifier
*/

void
pds_firmware_acknowledge(firmware_header_t *fwh, uint8_t *uid)
{
        pds_firmware_received_msg_v2_t *ack = (pds_firmware_received_msg_v2_t *)the_buffer.modem;

        // Update looks okay - send an ack back to the server
        ack->type           = PDS_TYPE_FIRMWARE_RECEIVED;
        ack->version        = PDS_TYPE_FIRMWARE_RECEIVED_VERSION;
        ack->device_type    = fwh->device_hardware;
        ack->device_version = fwh->device_version;
        set_uint32(ack->buildstamp, fwh->buildstamp);
        set_uint16(ack->build_crc16, fwh->crc16);
        uid_copy(ack->board_uid, uid);

        modem_send_data((uint8_t *)ack, sizeof(pds_firmware_received_msg_v2_t), MODEM_WAIT);
}

/**
    @brief  construct and send a firmware update message
*/

void
pds_firmware_updated()
{
        int result;
        pds_firmware_updated_msg_v2_t *ack = (pds_firmware_updated_msg_v2_t *)the_buffer.modem;

        // Send an acknowledgement back to the server
        ack->type           = PDS_TYPE_FIRMWARE_UPDATED;
        ack->version        = PDS_TYPE_FIRMWARE_UPDATED_VERSION;
        ack->device_type    = DEVICE_VMS;
        ack->device_version = DEVICE_VERSION;
        set_uint32(ack->buildstamp, build_timestamp);
        uid_set_me(ack->board_uid);
        set_uint16(ack->build_crc16, board_info_read_build_crc());
        ack->build_tag_length = strlen(build_tag);
        memcpy(ack->build_tag, build_tag, ack->build_tag_length);

        result = modem_send_data((uint8_t *)ack, sizeof(pds_firmware_updated_msg_v2_t)+ack->build_tag_length, MODEM_WAIT);
        if (result == MODEM_SUCCESS) {
                firmware_updated = 0;
                EVENT_LOG(EVT_FIRMWARE_UPGRADE_MSG_SENT, "firmware upgrade message sent");
                return;
        }

        EVENT_LOG1(EVT_FIRMWARE_UPGRADE_MSG_FAIL, "firmware upgrade message failure", "result", EVT_8BIT, result);
}

/** @} */

#ifndef __HELM_H__
#define __HELM_H__

typedef enum {
        HELM_ACTION_SUN_POS         = 0x0001,     // Calculate sun position
        HELM_ACTION_POWER_MANAGE    = 0x0002,     // manage power
        HELM_ACTION_POWER_CYCLE     = 0x0004,     // Power cycle some components
        HELM_ACTION_TEST_RADIO      = 0x0008,     // Send a test packet out
        HELM_ACTION_BOARD_LOG       = 0x0010,
        HELM_ACTION_NAP             = 0x0020,
        HELM_ACTION_RADIO_CALIBRATE = 0x0040,
} helm_action_t;

extern volatile helm_action_t helm_actions;
extern volatile bool helm_running;
extern bool helm_watch;

#endif

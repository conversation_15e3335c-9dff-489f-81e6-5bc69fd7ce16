/** @{

    @ingroup    vms
    @file
    @brief      Power Average
*/

#include "pelagic-types.h"

#if 0
enum {
        BATTERY_LOG_SIZE    = (12*24),
};

uint16_t battery_out = 0, battery_in = 0;
uint16_t battery_count = 0;
uint16_t battery_log[BATTERY_LOG_SIZE];

uint16_t battery_avg;
#endif

/**
    @brief  compute the power average over a 24 hour period
    @param[in]  battery battery level to average in
*/

void
power_compute_average(uint16_t battery)
{
#if 0
        uint32_t avg = 0;

        battery_log[battery_in++] = battery;
        if (battery_in == BATTERY_LOG_SIZE)
                battery_in = 0;

        if (battery_count >= BATTERY_LOG_SIZE)
                battery_out = (battery_out + 1) % BATTERY_LOG_SIZE;
        else
                battery_count++;

        for (int out = battery_out, i = 0; i < battery_count; i++) {
                avg += battery_log[out];
                out++;
                if (out == BATTERY_LOG_SIZE)
                        out = 0;
        }

        battery_avg = avg / battery_count;
#endif
}

/**@} */

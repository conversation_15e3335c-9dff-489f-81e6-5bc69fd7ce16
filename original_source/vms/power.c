/**@{
    @ingroup    vms
    @file
    @brief   Power Management
*/

#include "pelagic.h"
#include "power.h"
#include "analogin_api.h"
#include "gpio_api.h"
#include "gpio_irq_api.h"
#include "signals.h"
#include "temp.h"
#include "nap.h"
#include "pinmap.h"
#include "factory_test.h"
#include "system_file.h"
#include "mcu_sleep.h"

volatile power_state_t board_power_state;

/**
    @brief  read the current battery level and signal threads appropriately
*/

void
power_management()
{
        uint16_t battery;

        battery = battery_read();

        if (battery <= BATTERY_VOLTAGE_CRITICAL) {
                board_power_state = POWER_STATE_CRITICAL;
                EVENT_LOG1(EVT_POWER_CRITICAL, "critical", "battery", EVT_16BIT, battery);
                osSignalSet(reboot_tid, REBOOT_SIGNAL_CRITICAL);
                return;
        }

        power_compute_average(battery);

        if (battery >= BATTERY_VOLTAGE_EXCELLENT) {
                if (board_power_state == POWER_STATE_EXCELLENT)
                        return;

                board_power_state = POWER_STATE_EXCELLENT;
                EVENT_LOG1(EVT_POWER_EXCELLENT, "excellent", "battery", EVT_16BIT, battery);
        } else if (battery >= BATTERY_VOLTAGE_GOOD_ENOUGH) {
                if (board_power_state == POWER_STATE_NORMAL)
                        return;

                board_power_state = POWER_STATE_NORMAL;
                EVENT_LOG1(EVT_POWER_NORMAL, "normal", "battery", EVT_16BIT, battery);
        } else if (battery <= BATTERY_VOLTAGE_LOW) {
                if (board_power_state == POWER_STATE_LOW)
                        return;

                board_power_state = POWER_STATE_LOW;
                EVENT_LOG1(EVT_POWER_LOW, "low", "battery", EVT_16BIT, battery);
        } else {
                return;
        }

        osSignalSet(gps_tid, POWER_SIGNAL_CHANGE);

#ifdef HAVE_RADIO
        if (bnet_tid)
                osSignalSet(bnet_tid, POWER_SIGNAL_CHANGE);
#endif
}

/**
    @note   When the board wakes from sleep that was caused by a
            critical power level, make sure its okay to proceed, otherwise
            go back to sleep
*/

void
board_check_power()
{
        if (SYS_REG_FILE->reboot_reason != REBOOT_CRITICAL)
                return;

        if (battery_read() >= BATTERY_VOLTAGE_LOW)
                return;

        mcu_sleep(CRITICAL_POWER_SLEEP);
}

/** @} */

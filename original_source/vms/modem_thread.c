/** @{

    @ingroup vms
    @file
    @brief  Modem Thread - manage modem communications
*/

#include "pelagic.h"
#include "modem.h"
#include "signals.h"
#include "firmware.h"
#include "led.h"
#include "power.h"
#include "sun.h"
#include "alarm.h"
#include "pds.h"
#include "modem_thread.h"
#include "board_info.h"
#include "modem_log.h"
#include "system_file.h"

enum {
        DEMO_MODE_SLEEP = 1 * MINUTE,

        NORMAL_TIME = 6 * HOUR,
        DOCK_TIME = 1 * HOUR,
        TARDY_TIME = 12 * HOUR, /* do not let the modem go more than this long
                                   without making contact */
        SUPER_TARDY_TIME = 24 * HOUR,

        LOW_POWER_TIME = 6 * HOUR,

        GOFAST_INTERVAL = 1 * HOUR,
        GOFAST_VOLTS = BATTERY_MODEM_VOLTAGE_GOFAST,    // see power.h

        PREPARK_MIN_INTERVAL = 1 * HOUR,

        NORMAL_SLEEP = 15 * MINUTE,
        SUCCESS_SLEEP = GOFAST_INTERVAL, // the earliest we'd upload again
        FAIL_SLEEP = 4 * HOUR,
        DOCK_FAIL1_SLEEP = 5 * MINUTE,
        DOCK_FAIL2_SLEEP = 15 * MINUTE,
};

typedef enum {
        STATE_NORMAL = 0,
        STATE_ENTER_DOCK,       // DEPRECATED
        STATE_TARDY,
        STATE_LOW_POWER,
        STATE_FORCED,
        STATE_SKIP_DOCK,        // DEPRECATED
        STATE_SKIP_CONTACT,
        STATE_SKIP_LOW_POWER,
        STATE_SKIP_CRIT_POWER,
        STATE_SKIP_NO_TIME,
        STATE_GOFAST,
        STATE_PREPARK,
} modem_thread_state_t;

static char *state_names[] = {
        "normal",
        "enter-dock",           // DEPRECATED
        "tardy",
        "low-power",
        "forced",
        "skip-dock",            // DEPRECATED
        "skip-contact",
        "skip-low-power",
        "skip-crit-power",
        "skip-no-time",
        "gofast",
        "prepark"
};

void modem_init();
void modem_notify_of_update();
bool modem_check_for_network();
void modem_shedule_next_run(bool success);

static void modem_defaults_init();

modem_status_t gsm_status;

volatile bool modem_thread_running = false, modem_thread_hold = false;
volatile uint32_t modem_sleep, modem_last_contact, modem_last_power_up;

alarm_t modem_alarm;

void modem_send_simple_message();

modem_info_t modem_info;

modem_thread_state_t modem_state;

#ifdef HAVE_LED
void demo_bt_blink();
osTimerDef(demo_bt_timer, demo_bt_blink);
osTimerId demo_bt_timer_id;

/**
    @brief  toggle the green LED at regular intervals (for debugging)
*/
void
demo_bt_blink()
{
        led_toggle(LED_GREEN);
}
#endif

/**
    @brief  retrieve the modem and store IMEI if hasn't been already
*/
void
modem_thread_retrieve_info()
{
        if (modem_info.major)
                return;

        modem_result_t info_result = modem_get_info(&modem_info);

        if (info_result == MODEM_SUCCESS) {
                EVENT_LOG2(EVT_MODEM_VERSION, "version",
                           "major", EVT_8BIT, modem_info.major,
                           "minor", EVT_8BIT, modem_info.minor);

                if (board_info_have_imei == false)
                        board_info_set_imei(modem_info.imei);
        } else {
                EVENT_LOG1(EVT_MODEM_VERSION_ERROR, "modem version error", "result", EVT_8BIT, info_result);
        }
}

#define DEFAULT_UINT32(var, dflt) if (var == 0 || var == 0xffffffff) var = dflt;

/**
    @brief  Ensure the modem settings are initialized
    @note   You might think this is an ugly pattern... been burned by adding new
            settings that were not properly initialized through provisioning,
            fw updates, whatever. So just swallow it.
*/
static void
modem_defaults_init()
{
        // not bothering with deprecated dock things
        DEFAULT_UINT32(settings.modem_gofast_interval, GOFAST_INTERVAL);
        DEFAULT_UINT32(settings.modem.normal_time, NORMAL_TIME);
        DEFAULT_UINT32(settings.modem.tardy_time, TARDY_TIME);
        DEFAULT_UINT32(settings.modem.low_power_time, LOW_POWER_TIME);
        DEFAULT_UINT32(settings.modem.normal_sleep, NORMAL_SLEEP);
        DEFAULT_UINT32(settings.modem.success_sleep, SUCCESS_SLEEP);
        DEFAULT_UINT32(settings.modem.fail_sleep, FAIL_SLEEP);
        DEFAULT_UINT32(settings.modem_gofast_volts, GOFAST_VOLTS);
}

/**
    @brief  Modem Thread loop - responsible for talking with the outside world.
    @note   The thread wakes up periodically to upload various logs and download settings and firmware updates.
            There are several wake up events:
            - The alarm has gone off (typically NORMAL_TIME seconds)
            - The boat has entered into a geo-fenced location.
            - A force connection was initiated by the console (for debugging)

*/
void
modem_thread(void const *arg)
{
        osEvent result;
        bool success, run_now;
#ifdef HAVE_LED
        bool led_blinking = false;
#endif

        // belt and suspenders
        modem_defaults_init();

        modem_init();
        EVENT_LOG(EVT_MODEM_INIT, "init");

        if (demo_mode) {
#ifdef HAVE_LED
                demo_bt_timer_id = osTimerCreate(osTimer(demo_bt_timer), osTimerPeriodic, NULL);
#endif
                modem_send_simple_message();
        }

        result.value.signals  = 0;
        modem_alarm.periodic  = 0;

        for (;;) {
                modem_thread_running = false;

                if (modem_sleep) {
                        EVENT_LOG1(EVT_MODEM_SLEEP, "modem sleep", "seconds", EVT_16BIT, modem_sleep);
                        alarm_start(&modem_alarm, modem_sleep);
                        result = osSignalWait(0, osWaitForever);
                        alarm_cancel(&modem_alarm);
                }

                // halt thread if reboot is about to happen.
                reboot_check_pending();

                // hold off - if the console is driving things atm
                if (modem_thread_hold) {
                        EVENT_LOG(EVT_MODEM_HOLD, "holding");
                        while (modem_thread_hold)
                                osDelay(1000);
                        EVENT_LOG(EVT_MODEM_RELEASED, "released");
                }

                modem_thread_running = true;

                log_stats.wakeups++;

                modem_log_new_entry();
                modem_log->timestamp = rtc_read();

                run_now = modem_should_run(result.value.signals);
                modem_log->run_state = modem_state;

                EVENT_LOG1(EVT_MODEM_RUN, "modem run", "state", EVT_STRCONST, state_names[modem_state]);

                if (run_now == false) {
                        if (result.value.signals == MODEM_SIGNAL_PARKING)
                                break;

                        modem_log->sleep = modem_sleep;
                        modem_log = NULL;
                        continue;
                }

#ifdef HAVE_LED
                if (demo_mode) {
                        osTimerStart(demo_bt_timer_id, 500);
                        led_blinking = true;
                }
#endif

                success = modem_attempt_upload();
                if (success)
                        modem_last_contact = rtc_read();

                // Were we sent here because we're parking?
                if (result.value.signals == MODEM_SIGNAL_PARKING)
                        break;

#ifdef HAVE_LED
                if (led_blinking) {
                        osTimerStop(demo_bt_timer_id);
                        led_off(LED_GREEN);
                        led_blinking = false;
                }
#endif

                modem_thread_running = false;

                modem_shedule_next_run(success);

                if (demo_mode)
                        modem_sleep = DEMO_MODE_SLEEP;

                modem_log->sleep = modem_sleep;
                modem_log = NULL;
        }

        // above is an infinite loop, the only out should be for parking
        modem_thread_running = false;
        osSignalSet(reboot_tid, REBOOT_SIGNAL_PARKED);
}

/**
    @brief  determine if its time to start walking with the modem
    @note   The routine will determine if the modem should run in the following order:
            - The connection is being forced (via the console for debugging)
            - This attempt will be aborted if no time has been acquired yet
            - If the last successful contact was outside NORMAL_TIME.
            - The battery level needs to be within reasonable limits
            - An exception is made if the battery within an allowed critical low power level
              and contact has not been made for some time.
*/
bool
modem_should_run(uint16_t signals)
{
        uint16_t battery;
        uint32_t wakeup_time;
        uint32_t start = 0;

        EVENT_LOG2(EVT_MODEM_DEBUG, "modem_should_run",
                   "signals", EVT_16BIT, signals,
                   "MODEM_SIGNAL_FORCE", EVT_16BIT, MODEM_SIGNAL_FORCE);

        if ((signals & MODEM_SIGNAL_FORCE) != 0) {
                EVENT_LOG(EVT_MODEM_DEBUG, "FORCING");
                modem_state = STATE_FORCED;
                return true;
        }

        if (time_acquired == false) {
                modem_state = STATE_SKIP_NO_TIME;
                modem_sleep = settings.modem.normal_sleep;
                return false;
        }

        wakeup_time = rtc_read();
        battery = battery_read();

        /*
         * Set a start time for later math using either the
         * time the modem last made contact or the parked time
         * if we're waking from a parked nap.
         */
        if (modem_last_contact) {
                EVENT_LOG1(EVT_MODEM_DEBUG, "should_run",
                           "modem_last_contact", EVT_32BIT, modem_last_contact);
                start = modem_last_contact;
        } else if (SYS_REG_FILE->reboot_reason == REBOOT_ACCEL_NAP) {
                start = settings.parking.parked_time;
                EVENT_LOG(EVT_MODEM_DEBUG, "should_run: reboot_reason: 7");
        }

        // Feeling chipper? Upload if gofast_interval has elapsed
        if (battery >= settings.modem_gofast_volts && wakeup_time >= (start + settings.modem_gofast_interval)) {
                EVENT_LOG(EVT_MODEM_DEBUG, "battery is good, upload");
                modem_state = STATE_GOFAST;
                return true;
        }

        // If we have the battery
        // and we're about to park
        // and last contact is more than one hour ago,
        // then upload now
        if (battery >= BATTERY_MODEM_VOLTAGE_PREPARK
            && (signals & MODEM_SIGNAL_PARKING)
            && (!modem_last_contact
                || (wakeup_time - modem_last_contact) > PREPARK_MIN_INTERVAL)) {
                modem_state = STATE_PREPARK;
                return true;
        }

        /*
         * Using the previously calculated start time, figure out if our
         * normal upload interval has elapsed. If not, sleep the modem.
         *
         * The goal is to _not_ upload before normal_time is reached.
         */
        if ((start &&
             (start + settings.modem.normal_time) > wakeup_time)) {
                modem_sleep = settings.modem.normal_sleep;
                modem_state = STATE_SKIP_CONTACT;
                EVENT_LOG3(EVT_MODEM_DEBUG, "should_run",
                           "start", EVT_32BIT, start,
                           "normal_time", EVT_32BIT, settings.modem.normal_time,
                           "wakeup_time", EVT_32BIT, wakeup_time);
                return false;
        }
        EVENT_LOG(EVT_MODEM_DEBUG, "gonna return normal barring battery problems");
        modem_state = STATE_NORMAL;

        // Check the battery to make sure everything is okay.
        if (battery > BATTERY_MODEM_VOLTAGE_CRITICAL && battery <= BATTERY_MODEM_VOLTAGE_LOW) {
                modem_state = STATE_LOW_POWER;

                // Only skip if
                // - its been less than LOW_POWER_TIME since the last modem power up
                // OR
                // - its been less than TARDY_TIME since the last successful contact

                if (((modem_last_contact && (modem_last_contact + settings.modem.tardy_time) > wakeup_time))
                    || (modem_last_power_up
                        && (modem_last_power_up + settings.modem.low_power_time) > wakeup_time) ) {
                        modem_sleep = settings.modem.normal_sleep;
                        modem_state = STATE_SKIP_LOW_POWER;
                        return false;
                }
        } else if (battery <= BATTERY_MODEM_VOLTAGE_CRITICAL) {
                modem_sleep = settings.modem.normal_sleep;
                modem_state = STATE_SKIP_CRIT_POWER;
                return false;
        }

        return true;
}

/**
    @brief  determine when the thread should wake up next
    @note   usually this will be NORMAL_TIME if a success contact happened.
            otherwise a failed contact will wake up in FAIL_SLEEP seconds.
*/
void
modem_shedule_next_run(bool success)
{
        // didn't fail, or just skipping this round
        if (success) {
                modem_sleep = settings.modem.success_sleep;
                modem_log->success = true;
                return;
        }

        modem_sleep = settings.modem.fail_sleep;
}

typedef struct {
        flash_partition_t   *partition;
        pds_type_t          type;
        uint8_t             version;
        void                (*reset_log)(void);
        uint32_t            *success;
        uint32_t            *failure;
        uint32_t            *log_time;
} upload_partition_t;

upload_partition_t upload_partitions[] = {
        {
                .partition = &boatlog_partition,
                .type = PDS_TYPE_BOAT_LOG,
                .version = PDS_BOAT_LOG_VERSION,
                .reset_log = boat_log_reset,
                .success = &log_stats.boat_log_transmits,
                .log_time = &log_stats.last_boat_log_time,
                .failure = &log_stats.boat_log_failures
        },

        {
                .partition = &event_partition,
                .type = PDS_TYPE_EVENT_LOG,
                .version = PDS_EVENT_LOG_VERSION,
                .reset_log = event_log_reset,
                .success = &log_stats.event_transmits,
                .log_time = &log_stats.last_event_time,
                .failure = &log_stats.event_failures
        }
};

/**
    @brief  check to see if the modem can connect by sending an empty message
*/
bool
modem_check_for_network()
{
        modem_result_t result;

        gsm_status.signal_strength = 0;
        gsm_status.rssi = 0;

        // send a ping message to see if modem can connect to
        // the servers
        result = modem_send_data(NULL, 0, MODEM_WAIT);

        if (modem_get_status(&gsm_status) == MODEM_SUCCESS) {
                if (gps_acquired_fix)
                        cell_signal_record(gps_lat, gps_lon,
                                           gsm_status.signal_strength, gsm_status.rssi);
        }

        return (result == MODEM_SUCCESS);
}


/**
    @brief  Attempt to upload once
*/
bool
modem_attempt_upload()
{
        bool have_network;
        int failures = 0;
        flash_partition_t *part;
        upload_partition_t *upload = upload_partitions;

        // Accounting
        modem_last_power_up = clock_read();

        modem_power_on();
        have_network = modem_check_for_network();
        modem_log->network_acquire_secs = clock_read() - modem_last_power_up;

        EVENT_LOG1(EVT_MODEM_NETWORK, "network present", "network", EVT_BOOL, have_network);

        // Grab the modem info if that hasn't been done yet.
        modem_thread_retrieve_info();

        if (!have_network)
                goto finish;

        for (int idx = 0; idx < ARRAY_SIZE(upload_partitions); idx++, upload++) {
                part = upload->partition;

                if (part->bytes_stored == 0)
                        continue;

                if (pds_send_log(part, upload->type, upload->version, upload->reset_log)) {
                        EVENT_LOG1(EVT_MODEM_LOG_SUCCESS, "upload success", "log", EVT_STRCONST, part->name);
                        (*upload->success)++;
                        *upload->log_time = rtc_read();
                } else {
                        EVENT_LOG1(EVT_MODEM_LOG_FAIL, "upload failure", "log", EVT_STRCONST, part->name);
                        (*upload->failure)++;
                        failures++;
                        break;
                }
        }

        // We have booted into an upgraded FW image -- construct and send an ACK
        if (firmware_updated)
                pds_firmware_updated();

        // If a firmware update arrived, then this does not return...
        pds_check_messages();

finish:
        modem_power_off();
        // Accounting
        modem_log->power_on_secs = clock_read() - modem_last_power_up;
        EVENT_LOG1(EVT_MODEM_ACTIVE, "duration", "elapsed", EVT_16BIT, modem_log->power_on_secs);
        EVENT_LOG2(EVT_MODEM_BYTES, "byte counts",
                   "sent", EVT_32BIT, modem_log->bytes_sent,
                   "received", EVT_32BIT, modem_log->bytes_received);

        return !failures;
}


/**
    @brief  run a bandwidth test - for debugging
*/
void
modem_bandwidth_test()
{
        uint32_t start, time;
        modem_result_t result;
        uint8_t *data = the_buffer.modem;

        for (int i = 0; i < 1000; i++) {
                data[i] = 0xff;
        }

        printf("Starting bandwidth test\n");
        time = rtc_read();
        for (int i = 0; i < 100; i++) {
                start = rtc_read();
                printf("%d: ", i);
                result = modem_send_data(data, 1000, MODEM_WAIT_NONE);
                if (result != MODEM_SUCCESS) {
                        printf("failure result [%d]\n", result);
                        return;
                } else {
                        printf("success. %d seconds.\n", rtc_read() - start);
                }
        }

        printf("Waiting for final transmission\n");
        result = modem_xmit_wait(MODEM_WAIT);
        if (result == MODEM_SUCCESS)
                printf("success!\n");
        else
                printf("failure %d\n", result);
        printf("finished, total elapsed time %d\n", rtc_read() - time);
}

/**
    @brief  send a tiny message - for demo mode
*/
void
modem_send_simple_message()
{
        uint8_t *data = the_buffer.modem;
        modem_result_t result;

        data[0] = 0x40;

        for (int i = 1; i < 10; i++)
                data[i] = i;

#ifdef HAVE_LED
        osTimerStart(demo_bt_timer_id, 500);
#endif

        modem_power_on();
        result = modem_send_data(data, 11, MODEM_WAIT);
        modem_power_off();

#ifdef HAVE_LED
        osTimerStop(demo_bt_timer_id);
        led_off(LED_GREEN);

        for (int i = 0; i < 10; i++) {
                led_toggle(result == MODEM_SUCCESS ? LED_GREEN : LED_RED);
                osDelay(250);
        }
#else
        (void) result;
#endif
}

/**
    @brief  modem state in ascii for debugging
*/
char *
modem_run_state(uint8_t state)
{
        if (state < ARRAY_SIZE(state_names))
                return state_names[state];
        else
                return "-";
}

/**
    @brief  initialize the modem settings to their defaults
    @note   TODO: implementing modem settings
*/
void
modem_settings_init()
{
        settings.modem.normal_time        = NORMAL_TIME;
        settings.modem.dock_time          = DOCK_TIME;
        settings.modem.tardy_time         = TARDY_TIME;
        settings.modem.low_power_time     = LOW_POWER_TIME;
        settings.modem.normal_sleep       = NORMAL_SLEEP;
        settings.modem.success_sleep      = SUCCESS_SLEEP;
        settings.modem.dock_fail_sleep[0] = DOCK_FAIL1_SLEEP;
        settings.modem.dock_fail_sleep[1] = DOCK_FAIL2_SLEEP;
        settings.modem.fail_sleep         = FAIL_SLEEP;
        settings.modem_gofast_interval    = GOFAST_INTERVAL;
        settings.modem_gofast_volts       = GOFAST_VOLTS;
}

/**
  @fn int parking_setting_dump(char *buffer)
  @param[in] buffer  memory to print settings info
  @return bytes dumped
*/
int
modem_setting_dump(char *buffer)
{
        return sprintf(buffer,
                       "modem normal_time %d\n"
                       "modem tardy_time %d\n"
                       "modem low_power_time %d\n"
                       "modem normal_sleep %d\n"
                       "modem success_sleep %d\n"
                       "modem fail_sleep %d\n"
                       "modem gofast_interval %d\n"
                       "modem gofast_volts %d\n",
                       settings.modem.normal_time,
                       settings.modem.tardy_time,
                       settings.modem.low_power_time,
                       settings.modem.normal_sleep,
                       settings.modem.success_sleep,
                       settings.modem.fail_sleep,
                       settings.modem_gofast_interval,
                       settings.modem_gofast_volts);
}

bool
modem_setting(int ac, char **av)
{
        int t;

        if (ac < 2) {
                return false;
        }

        t = atoi(av[1]);
        if (t <= 0 || t > 7*DAY)
                return false;

        if (strcmp(av[0], "normal_time") == 0) {
                settings.modem.normal_time = t;
        } else if (strcmp(av[0], "tardy_time") == 0) {
                settings.modem.tardy_time = t;
        } else if (strcmp(av[0], "low_power_time") == 0) {
                settings.modem.low_power_time = t;
        } else if (strcmp(av[0], "normal_sleep") == 0) {
                settings.modem.normal_sleep = t;
        } else if (strcmp(av[0], "success_sleep") == 0) {
                settings.modem.success_sleep = t;
        } else if (strcmp(av[0], "fail_sleep") == 0) {
                settings.modem.fail_sleep = t;
        } else if (strcmp(av[0], "gofast_interval") == 0) {
                settings.modem_gofast_interval = t;
        } else if (strcmp(av[0], "gofast_volts") == 0) {
                settings.modem_gofast_volts = t;
        } else
                return false;

        return true;
}

/** @} */

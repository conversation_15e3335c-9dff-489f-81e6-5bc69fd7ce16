/**@{

    @ingroup vms
    @file
    @brief  Modem connection log (for debugging)
*/

#include "pelagic.h"
#include "alarm.h"
#include "modem.h"
#include "modem_log.h"
#include "modem_bodytrace.h"
#include "modem_thread.h"

uint8_t modem_log_in = 0, modem_log_out = 0, modem_log_count = 0;
modem_log_t *modem_log, modem_logs[MODEM_LOG_SIZE];

/**
    @brief  display the results of MODEM_LOG_SIZE connection (connect duration, bytes sent & received, etc)
*/

void
modem_log_display()
{
        modem_log_t *log;

        if (modem_log_count == 0) {
                printf("modem has not turned on yet.\n");
                return;
        }

        for (int i = 0; i < modem_log_count; i++) {
                log = &modem_logs[(modem_log_out + i) % MODEM_LOG_SIZE];

                datetime_display(log->timestamp);
                printf(" run %s state %s success %c power-on %d net(%c) acquire %d xmit-secs %d sent %d recv %d\n",
                       modem_run_state(log->run_state),
                       log->modem_state ?  modem_state_label(log->modem_state) : "ok",
                       log->success ? 'Y' : 'N',
                       log->power_on_secs,
                       log->have_network ? 'Y' : 'N',
                       log->network_acquire_secs,
                       log->transmit_secs,
                       log->bytes_sent,
                       log->bytes_received);

        }
}

/**
    @brief  create a new log entry and set modem_log
*/

void
modem_log_new_entry()
{
        modem_log = &modem_logs[modem_log_in];
        modem_log_in = (modem_log_in + 1) % MODEM_LOG_SIZE;
        if (modem_log_count < MODEM_LOG_SIZE) {
                modem_log_count++;
        } else {
                modem_log_out = (modem_log_out + 1) % MODEM_LOG_SIZE;
        }

        memset(modem_log, 0, sizeof(modem_log_t));
}


/** @} */

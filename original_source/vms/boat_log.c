/** @{

    @file
    @ingroup vms
    @brief Boat/GPS Log - records where the boat has been, also includes remote sensor info.

*/

#include "pelagic.h"
#include "sensor_types.h"

int32_t lat_current = 0, lon_current = 0;
uint32_t time_current = 0;

enum {
        TIME_SMALL_DELTA     = 255,     /**< unsigned 8-bits diff worth */
        TIME_MEDIUM_DELTA    = 32767,   /**< unsigned 16-bit diff worth */

        GPS_SMALL_DELTA      = 127,     /**< signed 8-bits diff worth */
        GPS_MEDIUM_DELTA     = 32767,   /**< signed 16-bit diff worth */

        GPS_IGNORE_DELTA     = 3,       /**< Ignore less than approx 3.3meters worth of difference movement */

        BOAT_LOG_DUP_TIME    = 30,      /**< Ignore remote sensor reports within how many seconds */
};

board_uid_t last_sensor_uid;
uint32_t last_sensor_time = 0;

uint8_t boat_log_buffer[64];

/**
    @brief  reset the current gps positions
    @note   This is called when the boat log has been uploaded.
*/

void
boat_log_reset()
{
        time_current = 0;
        lat_current = 0;
        lon_current = 0;
        last_sensor_time = 0;
}

uint8_t *
store_delta(uint8_t *data, uint16_t value, delta_size_t delta, uint8_t *header, uint8_t shift)
{
        if (delta == SIZE_MEDIUM) {
                data = store_uint16(data, value);
        } else if (delta == SIZE_SMALL) {
                *data++ = value & 0xff;
        }

        // do nothing for SIZE_EMPTY
        *header |= (delta << shift);
        return data;
}

/**
    @brief compute the delta size (small, medium, full) between the last record position and the new position.
    @param[out] coord       the difference between the current coordinate and previous (set to zero if diff is too large)
    @param[in]  coord_now   the current coordinate
    @param[inout] coord_previous the previously recorded coordinate
    @return Returns the delta size (SIZE_{EMPTY, SMALL, MEDIUM, FULL})
*/

delta_size_t
boat_log_get_delta(int16_t * coord, int32_t coord_now, int32_t *coord_previous)
{
        int32_t delta = *coord_previous - coord_now;
        uint32_t abs_delta = abs(delta);

        if (abs_delta < GPS_IGNORE_DELTA) {
                *coord = 0;
                return SIZE_EMPTY;
        } else if (abs_delta > GPS_MEDIUM_DELTA) {
                return SIZE_FULL;
        }

        *coord_previous = coord_now;
        *coord = -delta;

        return (abs_delta > GPS_SMALL_DELTA ? SIZE_MEDIUM : SIZE_SMALL);
}

/**
    @brief  Records the position, sensor temperature (optional), and GPS precision for position (optional)
    @param[in]  lat_now     GPS latitude
    @param[in]  lon_now     GPS longitude
    @param[in]  flags       additional attributes to store from attr (optional)
    @param[in]  attr        GPS additional info (speed, bearing, accelerometer info, etc) (optional)
    @param[in]  sensor      Sensor data to store (optional)

    @note   Both the GPS position + optional info and remote sensor is recorded with this routine.
            Attempts are made to compact the data by storing the deltas of the timestamp,
            latitude, and longitude from the previous record.
            A full record will be stored if the deltas are too great (exceeding more than 16-bits
            worth of difference), OR if the record will be stored on at the beginning of a flash page.
            The rationale to store a full record on a page boundary is if a flash page becomes
            corrupted, only that page will be affected leaving stream intact for the later pages.
            (This was seen while using DMA with the SPI interface - which is a known chip bug on the KL series.)
            Both the gps & BNET threads call this, and locking is done appropriately.

*/

void
boat_log_record(
        int32_t lat_now, int32_t lon_now,
        uint8_t flags,
        log_attribute_t *attr,
        log_sensor_t    *sensor)
{

        int record_size;
        uint16_t    time = 0;
        int16_t     lat, lon;
        delta_size_t time_size, lat_size, lon_size;
        bool        full_record = false;
        uint32_t    time_now, time_delta;
        uint8_t     *header, *gps_header, *data;

        // Wait up to 100ms to lock, if failed, then return.

        if (flash_store_lock(&boatlog_partition, 100) == false) {
                uart_printf("BOATLOG - locked returning\n");
                return;
        }

        // The modem thread might be busy uploading things..
        if (boatlog_partition.is_busy) {
                flash_store_unlock(&boatlog_partition);
                return;
        }

        time_now = rtc_read();

        // The RTC may get ahead of what the satellites report
        if (time_now < time_current) {
                //uart_printf("Time backwards now=%"PRIu32" current=%"PRIu32"\n", time_now, time_current);
                time_now = time_current;
        }

        time_delta = time_now - time_current;

        if (time_delta > TIME_MEDIUM_DELTA) {
                time_size =  SIZE_FULL;
        } else {
                time = time_delta;
                time_size = (time_delta > TIME_SMALL_DELTA ? SIZE_MEDIUM : SIZE_SMALL);
        }

        lat_size = boat_log_get_delta(&lat, lat_now, &lat_current);
        lon_size = boat_log_get_delta(&lon, lon_now, &lon_current);

        //
        // This should only loop twice at worse case.
        //

        for (;;) {
                data       = boat_log_buffer;

                *data++    = LOG_RECORD_MARKER;
                header     = data++;

                if (full_record || time_size == SIZE_FULL || lat_size == SIZE_FULL || lon_size == SIZE_FULL) {
                        // Need to do a full checkpoint
                        lat_current = lat_now;
                        lon_current = lon_now;

                        *header = (LOG_HEADER_TIME_FULL << LOG_HEADER_TIME_SHIFT);
                        data = store_uint32(data, time_now);
                        gps_header = data++;
                        *gps_header = LOG_GPS_HEADER_LAT_FULL | LOG_GPS_HEADER_LON_FULL;
                        data = store_uint32(data, lat_now);
                        data = store_uint32(data, lon_now);
                        full_record = true;
                } else if (lat_size == SIZE_EMPTY && lon_size == SIZE_EMPTY && sensor == NULL) {
                        // Don't record if position did not change, and no sensor is being recorded.
                        flash_store_unlock(&boatlog_partition);
                        return;
                } else {
                        if (time_delta == 1) {
                                *header = LOG_HEADER_TIME_ONE_SECOND;
                        } else {
                                *header = 0;
                                data = store_delta(data, time, time_size, header, LOG_HEADER_TIME_SHIFT);
                        }

                        gps_header = data++;
                        *gps_header = 0;
                        data = store_delta(data, lat, lat_size, gps_header, LOG_GPS_HEADER_LAT_SHIFT);
                        data = store_delta(data, lon, lon_size, gps_header, LOG_GPS_HEADER_LON_SHIFT);
                }

                if (flags & LOG_ATTR_HIGH_RES)
                        *gps_header |= LOG_GPS_HEADER_HIGH_RES;

                time_current = time_now;

                if (flags & LOG_ATTR_SPEED_HEADING) {
                        *gps_header |= LOG_GPS_HEADER_SPEED_HEADING;
                        data = store_uint16(data, attr->heading);
                        data = store_uint16(data, attr->speed);
                }

                if (flags & LOG_ATTR_ACCEL_HEADING) {
                        *gps_header |= LOG_GPS_HEADER_ACCEL_HEADING;
                        data = store_uint16(data, attr->accel_heading);
                }

                if (flags & (LOG_ATTR_PRECISION | LOG_ATTR_ACCEL_RAW)) {
                        uint8_t *ext_header = data++;
                        *ext_header = 0;
                        *gps_header |= LOG_GPS_HEADER_EXTENDED;

                        if (flags & LOG_ATTR_PRECISION) {
                                *ext_header |= LOG_GPS_EXT_HEADER_PRECISION;
                                *data++ = attr->satellites;
                                *data++ = attr->hdop;
                                *data++ = attr->vdop;
                                *data++ = attr->pdop;
                        }

                        if (flags & LOG_ATTR_ACCEL_RAW) {
                                *ext_header |= LOG_GPS_EXT_HEADER_ACCEL_RAW;
                                data = store_uint16(data, attr->accel_x);
                                data = store_uint16(data, attr->accel_y);
                                data = store_uint16(data, attr->accel_z);
                                data = store_uint16(data, attr->mag_x);
                                data = store_uint16(data, attr->mag_y);
                                data = store_uint16(data, attr->mag_z);
                        }
                }

                if (sensor) {
                        //
                        // TODO - start tracking what sensor UIDS are seen and
                        // build up a dynamic table to reduce the the record payload.
                        // assign sensor uid to a node id first time and then start just
                        // using the node id.
                        *header |= LOG_HEADER_TYPE_SENSOR_REMOTE;
                        *data++ = sensor->sensor_type;
                        uid_copy(data, sensor->sensor_uid);
                        data += BOARD_UID_SIZE;
                        *data++ = sensor->sensor_voltage;
                        *data++ = sensor->sensor_data_length;

                        memcpy(data, sensor->sensor_data, sensor->sensor_data_length);
                        data += sensor->sensor_data_length;
                }

                record_size = data - boat_log_buffer;

                if (full_record)
                        break;

                if (flash_store_partial_okay(&boatlog_partition, record_size))
                        break;

                full_record = true;
        }

        flash_store_write(&boatlog_partition, boat_log_buffer, record_size);
        flash_store_unlock(&boatlog_partition);
}

/**
    @brief  record the remote sensor temperature. (for the BNET thread)
*/

void
boat_log_sensor(log_sensor_t *sensor)
{
        uint32_t now = rtc_read();

        // might be a duplicate due to a retransmit (if the sensor did not see the ack)
        if (!uid_match(sensor->sensor_uid, last_sensor_uid)) {
                uid_copy(last_sensor_uid, sensor->sensor_uid);
        } else if (now == last_sensor_time) {
                return; // Saw this already..
        }

        last_sensor_time = (uint32_t)sensor->sensor_data[0] | (uint32_t)sensor->sensor_data[1] << 8 | (uint32_t)sensor->sensor_data[2] << 16 | (uint32_t)sensor->sensor_data[3] << 24;

        if (lat_current && lon_current) {
                boat_log_record(lat_current, lon_current, 0, NULL, sensor);
        }
}

/**
    @brief  record the GPS position and optional GPS precision (for the gps thread)
*/

void
boat_log_gps(int32_t lat_now, int32_t lon_now, uint16_t flags, log_attribute_t *attrs)
{
        extern volatile bool    gps_high_res;
        static uint8_t          count;

        // GPS logging frequency of 0 is magic... GPS logging is disabled
        if (settings.gps_log_freq == 0) return;

        if (gps_high_res == false || ++count >= settings.gps_log_freq) {
                boat_log_record(lat_now, lon_now, flags, attrs, NULL);
                count = 0;
        }
}

#if !defined(TARGET_TEST)
void
boat_log_dump()
{
#if 0
        flash_read_t log_read;
        log_precision_t prec;

        int32_t lat = 0, lon = 0;
        uint32_t period = 0;

        uint16_t delta, temperature;
        int16_t signed_delta;

        board_uid_t uid;
        int lines = 0;

        flash_read_init(&boatlog_partition, &log_read, the_cache);

        while (log_read.is_eof == false) {
                printf("%"PRIu32": ", log_read.offset);
                uint8_t marker = flash_read_uint8(&log_read);
                uint8_t header = flash_read_uint8(&log_read);

                if (marker != LOG_RECORD_MARKER)
                        printf("** missing marker value=0x%x\n", marker);

                if (header & LOG_HEADER_FULL) {
                        printf(" full record ");
                        period = flash_read_uint32(&log_read);
                        lat = flash_read_uint32(&log_read);
                        lon = flash_read_uint32(&log_read);
                } else {
                        if (header & LOG_HEADER_ONE_SECOND) {
                                delta = 1;
                        } else if (header & LOG_HEADER_TIME_MEDIUM) {
                                delta = flash_read_uint16(&log_read);
                        } else {
                                delta = flash_read_uint8(&log_read);
                        }

                        period += delta;

                        printf("time %s [%"PRIu16"] ", (header & LOG_HEADER_TIME_MEDIUM ? "med" : "short"), delta);

                        if (header & LOG_HEADER_LAT_MEDIUM) {
                                signed_delta = flash_read_uint16(&log_read);
                        } else {
                                signed_delta = (int8_t)flash_read_uint8(&log_read);
                        }

                        lat += signed_delta;

                        printf("lat %s [%" PRIi16 "] ", (header & LOG_HEADER_LAT_MEDIUM ? "med" : "short"), signed_delta);

                        if (header & LOG_HEADER_LON_MEDIUM) {
                                signed_delta = flash_read_uint16(&log_read);
                        } else {
                                signed_delta = (int8_t)flash_read_uint8(&log_read);
                        }

                        lon += signed_delta;

                        printf("lon %s [%" PRIi16 "] ", (header & LOG_HEADER_LON_MEDIUM ? "med" : "short"), signed_delta);
                }

                if (header & LOG_HEADER_SENSOR) {
                        temperature = flash_read_uint16(&log_read);
                        flash_read_block(&log_read, uid, BOARD_UID_SIZE);
                } else {
                        temperature = 0;
                }


                printf(" time=%d lat %d lon %d\n", period, lat, lon);

                if (temperature) {
                        printf(" temp %d %Z\n", temperature, uid);
                }

                if (header & LOG_HEADER_PRECISION) {
                        flash_read_block(&log_read, &prec, sizeof(prec));
                        printf("sats %d hdop %d vdop %d pdop %d\n", prec.satellites, prec.hdop, prec.vdop, prec.pdop);
                }

                if (!display_more(&lines))
                        return;
        }
#endif
}
#endif

/** @} */

/** @{

    @file
    @ingroup vms
    @brief  Flash storage definitions
*/

#include "pelagic.h"
#include "memmap.h"

#define DEFAULT_FLASH_DEVICE    &stm_flash_device
// PORT Ian: the modem has something like 1M of flash for flushing logs to

// TODO Ian: allocate the required memory regions
enum {
        // Boat log and event log data stored in FW staging area until new image received
        BOATLOG_PART_OFFSET         = FIRMWARE_ADDR,
        BOATLOG_PART_SIZE           = (60*KILOBYTE),

        EVENT_PART_OFFSET           = (BOATLOG_PART_SIZE + BOATLOG_PART_OFFSET),
        EVENT_PART_SIZE             = (60*KILOBYTE),

        // TODO Ian: remove sensor node firmware region
        FIRMWARE_SENSOR_PART_SIZE   = (1*KILOBYTE),
        FIRMWARE_SENSOR_PART_OFFSET = (EVENT_PART_OFFSET + EVENT_PART_SIZE),

        // VMS Firmware update uses the internal flash
        FIRMWARE_PART_OFFSET        = FIRMWARE_ADDR,
        FIR<PERSON><PERSON>RE_PART_SIZE          = FIRMWARE_SIZE,
};

static uint8_t boatlog_buffer[FLASH_PAGE_SIZE] __attribute__ ((aligned (4) ));
static uint8_t event_buffer[FLASH_PAGE_SIZE] __attribute__ ((aligned (4) ));

flash_partition_t boatlog_partition = {
        .name = "boatlog", 
        .offset = BOATLOG_PART_OFFSET, 
        .size = BOATLOG_PART_SIZE, 
        .staging_buffer = boatlog_buffer, 
        .device = DEFAULT_FLASH_DEVICE,
};

flash_partition_t event_partition = {
        .name = "eventlog", 
        .offset = EVENT_PART_OFFSET, 
        .size = EVENT_PART_SIZE, 
        .staging_buffer = event_buffer, 
        .device = DEFAULT_FLASH_DEVICE
};

// TODO Ian: remove sensor node firmware update
flash_partition_t firmware_sensor_partition = {
        .name = "fw-sensor", 
        .offset = FIRMWARE_SENSOR_PART_OFFSET, 
        .size = FIRMWARE_SENSOR_PART_SIZE, 
        .staging_buffer = the_cache, 
        .device = DEFAULT_FLASH_DEVICE, 
        .is_raw = false
};

// firmware_update assumes the update is stored in the internal flash
flash_partition_t firmware_partition = {
        .name = "firmware", 
        .offset = FIRMWARE_PART_OFFSET, 
        .size = FIRMWARE_PART_SIZE,
        .staging_buffer = the_cache, 
        .device = &stm_flash_device, 
        .is_raw = true
};

/* @} */

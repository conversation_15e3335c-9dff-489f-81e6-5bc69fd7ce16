/**@{

	@ingroup	vms
	@file
	@brief	Settings Management
*/

#include "pelagic.h"
#include "memmap.h"

enum {
        SETTINGS_MAX_ARGS	 = 10,
        SETTINGS_LINE_SIZE   = 128,
};

char *set_parse_ptr;
int set_parse_size;
char set_parse_line[SETTINGS_LINE_SIZE];

settings_t settings;

void gps_settings_init();
bool gps_setting(int argc, char **argv);
int gps_settings_dump(char *);

void event_settings_init();
bool event_setting(int argc, char **argv);
int event_setting_dump(char *buffer);

void reboot_settings_init();
bool reboot_setting(int argc, char **argv);
int reboot_setting_dump(char *buffer);

bool provision_setting(int argc, char **argv);
int provision_setting_dump(char *buffer);

#ifdef HAVE_RADIO
void radio_settings_init();
bool radio_setting(int argc, char **argv);
int radio_setting_dump(char *buffer);
#endif

void nap_settings_init();
bool nap_setting(int argc, char **argv);
int nap_setting_dump(char *buffer);

void modem_settings_init();

bool reset_setting(int argc, char **argv);

bool parking_setting(int ac, char **av);
int parking_setting_dump(char *buffer);

int modem_setting_dump(char *buffer);
bool modem_setting(int ac, char **av);

void parking_settings_init(void);

const setting_command_t setting_commands[] = {
        { .name = "gps",        .command = gps_setting },
        { .name = "event",      .command = event_setting },
        { .name = "reboot",     .command = reboot_setting },
        { .name = "provision",  .command = provision_setting },
#ifdef HAVE_RADIO
        { .name = "radio",      .command = radio_setting },
#endif
        { .name = "reset",      .command = reset_setting },
        { .name = "nap",        .command = nap_setting },
        { .name = "parking",    .command = parking_setting },
        { .name = "modem",      .command = modem_setting }
};


char *set_parse_ptr;
int set_parse_size;

char setting_buffer[1024];

/**
	@brief	store the current settings into flash memory
*/

void
settings_store(void)
{
        uint32_t	data[2] = { SETTINGS_MAGIC, SETTINGS_VERSION };
        kinetis_flash_erase_sector(SETTINGS_ADDR);
        kinetis_flash_write(SETTINGS_ADDR, data, sizeof(data));
        kinetis_flash_write(SETTINGS_ADDR + sizeof(data), (void *)&settings, sizeof(settings));
}

/**
	@brief	initialize all default settings
*/

void
settings_init_all()
{
        event_settings_init();
        gps_settings_init();
        reboot_settings_init();
#ifdef HAVE_RADIO
        radio_settings_init();
#endif
        nap_settings_init();
        modem_settings_init();
        parking_settings_init();
        settings_store();
}

/**
	@brief	initialize the settings structure by reading from flash
*/

void
settings_init()
{
        settings_area_t *nvram = (settings_area_t *) SETTINGS_ADDR;

        if (nvram->magic != SETTINGS_MAGIC || nvram->version != SETTINGS_VERSION) {
                memset((void *)&settings, 0, sizeof(settings));
                settings_init_all();
                return;
        }

        memcpy((void *)&settings, (void *)&nvram->settings, sizeof(settings));
}

/**
	@brief	dump BoatOS settings to buffer
	@param[in]	buffer	area to dump settings to
	@return	bytes used
*/

int
settings_dump(char *buffer)
{
        int len;

        len = sprintf(buffer, "buildstamp %d\ntag %s\nuid %Z\n", build_timestamp, build_tag, board_uid);
        len += gps_settings_dump(buffer + len);
        len += event_setting_dump(buffer + len);
        len += nap_setting_dump(buffer + len);
#ifdef HAVE_RADIO
        len += radio_setting_dump(buffer + len);
#endif
        len += reboot_setting_dump(buffer + len);

        len += provision_setting_dump(buffer + len);

        len += parking_setting_dump(buffer + len);

        len += modem_setting_dump(buffer + len);

        return len;
}

int
setting_get_char()
{
        if (set_parse_size <= 0)
                return -1;

        set_parse_size--;
        return *set_parse_ptr++;
}

/**
	@brief	parse a into argument array
	@param[in]	line	line to parse
	@param[in]	argv	array area to place arguments
	@return arguments parsed
*/

int
setting_parse_line(char **argv, char *line)
{
        int argc = 0;
        int ch;
        char *pos = line;

        for (int i = 0; i < SETTINGS_MAX_ARGS; i++)
                argv[i] = NULL;

        for (;;) {
                ch = setting_get_char();

                if ((unsigned char) ch > ' ') {
                        argv[argc++] = pos;
                        do {
                                *pos++ = ch;
                        } while ((ch = setting_get_char()) > ' ');

                        *pos++ = 0;
                }

                if (ch == -1 || ch == '\n')
                        break;

                if (argc >= SETTINGS_MAX_ARGS) {
                        while ((ch = setting_get_char()) != '\n' && ch != -1)
                                ;
                        break;
                }
        }

        return argc;
}

/**
	@brief	locate and execute a setting
	@param[in]	argc	array size
	@param[in]	argv	settings argument
	@return true if setting succeeded
*/

bool
settings_execute(int argc, char **argv)
{
        const setting_command_t *cmd = setting_commands;

        for (int i = 0; i < ARRAY_SIZE(setting_commands); i++, cmd++) {
                if (strcmp(cmd->name, argv[0]) == 0) {
                        if (cmd->command(argc - 1, argv + 1) == 0) {
                                EVENT_LOG1(EVT_SETTING_FAILED, "setting failed", "name", EVT_STRCONST, cmd->name);
                                return false;
                        }

                        return true;
                }
        }

        EVENT_LOG1(EVT_SETTING_NOT_FOUND, "setting not found", "name", EVT_STRING, argv[0]);
        return false;
}

/**
	@brief	parse, execute, and store updated settings
	@param[in]	buffer	string to parse into settings arguments
	@param[in]	size	buffer size
	@param[in]	results	area to place new settings with possible errors
	@return bytes placed into results area
*/

int
settings_parse(char *buffer, int size, char *results)
{
        int argc = 0, len = 0, errors = 0, line = 0, error_line;
        char *argv[SETTINGS_MAX_ARGS];

        set_parse_ptr = buffer;
        set_parse_size = size;

        while (set_parse_size > 0) {
                line++;

                argc = setting_parse_line(argv, set_parse_line);
                if (argc == 0 || argv[0] == NULL)
                        continue;

                for (int i = 0; i < argc; i++) {
                        printf("argv[%d]=[%s] ", i, argv[i]);
                }
                printf("\n");

                if (!settings_execute(argc, argv)) {
                        if (errors++ == 0) {
                                error_line = line;
                        }
                }
        }

        settings_store();

        if (errors) {
                len = sprintf(results, "# errors [%d] line [%d]\n", errors, error_line);
                results += len;
        }

        len += settings_dump(results);
        return len;
}

/**
	@brief	locate a key within an array
	@param[in]	key		string to locate
	@param[in]	keys	array to lookup key in
	@param[in]	count	size of keys array
	@param[out]	value	value located
	@return	true if key was found
*/

int
settings_find_key(char *key, const setting_key_t *keys, int count, uint32_t *value)
{
        for (int i = 0; i < count; keys++, i++) {
                if (strcmp(keys->key, key) == 0) {
                        *value = keys->value;
                        return true;
                }
        }

        return false;
}

/**
	@brief	reset setting - initialize all settings to their defaults
*/

bool
reset_setting(int argc, char **argv)
{
        settings_init_all();

        return true;
}

/** @} */

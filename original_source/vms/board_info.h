#ifndef __BOARD_INFO_H__
#define __BOARD_INFO_H__

enum {
        BOARD_INFO_MAGIC    = 0xb123d123,
        BOARD_INFO_VERSION  = 1,
};

typedef struct {
        uint32_t    magic;
        uint32_t    version;
        imei_t      imei;
        uint16_t    build_crc;  // only used for firmware update notification
} board_info_t;


void board_info_init(void);
void board_info_set_imei(uint64_t imei);
void board_info_set_build_crc(uint16_t crc);
bool board_info_ensure_nfc_imei();
uint16_t board_info_read_build_crc();

extern bool board_info_have_imei;
extern imei_t board_info_imei;

#define BOARD_INFO  ((board_info_t *)BOARD_INFO_ADDR)

#endif

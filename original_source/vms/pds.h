#ifndef __PDS_H__
#define __PDS_H__

#include "firmware.h"

typedef enum {
        PDS_TYPE_BOAT_LOG            = 0x01,
        PDS_TYPE_FIRMWARE_UPDATED    = 0x03, // Acknowledge firmware update
        PDS_TYPE_FIRMWARE_RECEIVED   = 0x04, // Firmware was received
        PDS_TYPE_EVENT_LOG           = 0x08,
        PDS_TYPE_SETTINGS            = 0x09, // Current settings - usually in response to settings incoming message
        PDS_TYPE_FACTORY_RESULT_TEST = 0x40, // Factory test message
        PDS_TYPE_TEST                = 0xff
} pds_type_t;

enum {
        PDS_CHUNK_VERSION                    = 4,

        PDS_BOAT_LOG_VERSION                 = 6, // AKA GPS log version
        PDS_EVENT_LOG_VERSION                = EVT_MSG_VERSION,
        PDS_SETTINGS_VERSION                 = 4,

        PDS_TYPE_FIRMWARE_UPDATED_VERSION    = 2,
        PDS_TYPE_FIRMWARE_RECEIVED_VERSION   = 2,
        PDS_TYPE_FACTORY_RESULT_TEST_VERSION = 2,
};

typedef enum {
        PDS_INCOMING_DATA     = 0x01, // some data type
        PDS_INCOMING_SETTINGS = 0x02, // settings
        PDS_INCOMING_FIRMWARE = 0xfe, // firmware update
        PDS_INCOMING_REBOOT   = 0xff // reboot request
} pds_incoming_msg_t;

typedef enum {
        PDS_CHUNK_FLAG_COMPRESSED    = 0x01,     // Compressed
        PDS_CHUNK_FLAG_RETRY         = 0x02,     // Attempted retransmission
        PDS_CHUNK_FLAG_ERROR         = 0x04,     // Error encountered when reading from flash
} pds_chunk_flags_t;

typedef struct {
        uint8_t        type;            // Type of packet
        uint8_t        version;         // Version
        uint32_t       timestamp;       // timestamp when this was created (board timestamp)
        uint16_t       total_chunks;    // total chunks expected
        uint16_t       chunk;           // this chunk (0..n-1)
} pds_chunk_header_v1_t;

typedef struct
__attribute__ ((packed))
{
        uint8_t       type;               // Type of packet
        uint8_t       version;            // Version
        uint8_t       flags;              // Chunk header flags
        uint8_t       timestamp[4];       // timestamp when this was created (board timestamp)
        uint8_t       chunk_count[2];    // total chunks expected
        uint8_t       chunk_index[2];     // this chunk (0..n-1)
}
pds_chunk_header_v2_t;

typedef struct
__attribute__ ((packed))
{
        uint8_t       type;               // Type of packet
        uint8_t       version;            // Version
        uint8_t       flags;              // Chunk header flags
}
pds_chunk_header_v4_t;

typedef struct
__attribute__ ((packed))
{
        uint8_t     type;
        uint8_t     version;
        uint8_t     device_type;
        uint8_t     device_version;
        uint8_t     buildstamp[4];
        board_uid_t board_uid;
        uint8_t     build_crc16[2];
}
pds_firmware_received_msg_v2_t;

typedef struct
__attribute__ ((packed))
{
        uint8_t     type;
        uint8_t     version;
        uint8_t     device_type;
        uint8_t     device_version;
        uint8_t     buildstamp[4];
        board_uid_t board_uid;
        uint8_t     build_crc16[2];
        uint8_t     build_tag_length;
        uint8_t     build_tag[];
}
pds_firmware_updated_msg_v2_t;

typedef struct
__attribute__ ((packed))
{
        uint8_t     type;
        uint8_t     version;
        uint8_t     data[];
}
pds_settings_incoming_msg_t;

typedef struct
__attribute__ ((packed))
{
        uint8_t     type;
        uint8_t     version;
        uint8_t     test_version;
        uint8_t     mode;
        board_uid_t board_uid;
        uint8_t     results_length[2];
        char        results[];
}
ft_result_msg_t;

modem_result_t pds_send_chunk(uint8_t log_type, uint8_t log_version,  uint8_t *chunk_data, uint32_t chunk_size);
bool pds_send_log(flash_partition_t *log_store, uint8_t log_type, uint8_t version, void (*reset_log)(void));
void pds_check_messages();
void pds_firmware_updated();
void pds_firmware_acknowledge(firmware_header_t *fwh, uint8_t *uid);
void pds_process_settings(pds_settings_incoming_msg_t *msg, uint32_t bytes);
modem_result_t pds_settings_send(uint8_t *settings, uint32_t length);
void pds_settings_process(pds_settings_incoming_msg_t *msg, uint32_t bytes);

#endif

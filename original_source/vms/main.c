/**
    @file
    @ingroup vms
    @brief  Main - initialize the board and all threads
    @{

*/

#include "pelagic.h"

#include "gpio_api.h"
#include "gpio_irq_api.h"
#include "clk_freqs.h"
// #include "shell.h"

// #include "bnet.h"
#include "semihost_api.h"
// #include "winbond_flash.h"
#include "firmware.h"
#include "console.h"
#include "dip.h"
#include "power.h"
#include "led.h"
#include "board_log.h"
#include "board_fault.h"
#include "system_file.h"
#include "radio.h"
#include "alarm.h"
#include "provision.h"
#include "temp.h"
#include "humidity.h"
#ifndef MODEM_3G
#include "nfc.h"
#endif
#include "modem.h"
#include "modem_log.h"
#include "factory_test.h"
#include "board_info.h"
#include "device_power.h"
#include "accel.h"
#include "signals.h"
#include "pds.h"

// TODO Ian: resolve stubs
#include "port_stubs.h"
#include "rtc_api.h"
#include "serial_wire_debug.h"

//#define GEORG_DEBUG
#define WAIT_FOR_FIX    120

void reboot_thread(void const *arg);
osThreadDef(reboot_thread, osPriorityHigh, 1, 0);
osThreadId reboot_tid;

osThreadDef(gps_thread, osPriorityHigh, 1, 0);
osThreadId gps_tid;

void helm_thread(void const *arg);
osThreadDef(helm_thread, osPriorityNormal, 1, 0);
osThreadId helm_tid;

void modem_thread(void const *arg);
osThreadDef(modem_thread, osPriorityHigh, 1, 0);
osThreadId modem_tid;

#ifdef HAVE_RADIO
void bnet_thread(void const *arg);
osThreadDef(bnet_thread, osPriorityNormal, 1, 0);
osThreadId bnet_tid;
#endif

#ifdef HAVE_ACCEL
void accel_thread(void const *arg);
osThreadDef(accel_thread, osPriorityNormal, 1, 0);
osThreadId accel_tid;
#endif

osThreadId console_tid; // main thread is the console

struct gps_stats gps_stats;
struct helm_stats helm_stats;
struct log_stats log_stats;

volatile int demo_mode;

// spi_t spi1_bus;

// Used by multiple locations - bodytrace, firmware update, firmware acknowledge/notify
shared_buffer_t the_buffer __attribute__ (( aligned (4) ));

// externs
void random_setup_seed(void);
void printf_init(void);

extern gpio_t winbond_cs_pin;
#ifdef HAVE_ACCEL
extern gpio_t lsm303d_cs_pin;
#endif

void device_pin_setup(void);
static inline void maybe_send_heartbeat(void);
static void still_parked(void);
static bool gps_one_shot(void);

//static shell_t         shell;

/**
    @brief  Main startup thread  - called after memory setup, and RTX has initialized
    @note   Some devices/pins are initialized, provision state is checked, and if everything passes -
            create the various board threads. After which the console shell starts running.
*/

int
main(void)
{
        int             i;
        // shell_t         shell;

        // serial_console_init(true);

        SWO_Init(); // Initialize SWO for debugging output
        ITM_SendString("Hello from STM32L4 via SWO!\r\n");

        printf_init();
        sys_file_init();
        rtc_init();
        event_init();
        battery_init();
        solar_init();

#ifdef HAVE_LED
        led_init();
#endif

#ifdef HAVE_DIP
        dip_init();
#endif

        device_power_init();
        device_pin_setup();

        uid_init();

        board_info_init();
        board_info_ensure_nfc_imei();

        random_setup_seed();

        // Create the reboot thread
        printf("Creating reboot thread\n");
        reboot_tid = osThreadCreate(osThread(reboot_thread), NULL);

#if !defined(HAVE_FT_STANDALONE)
        // This is the normal state; this will look at what mode we're
        // in and act accordingly

        provision_board();
#endif // END !defined(HAVE_FT_STANDALONE)


#ifdef HAVE_LED
        led_on(LED_GREEN);
#endif

        board_check_power();

        announce("VMS");

        // See if its safe to continue on..

#ifdef HAVE_FT_STANDALONE
        factory_test_standalone();
#else
        random_setup_seed();

        // The following needs to happen before the nap mode check below
        settings_init();
        // winbond_flash_init();
        flash_store_init();
#ifndef HAVE_V1
        thermistor_init();
#endif

#ifdef HAVE_HUMIDITY
        humidity_init();
#endif
#if 0
        {
                extern uint32_t const mp_stk_size;
                printf("mp_stk_size: %u\n", mp_stk_size);
        }
#endif

        if (enable_park == true && SYS_REG_FILE->reboot_reason == REBOOT_ACCEL_NAP) {
                if (accel_should_stay_in_nap_mode()) {
                        still_parked();
                        // not reached
                }
                // If GPS for parking/wakeup is disabled then just get on with it
                if (settings.parking_use_gps == 0) goto wakeup;
                // if we have enough battery, fire up the GPS
                if (battery_read() > BATTERY_VOLTAGE_GOOD_ENOUGH) {

                        if (!gps_one_shot()) {
                                EVENT_LOG(EVT_ACCEL_WFP_NO_GPS, "woke from park, no GPS fix");
                                goto wakeup;
                        }

                        uint32_t        metres = 0, pdop;
                        uint8_t         still_count = 0, skip_count = 0;
                        int             percent_still;
                        double          cached_lat, cached_lon;

                        cached_lat = (double)settings.parking.parked_lat / GPS_PRECISION;
                        cached_lon = (double)settings.parking.parked_lon / GPS_PRECISION;

                        // Take 10 distances. Note (in j) when they're below threshold
                        for (i = 0, still_count = 0, pdop = PDOP_INT(); i < 20; i++, pdop = PDOP_INT()) {
                                // cut the crap
                                if (pdop > 49999) {
                                        skip_count++;
                                        osDelay(1000);
                                        continue;
                                }
                                metres = distance_in_meters(gps_current_lat, gps_current_lon,
                                                            cached_lat, cached_lon);
                                if (metres < settings.parking.metres_allowed) {
                                        still_count++;
                                }
#ifdef GEORG_DEBUG
                                printf("pdop approx: %d, metres: %d\n", pdop, metres);
#endif
                                osDelay(1000);
                        }
                        // If more than 60% of the good samples, then we must be stationary
                        percent_still = (int)(still_count * 100) / (i - skip_count);
#ifdef GEORG_DEBUG
                        printf("i: %d, skip_count: %d, still_count: %d\n", i, skip_count, still_count);
                        printf("percent_still = %d\n", percent_still);
#endif
                        if (percent_still >= 60) {
                                EVENT_LOG3(EVT_ACCEL_WFP_GBTS, "gps says go back to sleep",
                                           "metres", EVT_32BIT, metres,
                                           "allowed", EVT_16BIT, settings.parking.metres_allowed,
                                           "pdop", EVT_32BIT, PDOP_INT());
                                still_parked();
                                // not reached
                        } else {
                                EVENT_LOG3(EVT_ACCEL_WFP_MOVED, "moved during park",
                                           "metres", EVT_32BIT, metres,
                                           "allowed", EVT_16BIT, settings.parking.metres_allowed,
                                           "pdop", EVT_32BIT, PDOP_INT());
                        }
                        // gps_one_shot() put us into hires -- maybe we don't want to stay that way
                        gps_track_appropriately();
                } else {
                        EVENT_LOG(EVT_ACCEL_WFP_LOW_BATTERY, "not enough battery to turn on GPS");
                }
wakeup:
                log_accel_deviations(EVT_ACCEL_MOVING, 0);
        }

        // bnet_packet_watch = true;
#if defined(HAVE_RADIO)
        // Create the boatnet thread
        bnet_tid = osThreadCreate(osThread(bnet_thread), NULL);
#endif

        board_fault_check();
#ifdef HAVE_NFC
        // not needed here
#endif

        // check to see if the firmware was updated, and erase
        // the flash stores if it was
        firmware_was_updated();

        EVENT_LOG3(EVT_SYS_BUILD, "image",
                   "build", EVT_32BIT, build_timestamp,
                   "tag", EVT_STRCONST, build_tag,
                   "battery", EVT_16BIT, battery_read());

        EVENT_LOG3(EVT_SYS_BOOT, "boot",
                // TODO: Ian verify these registers do what we want
                // STM32L431: Use RCC reset flags instead of Kinetis RCM
                "CSR", EVT_32BIT | EVT_HEX, RCC->CSR,
                "reserved", EVT_32BIT | EVT_HEX, 0,
                "reason", EVT_8BIT, SYS_REG_FILE->reboot_reason);

        firmware_sensor_init();

#ifdef HAVE_DIP
        demo_mode = dip_on(DIP3);

        if (demo_mode)
                printf("IN DEMO MODE\n");

        if (dip_on(DIP2))
                modem_firmware_update_mode();
#endif

        // Create the gps thread
        if (!gps_tid)
                gps_tid = osThreadCreate(osThread(gps_thread), NULL);

        // Create the helm thread
        helm_tid = osThreadCreate(osThread(helm_thread), NULL);

        // Create the modem thread
        modem_tid = osThreadCreate(osThread(modem_thread), NULL);

#ifdef HAVE_ACCEL
        // Create the accelerometer thread
        accel_tid = osThreadCreate(osThread(accel_thread), NULL);
#endif

#ifdef HAVE_LED
        led_off(LED_GREEN);
#endif

        console_tid = osThreadGetId();

        osDelay(1000);

        for (;;) {
                // shell_init(&shell, shell_commands);
                // shell_run(&shell);
        }
#endif
}

void
device_pin_setup(void)
{
        gps_pin_setup();
        modem_pin_setup();

        // winbond_pin_setup();
#ifdef HAVE_ACCEL
        accel_pin_setup();
#endif

#ifdef HAVE_RADIO
        radio_pin_setup();
#endif
}

/**
    @brief  check to see if the firmware was updated
    @note   When the firmware is updated, the flash partitions are
            erased (partition sizes & log formats may have changed),
            and an event is logged.
*/

void
firmware_was_updated(void)
{
        if (SYS_REG_FILE->firmware_updated == false)
                return;

        firmware_updated = true;
        SYS_REG_FILE->firmware_updated = false;

        // If we have never saved the log versions, stuff them into settings
        if (settings.persistent_boat_log_vers == 0 || settings.persistent_boat_log_vers == 0xff) {
                printf("Storing boat log version as: %d\n", PDS_BOAT_LOG_VERSION);
                settings.persistent_boat_log_vers = PDS_BOAT_LOG_VERSION;
        }
        if (settings.persistent_event_log_vers == 0 || settings.persistent_event_log_vers == 0xff) {
                printf("Storing event log_version as: %d\n", PDS_EVENT_LOG_VERSION);
                settings.persistent_event_log_vers = PDS_EVENT_LOG_VERSION;
        }
        // Erase the telemetry partitions if the version has changed
        if (settings.persistent_boat_log_vers != PDS_BOAT_LOG_VERSION) {
                printf("Erasing the boat log partition\n");
                flash_store_erase(&boatlog_partition);
        }
        if (settings.persistent_event_log_vers != PDS_EVENT_LOG_VERSION) {
                printf("Erasing the event log partition\n");
                flash_store_erase(&event_partition);
        }
        flash_store_erase(&firmware_partition);
        flash_store_erase(&firmware_sensor_partition);

        EVENT_LOG(EVT_FIRMWARE_UPDATED, "firmware updated");
}

/**
   @fn static inline void maybe_send_heartbeat(void)
   @brief Log that we are still alive after a particularly long sleep
   @param[in] slept the time, in seconds, that we have been asleep
   since the last time this function was called

   Expectation is that this is called while VTS is parked and has been
   already been called.
 */
static inline void
maybe_send_heartbeat(void)
{
        uint32_t slept = rtc_read() - settings.parking.parked_time;
        int hb_mult = 1; // heartbeat mulitiplier
        /*
         * If battery level is below 3.1v then we don't even get
         * here because of board_check_power() but...
         * if battery level is 3.15v then that's pretty low so
         * back off the heartbeat interval by some factor (3).
         */
        if (battery_read() < BATTERY_V3_15) hb_mult = 3;
        if (slept < settings.parking.heartbeat_interval * hb_mult) {
                return;
        }
        /*
         * if enough battery, make sure that GPS runs during upload
         * because that way we get a cell log with lat/lon
         */
        if (battery_read() > BATTERY_VOLTAGE_GOOD_ENOUGH)
                gps_one_shot();

        EVENT_LOG2(EVT_ACCEL_HEARTBEAT, "heartbeat",
                   "type", EVT_8BIT, 5,
                   "slept", EVT_32BIT, slept);

        // store the time in non-volatile
        settings.parking.parked_time = rtc_read();
        settings_store();

        // Wake up the modem and attempt to upload the log
        modem_init();
        EVENT_LOG(EVT_MODEM_INIT, "init");
        modem_log_new_entry();
        modem_attempt_upload();
        // Make this tunable?
        osDelay(2000);
}

/**
   @fn static void still_parked(void)
   @brief Log the board stats and go back to sleep parked
   @note does not return -- host is shutdown before the 10 second delay expires

*/
static void
still_parked(void)
{
        maybe_send_heartbeat();
        board_log_record(BOARD_LOG_FLAG_PARKED);    // going to sleep, log board status stuff
        osDelay(500);           // ultra cautious, give the log record a chance
        osSignalSet(reboot_tid, REBOOT_SIGNAL_STILL_PARKED);
        printf("Stationary... sleeping\n");
        osDelay(10000); // Give reboot time to happen without starting more threads
}

/**
   @fn static void gps_one_shot()
   @brief Start the GPS, wait limited time for fix. Used in parking/heartbeat
   when we need a location but gps_thread() may not be running
   @return true when we have GPS location
*/
static bool
gps_one_shot(void)
{
        int i;

        // GPS thread is already running, nothing for us to do
        if (gps_tid)
                return true;

        gps_tid = osThreadCreate(osThread(gps_thread), NULL);

        // wait some time for a gps fix _and_ a non-zero PDOP
        // XXX - "some time" should be a setting
        for (i = 0; (gps_have_fix == false || PDOP_INT() == 0) && i < WAIT_FOR_FIX; i++) {
                osDelay(1000);
                i++;
        }
        if (i >= WAIT_FOR_FIX)
                return false;

        gps_set_high_res_tracking();
        return true;
}

/** @} */

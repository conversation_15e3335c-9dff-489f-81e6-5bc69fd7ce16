/**
   @ingroup	vms
   @{
   @file
   @brief	GPS thread - parses NMEA sentences, signals when time has been acquired, and
*/

#include "pelagic.h"
#include "console.h"
#include "pinmap.h"
#include "helm.h"
#include "signals.h"
#include "led.h"
#include "power.h"
#include "sun.h"
#include "alarm.h"
#include "nap.h"
#include "factory_test.h"
#include "modem.h"
#include "modem_thread.h"
#include "geo.h"
#ifdef HAVE_ACCEL
#include "accel.h"
#endif

// TODO Ian: implement
#include "port_stubs.h"

#ifdef TARGET_TEST
#include <stdlib.h>
#endif

enum {
        TIME_CHECK_INTERVAL           = (30*MINUTE),  ///< Minutes to check for RTC drift
};

volatile bool gps_running            = false;   ///< True if the GPS thread is busy
volatile bool gps_capture_location   = true;    ///< Capture/record GPS location
volatile bool gps_show_bytes         = false;   ///< Display the bytes received from the GPS (debugging)
volatile bool gps_watch_location     = false;   ///< Show location updates
volatile uint8_t gps_settings_update = 0;       ///< Which settings have been updated
volatile gps_state_t gps_state;                 ///< Current GPS state (normal, stationary, etc)
gps_state_t gps_state_requested;                ///< New GPS state requested  (for debugging)
volatile gps_track_t gps_tracking;              ///< GPS tracking (high/low resolution, periodic, off)

volatile uint32_t gps_idle_timestamp;           ///< Timestamp for last position being tracked for stationary consideration
volatile bool gps_have_fix = false;             ///< True if a fix has been acquired since last gps sentence (low res may not issue a valid sentece)
volatile bool gps_acquired_fix = false;         ///< True if a fix has been acquired this boot
volatile bool gps_high_res = false;             ///< True if running in high resolution mode
volatile uint32_t gps_fix_time;                 ///< clock at last valid sentence 

volatile bool time_acquired = false;            ///< True if time has been acquired from GPS or carried across sleep/reboot.

nmea_t gps_loc; ///< Structure to hold parsed NMEA sentences

volatile int32_t gps_lat, gps_lon;              ///< Current latitude & longitude in integer form
double previous_lat = 0.0, previous_lng = 0.0;  ///< Previous latitude & longitude in floating point

volatile double gps_current_lat = 0.0, gps_current_lon = 0.0;   ///< Current latitude & longitude in float point
volatile double gps_current_pdop = 0.0;

uint32_t time_checked = 0;          ///< Time last checked for drift

log_attribute_t gps_attr;           ///< Structure to hold additional GPS info beyond just position

void gps_set_state(gps_state_t mode);
void gps_display_satellites(log_attribute_t *p);

char gps_op[GPS_DATA_SIZE];         ///< holds GPS command to send
char gps_data[GPS_DATA_SIZE];       ///< current received GPS sentences

/**
    @brief  convert a GPS point from floating point to integer
*/

int32_t
gps_fixed_integer(double num)
{
        double integer, fractional;

        fractional = modf(num, &integer);

        return ( ((int32_t)integer) * GPS_PRECISION) + (int32_t)(fractional * (double) GPS_PRECISION);
}

/**
    @brief  notify interested threads when the time has been acquired
*/

void
gps_acquired_time()
{
        osSignalSet(modem_tid, SYS_SIGNAL_ACQUIRED_TIME);
#ifdef HAVE_RADIO
        osSignalSet(bnet_tid, SYS_SIGNAL_ACQUIRED_TIME);
#endif
}

/**
    @brief  update the RTC from the GPS at regular intervals and calculate initial sunrise/sunset position
*/

uint32_t
gps_get_time()
{
        datetime_t dt;
        int32_t drift, rtc_now, gps_now;

        rtc_now = rtc_read();

        if (time_acquired && (rtc_now - time_checked) < TIME_CHECK_INTERVAL)
                return rtc_now;

        dt.year   = gps_loc.year;
        dt.month  = gps_loc.month;
        dt.day    = gps_loc.day;
        dt.hour   = gps_loc.hour;
        dt.minute = gps_loc.minute;
        dt.second = gps_loc.second;
        gps_now   = datetime_to_seconds(&dt);

        drift = rtc_now - gps_now;
        time_checked = gps_now;

        if (time_acquired && !drift)
                return rtc_now;

        rtc_write(gps_now);

        if (time_acquired == false) {
                time_acquired = true;

                if (gps_have_fix)
                        sun_position(gps_now, gps_loc.latitude, gps_loc.longitude);

                EVENT_LOG1(EVT_SYS_TIME_SET, "time set", "counter", EVT_32BIT, rtc_now);
                gps_acquired_time();
        } else {
                EVENT_LOG1(EVT_SYS_TIME_ADJUST, "time adjust", "seconds", EVT_S32BIT, drift);
        }

        return gps_now;
}

/**
    @brief  handle thread notifications related to power management, updated settings, and debugging actions.
*/

void
gps_process_signals(uint16_t signals)
{
        if (signals & SYS_SIGNAL_SHUTDOWN) {
                if (gps_state != GPS_STATE_OFF)
                        gps_set_state(GPS_STATE_OFF);
        } else if (signals & POWER_SIGNAL_CHANGE) {
                switch (board_power_state) {
                case POWER_STATE_EXCELLENT:
                case POWER_STATE_NORMAL:
                        gps_set_state(GPS_STATE_NORMAL);
                        break;

                case POWER_STATE_LOW:
                case POWER_STATE_CRITICAL:
                        if (gps_state != GPS_STATE_OFF) {
                                gps_set_state(GPS_STATE_OFF);
                                gps_idle_timestamp = 0;
                        }
                        break;
                case POWER_STATE_UNKNOWN:
                default:
                        printf("gps_process_signals: unknown power state %d\n", board_power_state);
                }
        } else if (signals & GPS_SIGNAL_SETTINGS) {
                if ((gps_settings_update & GPS_SETTING_UPDATE_IDLE) && gps_state == GPS_STATE_STATIONARY) {
                        gps_set_state(GPS_STATE_STATIONARY);
                } else if ((gps_settings_update & (GPS_SETTING_UPDATE_RUN_MODE|GPS_SETTING_UPDATE_PERIODIC)) && gps_state == GPS_STATE_NORMAL) {
                        gps_set_state(GPS_STATE_NORMAL);
                }
                if ((settings.gps.flags & GPS_FLAG_IDLE_ON) == 0 && gps_state == GPS_STATE_STATIONARY) {
                        gps_set_state(GPS_STATE_NORMAL);
                }

                gps_settings_update = 0;
        } else if (signals & GPS_SIGNAL_STATE_CHANGE) { // debugging command
                gps_set_state(gps_state_requested);
        }
}

#ifdef HAVE_ACCEL
/**
    @brief  store accelerometer heading along with the raw accelerometer & compass axises if requested.
*/

uint16_t
gps_add_accel_info()
{
        uint16_t    flags = LOG_ATTR_ACCEL_HEADING;

        gps_attr.accel_heading = heading_current;

        if (settings.gps.flags & GPS_FLAG_SEND_ACCEL_RAW) {
                flags |= LOG_ATTR_ACCEL_RAW;

                gps_attr.accel_x = accel_current.x;
                gps_attr.accel_y = accel_current.y;
                gps_attr.accel_z = accel_current.z;

                gps_attr.mag_x   = mag_current.x;
                gps_attr.mag_y   = mag_current.y;
                gps_attr.mag_z   = mag_current.z;
        }

        return flags;
}
#endif

/**
    @brief  GPS thread - records boat position, signals other threads for position updates.
*/

void
gps_thread(void const *arg)
{
        char    *buffer;
        int bytes, meters;
        uint16_t signals, flags;
        uint32_t time_now = 0, timeout;
        bool have_coords = false, tracking_precision = true;

        // XXX - fix in settings2.0
        // hack to set a default for gps logging frequency
        if (settings.gps_log_freq == 255) {
                settings.gps_log_freq = 1;
        }

        gps_settings_verify();
        gps_init();
        gps_reset();

        gps_set_state(GPS_STATE_NORMAL);

        EVENT_LOG(EVT_GPS_INIT, "init");

        if (time_acquired)
                gps_acquired_time();

        gps_capture_location = true;

#if defined(HAVE_LED)
        bool led_is_on = true;
        led_on(LED_BLUE);
#endif

        for (;; ) {
                gps_running = false;

                if (!gps_have_fix && gps_state != GPS_STATE_OFF && gps_tracking != GPS_TRACK_HIGH_RES) {
                        timeout = 20000;
                } else
                        timeout = osWaitForever;

                bytes = serial_read_timeout_signal(&gps_port, gps_data, sizeof(gps_data), timeout, &signals);
                reboot_check_pending();

                gps_running = true;

                if (signals) {
                        gps_process_signals(signals);

                        if (bytes == 0)
                                continue;
                }

                buffer = gps_data;

                //
                // The periodic on cycle may be too short, or the
                // always-locate mode may sleep too soon to acquire a satellite lock.
                // put the gps into full mode to try and acquire a lock.
                // NOTE: this will need to change when switching to other GPS chips.
                //

                if (bytes == 0 && !gps_have_fix && gps_tracking != GPS_TRACK_HIGH_RES) {
                        gps_set_high_res_tracking();
                        continue;
                }

                if (gps_show_bytes) {
                        console_write(buffer, bytes);
                        console_flush();
                }

                while (bytes-- > 0) {
                        switch (nmea_fusedata(&gps_loc, *buffer++)) {
                        case NMEA_NONE:
                                continue;

                        case NMEA_CRC_ERR:
                                gps_stats.crc_errors++;
                                continue;

                        case NMEA_GPGGA:
                                gps_attr.satellites = gps_loc.satellites_used;
                                continue;

                        case NMEA_GPRMC:
                                gps_stats.sentences++;

                                gps_acquired_fix = true;
                                gps_fix_time = clock_read();
                                gps_current_lat = gps_loc.latitude;
                                gps_current_lon = gps_loc.longitude;
                                gps_current_pdop = gps_loc.pdop;

                                // used by signal strength tracking
                                gps_lat = gps_fixed_integer(gps_loc.latitude);
                                gps_lon = gps_fixed_integer(gps_loc.longitude);

                                if (!gps_have_fix) {
                                        gps_have_fix = true;

#if defined(HAVE_LED)
                                        if (led_is_on) {
                                                led_off(LED_BLUE);
                                                led_is_on = false;
                                        }
#endif
                                        gps_track_appropriately();
                                }

                                time_now = gps_get_time();

                                if ((settings.gps.flags & GPS_FLAG_IDLE_ON) && gps_idle_timestamp) {
                                        // determine movement
                                        meters = distance_in_meters(gps_loc.latitude, gps_loc.longitude, previous_lat, previous_lng);
                                        //uart_printf("[moved: %d threshold %d time %d]\n", meters, settings.gps.idle_meters, rtc_read() - gps_idle_timestamp);
                                        if (meters >= settings.gps.idle_meters) {
                                                previous_lat = gps_loc.latitude;
                                                previous_lng = gps_loc.longitude;
                                                gps_idle_timestamp = time_now;

                                                if (gps_state == GPS_STATE_STATIONARY) {
                                                        gps_set_state(GPS_STATE_NORMAL);
                                                        EVENT_LOG1(EVT_GPS_MOVING, "moving", "meters", EVT_16BIT, meters);
                                                }
                                        } else if ((time_now - gps_idle_timestamp) >= (uint32_t)settings.gps.idle_time) {
                                                gps_idle_timestamp = 0;

                                                if (gps_state != GPS_STATE_STATIONARY)
                                                        gps_set_state(GPS_STATE_STATIONARY);
                                        }
                                } else {
                                        gps_idle_timestamp = time_now;
                                        previous_lat = gps_loc.latitude;
                                        previous_lng = gps_loc.longitude;
                                }

                                gps_attr.speed = gps_loc.speed * SPEED_PRECISION;
                                gps_attr.heading = gps_loc.heading;

                                if (have_coords || tracking_precision == false) {
                                        flags = LOG_ATTR_SPEED_HEADING;
                                        if (gps_high_res)
                                                flags |= LOG_ATTR_HIGH_RES;
#ifdef HAVE_ACCEL
                                        flags |= gps_add_accel_info();
#endif
                                        boat_log_gps(gps_lat, gps_lon, flags, &gps_attr);
                                        have_coords = false;
                                } else {
                                        have_coords = true;
                                }

                                if (gps_watch_location)
                                        gps_display_location();

#ifdef HAVE_LED
                                if (demo_mode) {
                                        led_on(LED_BLUE);
                                        osDelay(250);
                                        led_off(LED_BLUE);
                                }
#endif
                                break;

                        case NMEA_GPGSA:
                                if (!have_coords)
                                        break;

                                flags = LOG_ATTR_SPEED_HEADING;
                                if (gps_high_res)
                                        flags |= LOG_ATTR_HIGH_RES;

                                if (settings.gps.flags & GPS_FLAG_SEND_PRECISION) {
                                        flags |= LOG_ATTR_PRECISION;
                                        gps_attr.hdop = gps_loc.hdop * DOP_PRECISION;
                                        gps_attr.vdop = gps_loc.vdop * DOP_PRECISION;
                                        gps_attr.pdop = gps_loc.pdop * DOP_PRECISION;
                                }

#ifdef HAVE_ACCEL
                                flags |= gps_add_accel_info();
#endif

                                boat_log_gps(gps_lat, gps_lon, flags, &gps_attr);
                                have_coords = false;

                                if (gps_watch_location)
                                        gps_display_satellites(&gps_attr);

                                break;

                        case NMEA_GPRMC_INVALID:
                                gps_have_fix = false;
                                have_coords = false;
                                break;

                        case NMEA_PMTK:
//                count = gps_loc.word_idx;
//                for (int i = 0; i <= count; i++) {
//                    uart_printf("[%d: %s] ", i, gps_loc.words[i]);
//                }
//                uart_printf("\n");
                                break;
                        }
                }
        }
}

/**
    @brief  place the GPS chip into high resolution tracking
*/

void
gps_set_high_res_tracking()
{
        gps_highres_mode();
        gps_tracking = GPS_TRACK_HIGH_RES;
        EVENT_LOG(EVT_GPS_HIGH_RES, "high res");
        gps_high_res = true;
}

/**
    @brief  put the GPS into periodic mode using on/off parameters appropriate for
            the requested state (stationary, normal, etc.)
*/

void
gps_set_periodic_tracking(gps_state_t state)
{
        switch (state) {
        case GPS_STATE_STATIONARY:
                gps_periodic_mode(settings.gps.idle_on, settings.gps.idle_off);
                break;
        case GPS_STATE_NORMAL:
                gps_periodic_mode(settings.gps.periodic_on, settings.gps.periodic_off);
                break;

        default:
                return;
        }

        EVENT_LOG(EVT_GPS_PERIODIC, "periodic");
        gps_tracking = GPS_TRACK_PERIODIC;
        gps_high_res = false;
}

/**
    @brief  place the GPS into a normal tracking state.
*/

void
gps_set_normal_tracking()
{
        switch (settings.gps.run_mode) {
        case GPS_RUN_MODE_HIGH_RES:
                gps_set_high_res_tracking();
                break;

        case GPS_RUN_MODE_PERIODIC:
                gps_set_periodic_tracking(GPS_STATE_NORMAL);
                break;

        default:
                gps_high_res = false;
                gps_low_res_mode();
                EVENT_LOG(EVT_GPS_LOW_RES, "low res");
                gps_tracking = GPS_TRACK_LOW_RES;
                break;
        }
}

/**
    @brief  place the GPS into an appropriate tracking mode appropriate for
            the current state. Typically this is called when the gps is placed into
            high resolution mode to acquire a satellite lock, after which the gps
            is placed back in a more appropriate (read lower power) mode.
*/

void
gps_track_appropriately()
{
        gps_track_t track;

        switch (gps_state) {
        case GPS_STATE_NORMAL:
                switch (settings.gps.run_mode) {
                case GPS_RUN_MODE_HIGH_RES:
                        track = GPS_TRACK_HIGH_RES;
                        break;

                case GPS_RUN_MODE_PERIODIC:
                        track = GPS_TRACK_PERIODIC;
                        break;

                default:
                        track = GPS_TRACK_LOW_RES;
                        break;
                }

                if (track != gps_tracking)
                        gps_set_normal_tracking();
                break;

        case GPS_STATE_STATIONARY:
                if (gps_tracking != GPS_TRACK_PERIODIC)
                        gps_set_periodic_tracking(gps_state);
                break;

        default:
                break;
        }
}

/**
    @brief  place the GPS into the new requested state. This may include powering off or on the chip.
*/

void
gps_set_state(gps_state_t state)
{
        if (gps_state == GPS_STATE_OFF) {
                serial_power_on(&gps_port);
                gps_reset();
                gps_have_fix = false;
        }

        switch (state) {
        case GPS_STATE_OFF:
                EVENT_LOG(EVT_GPS_POWER_OFF, "power off");
                gps_standby_mode();
                serial_power_off(&gps_port);
                break;

        case GPS_STATE_STATIONARY:
                EVENT_LOG(EVT_GPS_STATIONARY, "stationary");
                gps_set_periodic_tracking(state);
                break;

        case GPS_STATE_NORMAL:
        default:
                EVENT_LOG(EVT_GPS_NORMAL, "normal");
                gps_set_normal_tracking();
                break;
        }

        gps_state = state;
}

#define GPS_PREC(x) (x / GPS_PRECISION), abs(x % GPS_PRECISION)

/**
    @brief  show the current position on the console (for debugging)
*/

void
gps_display_location()
{
        printf("[%d/%2.2d/%2.2d %2.2d:%2.2d:%2.2d] ",
               gps_loc.year, gps_loc.month, gps_loc.day, gps_loc.hour, gps_loc.minute, gps_loc.second);

        printf("%d.%5.5d , %d.%5.5d speed %d heading %d accel_heading %d\n",
               GPS_PREC(gps_lat), GPS_PREC(gps_lon),
               (uint32_t) (gps_loc.speed*100.0), (uint32_t)gps_loc.heading,
               gps_attr.accel_heading);
}

#define DOP_DIV(x) (x / (int)DOP_PRECISION), (x % (int)DOP_PRECISION)

/**
    @brief  show the satellite & DOP info on the console (for debugg)
*/

void
gps_display_satellites(log_attribute_t *p)
{
        printf(" sats %d hdop %d.%2.2d vdop %d.%2.2d pdop %d.%2.2d\n",
               p->satellites, DOP_DIV(p->hdop), DOP_DIV(p->vdop), DOP_DIV(p->pdop));
}

/// @}

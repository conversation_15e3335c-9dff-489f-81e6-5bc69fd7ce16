/** @{

    @ingroup    vms
    @file
    @brief      Board Napping
*/

#include "pelagic.h"
#include "nap.h"
#include "sun.h"
#include "signals.h"
#include "gps.h"
#ifdef TARGET_TEST
#include <stdlib.h>
#endif

uint32_t nap_sleep_time;

/**
    @brief  determine if its time to put the board to sleep for some period
    @note   There are four mutually exclusive nap modes:
            - off: no napping happens at all
            - day: nap during the day - between sunrise and sunset
            - night: nap at night - between sunset and sunrise
            - time: between specific hours each day

    @return true if the board should sleep now
*/

bool
is_nap_time()
{
        datetime_t dt;
        bool night;
        int16_t minute_start, minute_end, minute_now;

        if (!time_acquired)
                return false;

        night = is_nighttime();

        switch (settings.nap.mode) {
        case NAP_MODE_OFF:
                return false;

        case NAP_MODE_DAY:
                return !night;

        case NAP_MODE_NIGHT:
                return night;

        case NAP_MODE_TIME:
                if (gps_acquired_fix == false)
                        return false;

                seconds_to_datetime(rtc_read(), &dt);
                minute_now = (dt.hour * 60) + dt.minute;
                minute_start = settings.nap.minute_start;
                minute_end = settings.nap.minute_end;

                if (minute_start > minute_end) {
                        // passes thru midnight
                        return (minute_now >= minute_start || minute_now <  minute_end);
                } else {
                        return (minute_now >= minute_start && minute_now < minute_end);
                }
                break;

        default:
                return false;
        }
}

/**
    @brief  figure out how long to sleep for
    @return seconds to sleep
*/

uint32_t
nap_duration()
{
        int32_t secs;
        uint32_t now = rtc_read();
        datetime_t dt;

        if (nap_sleep_time)
                return nap_sleep_time;

        switch (settings.nap.mode) {
        case NAP_MODE_DAY:
                secs = sunset - now;
                break;

        case NAP_MODE_NIGHT:
                secs = sunrise - now;
                break;

        case NAP_MODE_TIME:
                seconds_to_datetime(now, &dt);

                secs = settings.nap.minute_end - ((dt.hour * 60) + dt.minute);
                if (secs < 0)   // setting spans midnight
                        secs += (24 * 60);

                secs = secs * MINUTE;
                break;

        default:        // not napping.
                secs = HOUR;
                break;
        }

        return (secs < 0) ? HOUR : secs;
}

/** @} */

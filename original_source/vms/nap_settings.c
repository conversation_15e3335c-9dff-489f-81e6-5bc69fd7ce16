/** @{

    @ingroup    vms
    @file

    @brief nap setting routines
*/

#include "pelagic.h"
#include "nap.h"
#include "signals.h"

const char *nap_mode_labels[] = {
        "off",
        "day",
        "night",
        "time"
};

/**
    @brief convert a hour minute string "hh:mm" to minutes (todo - better error handling)
    @param[out] minutes   area to store converted minutes
    @return true if string was successfully parse
*/

bool
parse_hour_minute(char *str, uint16_t *minutes)
{
        int len;
        char num[4];

        for (len = 0; len < 2; len++) {
                if (*str == ':' || *str == '\0')
                        break;
                num[len] = *str++;
        }

        if (len == 0)
                return false;

        num[len] = 0;
        *minutes = atoi(num) * 60;

        if (*str == '\0')   // Just hour, for backwards compatibility.
                return true;

        if (*str != ':')
                return false;

        str++;  // skip over colon :

        for (len = 0; len < 2; len++) {
                if (*str == '\0')
                        break;
                num[len] = *str++;
        }

        if (*str != '\0' || len == 0)
                return false;

        num[len] = 0;

        *minutes += atoi(num);

        if (*minutes >= 24*60)
                return false;

        return true;
}

/**
    @brief  dump the nap settings to a buffer
    @param[out] buffer  area to dump the settings
    @return bytes used
*/

#define HOUR_MIN(x) (x / 60), (x % 60)

int
nap_setting_dump(char *buffer)
{
        int len;
        uint8_t mode = settings.nap.mode;

        if (mode == NAP_MODE_TIME) {
                len = sprintf(buffer, "nap time %d:%2.2d %d:%2.2d\n",
                              HOUR_MIN(settings.nap.minute_start),
                              HOUR_MIN(settings.nap.minute_end));
        } else {
                len = sprintf(buffer, "nap %s\n", (mode >= ARRAY_SIZE(nap_mode_labels)) ? "off" : nap_mode_labels[mode]);
        }

        return len;
}

/**
    @brief  initialize the nap settings to defaults
*/

void
nap_settings_init()
{
        settings.nap.flags        = 0;
        settings.nap.mode         = 0;
        settings.nap.minute_start = 0;
        settings.nap.minute_end   = 0;

        settings.parking.last_accel.x = -1;
        settings.parking.last_accel.y = -1;
        settings.parking.last_accel.z = -1;
        settings.parking.last_mag.x = -1;
        settings.parking.last_mag.y = -1;
        settings.parking.last_mag.x = -1;
        settings.parking.last_heading = -1;
}

/**
    @brief  process nap setting commands
    @param[in]  argc    array size for argv
    @param[in]  argv    the setting command
    @return true if the setting was successful
    @note   the following commands are supported:
        - nap day : nap between sunrise and sunset
        - nap night : nap between sunset and sunrise
        - nap time START END : nap between the hours of START and END
        - nap off : clear any nap settings
*/

bool
nap_setting(int argc, char **argv)
{
        char *command = argv[0];
        uint8_t mode;
        uint16_t minute_start, minute_end;
        int i;

        if (argc == 0)
                return false;

        for (i = 0; i < ARRAY_SIZE(nap_mode_labels); i++) {
                if (strcmp(command, nap_mode_labels[i]) == 0) {
                        mode = i;
                        break;
                }
        }

        if (i == ARRAY_SIZE(nap_mode_labels)) {
                if (strcmp(command, "now") == 0) {
                        if (argc != 2)
                                return false;

                        nap_sleep_time = atoi(argv[1]);

                        if (nap_sleep_time)
                                osSignalSet(reboot_tid, REBOOT_SIGNAL_NAP);

                        return true;
                }

                return true;
        }

        switch (mode) {
        case NAP_MODE_OFF:
                EVENT_LOG(EVT_NAP_SET_OFF, "nap off");
                break;

        case NAP_MODE_DAY:
                EVENT_LOG(EVT_NAP_SET_DAY, "nap day");
                break;

        case NAP_MODE_NIGHT:
                EVENT_LOG(EVT_NAP_SET_NIGHT, "nap night");
                break;

        case NAP_MODE_TIME:
                if (argc != 3)
                        return false;

                if (parse_hour_minute(argv[1], &minute_start) == false)
                        return false;

                if (parse_hour_minute(argv[2], &minute_end) == false)
                        return false;

                // start cannot equal end..
                // which also handles 0 == 0
                if (minute_start == minute_end)
                        return false;

                settings.nap.minute_start = minute_start;
                settings.nap.minute_end = minute_end;

                EVENT_LOG2(EVT_NAP_SET_TIME, "nap set time", "start", EVT_16BIT, minute_start, "end", EVT_16BIT, minute_end);
                break;

        default:
                return false;
        }

        settings.nap.mode = mode;
        return true;
}

/** @} */

/**
   @ingroup vms
   @{
   @file

   @brief  GPS operation settings
*/

#include "pelagic.h"
#include "gps.h"
#include "signals.h"

// GPS settings
enum {
        GPS_SETTING_PERIODIC_ON_DEFAULT       = 15,
        GPS_SETTING_PERIODIC_ON_MIN           = 1,
        GPS_SETTING_PERIODIC_ON_MAX           = HOUR,

        GPS_SETTING_PERIODIC_OFF_DEFAULT      = 45,
        GPS_SETTING_PERIODIC_OFF_MIN          = 1,
        GPS_SETTING_PERIODIC_OFF_MAX          = HOUR,

        GPS_SETTING_IDLE_ON_DEFAULT           = 15,
        GPS_SETTING_IDLE_ON_MIN               = 15,
        GPS_SETTING_IDLE_ON_MAX               = HOUR,

        GPS_SETTING_IDLE_OFF_DEFAULT          = 5 * MINUTE,
        GPS_SETTING_IDLE_OFF_MIN              = 15,
        GPS_SETTING_IDLE_OFF_MAX              = HOUR,

        GPS_SETTING_IDLE_METER_DEFAULT        = 20,
        GPS_SETTING_IDLE_METER_MIN            = 10,
        GPS_SETTING_IDLE_METER_MAX            = 500,

        GPS_SETTING_DOCK_ON_DEFAULT           = 15,             // DEPRECATED
        GPS_SETTING_DOCK_ON_MIN               = 15,             // DEPRECATED
        GPS_SETTING_DOCK_ON_MAX               = HOUR,           // DEPRECATED

        GPS_SETTING_DOCK_OFF_DEFAULT          = 5*MINUTE,       // DEPRECATED
        GPS_SETTING_DOCK_OFF_MIN              = 15,             // DEPRECATED
        GPS_SETTING_DOCK_OFF_MAX              = HOUR,           // DEPRECATED

        GPS_SETTING_IDLE_TIME                = (15*MINUTE),
        GPS_SETTING_IDLE_TIME_MIN            = (5*MINUTE),
        GPS_SETTING_IDLE_TIME_MAX            = (1*HOUR),
};

const setting_key_t gps_run_mode_settings[] = {
        { .key="periodic", .value=GPS_RUN_MODE_PERIODIC },
        { .key="lowres",   .value=GPS_RUN_MODE_LOW_RES },
        { .key="hires",    .value=GPS_RUN_MODE_HIGH_RES},
};

const char *gps_run_mode_labels[] = {
        "periodic",
        "lowres",
        "hires"
};

const setting_key_t gps_send_flags[] = {
        { .key = "precision", .value = GPS_FLAG_SEND_PRECISION },
        { .key = "accel-raw", .value = GPS_FLAG_SEND_ACCEL_RAW },
};

bool gps_settings_run_mode(int argc, char **argv);
bool gps_settings_idle(int argc, char **argv);
bool gps_settings_idle_meters(int argc, char **argv);
bool gps_settings_idle_time(int argc, char **argv);
bool gps_settings_periodic(int argc, char **argv);
bool gps_settings_send(int argc, char **argv);
bool gps_settings_freq(int argc, char **argv);

setting_command_t gps_settings_commands[] = {
        { .name = "run", .command = gps_settings_run_mode },
        { .name = "idle", .command = gps_settings_idle },
        { .name = "idle-meters", .command = gps_settings_idle_meters },
        { .name = "idle-time", .command = gps_settings_idle_time },
        { .name = "periodic", .command = gps_settings_periodic },
        { .name = "send", .command = gps_settings_send },
        { .name = "freq", .command = gps_settings_freq },
};

/**
    @brief  initialize the GPS settings to their default values
*/

void
gps_settings_init()
{
        settings.gps.periodic_on  = GPS_SETTING_PERIODIC_ON_DEFAULT;
        settings.gps.periodic_off = GPS_SETTING_PERIODIC_OFF_DEFAULT;
        settings.gps.dock_on      = GPS_SETTING_DOCK_ON_DEFAULT;
        settings.gps.dock_off     = GPS_SETTING_DOCK_OFF_DEFAULT;
        settings.gps.idle_on      = GPS_SETTING_IDLE_ON_DEFAULT;
        settings.gps.idle_off     = GPS_SETTING_IDLE_OFF_DEFAULT;
        settings.gps.idle_meters  = GPS_SETTING_IDLE_METER_DEFAULT;
        settings.gps.run_mode     = GPS_RUN_MODE_LOW_RES;
        settings.gps.idle_time    = GPS_SETTING_IDLE_TIME;
        settings.gps.flags        = 0;
        settings.gps_log_freq     = 1;
}

/**
    @brief  verify the new settings are reasonable values
*/

void
gps_settings_verify()
{
        if (settings.gps.periodic_on < GPS_SETTING_PERIODIC_ON_MIN || settings.gps.periodic_on > GPS_SETTING_PERIODIC_ON_MAX)
                settings.gps.periodic_on = GPS_SETTING_PERIODIC_ON_DEFAULT;

        if (settings.gps.periodic_off < GPS_SETTING_PERIODIC_OFF_MIN || settings.gps.periodic_off > GPS_SETTING_PERIODIC_OFF_MAX)
                settings.gps.periodic_off = GPS_SETTING_PERIODIC_OFF_DEFAULT;

        if (settings.gps.idle_on < GPS_SETTING_IDLE_ON_MIN || settings.gps.idle_on > GPS_SETTING_IDLE_ON_MAX)
                settings.gps.idle_on = GPS_SETTING_IDLE_ON_DEFAULT;

        if (settings.gps.idle_off < GPS_SETTING_IDLE_OFF_MIN || settings.gps.idle_off > GPS_SETTING_IDLE_OFF_MAX)
                settings.gps.idle_off = GPS_SETTING_IDLE_OFF_DEFAULT;

        if (settings.gps.idle_meters < GPS_SETTING_IDLE_METER_MIN || settings.gps.idle_meters > GPS_SETTING_IDLE_METER_MAX)
                settings.gps.idle_meters = GPS_SETTING_IDLE_METER_DEFAULT;

        if (settings.gps.idle_time <= GPS_SETTING_IDLE_TIME_MIN || settings.gps.idle_time > GPS_SETTING_IDLE_TIME_MAX)
                settings.gps.idle_time = GPS_SETTING_IDLE_TIME;
}

/**
    @brief  notify the GPS thread that new settings are in effect
*/

void
gps_signal_setting_change(uint32_t setting)
{
        gps_settings_update |= setting;
        osSignalSet(gps_tid, GPS_SIGNAL_SETTINGS);
}

/**
    @brief  update the gps run mode setting (gps run newmode)
*/

bool
gps_settings_run_mode(int argc, char **argv)
{
        uint32_t new_run_mode;

        if (argc == 0)
                return false;

        if (!settings_find_key(argv[0], gps_run_mode_settings, ARRAY_SIZE(gps_run_mode_settings), &new_run_mode))
                return false;

        settings.gps.run_mode = new_run_mode;
        gps_signal_setting_change(GPS_SETTING_UPDATE_RUN_MODE);
        EVENT_LOG1(EVT_GPS_SET_RUN_MODE, "setting run", "new-mode", EVT_8BIT, new_run_mode);

        return true;
}

/**
    @brief  update the periodic on/off setting (gps idle on off)
*/

bool
gps_settings_periodic(int argc, char **argv)
{
        if (argc != 2)
                return false;

        settings.gps.periodic_on = atoi(argv[0]);
        settings.gps.periodic_off = atoi(argv[1]);
        gps_settings_verify();
        gps_signal_setting_change(GPS_SETTING_UPDATE_PERIODIC);
        EVENT_LOG2(EVT_GPS_SET_PERIODIC, "setting periodic", "on-secs", EVT_16BIT, settings.gps.periodic_on, "off-secs", EVT_16BIT, settings.gps.periodic_off);

        return true;
}

/**
    @brief  update the GPS idle on/off settings (gps idle on off)
*/

void
gps_settings_event_set_idle()
{
        EVENT_LOG4(EVT_GPS_SET_IDLE, "setting idle",
                   "on-secs", EVT_16BIT, settings.gps.idle_on,
                   "off-secs", EVT_16BIT, settings.gps.idle_off,
                   "meters", EVT_16BIT, settings.gps.idle_meters,
                   "time", EVT_16BIT, settings.gps.idle_time / 60);
}

/**
    @brief  update the GPS idle meters setting (gps idle-meters meters)
*/

bool
gps_settings_idle_meters(int argc, char **argv)
{
        if (argc != 1)
                return false;

        settings.gps.idle_meters = atoi(argv[0]);
        gps_settings_verify();

        gps_settings_event_set_idle();
        return true;
}

/**
    @brief  update the GPS idle time (gps idle-time time)
*/

bool
gps_settings_idle_time(int argc, char **argv)
{
        uint32_t minutes;

        if (argc != 1)
                return false;

        minutes = atoi(argv[0]);
        settings.gps.idle_time = minutes * 60;
        gps_settings_verify();

        gps_settings_event_set_idle();

        return true;
}

/**
    @brief  turn the GPS idle on or off (gps idle {on,off})
*/

bool
gps_settings_idle(int argc, char **argv)
{
        if (argc == 1) {
                if (strcmp(argv[0], "off") == 0) {
                        settings.gps.flags &= ~GPS_FLAG_IDLE_ON;
                } else if (strcmp(argv[0], "on") == 0) {
                        settings.gps.flags |= GPS_FLAG_IDLE_ON;
                } else {
                        return false;
                }

                gps_signal_setting_change(GPS_SETTING_UPDATE_IDLE);
                EVENT_LOG1(EVT_GPS_SET_IDLE_MODE, "idle mode", "on", EVT_BOOL, (settings.gps.flags & GPS_FLAG_IDLE_ON) != 0);
                return true;
        }

        if (argc != 2)
                return true;

        settings.gps.idle_on = atoi(argv[0]);
        settings.gps.idle_off = atoi(argv[1]);

        gps_settings_verify();
        gps_signal_setting_change(GPS_SETTING_UPDATE_IDLE);

        gps_settings_event_set_idle();
        return true;
}

/**
    @brief  send or don't send additional gps information (gps send item or gps send !item)
*/

bool
gps_settings_send(int argc, char **argv)
{
        bool unset = false;
        uint32_t flag;

        char *str;
        if (argc == 0)
                return false;

        for (int i = 0; i < argc; i++) {
                str = argv[i];

                if (strcmp(str, "none") == 0) {
                        settings.gps.flags &= GPS_FLAG_IDLE_ON;
                        continue;
                }

                if (*str == '!') {
                        str++;
                        unset = true;
                } else {
                        unset = false;
                }

                if (!settings_find_key(str, gps_send_flags, ARRAY_SIZE(gps_send_flags), &flag))
                        return false;

                if (unset)
                        settings.gps.flags &= ~flag;
                else
                        settings.gps.flags |= flag;
        }

        return true;
}

bool
gps_settings_freq(int argc, char **argv)
{
        int v;

        if (argc == 0) return false;

        v = atoi(argv[0]);
        if (v < 0 || v >= 255) return false;

        settings.gps_log_freq = v;
        return true;
}

/**
    @brief  dump the current GPS send settings into a buffer
    @return how many bytes were used
*/

int
gps_setting_send_dump(char *buffer)
{
        int len;

        len = sprintf(buffer, "gps send");

        if ((settings.gps.flags & ~GPS_FLAG_IDLE_ON) == 0) {
                len += sprintf(buffer+len, " none");
        } else {
                const setting_key_t *flag = gps_send_flags;
                for (int f = 0; f < ARRAY_SIZE(gps_send_flags); f++, flag++) {
                        if (flag->value & settings.gps.flags)
                                len += sprintf(buffer+len, " %s", flag->key);
                }
        }

        len += sprintf(buffer + len, "\n");
        return len;
}

/**
    @brief  parse GPS settings
*/

bool
gps_setting(int argc, char **argv)
{
        char *command = argv[0];
        char **cmd_argv = argv+1;
        int cmd_argc = argc-1;

        if (argc == 0)
                return false;

        setting_command_t *cmd = gps_settings_commands;
        for (int i = 0; i < ARRAY_SIZE(gps_settings_commands); i++, cmd++) {
                if (strcmp(command, cmd->name) == 0) {
                        return cmd->command(cmd_argc, cmd_argv);
                }
        }

        return false;
}

/**
    @brief  dump all GPS settings into a buffer
    @return how many bytes were used
*/

int
gps_settings_dump(char *buffer)
{
        int len;

        len = sprintf(buffer, "gps run %s\n",
                      (settings.gps.run_mode < ARRAY_SIZE(gps_run_mode_labels) ? gps_run_mode_labels[settings.gps.run_mode] : "unknown"));
        len += sprintf(buffer+len, "gps periodic %d %d\n", settings.gps.periodic_on, settings.gps.periodic_off);

        if (settings.gps.flags & GPS_FLAG_IDLE_ON) {
                len += sprintf(buffer+len, "gps idle %d %d\n", settings.gps.idle_on, settings.gps.idle_off);
                len += sprintf(buffer+len, "gps idle-meters %d\n", settings.gps.idle_meters);
                len += sprintf(buffer+len, "gps idle-time %d\n", settings.gps.idle_time / MINUTE);
        } else {
                len += sprintf(buffer+len, "gps idle off\n");
        }

        len += gps_setting_send_dump(buffer+len);
        len += sprintf(buffer + len, "gps freq %d\n", settings.gps_log_freq);
        return len;
}

/// @}

/** @{

    @file
    @ingroup vms
    @brief   Factory Test Standalone (for boards with LEDs)
*/

#include "pelagic.h"
#include "alarm.h"
#include "bnet.h"
#include "radio.h"
#include "signals.h"
#include "factory_test.h"
#include "provision.h"
#include "power.h"
#include "bodytrace.h"
#include "led.h"
#include <stdarg.h>

board_uid_t test_server_uid;
extern modem_info_t modem_info;

int vsprintf(char *buf, const char *fmt, va_list args);

void ft_led_tick();
osTimerDef(ft_led_timer, ft_led_tick);
osTimerId ft_led_timer_id;

static char ft_buffer[64];

/**
    @brief Toggle the blue LED
*/

void
ft_led_tick()
{
        led_toggle(LED_BLUE);
}

/**
    @brief report on test progress
    @param[in]  message printf like format string
    @param[in]  ...     optional arguments
*/

void
ft_update(const char *message, ...)
{
        va_list args;

        va_start(args, message);
        vsprintf(ft_buffer, message, args);
        va_end(args);

        uart_printf("ft: %s\n", ft_buffer);
}

/**
    @brief report on test results
    @param[in] result   success or specific test failure

    @note  A successful result will turn the green LED on while a failure will turn on the red LED.
           The routine will loop until power off.
*/

bool
ft_report_results(ft_result_t result)
{
        osTimerStop(ft_led_timer_id);

        led_off(LED_BLUE);

        if (result == FT_SUCCESS)
                led_on(LED_GREEN);
        else
                led_on(LED_RED);

        for (;;)
                osDelay(10000);
}

/**
    @brief  main factory test loop for the standalone configuration
*/

void
factory_test_standalone()
{
        const ft_device_t *device = ft_devices;
        ft_result_t result;

        led_off(LED_GREEN);

        ft_led_timer_id = osTimerCreate(osTimer(ft_led_timer), osTimerPeriodic, NULL);
        osTimerStart(ft_led_timer_id, 500);

        for (int i = 0; i < ft_device_count; i++, device++) {
                ft_update(device->name);

                result = device->test();
                if (result == FT_SUCCESS)
                        continue;

                ft_report_results(result);
                return;
        }

        result = modem_factory_test_and_report(result, 0);

        ft_report_results(result);
}

/**@} */

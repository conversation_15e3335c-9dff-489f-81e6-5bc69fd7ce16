/** @{

    @ingroup    vms
    @file
    @brief  PDS message routines
*/

#include "pelagic.h"
#include "gpio_api.h"
#include "firmware.h"
#include "compress.h"
#include "factory_test.h"
#include "modem.h"
#include "modem_bodytrace.h"
#include "modem_log.h"
#include "pds.h"

enum {
        COMPRESSION_THRESHOLD = 100,
};

/**
    @brief  send a message chunk
    @param[in]  log_type    Message Type to upload
    @param[in]  version     message version
    @param[in]  chunk_data  buffer to send
    @param[in]  chunk_size  buffer size
    @return
            - MODEM_SUCCESS chunk was uploaded successfully
            - other errors are possible
*/

modem_result_t
pds_send_chunk(uint8_t log_type,  uint8_t version, uint8_t *chunk_data, uint32_t chunk_size)
{
        uint32_t data_size, compress_size = 0;
        pds_chunk_header_v4_t header;
        uint8_t *data;
        modem_iovec_t vec[2];

        if (chunk_size >= COMPRESSION_THRESHOLD)
                compress_size = compress_flatten(the_buffer.modem, chunk_size);

        // Non-zero size means the data was compressed, zero means it increased
        if (compress_size) {
                data = compress_buffer;
                data_size = compress_size;
        } else {
                data = the_buffer.modem;
                data_size = chunk_size;
        }

#if 0
        if (log_type == PDS_TYPE_SETTINGS) {
                printf("Settings: chunk_size: %u, data_size: %u\n", chunk_size, data_size);
        }
#endif

        header.type    = log_type;
        header.version = version;
        header.flags   = compress_size ? PDS_CHUNK_FLAG_COMPRESSED : 0;

        EVENT_LOG3(EVT_PDS_SENDING, "sending",
                   "chunk", EVT_16BIT, 0,
                   "bytes", EVT_16BIT, chunk_size,
                   "compresss", EVT_16BIT, compress_size)

        vec[0].data = &header;
        vec[0].size = sizeof(header);
        vec[1].data = data;
        vec[1].size = data_size;

        return modem_send_data_vec(vec, ARRAY_SIZE(vec), MODEM_WAIT_NONE, MODEM_CMD_SEND_DATA);
}

/**
    @brief  read several flash pages and upload it, marking each page free after success
    @param[in]  log_type    log type being loaded
    @param[in]  version     log version
    @param[in]  log_read    partition stream
    @param[out] bytes_sent  byte count that was uploaded
    @return
            - MODEM_SUCCESS chunk was uploaded successfully
            - other errors are possible

*/

modem_result_t
pds_transmit_log_pages(uint8_t log_type, uint8_t version, flash_read_t *log_read, uint32_t *bytes_sent)
{
        int page;
        uint32_t chunk_size = 0, bytes;
        uint32_t page_nums[MODEM_FLASH_PAGES_PER_MSG];
        modem_result_t result;

        for (page = 0; page < MODEM_FLASH_PAGES_PER_MSG; page++) {
                bytes = flash_read_page(log_read, &the_buffer.modem[chunk_size], &page_nums[page]);

                if (bytes == 0)
                        break;

                chunk_size += bytes;
        }

        if (chunk_size == 0)
                return MODEM_EOF;

        result =  pds_send_chunk(log_type,  version, the_buffer.modem, chunk_size);

        if (result == MODEM_SUCCESS) {
                *bytes_sent += chunk_size;
                for (int i = 0; i < page; i++)
                        flash_store_page_mark_free(log_read->partition, page_nums[i]);
        }

        return result;

}

/**

    @brief  upload a log
    @param[in]  log_store   log in flash partition to upload
    @param[in]  log_type    log type
    @param[in]  log_version log version
    @param[in]  reset_log   function to call after a successful upload
    @return     true if the log was successfully uploaded
*/

bool
pds_send_log(flash_partition_t *log_store, uint8_t log_type, uint8_t log_version, void (*reset_log)(void))
{
        uint32_t total_bytes = 0, start;
        int chunk_count = 0, result;
        flash_read_t log_read;
        uint32_t chunk_start, elapsed;

        flash_store_set_busy(log_store, true);

        flash_read_init(log_store, &log_read, the_cache);

        start = clock_read();

        EVENT_LOG2(EVT_PDS_LOG_SEND, "start xmit",
                   "log", EVT_STRCONST, log_store->name,
                   "size", EVT_32BIT, log_store->bytes_stored);

        while (log_read.is_eof == false) {
                chunk_start = clock_read();

                result = pds_transmit_log_pages(log_type, log_version, &log_read, &total_bytes);


                if (result == MODEM_EOF)
                        break;

                chunk_count++;

                if (result != MODEM_SUCCESS) {
                        elapsed = clock_read() - chunk_start;

                        EVENT_LOG2(EVT_PDS_XMIT_FAIL, "xmit fail",
                                   "result", EVT_8BIT, result,
                                   "elapsed", EVT_16BIT, elapsed);
                        flash_store_set_busy(log_store, false);
                        return false;
                }

                result = modem_xmit_wait(total_bytes < MODEM_QUEUE_THRESHOLD ? MODEM_WAIT_NONE : MODEM_WAIT);
                elapsed = clock_read() - chunk_start;

                if (result != MODEM_SUCCESS) {
                        EVENT_LOG2(EVT_PDS_XMIT_FAIL, "xmit wait fail",
                                   "result", EVT_8BIT, result,
                                   "elapsed", EVT_16BIT, elapsed);
                        flash_store_set_busy(log_store, false);
                        return false;
                }

                EVENT_LOG1(EVT_PDS_CHUNK_SUCCESS, "chunk success", "elapsed", EVT_16BIT, elapsed);
        }

        // Wait until the final message is fully transmitted.
        if (chunk_count > 0)
                modem_xmit_wait(MODEM_WAIT);

        elapsed = clock_read() - start;

        if (elapsed == 0)
                elapsed = 1;

        if (modem_log)
                modem_log->transmit_secs += elapsed;

        EVENT_LOG2(EVT_PDS_LOG_SUCCESS, "xmit log success",
                   "elasped", EVT_16BIT, elapsed,
                   "bytes-per-sec", EVT_16BIT, total_bytes / elapsed);

        if (reset_log)
                reset_log();

        flash_store_set_busy(log_store, false);
        return true;
}

/** @} */

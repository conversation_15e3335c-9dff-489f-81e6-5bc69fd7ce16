#ifndef __POWER_H__
#define __POWER_H__

void battery_init();
uint16_t battery_read();
bool battery_charge_read();
bool battery_fault_read();
void board_check_power();

void solar_init();
uint16_t solar_read();
bool solar_present(int wait_time);

void power_management();
void power_compute_average(uint16_t battery);

extern uint16_t battery_avg;

typedef enum {
        POWER_FLAG_CHARGING = 0x01,
        POWER_FLAG_FAULTED  = 0x02
} power_flags_t;

typedef enum {
        POWER_STATE_UNKNOWN   = 0, // just booted, we don't know yet
        POWER_STATE_NORMAL    = 1, // Normal power - everything powered up
        POWER_STATE_LOW       = 2, // Low power - devices put into sleep or not powered up
        POWER_STATE_EXCELLENT = 3, // Overcharge - devices in full power mode to bleed off power
        POWER_STATE_CRITICAL  = 4  // Below - devices off, MCU placed into VLLS mode.
} power_state_t;

enum {
        // As actually measured in <PERSON>'s garage
        // calculated potatoes per volt = 12576.24
        BATTERY_V2_70 = 35332,
        BATTERY_V2_75 = 35385,
        BATTERY_V2_80 = 35311,
        BATTERY_V2_85 = 35809,
        BATTERY_V2_90 = 36451,
        BATTERY_V2_95 = 37061,
        BATTERY_V3_00 = 37706,
        BATTERY_V3_05 = 38358,
        BATTERY_V3_10 = 38977,
        BATTERY_V3_15 = 39604,
        BATTERY_V3_175 = 39930, // derived from potatoes:volts ratio
        BATTERY_V3_20 = 40220,
        BATTERY_V3_25 = 40849,
        BATTERY_V3_30 = 41486,
        BATTERY_V3_35 = 42126,
        BATTERY_V3_40 = 42771,
        BATTERY_V3_45 = 43394,
        BATTERY_V3_50 = 44014,
        BATTERY_V3_55 = 44658,
        BATTERY_V3_60 = 45257,
        BATTERY_V3_65 = 45930,
        BATTERY_V3_70 = 46326,  // from old code

        POTATOES_PER_VOLT = 12576,  // rounding error can suck it

        BATTERY_VOLTAGE_CRITICAL       = BATTERY_V3_00,
        BATTERY_VOLTAGE_LOW            = BATTERY_V3_10,
        BATTERY_VOLTAGE_GOOD_ENOUGH    = BATTERY_V3_175,
        BATTERY_VOLTAGE_GOOD           = BATTERY_V3_30,

        BATTERY_MODEM_VOLTAGE_CRITICAL = BATTERY_V3_00,
        BATTERY_MODEM_VOLTAGE_LOW      = BATTERY_V3_15,
        BATTERY_MODEM_VOLTAGE_GOOD     = BATTERY_V3_30,
        BATTERY_MODEM_VOLTAGE_GOFAST   = BATTERY_V3_35,
        BATTERY_MODEM_VOLTAGE_PREPARK  = BATTERY_V3_30,

        BATTERY_VOLTAGE_EXCELLENT      = BATTERY_V3_60,

        CRITICAL_POWER_SLEEP = 8 * HOUR,
};

extern volatile power_state_t board_power_state;
#endif

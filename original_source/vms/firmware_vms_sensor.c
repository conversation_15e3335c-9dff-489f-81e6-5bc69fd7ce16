/** @{

    @ingroup    vms
    @file
    @brief      sensor firmware image storage on VMS
*/

#include "pelagic.h"
#include "firmware.h"
#include "memmap.h"
#include "led.h"
#include "system_file.h"

firmware_header_t firmware_sensor_header;
flash_read_t firmware_sensor_read;
uint8_t firmware_sensor_cache[FLASH_PAGE_SIZE] __attribute__ ((aligned (4)));

/**
    @brief  verify firmware image is valid (length okay, crc valid, etc.)
    @param[in]  header  firmware info
    @param[in]  read    stream to firmware partition
    @return true if image is valid
*/

bool
firmware_is_valid(firmware_header_t *header, flash_read_t *read)
{
        uint16_t crc = 0;
        uint32_t length = header->length, bytes;

        if (flash_read_seek(read, sizeof(firmware_header_t)) == false)
                return false;

        while (length > 0) {
                bytes = (length > sizeof(the_buffer.firmware) ? sizeof(the_buffer.firmware) : length);

                if (flash_read_block(read, the_buffer.firmware, bytes) != bytes)
                        return false;

                length -= bytes;
                crc = crc16_with_seed(the_buffer.firmware, bytes, crc);
        }

        return (crc == header->crc16);
}

/**
    @brief  initialize and verify the sensor firmware partition
*/

void
firmware_sensor_init()
{
        int bytes;

        flash_read_init(&firmware_sensor_partition, &firmware_sensor_read, firmware_sensor_cache);

        bytes = flash_read_block(&firmware_sensor_read, &firmware_sensor_header, sizeof(firmware_sensor_header));
        if (bytes != sizeof(firmware_sensor_header)) {
                firmware_sensor_header.buildstamp = 0;
                return;
        }

        if (firmware_sensor_header.buildstamp == 0xffffffff || firmware_sensor_partition.size < firmware_sensor_header.length) {
                uart_printf("SENSOR size error part size %d, length %d\n", firmware_sensor_partition.size, firmware_sensor_header.length);
                firmware_sensor_header.buildstamp = 0;
                return;
        } else if (!firmware_is_valid(&firmware_sensor_header, &firmware_sensor_read)) {
                firmware_sensor_header.buildstamp = 0;
                EVENT_LOG1(EVT_FIRMWARE_IMAGE_CRC_ERROR, "firmware image crc", "type", EVT_8BIT, DEVICE_SENSOR);
        }
}

/** @} */

/** @{

    @ingroup    testbox
    @file

    @brief      Abort thread
*/

#include "pelagic.h"
#include "test_server.h"

extern osThreadId main_tid;

/**
    @brief  checks once a second to see if the user hit <ESC> to abort the test.
*/

void abort_thread(void const *arg) {

    for (;;) {
        if (test_server_running) {
            if (have_esc()) {
                uart_printf("\nAborting\n");
                osSignalSet(main_tid, TS_SIGNAL_ABORT);
            }
        }

        osDelay(1000);
    }
}

/** @} */

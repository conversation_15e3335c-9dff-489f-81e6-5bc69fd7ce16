#include "pelagic.h"
#include "bnet.h"
#include "bnet_factory.h"
#include "factory_test.h"
#include "signals.h"
#include "alarm.h"
#include "radio.h"
#include "test_server.h"
#include "led.h"
#include "imei.h"

enum {
    START_TEST_WAIT_MS = (1 * 1000), // 1 second wait
    TESTING_WAIT_MS    = (1 * MINUTE * 1000), // time out after 5 minutes.

    RESPONSE_TIMEOUT  = 30,
    FINISH_WAIT = 20,
};

typedef enum {
    STAGE_SEND_TEST  = 0,
    STAGE_TESTING    = 1,
    STAGE_FINISHED   = 2
} test_stage_t;

board_uid_t test_board;

volatile bool test_server_running = false;

bool blink_running = false;
void led_blink_toggle();
osTimerDef(led_blink_timer, led_blink_toggle);
osTimerId led_blink_timer_id;

extern imei_t target_imei;

alarm_t test_buzzer;

void led_blink_toggle() {
#ifdef HAVE_LED
    led_toggle(LED_BLUE);
#endif
}

void led_blink_start() {
    if (!blink_running) {
        osTimerStart(led_blink_timer_id, 750);
        blink_running = true;
    }
}

void led_blink_stop() {
    if (blink_running) {
        osTimerStop(led_blink_timer_id);
        blink_running = false;
    }
}

void led_all_off() {
    led_blink_stop();
#ifdef HAVE_LED
    led_off(LED_RED);
    led_off(LED_GREEN);
    led_off(LED_BLUE);
#endif
}
void test_success(uint64_t imei) {
    printf("\n+++\n+++ SUCCESS - IMEI %6.6d%9.9d\n+++\n\n", (uint32_t)(imei / 1000000000), (uint32_t)(imei % 1000000000));
    led_all_off();
#ifdef HAVE_LED
    led_on(LED_GREEN);
#endif
}

void test_failed(ft_result_t result) {
    printf("\n!!!\n!!! FAILURE CODE %d\n!!!\n", result);
    uint64_t target = get_uint64(target_imei);
    printf("!!! TARGET IMEI %6.6d%9.9d\n!!!\n\n", (uint32_t)(target / 1000000000), (uint32_t)(target % 1000000000));
    led_all_off();
#ifdef HAVE_LED
    led_on(LED_RED);
#endif
}

void ack_packet(uint8_t type, uint8_t flags) {
    bnet_ft_ack_t *ack_pkt = (bnet_ft_ack_t *) the_buffer.test;

    ack_pkt->type = type;
    ack_pkt->flags = flags;
    uid_copy(ack_pkt->to_uid, test_board);
    radio_send(ack_pkt, sizeof(*ack_pkt));
}


void send_test_start_packet() {
    bnet_ft_target_t start_pkt;

    start_pkt.type = BNET_TYPE_FACTORY_TEST_TARGET;
    imei_copy(start_pkt.to_imei, target_imei);
    start_pkt.deploy_mode = FACTORY_MODE_PCB;

    uid_set_me(start_pkt.from_uid);
    radio_send(&start_pkt, sizeof(start_pkt));
    led_blink_start();
    uart_printf(".");
}


void uid_mismatch(char *msg, board_uid_t wanted_uid, board_uid_t got_uid) {
    //uart_printf("%s - uid mismatch got %Z wanted %Z\n", msg, got_uid, wanted_uid);
}

void announce_start() {
    uart_printf("\n> Sending start test packet\n");
    led_all_off();
}

void test_server_loop() {
    test_stage_t stage = STAGE_SEND_TEST;
    uint8_t *type = the_buffer.test, last_sequence = 0;
    uint32_t coverup_time = 0, last_response = 0;
    uint16_t signals;
    ft_result_t test_result;
    uint64_t imei;
    int bytes;
    bnet_ft_finish_t *finish_pkt = (bnet_ft_finish_t *) the_buffer.test;
    bnet_ft_begin_t *begin_pkt = (bnet_ft_begin_t *)the_buffer.test;
    bnet_ft_update_t *test_update_pkt = (bnet_ft_update_t *)the_buffer.test;

    led_blink_timer_id = osTimerCreate(osTimer(led_blink_timer), osTimerPeriodic, NULL);

    uid_clear(test_board);

    test_server_running = true;

    radio_flush();

    send_test_start_packet();
    announce_start();

    alarm_start_periodic(&test_buzzer, 1);

    for (;;) {
        bytes = radio_read_signal(the_buffer.test, osWaitForever, NULL, &signals);

        if (signals & TS_SIGNAL_ABORT) {
            alarm_cancel(&test_buzzer);
            uart_printf("aborting\n");
            test_server_running = false;
            led_all_off();
            return;
        }

        if (signals & ALARM_SIGNAL_BUZZER) {
            switch (stage) {
            case STAGE_SEND_TEST:
                //uart_printf("sending test packet\n");
                send_test_start_packet();
                break;

            case STAGE_FINISHED:
                uart_printf(" %d", coverup_time - clock_read());
                if (coverup_time <= clock_read()) {
                    uid_clear(test_board);
                    led_all_off();
                    uart_printf("\n");
                    alarm_cancel(&test_buzzer);
                    return;
                }
                break;

            default:
                if (last_response+RESPONSE_TIMEOUT > clock_read())
                    continue;

                uart_printf("no response: now %d, last response %d\n", clock_read(), last_response);
                test_failed(FT_NO_REPONSE);
                coverup_time = clock_read() + FINISH_WAIT;
                uart_printf("@ start test in %d ",FINISH_WAIT);
                uid_clear(test_board);
                stage = STAGE_FINISHED;
                continue;
            }
        }

        if (bytes == 0)
            continue;

        //uart_printf("bytes %d, type 0x%x\n", bytes, *type);

        switch (*type) {
        case BNET_TYPE_FACTORY_BEGIN:
            if (bytes != sizeof(bnet_ft_begin_t)) {
                uart_printf("factory begin bytes %d, wanted %d\n", bytes, sizeof(bnet_ft_begin_t));
                continue;
            }

            if (!uid_match_me(begin_pkt->to_uid))
                continue;

            last_response = clock_read();
            if (stage == STAGE_SEND_TEST) {
                uart_printf("> begining test %Z\n", begin_pkt->from_uid);
                uid_copy(test_board, begin_pkt->from_uid);
                stage = STAGE_TESTING;
            } else if (stage != STAGE_TESTING || !uid_match(begin_pkt->from_uid, test_board)) {
                continue;
            }
#ifdef HAVE_LED
            led_blink_stop(LED_BLUE);
#endif
            ack_packet(BNET_TYPE_FACTORY_BEGIN_ACK, 0);
            last_sequence = 0;
#ifdef HAVE_LED
            led_on(LED_BLUE);
#endif
            break;

        case BNET_TYPE_FACTORY_UPDATE:
            if (bytes != (sizeof(bnet_ft_update_t) + test_update_pkt->size)) {
                uart_printf("ft update: byte sized want %d, got %d\n", sizeof(bnet_ft_update_t)+test_update_pkt->size, bytes);
                continue;
            }

            if (!uid_match(test_board, test_update_pkt->from_uid))
                continue;

            last_response = clock_read();
            if (test_update_pkt->sequence != last_sequence) {
                uart_printf("< Update: ");
                serial_write(&console_uart, test_update_pkt->data, test_update_pkt->size);
                uart_printf("\n");
                last_sequence = test_update_pkt->sequence;
            }

            ack_packet(BNET_TYPE_FACTORY_UPDATE_ACK, 0);
            continue;

        case BNET_TYPE_FACTORY_FINISH:
            if (bytes != sizeof(bnet_ft_finish_t))
                continue;

            if (!uid_match(finish_pkt->from_uid, test_board))
                continue;

            test_result = get_uint16(finish_pkt->result);
            imei = get_uint64(finish_pkt->imei);

            ack_packet(BNET_TYPE_FACTORY_FINISH_ACK, 0);

            uart_printf(" * ");
            if (stage == STAGE_TESTING) {
                stage = STAGE_FINISHED;
                if (test_result == FT_SUCCESS) {
                    test_success(imei);
                } else {
                    test_failed(test_result);
                }
                coverup_time = clock_read() + FINISH_WAIT;
                uart_printf("@ finished in %d ",FINISH_WAIT);
            }
            break;
        }
    }

}

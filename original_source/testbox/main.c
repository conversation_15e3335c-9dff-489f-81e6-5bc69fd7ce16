/** @{

    @ingroup    testbox
    @file

    @brief      Test Box main thread
*/

#include "pelagic.h"
#include "semihost_api.h"
#include "console.h"
#include "led.h"
#include "bnet.h"
#include "board_fault.h"
#include "system_file.h"
#include "radio.h"
#include "alarm.h"
#include "shell.h"
#include "board_info.h"
#include "bnet_new_factory.h"

// Used by multiple locations
shared_buffer_t the_buffer __attribute__ (( aligned (4) ));

extern void random_setup_seed();
void abort_thread(void const *arg);
void printf_init();
bool send_new_ft_packet(uint8_t);
bnet_new_ft_response_t* listen_for_response();
void get_imei();
void interactive_loop();
void automatic_loop();
void print_response();
void finish_test();
void bnet_new_factory_response_ack(uint8_t type, imei_t target_imei);

osThreadDef(abort_thread, osPriorityNormal, 1, 0);
osThreadId abort_tid;

osThreadId main_tid;


char imei[16];
volatile bool have_imei;
volatile bool tests_running;
volatile bool test_buzzer;
imei_t target_imei;
alarm_t timeout;

enum {
    TEST_TIMEOUT        = 15 * MINUTE,
    NO_RESPONSE_TIMEOUT = 1 * MINUTE,
};


int main() {

    serial_console_init(true);

    imei_to_i(imei, target_imei);

    rtc_init();
    uid_init();
    board_info_init();
    board_info_ensure_nfc_imei();

    announce("Test Box");

    main_tid = osThreadGetId();

    abort_tid = osThreadCreate(osThread(abort_thread), NULL);

    random_setup_seed();

    radio_init(true);
    radio_set_rx_mode(RADIO_RX_MODE_HIGH);

    serial_flush(&console_uart);

    for (;;) {
        if (!have_imei) {
            get_imei();
            continue;
        }

#ifdef TESTBOX_INTERACTIVE
        interactive_loop();
#else
        automatic_loop();
#endif
    }
}

void automatic_loop() {
    uint32_t now, last_received;
    bnet_new_ft_response_t *response;

    // Enter Test Mode
    if (send_new_ft_packet(BNET_TYPE_NEW_FT_ENTER_TEST_MODE)) {
        tests_running = true;

#ifndef TESTBOX_INTERACTIVE
        alarm_start_set(&timeout, TEST_TIMEOUT, &test_buzzer);
#endif

    } else {
        uart_printf("%s: [ERROR] Failed Enter Test Mode\n", imei);
        return finish_test();
    }

    // Start Self Test
    if (send_new_ft_packet(BNET_TYPE_NEW_FT_START_SELF_TEST)) {
        response = listen_for_response();
        if (response == NULL) {
            uart_printf("%s: [ERROR] Failed Start Self Test\n", imei);
            return finish_test();
        }
    } else {
        uart_printf("%s: [ERROR] Failed Start Self Test\n", imei);
        return finish_test();
    }

    // Report Status
    last_received = clock_read();
    for(;;) {
        now = clock_read();

        if (test_buzzer) {
            uart_printf("%s: [ERROR] Timeout\n", imei);
            return finish_test();
        }

        /* Pending Abort mode */
        // if (!tests_running) {
        //     // We may have aborted
        //     return;
        // }

        if (send_new_ft_packet(BNET_TYPE_NEW_FT_SELF_TEST_STATUS)) {
            response = listen_for_response();

            if (response == NULL) {
                uart_printf("%s: last_received delta: %d\n", imei, last_received - now);
                if (last_received - now > NO_RESPONSE_TIMEOUT) {
                    uart_printf("%s: [ERROR] Device Not Responding\n", imei);
                    return finish_test();
                } else {
                    break;
                }
            }

            last_received = now;
            switch (response->response_code) {
                case NEW_FT_RESPONSE_TESTS_RUNNING:
                    if (response->message_size) {
                        uart_printf("%s: [TESTS RUNNING]: \"%.*s\"\n", imei, response->message_size, response->message);
                    } else {
                        uart_printf("%s: [TESTS RUNNING]\n", imei);
                    }
                    break;

                case NEW_FT_RESPONSE_PASS:
                    uart_printf("\n\n%s: [PASS]\n\n\n", imei);
                    send_new_ft_packet(BNET_TYPE_NEW_FT_EXIT_TEST_MODE);
                    return finish_test();

                case NEW_FT_RESPONSE_FAIL:
                    uart_printf("\n\n%s: [FAIL]: \"%.*s\"\n", imei, response->message_size, response->message);
                    send_new_ft_packet(BNET_TYPE_NEW_FT_EXIT_TEST_MODE);
                    return finish_test();

                case NEW_FT_RESPONSE_UNKNOWN:
                    uart_printf("%s: [ERROR] Unknown Response Code\n", imei);
                    send_new_ft_packet(BNET_TYPE_NEW_FT_EXIT_TEST_MODE);
                    return finish_test();

                case NEW_FT_RESPONSE_NOT_IN_FT_MODE:
                    uart_printf("%s: [RESTART]\n", imei);
                    return;

                default:
                    break;
            }
        } else {
            if (last_received - now > NO_RESPONSE_TIMEOUT) {
                uart_printf("%s: [ERROR] Device Not Responding\n", imei);
                return finish_test();
            } else {
                break;
            }
        }

        osSignalWait(0, 1000);
    }

}

void interactive_loop() {
    char ch;
    bnet_new_ft_response_t *response;

    uart_printf("\nSelect a command for %s:\n", imei);
    uart_printf("e  - Enter test mode\n");
    uart_printf("v  - Report Voltages\n");
    uart_printf("d  - Deep Sleep\n");
    uart_printf("t  - Start Tests\n");
    uart_printf("s  - Report Test Status\n");
    uart_printf("x  - Exit test mode\n");
    uart_printf("X  - Exit test mode and switch to Shipping Mode\n");
    uart_printf("command? ");

    serial_read(&console_uart, &ch, 1);
    uart_printf("%c\n", ch);

    switch (ch) {
    case 'e':
        if (send_new_ft_packet(BNET_TYPE_NEW_FT_ENTER_TEST_MODE)) {
            tests_running = true;
        }
        break;
    case 'v':
        if (send_new_ft_packet(BNET_TYPE_NEW_FT_REPORT_VOLTAGES)) {
            response = listen_for_response();
            print_response(response);
        }
        break;
    case 'd':
        if (send_new_ft_packet(BNET_TYPE_NEW_FT_ENTER_DEEP_SLEEP)) {
            response = listen_for_response();
            print_response(response);
        }
        break;
    case 't':
        if (send_new_ft_packet(BNET_TYPE_NEW_FT_START_SELF_TEST)) {
            response = listen_for_response();
            print_response(response);
        }
        break;
    case 's':
        if (send_new_ft_packet(BNET_TYPE_NEW_FT_SELF_TEST_STATUS)) {
            response = listen_for_response();
            print_response(response);
        }
        break;
    case 'x':
        if (send_new_ft_packet(BNET_TYPE_NEW_FT_EXIT_TEST_MODE)) {
            finish_test();
        }
        break;
    case 'X':
        if (send_new_ft_packet(BNET_TYPE_NEW_FT_SHIP)) {
            finish_test();
        }
        break;
    default:
        uart_printf("invalid character.\n");
        break;
    }
}

void finish_test() {
    have_imei = false;
    tests_running = false;
}

void get_imei() {
    readline("\nTarget IMEI? ", imei, 16);
    if (imei_valid_str(imei)) {
        imei_to_i(imei, target_imei);
        have_imei = true;
        uart_printf("\n\n[START]\n\n");
    }
}

bool send_new_ft_packet(uint8_t type) {
    int bytes;
    bool success = false;
    uint32_t now, last_sent;
    volatile bool buzzer;
    alarm_t timeout;
    bnet_new_ft_request_t *request = (bnet_new_ft_request_t *)bnet_send_buffer;
    bnet_new_ft_request_ack_t *ack = (bnet_new_ft_request_ack_t *)bnet_receive_buffer;

    last_sent = 0;
    alarm_start_set(&timeout, 10, &buzzer);
    for (;;) {
        now = clock_read();

        request->type = type;
        uid_set_me(request->from_uid);
        imei_copy(request->to_imei, target_imei);

        if (last_sent != now) {
            radio_send(request, sizeof(bnet_new_ft_request_t));
            last_sent = now;
        }

        bytes = radio_read(ack, 1000, NULL);

        if (buzzer) {
            // uart_printf("! timeout on ack\n");
            break;
        }

        if (bytes == sizeof(bnet_new_ft_request_ack_t) && ack->type == (type | BNET_ACK) && uid_match_me(ack->to_uid)) {
            alarm_cancel(&timeout);

            success = true;
            break;
        }
    }

    // uart_printf("\n");
    return success;
}

void bnet_new_factory_response_ack(uint8_t type, imei_t target_imei) {
    bnet_new_ft_response_ack_t *ack = (bnet_new_ft_response_ack_t *)bnet_send_buffer;

    ack->type = type | BNET_ACK;
    uid_set_me(ack->from_uid);
    imei_copy(ack->to_imei, target_imei);

    radio_send(ack, sizeof(bnet_new_ft_response_ack_t));
}

bnet_new_ft_response_t* listen_for_response() {
    int bytes;
    volatile bool buzzer;
    alarm_t timeout;

    bnet_new_ft_response_t *response = (bnet_new_ft_response_t *)bnet_receive_buffer;

    alarm_start_set(&timeout, 10, &buzzer);
    for (;;) {
        bytes = radio_read(response, 1000, NULL);

        if (buzzer) {
            // uart_printf("! timeout on response\n");
            break;
        }

        if (response->type == BNET_TYPE_NEW_FT_RESPONSE && bytes == sizeof(bnet_new_ft_response_t) + response->message_size && uid_match_me(response->to_uid)) {
            alarm_cancel(&timeout);

            bnet_new_factory_response_ack(BNET_TYPE_NEW_FT_RESPONSE, target_imei);
            return response;
        }
    }
    return NULL;
}

void print_response (bnet_new_ft_response_t *response) {
    switch (response->response_code) {
    case NEW_FT_RESPONSE_UNKNOWN:
        if (response->message_size) {
            uart_printf("[%s] UNKNOWN: \"%.*s\"\n", imei, response->message_size, response->message);
        } else {
            uart_printf("[%s] UNKNOWN\n", imei);
        }
        break;
    case NEW_FT_RESPONSE_PASS:
        if (response->message_size) {
            uart_printf("[%s] PASS: \"%.*s\"\n", imei, response->message_size, response->message);
        } else {
            uart_printf("[%s] PASS\n", imei);
        }
        break;
    case NEW_FT_RESPONSE_FAIL:
        if (response->message_size) {
            uart_printf("[%s] FAIL: \"%.*s\"\n", imei, response->message_size, response->message);
        } else {
            uart_printf("[%s] FAIL\n", imei);
        }
        break;
    case NEW_FT_RESPONSE_TESTS_RUNNING:
        if (response->message_size) {
            uart_printf("[%s] TESTS_RUNNING: \"%.*s\"\n", imei, response->message_size, response->message);
        } else {
            uart_printf("[%s] TESTS_RUNNING\n", imei);
        }
        break;
    case NEW_FT_RESPONSE_VOLTAGE_REPORT:
        uart_printf("[%s] Battery %d, Solar %d\n", imei, response->data[0], response->data[1]);
        break;
    case NEW_FT_RESPONSE_NOT_IN_FT_MODE:
        uart_printf("[%s] NOT_IN_FT_MODE\n", imei);
        break;
    default:
        break;
    }
}

/**
    @brief  checks once a second to see if the user hit <ESC> to abort the test.
*/

void abort_thread(void const *arg) {
    // volatile int retries;

    for (;;) {
        if (tests_running) {
            if (have_esc()) {
                // Aborting currently causes a fault on the VMS, so turning this off for now.


                // uart_printf("\nAborting\n");

                // retries = 0;
                // while (retries < 10 && !send_new_ft_packet(BNET_TYPE_NEW_FT_EXIT_TEST_MODE)) {
                //     retries++;
                // }
                // finish_test();
            }
        }
        osDelay(1000);
    }
}

/** @} */


# VMS Operations #


### Activity ###

* Upload GPS coordinates & temperature recordings, and event log every 3 to 6 hours.
* Sample battery & solar voltage, every 5 minutes.


### Led Indicators (pre-V0.06 hardware) ###

* GREEN LED -  will flash for power on. If its on more than 1/4 second it may indicate something is wrong.
* BLUE LED - The blue led will turn on about 1 second after power on and stay on until the first valid GPS sentence is received. This may a couple of minutes while the gps chip acquires a satellite lock.
* BLUE LED w/DIP2 on - will flash every second when the modem is ready to be reprogrammed
* A constant red led means the board has experienced too many faults.

### DIP Settings (pre-V0.06 hardware) ###

Set the DIP switches BEFORE powering on the board.
POWER CYCLE the board when changing the DIP switches.

* DIP1 on - by-pass deployment check
* DIP2 on - place modem into firmware-update modem, also by-passes deployment check. Board will need to be power cycled after this is done.
* DIP3 on - demo mode

### Demo mode (DIP3 set on): (pre-V0.06 hardware)  ###

* Both the green & blue leds will the same as above for power on
* The blue led will blink for every valid GPS sentence received
* Uploading of the logs will occur once a minute.
* The green led will blink when the bodytrace modem is powered up and stop blinking when it turns off. (approx once a minute)
* The red led will blink briefly for each packet received over the radio interface.

# SENSOR Operations #

### Activity ###

The normal operation for the sensor is to wake every 5 minutes, attempt to send a temperature packet up waiting for acknowledgement up to 20 times. Time out is 350 ms waiting for an ack.

### DIP Settings (pre-V0.06 hardware)  ###

* DIP1 on - Shell mode - board will stay powered up until it is power cycled, and a shell is activated on the console. No temp packets are sent.
* DIP2 on - Fast mode - deep sleep for only 5 seconds instead of 5 minutes
* DIP3 on - Demo mode - stay awake and send a temp packet every 2 seconds.

#include "pelagic.h"
#include "humidity.h"
#include "i2c_api.h"
#include "gpio_api.h"
#include "factory_test.h"


enum {
        SI7020_READ_HUMIDITY    = 0xe5,
        SI7020_READ_TEMP        = 0xe3,
        SI7020_RESET            = 0xfe,

        SI7020_READ_SERIAL1_0   = 0xfa,
        SI7020_READ_SERIAL1_1   = 0x0f,

        SI7020_READ_SERIAL2_0   = 0xfc,
        SI7020_READ_SERIAL2_1   = 0xc9,

        SI7020_POWER_UP_TIME_MS = 80,
        SI7020_HUMIDITY_CONV_MS = 20,
        SI7020_TEMP_CONV_MS     = 20,
        SI7020_RESET_DELAY_MS   = 20,

        SI7020_CHIP_ID          = 0x14,
        SI7021_CHIP_ID          = 0x15,

        HUMIDITY_TEST_LIMIT     = 90,   // bad chip?
};

bool si7020_pin_initted = false;
bool humidity_initted = false;

i2c_t i2c_bus;

gpio_t si7020_power_pin;

void
si7020_pin_setup()
{
        if (si7020_pin_initted)
                return;

#ifndef HAVE_PREV1
        gpio_init_out_ex(&si7020_power_pin, SI7020_PWR_SWITCH, 0);
#endif

        i2c_init(&i2c_bus, IMU_SDA, IMU_SCL);
        si7020_pin_initted = true;
}

void
si7020_power_on()
{
#ifndef HAVE_PREV1
        gpio_write(&si7020_power_pin, 1);
        osDelay(SI7020_POWER_UP_TIME_MS);
#endif
        i2c_power_on(&i2c_bus);
}

void
si7020_power_off()
{
        i2c_power_off(&i2c_bus);
#ifndef HAVE_PREV1
        gpio_write(&si7020_power_pin, 0);
#endif
}

bool
si7020_write(const uint8_t *data, int size, bool stop)
{
        int bytes;

        bytes = i2c_write(&i2c_bus, HUMIDITY_I2C_ADDR, (char *)data, size, stop);

        if (bytes == size)
                return true;

        uart_printf("si7020 write : not found, command=[0x%x]\n", data[0]);
        return false;
}

bool
si7020_read(uint8_t *data, int size)
{
        int bytes;

        bytes = i2c_read(&i2c_bus, HUMIDITY_I2C_ADDR, (char *)data, size, 1);

        if (bytes == size)
                return true;

        //uart_printf("not found\n");
        return false;
}

bool
si7020_reset()
{
        uint8_t reset_cmd[] = { SI7020_RESET };

        if (si7020_write(reset_cmd, sizeof(reset_cmd), true) == false)
                return false;

        osDelay(SI7020_POWER_UP_TIME_MS);
        return true;
}

bool
si7020_have_chip()
{
        uint8_t serial2_cmd[] = { SI7020_READ_SERIAL2_0, SI7020_READ_SERIAL2_1 };
        uint8_t serial_id[4];

        if (si7020_write(serial2_cmd, sizeof(serial2_cmd), false) == false)
                return false;

        if (si7020_read(serial_id, sizeof(serial_id)) == false)
                return false;

        if (serial_id[0] != SI7020_CHIP_ID && serial_id[0] != SI7021_CHIP_ID)
                return false;

        return true;
}

bool
humidity_init()
{
        if (humidity_initted)
                return true;

        si7020_pin_setup();
        si7020_power_on();

        if (si7020_reset() == false)
                goto not_present;

        if (si7020_have_chip() == false)
                goto not_present;

        si7020_power_off();
        humidity_initted = true;

        EVENT_LOG(EVT_HUMIDITY_INIT, "si7020 init");
        return true;

not_present:
        si7020_power_off();
        EVENT_LOG1(EVT_HUMIDITY_NOT_PRESENT, "not present", "chip", EVT_STRCONST, "si7020");
        return false;
}

uint8_t
si7020_humidity_read()
{
        uint8_t rh_cmd[] = { SI7020_READ_HUMIDITY };
        uint8_t code[2];
        uint32_t result;

        if (si7020_write(rh_cmd, sizeof(rh_cmd), true) == false) {
                return 0;
        }

        osDelay(SI7020_HUMIDITY_CONV_MS);

        if (si7020_read(code, sizeof(code)) == false)
                return 0;

        result = (code[0] << 8) | code[1];
        result = ((125 * result) >> 16) - 6;
        return result;

}

uint8_t
humidity_read()
{
        uint8_t result;

        si7020_power_on();
        result = si7020_humidity_read();
        si7020_power_off();

        return result;
}

int16_t
si7020_temp_read()
{
        uint8_t rh_cmd[] = { SI7020_READ_TEMP };
        uint8_t code[2];
        uint32_t result;

        if (humidity_initted == false)
                return 0;

        if (si7020_write(rh_cmd, sizeof(rh_cmd), true) == false)
                return 0;

        if (si7020_read(code, sizeof(code)) == false)
                return 0;

        result = ((uint32_t)code[0] << 8) | code[1];
        result = (((17572 * result) >> 16) - 4685);

        return (result / 10);
}

#ifdef HAVE_FACTORY_TEST
ft_result_t
humidity_factory_test()
{
        uint16_t value;

        ft_update("si7020: start");
        si7020_pin_setup();
        si7020_power_on();

        if (si7020_reset() == false) {
                ft_update("si7020: no response");
                si7020_power_off();
                return FT_HUMIDITY_UNREPONSIVE;
        }

        if (si7020_have_chip() == false) {
                ft_update("si7020: chip not present");
                si7020_power_off();
                return FT_HUMIDITY_UNREPONSIVE;
        }

        ft_update("si7020: chip present.");

        value = si7020_humidity_read();

        si7020_power_off();

        ft_device_result("si7020 humidity %d", value);

        if (value == 0 || value > HUMIDITY_TEST_LIMIT) {
                ft_update("si7020: value error read=[%d]", value);
                return FT_HUMIDITY_FAIL;
        }

        ft_update("si7020: success");
        return FT_SUCCESS;
}
#endif

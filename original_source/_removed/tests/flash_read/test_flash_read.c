#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include <stdlib.h>

#include "unity.h"

#include "pelagic-types.h"
#include "cmsis_os.h"
#include "flash_store.h"
#include "winbond_flash.h"

#define TEST_PARTITION_SIZE (64*1024)

extern uint8_t winbond_store[];

uint8_t staging_buffer[4096];

uint8_t read_cache[FLASH_PAGE_SIZE];

void setUp() {
    winbond_flash_init();
}

void test_setup(flash_partition_t *part, flash_read_t *read,  void *data, int size) {
    flash_store_setup(part);
    for (int i = 0; i < size; i++)
        flash_store_write(part, ((uint8_t *)data) + i, 1);

    flash_read_init(part, read, read_cache);
}

void test_flash_read_block_FromStaging(void) {
    flash_partition_t part = {
        .name           = "test-partition",
        .is_raw         = 0,
        .offset         = 0,
        .size           = TEST_PARTITION_SIZE,
        .staging_buffer = staging_buffer,
        .device         = &winbond_flash_device
    };

    flash_read_t read;

    uint8_t page[20];

    for (int i = 0; i < sizeof(page); i++) {
        page[i] = i+1;
    }

    test_setup(&part, &read, page, sizeof(page));

    TEST_ASSERT_EQUAL_INT(20, part.staging_offset);

    for (int i = 0; i < sizeof(page); i++) {
        TEST_ASSERT_EQUAL_INT(page[i], flash_read_uint8(&read));
    }
}

void test_flash_read_block_SpanStoreAndStaging(void) {
    flash_partition_t part = {
        .name           = "test-partition",
        .is_raw         = 0,
        .offset         = 0,
        .size           = TEST_PARTITION_SIZE,
        .staging_buffer = staging_buffer,
        .device         = &winbond_flash_device
    };

    const int data_size = 512;
    flash_read_t read;
    uint32_t data[data_size];

    for (int i = 0; i < data_size; i++)
        data[i] = i;

    test_setup(&part, &read, data, data_size);

    memset(data, 0, data_size);

    int bytes = flash_read_block(&read, data, data_size);
    TEST_ASSERT_EQUAL_INT(bytes, data_size);

    for (int i = 0; i < 512; i++) {
        TEST_ASSERT_EQUAL_INT(i, data[i]);
    }
}

void test_flash_read_block_RawSpanStoreAndStaging(void) {
    flash_partition_t part = {
        .name           = "test-partition",
        .is_raw         = true,
        .offset         = 0,
        .size           = TEST_PARTITION_SIZE,
        .staging_buffer = staging_buffer,
        .device         = &winbond_flash_device
    };

    flash_read_t read;

    uint8_t page[400], written[400];

    for (int i = 0; i < sizeof(page); i++) {
        page[i] = i+1;
    }

    test_setup(&part, &read, page, sizeof(page));
    int bytes = flash_read_block(&read, written, sizeof(written));
    TEST_ASSERT_EQUAL_INT(bytes, sizeof(written));

    for (int i = 0; i < sizeof(page); i++) {
        TEST_ASSERT_EQUAL_INT(page[i], written[i]);
    }
}

void test_flash_read_block_TryToReadBeyond(void) {
    flash_partition_t part = {
        .name           = "test-partition",
        .is_raw         = 0,
        .offset         = 0,
        .size           = 4096,
        .staging_buffer = staging_buffer,
        .device         = &winbond_flash_device
    };

    flash_read_t read;

    uint8_t page[FLASH_DATA_SIZE];

    for (int i = 0; i < FLASH_DATA_SIZE; i++) {
        page[i] = i+1;
    }

    test_setup(&part, &read, page, FLASH_DATA_SIZE);
    int bytes = flash_read_block(&read, page, FLASH_DATA_SIZE);

    // only 248 bytes should be available because of header
    TEST_ASSERT_EQUAL_INT(FLASH_DATA_SIZE, bytes);

    // And make sure any additional reads return zero.
    bytes = flash_read_block(&read, page, FLASH_DATA_SIZE);
    TEST_ASSERT_EQUAL_INT(0, bytes);
}

void test_flash_read_block_RawTryToReadBeyond(void) {
    flash_page_header_t *header;
    flash_partition_t part = {
        .name           = "test-partition",
        .is_raw         = 1,
        .offset         = 0,
        .size           = 4096,
        .staging_buffer = staging_buffer,
        .device         = &winbond_flash_device
    };

    flash_read_t read;

    uint8_t page[300];

    for (int i = 0; i < 256; i++) {
        page[i] = i+1;
    }

    test_setup(&part, &read, page, 256);
    int bytes = flash_read_block(&read, page, 300);
    TEST_ASSERT_EQUAL_INT(256, bytes);

    // And make sure any additional reads return zero.
    bytes = flash_read_block(&read, page, 300);
    TEST_ASSERT_EQUAL_INT(0, bytes);
}

void test_flash_read_CircularBuffer() {
    int bytes;
    flash_partition_t part = {
        .name           = "test-partition",
        .is_raw         = 0,
        .offset         = 0,
        .size           = FLASH_PAGE_SIZE * 32, // 2 sectors, 32 pages..
        .staging_buffer = staging_buffer,
        .staging_offset = 0,
        .staging_page   = 0,
        .device         = &winbond_flash_device
    };
    flash_read_t read;
    const int data_size = FLASH_DATA_SIZE / 4;
    uint32_t data[data_size];

    flash_store_setup(&part);


    // loop around once.. and partial page..
    for (int loop = 0; loop < 34; loop++) {
        for (int i = 0; i < data_size; i++) {
            data[i] = (loop << 16) | i;
        }
        flash_store_write(&part, (uint8_t *)data, sizeof(data));
    }

    // After the write, there should be only 18 pages.

    flash_read_init(&part, &read, read_cache);

    TEST_ASSERT_EQUAL_UINT32(16, read.page);

    // Start reading..

    int page = 16;
    for (int loop = 0; loop < 18; loop++) {
        memset(data, 0, sizeof(data));

        int result = flash_read_block(&read, data, sizeof(data));

        TEST_ASSERT_EQUAL_INT(result, sizeof(data));

        for (int i = 0; i < data_size; i++) {
            TEST_ASSERT_EQUAL_UINT32((page << 16) | i, data[i]);
        }

        page++;
    }
}

void populate_flash(flash_partition_t *part) {
    uint8_t data[FLASH_DATA_SIZE];

    flash_store_setup(part);
    for (int page = 0; page < 32; page++) {
        memset(data, page + 1, sizeof(data));
        flash_store_write(part, data, sizeof(data));
    }
}

void test_flash_read_seek_ContinuousSuccess() {
    uint32_t bytes;
    flash_partition_t part = {
        .name           = "test-partition",
        .is_raw         = 0,
        .offset         = 0,
        .size           = FLASH_PAGE_SIZE * 64,
        .staging_buffer = staging_buffer,
        .staging_offset = 0,
        .staging_page   = 0,
        .device         = &winbond_flash_device
    };
    flash_read_t read;
    uint8_t cache[FLASH_PAGE_SIZE];
    uint8_t data[FLASH_DATA_SIZE], compare[FLASH_DATA_SIZE];

    populate_flash(&part);
    flash_read_init(&part, &read, cache);

    // read & seek in a sequental manner
    for (int page = 0; page < 32; page++) {
        TEST_ASSERT_EQUAL(true, flash_read_seek(&read, page * FLASH_DATA_SIZE));
        memset(compare, page + 1, sizeof(compare));
        bytes = flash_read_block(&read, data, FLASH_DATA_SIZE);
        TEST_ASSERT_EQUAL(FLASH_DATA_SIZE, bytes);
        TEST_ASSERT_EQUAL_UINT8_ARRAY(compare, data, FLASH_DATA_SIZE);
    }
}

void test_flash_read_seek_SkipForwardSuccess() {
    uint32_t bytes;
    flash_partition_t part = {
        .name           = "test-partition",
        .is_raw         = 0,
        .offset         = 0,
        .size           = FLASH_PAGE_SIZE * 64,
        .staging_buffer = staging_buffer,
        .staging_offset = 0,
        .staging_page   = 0,
        .device         = &winbond_flash_device
    };
    flash_read_t read;
    uint8_t cache[FLASH_PAGE_SIZE];
    uint8_t compare[FLASH_DATA_SIZE], data[FLASH_DATA_SIZE];

    populate_flash(&part);
    flash_read_init(&part, &read, cache);

    TEST_ASSERT_EQUAL(30, flash_read_block(&read, data, 30));

    // seek forward to next page
    TEST_ASSERT_EQUAL(true, flash_read_seek(&read, FLASH_DATA_SIZE));
    TEST_ASSERT_EQUAL(FLASH_DATA_SIZE, flash_read_block(&read, data, FLASH_DATA_SIZE));
    memset(compare, 1, FLASH_DATA_SIZE);
    TEST_ASSERT_EQUAL_UINT8_ARRAY(compare, data, FLASH_DATA_SIZE - 30);
    memset(compare, 2, FLASH_DATA_SIZE);
    TEST_ASSERT_EQUAL_UINT8_ARRAY(compare, &data[FLASH_DATA_SIZE - 30], 30);
}

void test_flash_read_seek_BackwardsSuccess() {
    uint32_t bytes;
    flash_partition_t part = {
        .name           = "test-partition",
        .is_raw         = 0,
        .offset         = 0,
        .size           = FLASH_PAGE_SIZE * 64,
        .staging_buffer = staging_buffer,
        .staging_offset = 0,
        .staging_page   = 0,
        .device         = &winbond_flash_device
    };
    flash_read_t read;
    uint8_t cache[FLASH_PAGE_SIZE];
    uint8_t compare[FLASH_DATA_SIZE], data[FLASH_DATA_SIZE];

    populate_flash(&part);
    flash_read_init(&part, &read, cache);

    TEST_ASSERT_EQUAL(30, flash_read_block(&read, data, 30));
    TEST_ASSERT_EQUAL(true, flash_read_seek(&read, FLASH_DATA_SIZE));

    TEST_ASSERT_EQUAL(true, flash_read_seek(&read, 0));
    TEST_ASSERT_EQUAL(FLASH_DATA_SIZE, flash_read_block(&read, data, FLASH_DATA_SIZE));
    memset(compare, 1, FLASH_DATA_SIZE);
    TEST_ASSERT_EQUAL_UINT8_ARRAY(compare, data, FLASH_DATA_SIZE);
}

int main(int argc, char **argv) {
    UnityBegin("flash_read.c");
    RUN_TEST(test_flash_read_block_SpanStoreAndStaging, 10);
    RUN_TEST(test_flash_read_block_FromStaging, 20);
    RUN_TEST(test_flash_read_block_RawSpanStoreAndStaging, 30);
    RUN_TEST(test_flash_read_block_TryToReadBeyond, 40);
    RUN_TEST(test_flash_read_block_RawTryToReadBeyond, 50);
    RUN_TEST(test_flash_read_CircularBuffer, 60);
    RUN_TEST(test_flash_read_seek_ContinuousSuccess, 80);
    RUN_TEST(test_flash_read_seek_SkipForwardSuccess, 90);
    RUN_TEST(test_flash_read_seek_BackwardsSuccess, 90);
    exit(UnityEnd());
}

#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include <stdlib.h>
#include "unity.h"

#include "pelagic.h"
#include "cmsis_os.h"
#include "bnet.h"
#include "bnet_sensor.h"
#include "rtc_api.h"
#include "radio_harness.h"
#include "system_file.h"
#include "sensor_types.h"

enum {
    TEST_TEMP_VALUE = 0xabcd
};

uint8_t bnet_receive_buffer[128], bnet_send_buffer[128];

volatile bool bnet_packet_watch = false;
volatile bool bnet_sensor_timed_out = false;

#define VMS_UID_HEX "\xf1\xf2\xf3\xf4\xf5\xf6\xf7\xf8\xf9\xfa"
#define SENSOR_UID_HEX "\x11\x12\x13\x14\x15\x16\x17\x18\x19\x1a"
#define SENSOR_WRONG_UID_HEX "\x21\x22\x23\x24\x25\x26\x27\x28\x29\x2a"

board_uid_t board_uid = { 0x11,0x12,0x13,0x14,0x15,0x16,0x17,0x18,0x19,0x1a };
board_uid_t vms_uid = { 0xf1, 0xf2, 0xf3, 0xf4, 0xf5, 0xf6, 0xf7, 0xf8, 0xf9, 0xfa };

uint8_t radio_captured[8192];

void bnet_sensor_create_timer() {

}

void bnet_sensor_timer_stop() {

}

void setUp() {
    sys_file_clear();
    uid_copy(SYS_REG_FILE->host_uid, vms_uid);
    SYS_REG_FILE->have_host = true;
    SYS_REG_FILE->radio_power = 10;
    radio_harness_init();
    memset(radio_captured, 0, sizeof(radio_captured));
    radio_harness_capture(radio_captured);
}

uint16_t thermistor_read() {
    return TEST_TEMP_VALUE;
}

radio_harness_read_t success_stream[] = {
    { .bytes = 21, .data = "\x90" SENSOR_UID_HEX VMS_UID_HEX }
};

void test_bnet_sensor_report_Success() {
    bool result;

    radio_harness_set_read(success_stream, ARRAY_SIZE(success_stream));

    result = bnet_sensor_report(false);
    TEST_ASSERT_EQUAL(true, result);
    TEST_ASSERT_EQUAL(24, radio_harness_captured_bytes);
    TEST_ASSERT_EQUAL_UINT8(BNET_TYPE_SENSOR_REPORT, radio_captured[0]);
    TEST_ASSERT_EQUAL_UINT8_ARRAY(vms_uid, &radio_captured[1], sizeof(board_uid_t));
    TEST_ASSERT_EQUAL_UINT8_ARRAY(board_uid, &radio_captured[11], sizeof(board_uid_t));
    TEST_ASSERT_EQUAL_UINT8(SENSOR_THERMISTOR, radio_captured[21]);
    TEST_ASSERT_EQUAL_UINT8(TEST_TEMP_VALUE & 0xff, radio_captured[22]);
    TEST_ASSERT_EQUAL_UINT8((TEST_TEMP_VALUE >> 8) & 0xff, radio_captured[23]);
}

radio_harness_read_t success_reeval_stream[] = {
    { .bytes = 0, .data = NULL},
    { .bytes = 0, .data = NULL},
    { .bytes = 21, .data = "\x90" SENSOR_UID_HEX VMS_UID_HEX }
};

void test_bnet_sensor_report_ReevalSuccess() {
    bool result;

    radio_harness_set_read(success_stream, ARRAY_SIZE(success_stream));

    result = bnet_sensor_report(true);
    TEST_ASSERT_EQUAL(true, result);
    TEST_ASSERT_EQUAL(24, radio_harness_captured_bytes);
    TEST_ASSERT_EQUAL_UINT8(BNET_TYPE_SENSOR_REPORT, radio_captured[0]);
    TEST_ASSERT_EQUAL_UINT8_ARRAY(vms_uid, &radio_captured[1], sizeof(board_uid_t));
    TEST_ASSERT_EQUAL_UINT8_ARRAY(board_uid, &radio_captured[11], sizeof(board_uid_t));
    TEST_ASSERT_EQUAL_UINT8(SENSOR_THERMISTOR, radio_captured[21]);
    TEST_ASSERT_EQUAL_UINT8(TEST_TEMP_VALUE & 0xff, radio_captured[22]);
    TEST_ASSERT_EQUAL_UINT8((TEST_TEMP_VALUE >> 8) & 0xff, radio_captured[23]);
    TEST_ASSERT_EQUAL(SYS_REG_FILE->radio_power, 2);
}


radio_harness_read_t wrong_uid_stream[BNET_SENSOR_REPORT_TRIES+1] = {
    { .bytes = 21, .data = "\x90" SENSOR_WRONG_UID_HEX VMS_UID_HEX },
};

void test_bnet_sensor_report_IgnoreWrongUID() {
    bool result;

    radio_harness_set_read(wrong_uid_stream, ARRAY_SIZE(wrong_uid_stream));

    result = bnet_sensor_report(false);
    TEST_ASSERT_EQUAL(false, result);
}

radio_harness_read_t wrong_type_stream[BNET_SENSOR_REPORT_TRIES+1] = {
    { .bytes = 21, .data = "\xff" SENSOR_UID_HEX VMS_UID_HEX },
};

void test_bnet_sensor_report_IgnoreWrongType() {
    bool result;

    radio_harness_set_read(wrong_type_stream, ARRAY_SIZE(wrong_type_stream));

    result = bnet_sensor_report(false);
    TEST_ASSERT_EQUAL(false, result);
}

int main(int argc, char **argv) {
    UnityBegin("bnet_sensor_report.c");
    RUN_TEST(test_bnet_sensor_report_Success, 10);
    RUN_TEST(test_bnet_sensor_report_IgnoreWrongUID, 20);
    RUN_TEST(test_bnet_sensor_report_IgnoreWrongType, 30);
    exit(UnityEnd());
}

#include "unity.h"
#include <stdio.h>
#include <stdint.h>
#include <string.h>

#include "flash_store.h"
#include "syslog.h"
#include "rtc_api.h"

typedef struct {
    syslog_level_t level;
    syslog_facility_t facility;
    uint32_t    timestamp;
    uint32_t     length;
    char    message[80];
} syslog_record_t;

static uint8_t record_cache[8192];
uint8_t the_buffer[8192];
extern int time_acquired;

void setup_reconstructed_log(flash_read_t *read) {
    flash_read_init(&syslog_partition, read, record_cache);
}

void get_reconstructed_record(flash_read_t *log_read, syslog_record_t *record) {
    uint16_t delta;
    int16_t signed_delta;
    uint8_t temp;

    TEST_ASSERT_TRUE(log_read->count < syslog_partition.stored);

    record->level = flash_read_uint8(log_read);
    record->facility = flash_read_uint8(log_read);
    record->timestamp = flash_read_uint32(log_read);
    record->length = flash_read_uint8(log_read);

    if (record->length > 80)
        TEST_FAIL_MESSAGE("message length is greater than 80");

    flash_read_block(log_read, record->message, record->length);
    record->message[record->length] = 0;
}

extern uint8_t    syslog_in, syslog_out;

void setup_syslog() {
    flash_store_erase(&syslog_partition);
    syslog_out = syslog_in = 0;
}

void test_syslog_commit() {
    flash_read_t log_read;
    syslog_record_t record;

    setup_syslog();
    rtc_write(123456);
    time_acquired = 1;


    syslog(LOG_ERR, LOG_BODYTRACE, "I'm here!");

    setup_reconstructed_log(&log_read);

    get_reconstructed_record(&log_read, &record);

    TEST_ASSERT_EQUAL_INT(LOG_ERR, record.level);
    TEST_ASSERT_EQUAL_INT(LOG_BODYTRACE, record.facility);
    TEST_ASSERT_EQUAL_INT(record.timestamp, 123456);
    TEST_ASSERT_EQUAL_INT(record.length, 9);
    TEST_ASSERT_EQUAL_STRING(record.message, "I'm here!");
}

int main(int argc, char **argv) {
    UnityBegin("syslog.c");
    RUN_TEST(test_syslog_commit, 10);
    UnityEnd();
}

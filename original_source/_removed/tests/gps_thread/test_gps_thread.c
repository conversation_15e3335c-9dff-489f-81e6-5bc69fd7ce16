#include "unity.h"
#include <stdio.h>
#include <stdint.h>
#include <string.h>

#include "boat_log.h"
#include "serial_harness.h"
#include "stats.h"

typedef struct {
    uint32_t    timestamp;
    int32_t     latitude;
    int32_t     longitude;
    int         have_precision;
    log_precision_t precision;
} log_record_t;

log_record_t log_records[8000];
int log_record_count = 0;

serial_stream_t process_one_receive = {
    .count = 4,
    .sequences = {
      { .data = "ABCDEFABCDEF", .bytes = 10 },    // For change baud logic
      { .data = "$GPRMC,215534.298,A,4339.3267,N,07212.2415,W,0.33,0.00,091214,,,A*7D\r\n",  .bytes = 70 },
      { .data = "$GPRMC,*7D\r\n",  .bytes = 12 },   // for crc error
      { .data = "$GPRMC,220503.000,A,4339.3164,N,07212.2412,W,0.02,208.92,091214,,,A*78\r\n", .bytes = 72 },
      { .data = "$GPGGA,042033.000,4339.3187,N,07212.2364,W,1,6,1.11,364.8,M,-32.6,M,,*69\r\n", .bytes = 76 },
  }
};

extern void gps_thread(void * arg);

void tearDown() {
    serial_teardown(GPS_PORT);
}

void boat_log_gps(uint32_t time_now, int32_t lat_now, int32_t lon_now, log_precision_t *precision){

    log_record_t *record = &log_records[log_record_count++];

    record->timestamp = time_now;
    record->latitude = lat_now;
    record->longitude = lon_now;

    if (precision) {
        record->have_precision = 1;
        memcpy(&record->precision, precision, sizeof(log_precision_t));
    } else {
        record->have_precision = 0;
    }

    printf("timestamp %d, lat %d, lon %d\n", time_now, lat_now, lon_now);
}

void test_gps_thread_ProcessOneSentence() {
    jmp_buf env;
    log_record_t *rec;

    log_record_count = 0;

    serial_stream(GPS_PORT, &process_one_receive, NULL, &env);

    if (setjmp(env) == 0) {
        gps_thread(NULL);
    }

    TEST_ASSERT_EQUAL_INT(1, gps_stats.crc_errors);
    TEST_ASSERT_EQUAL_INT(2, log_record_count);
    rec = &log_records[0];
    TEST_ASSERT_EQUAL_UINT32(29627734, rec->timestamp);
    TEST_ASSERT_EQUAL_INT32(4365544, rec->latitude);
    TEST_ASSERT_EQUAL_INT32(-7220402, rec->longitude);

    rec = &log_records[1];
    TEST_ASSERT_EQUAL_UINT32(29627734, rec->timestamp);
    TEST_ASSERT_EQUAL_INT32(4365527, rec->latitude);
    TEST_ASSERT_EQUAL_INT32(-7220401, rec->longitude);
}

int main(int argc, char **argv) {
    UnityBegin("gps.c");
    RUN_TEST(test_gps_thread_ProcessOneSentence, 10);
    UnityEnd();
}

#include "pelagic.h"
#include "modem.h"
#include "modem_bodytrace.h"
#include "modem_harness.h"
#include "unity.h"

modem_result_t modem_harness_response;
static uint8_t *modem_harness_capture_buffer = NULL;
int modem_captured_bytes = 0;
int modem_harness_message_count = 0;
int modem_harness_bomb_on_read_chunk = 0;

static modem_harness_message_t *modem_harness_messages = NULL;


void modem_harness_init() {
    modem_harness_response = MODEM_SUCCESS;
    modem_harness_capture_buffer = NULL;
    modem_captured_bytes = 0;
    modem_harness_message_count = 0;
    modem_harness_bomb_on_read_chunk = 0;
    modem_harness_message_count = 0;
}


void modem_harness_capture(uint8_t *buffer) {
    modem_harness_capture_buffer = buffer;
}

void modem_harness_set_messages(modem_harness_message_t *msgs, int count) {
    modem_harness_messages = msgs;
    modem_harness_message_count = count;
}

modem_result_t modem_send_data_vec(const modem_iovec_t *vec, const int vec_size, modem_wait_t wait) {
    if (modem_harness_response != MODEM_SUCCESS)
        return modem_harness_response;

    if (modem_harness_capture_buffer) {
        for (int i = 0; i < vec_size; i++) {
            memcpy(modem_harness_capture_buffer + modem_captured_bytes, vec[i].data, vec[i].size);
            modem_captured_bytes += vec[i].size;
        }
    }

    return MODEM_SUCCESS;
}

modem_result_t modem_send_data(void *data, const uint32_t bytes, modem_wait_t wait) {
    modem_iovec_t vec = { .data = data, .size = bytes };

    return modem_send_data_vec(&vec, 1, wait, MODEM_CMD_SEND_DATA);
}

modem_result_t modem_xmit_wait(modem_wait_t wait) {
    return MODEM_SUCCESS;
}

modem_result_t modem_get_message_count(uint32_t *count) {
    *count = modem_harness_message_count;
    return MODEM_SUCCESS;
}

modem_result_t modem_read_chunk(void *buffer, const uint32_t offset, const uint32_t length, uint32_t *bytes, uint32_t *remaining) {
    int avail, count;

    if (modem_harness_bomb_on_read_chunk)
        TEST_FAIL_MESSAGE("unexpected call to modem_read_chunk");

    *bytes = 0;
    *remaining = 0;

    if (modem_harness_message_count == 0)
        return MODEM_SUCCESS;

    avail = modem_harness_messages->size - (int) offset;

    if (avail <= 0)
        return MODEM_SUCCESS;

    count = ((int) length < avail) ? (int) length : avail;

    memcpy(buffer, modem_harness_messages->data + offset, count);

    *bytes = count;
    *remaining = modem_harness_messages->size - (offset + count);

    return MODEM_SUCCESS;
}

modem_result_t modem_delete_message() {
    if (modem_harness_message_count > 0) {
        modem_harness_messages++;
        modem_harness_message_count--;
    } else {
        TEST_FAIL_MESSAGE("modem_delete_message called but no messages left");
    }

    return MODEM_SUCCESS;
}

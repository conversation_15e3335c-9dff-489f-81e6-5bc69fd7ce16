#include "pelagic.h"

#include <string.h>

//
// winbond flash harness
//

#define WINBOND_SECTOR_SIZE 4096
#define WINBOND_PAGE_SIZE 256
#define WINBOND_SIZE    (16*1024*1024)

uint8_t winbond_store[WINBOND_SIZE];

void winbond_flash_init() {
    memset(winbond_store, 0xff, WINBOND_SIZE);
}

flash_result_t winbond_flash_erase_sector(uint32_t address) {
    if (address & (WINBOND_SECTOR_SIZE - 1))
        return FLASH_ERR_ALIGNMENT;

    if (address + WINBOND_SECTOR_SIZE > WINBOND_SIZE)
        return FLASH_ERR_RUNTIME;

    memset(&winbond_store[address], 0xff, WINBOND_SECTOR_SIZE);

    return FLASH_SUCCESS;
}

flash_result_t winbond_flash_write(uint32_t address, void *data, int bytes)  {
    if (address + bytes > WINBOND_SIZE)
        return FLASH_ERR_RUNTIME;

    memcpy(((uint8_t *) winbond_store) + address, data, bytes);

    return FLASH_SUCCESS;
}

flash_result_t winbond_flash_read(uint32_t address, void *data, int size) {
    if (address + size > WINBOND_SIZE)
        return FLASH_ERR_RUNTIME;


    memcpy(data, ((uint8_t *)winbond_store) + address, size);

    return FLASH_SUCCESS;
}

void winbond_keep_on(bool force_on) {
    return;
}

flash_device_t winbond_flash_device = {
    .read             = winbond_flash_read,
    .write            = winbond_flash_write,
    .erase            = winbond_flash_erase_sector,
    .keep_on          = winbond_keep_on,
    .page_size        = 256,
    .sector_size      = 4096,
    .pages_per_sector = 16,
};

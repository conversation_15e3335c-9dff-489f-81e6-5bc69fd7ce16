#ifndef __SERIAL_HARNESS_H__
#define __SERIAL_HARNESS_H__

typedef enum {
    GSM_PORT,
    GPS_PORT,
    CONSOLE_PORT
} serial_port_t;

typedef struct {
    char    *data;
    int     bytes;
} serial_sequence_t;

typedef struct {
    int     count;
    int    index;
    serial_sequence_t   sequences[];
} serial_stream_t;

void serial_stream(serial_port_t port, serial_stream_t *receive, serial_stream_t *transmit, jmp_buf *jump);
void serial_capture(serial_port_t port, void *buffer);
int serial_captured_bytes(serial_port_t port);
void serial_teardown(serial_port_t port);
#endif

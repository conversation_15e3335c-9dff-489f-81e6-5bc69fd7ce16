#include <string.h>
#include <stdint.h>
#include "pelagic-types.h"
#include "radio.h"
#include "radio_harness.h"
#include "unity.h"

uint8_t *radio_harness_captured_data = NULL;
uint32_t radio_harness_captured_bytes = 0;
uint8_t radio_harness_level = 0;

radio_harness_read_t *radio_harness_read_stream = NULL;
int radio_harness_read_count = 0;

void radio_harness_init() {
    radio_harness_captured_data = NULL;
    radio_harness_captured_bytes = 0;
    radio_harness_level = 0;
    radio_harness_read_stream = NULL;
    radio_harness_read_count = 0;
}

void radio_harness_capture(void *data) {
    radio_harness_captured_data = data;
}

void radio_harness_set_read(radio_harness_read_t *stream, int count) {
    radio_harness_read_stream = stream;
    radio_harness_read_count = count;
}

int  radio_read(void *buffer, uint32_t millisecs, int8_t *rssi) {
    radio_harness_read_t *read = radio_harness_read_stream++;
    if (radio_harness_read_count <= 0) {
        TEST_FAIL_MESSAGE("radio_read - read too much");
    }

    radio_harness_read_count--;

    // simulate timeout
    if (read->bytes == 0)
        return 0;

    memcpy(buffer, read->data, read->bytes);

    return read->bytes;
}

void radio_send(void *packet, int bytes) {
    if (radio_harness_captured_data == NULL)
        return;

    TEST_ASSERT_TRUE(bytes > 0);
    TEST_ASSERT_TRUE(packet != NULL);

    memcpy(&radio_harness_captured_data[radio_harness_captured_bytes], packet, bytes);
    radio_harness_captured_bytes += bytes;
}

void radio_set_power_level(int8_t level) {
    radio_harness_level = level;
}

void radio_flush() {

}

#include <stdlib.h>
#include <stdint.h>
#include <string.h>
#include "pelagic-types.h"
#include "imei.h"
#include "system_file.h"

system_register_file_t test_sys_reg_file;

void sys_file_clear() {
    memset(&test_sys_reg_file, 0, sizeof(test_sys_reg_file));
    SYS_REG_FILE->magic = SYS_REG_FILE_MAGIC;
    SYS_REG_FILE->reboot_reason = REBOOT_NORMAL;
}

void sys_file_init() {
    sys_file_clear();
}

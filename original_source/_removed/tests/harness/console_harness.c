#include <stdio.h>
#include <stdint.h>
#include "console.h"

void console_serial_write(void *buffer, int bytes) {
    fwrite(buffer, bytes, 1, stdout);
}

int console_serial_getchar() {
    return 0;
}

int console_serial_available() {
    return 0;
}

console_io_t consoles[] = {
    { console_serial_write, console_serial_getchar, console_serial_available },
};

console_io_t *console = &consoles[0];

int display_more(int *lines) {
    return 0;
}

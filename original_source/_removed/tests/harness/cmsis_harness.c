#include <stdio.h>
#include <stdint.h>
#include <stdarg.h>
#include <stdlib.h>
#include <sys/time.h>
#include "cmsis_os.h"
#include "us_ticker_api.h"

int demo_mode = 0;

uint32_t us_ticker_read() {
    uint32_t usecs;

    struct timeval tv;
    struct timezone tz;
    gettimeofday(&tv, &tz);

    usecs = (uint64_t) tv.tv_sec * 1000000;
    usecs += tv.tv_usec;

    return (uint32_t) usecs;
}

osStatus osDelay (uint32_t millisec) {
    return osOK;
}

int32_t osSignalSet (osThreadId thread_id, int32_t signals) {
    return 0;
}

osMutexId osMutexCreate (const osMutexDef_t *mutex_def) {
    return (osMutexId) 1;
}

osStatus osMutexWait (osMutexId mutex_id, uint32_t millisec) {
    return osOK;
}

osStatus osMutexRelease (osMutexId mutex_id) {
    return osOK;
}

osTimerId osTimerCreate (const osTimerDef_t *timer_def, os_timer_type type, void *argument) {
    return (osTimerId) timer_def;
}

osStatus osTimerStart (osTimerId timer_id, uint32_t millisec) {
    return osOK;
}

osStatus osTimerStop (osTimerId timer_id) {
    return osOK;
}

void mbed_assert_internal(const char *expr, const char *file, int line) {
    printf("mbed assert internal called: expr [%s] file [%s] line [%d]\n", expr, file, line);
    exit(-1);
}

void uart_printf(const char *expr, ...) {
    va_list ap;

    printf("\nDEBUG: ");
    va_start(ap, expr);
    vprintf(expr, ap);
    va_end(ap);
}

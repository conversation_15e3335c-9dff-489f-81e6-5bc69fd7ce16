#include "pelagic-types.h"
#include "event.h"
#include "event_harness.h"
#include <string.h>
#include <stdlib.h>
#include "unity.h"

enum {
    EVENT_LIMIT = 1000,
};

typedef struct {
    event_t     event;
    char        *desc;
    int          param_count;
    event_param_t   params[8];
} event_log_t;

int         event_log_count = 0;
event_log_t event_store[EVENT_LIMIT];

void event_harness_init() {
    event_log_t *e = event_store;
    for (int i = 0; i < event_log_count; i++, e++) {
        free(e->desc);
    }

    event_log_count = 0;
}

bool event_harness_have_entry(event_t event) {
    event_log_t *e = event_store;

    for (int i = 0; i < event_log_count; i++, e++)
        if (e->event == event)
            return true;

    return false;
}

void event_log(event_t event, char *desc, int param_count, event_param_t *params) {
    event_log_t *e = &event_store[event_log_count++];

    if (event_log_count >= EVENT_LIMIT)
        TEST_FAIL_MESSAGE("too many events logged - forget to call init?");

    if (param_count > 8)
        TEST_FAIL_MESSAGE("event_log: params > 8");

    e->event = event;
    e->desc = strdup(desc);
    e->param_count = param_count;

    memcpy(e->params, params, sizeof(event_param_t) * param_count);
}

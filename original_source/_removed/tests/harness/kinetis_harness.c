#include "pelagic.h"

#include <string.h>

//
// kinetis flash harness
//

#define KINETIS_SECTOR_SIZE 1024
#define KINETIS_PAGE_SIZE   4
#define KINETIS_SIZE        (256*1024)

uint8_t kinetis_store[KINETIS_SIZE];

void kinetis_init() {
    memset(kinetis_store, 0xff, KINETIS_SIZE);
}

flash_result_t kinetis_flash_erase_sector(uint32_t address) {
    if (address & (KINETIS_SECTOR_SIZE - 1))
        return FLASH_ERR_ALIGNMENT;

    if (address + KINETIS_SECTOR_SIZE > KINETIS_SIZE)
        return FLASH_ERR_RUNTIME;

    memset(&kinetis_store[address], 0xff, KINETIS_SECTOR_SIZE);

    return FLASH_SUCCESS;
}

flash_result_t kinetis_flash_write(uint32_t address, void *data, int bytes)  {
    if (address + bytes > KINETIS_SIZE)
        return FLASH_ERR_RUNTIME;

    if (address & (KINETIS_PAGE_SIZE-1)) {
        return FLASH_ERR_ALIGNMENT;
    }

    memcpy(((uint8_t *) kinetis_store) + address, data, bytes);

    return FLASH_SUCCESS;
}

flash_result_t kinetis_flash_read(uint32_t address, void *data, int size) {
    if (address + size > KINETIS_SIZE)
        return FLASH_ERR_RUNTIME;


    memcpy(data, ((uint8_t *)kinetis_store) + address, size);

    return FLASH_SUCCESS;
}

void kinetis_keep_on(bool keepon) {
    return;
}

flash_device_t kinetis_flash_device = {
    .erase = kinetis_flash_erase_sector,
    .write = kinetis_flash_write,
    .read = kinetis_flash_read,
    .keep_on = kinetis_keep_on,
    .page_size  = 256,
    .sector_size = 1024,
    .pages_per_sector = 4
};

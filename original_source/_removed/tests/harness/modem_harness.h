#ifndef __MODEM_HARNESS_H__
#define __MODEM_HARNESS_H__

typedef struct {
    char    *data;
    int     size;
} modem_harness_message_t;

void modem_harness_init();
void modem_harness_capture(uint8_t *buffer);
void modem_harness_set_messages(modem_harness_message_t *msgs, int count);
extern int modem_captured_bytes;
extern modem_result_t modem_harness_response;

extern int modem_harness_message_count;
extern int modem_harness_bomb_on_read_chunk;

#endif

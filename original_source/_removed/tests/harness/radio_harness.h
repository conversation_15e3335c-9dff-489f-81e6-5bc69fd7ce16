#ifndef __RADIO_HARNESS_H__
#define __RADIO_HARNESS_H__

typedef struct {
    int     bytes;
    char    *data;
} radio_harness_read_t;

void radio_harness_init();
void radio_harness_capture(void *data);
void radio_harness_set_read(radio_harness_read_t *stream, int count);

extern uint8_t *radio_harness_captured_data;
extern uint32_t radio_harness_captured_bytes;

extern int radio_harness_read_count;
#endif

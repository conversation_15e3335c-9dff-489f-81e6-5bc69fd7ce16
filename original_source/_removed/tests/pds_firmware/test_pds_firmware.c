#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include <stdlib.h>
#include "unity.h"

#include "pelagic.h"
#include "flash_store.h"
#include "modem.h"
#include "modem_bodytrace.h"
#include "modem_harness.h"
#include "stats.h"
#include "firmware.h"
#include "modem.h"
#include "modem_log.h"
#include "winbond_flash.h"
#include "kinetis_flash.h"
#include "pds.h"
#include "compress.h"


modem_log_t *modem_log = NULL;

enum {
    TEST_PARTITION_SIZE = (64*1024)
};

shared_buffer_t the_buffer;

board_uid_t board_uid = { 0, 1, 2, 3, 4, 5, 6, 7, 8, 9 };

const char build_tag[] = "v2.2.1";
const uint32_t build_timestamp = 0xaabbccdd;
bool firmware_updated = false;

uint16_t board_info_read_build_crc() {
    return 0xdeaf;
}

void test_pds_firmware_acknowledge_Success() {
    modem_result_t result;
    uint32_t buildstamp;
    uint8_t buffer[512];

    modem_harness_init();

    buildstamp = 0xdeadbeef;

    modem_harness_capture(buffer);

    pds_firmware_acknowledge(10, 11, buildstamp, board_uid, 0xdead);

    TEST_ASSERT_EQUAL_INT(20, modem_captured_bytes);

    // Inspect the buffer to see if the packet was constructed correctly
    TEST_ASSERT_EQUAL_UINT8(PDS_TYPE_FIRMWARE_RECEIVED, buffer[0]);
    TEST_ASSERT_EQUAL_UINT8(PDS_TYPE_FIRMWARE_RECEIVED_VERSION, buffer[1]);
    TEST_ASSERT_EQUAL_UINT8(10, buffer[2]);
    TEST_ASSERT_EQUAL_UINT8(11, buffer[3]);
    TEST_ASSERT_EQUAL_UINT8(buildstamp & 0xff, buffer[4]);
    TEST_ASSERT_EQUAL_UINT8((buildstamp >> 8) & 0xff, buffer[5]);
    TEST_ASSERT_EQUAL_UINT8((buildstamp >> 16) & 0xff, buffer[6]);
    TEST_ASSERT_EQUAL_UINT8((buildstamp >> 24) & 0xff, buffer[7]);
    TEST_ASSERT_EQUAL_UINT8_ARRAY(board_uid, &buffer[8], sizeof(board_uid_t));
    TEST_ASSERT_EQUAL_UINT8(0xad, buffer[18]);
    TEST_ASSERT_EQUAL_UINT8(0xde, buffer[19]);
}

void test_pds_firmware_updated_Success() {
    modem_result_t result;
    uint8_t buffer[512];

    modem_harness_init();

    modem_harness_capture(buffer);

    pds_firmware_updated();

    TEST_ASSERT_EQUAL_INT(27, modem_captured_bytes);

    // Inspect the buffer to see if the packet was constructed correctly
    TEST_ASSERT_EQUAL_UINT8(PDS_TYPE_FIRMWARE_UPDATED, buffer[0]);
    TEST_ASSERT_EQUAL_UINT8(PDS_TYPE_FIRMWARE_UPDATED_VERSION, buffer[1]);
    TEST_ASSERT_EQUAL_UINT8(DEVICE_VMS, buffer[2]);
    TEST_ASSERT_EQUAL_UINT8(DEVICE_VERSION, buffer[3]);
    TEST_ASSERT_EQUAL_UINT8(build_timestamp & 0xff, buffer[4]);
    TEST_ASSERT_EQUAL_UINT8((build_timestamp >> 8) & 0xff, buffer[5]);
    TEST_ASSERT_EQUAL_UINT8((build_timestamp >> 16) & 0xff, buffer[6]);
    TEST_ASSERT_EQUAL_UINT8((build_timestamp >> 24) & 0xff, buffer[7]);
    TEST_ASSERT_EQUAL_UINT8_ARRAY(board_uid, &buffer[8], sizeof(board_uid_t));
    TEST_ASSERT_EQUAL_UINT8(0xaf, buffer[18]);
    TEST_ASSERT_EQUAL_UINT8(0xde, buffer[19]);
}



int main(int argc, char **argv) {
    UnityBegin("pds_firmware.c");
    RUN_TEST(test_pds_firmware_acknowledge_Success, 10);
    RUN_TEST(test_pds_firmware_updated_Success, 20);
    exit(UnityEnd());
}

#include "unity.h"
#include <stdio.h>
#include <stdint.h>
#include <string.h>

#include "pelagic-types.h"
#include "cmsis_os.h"
#include "flash_store.h"
#include "winbond_flash.h"
#include "boat_log.h"
#include "sensor_types.h"
#include "rtc_api.h"

enum {
    BASE_TIME   = 1000000,
};

typedef struct {
    uint8_t type;
    int32_t timestamp;

    int32_t latitude;
    int32_t longitude;
    int high_res;

    board_uid_t sensor_uid;
    uint8_t sensor_type;
    uint16_t temperature;
    uint8_t sensor_length;
    uint8_t sensor_data[64];

    uint8_t satellites;
    uint16_t hdop, vdop, pdop;
    uint16_t speed;
    uint16_t heading;
    uint16_t accel_heading;
    int16_t accel_x, accel_y, accel_z;
    int16_t mag_x, mag_y, mag_z;
} log_record_t;

static int32_t test_latitude = 0, test_longitude = 0;
static uint32_t test_timestamp = 0;

static uint8_t read_cache[8192];
flash_read_t log_read;

log_record_t log_record;

void setUp() {
    winbond_flash_init();
    flash_store_setup(&boatlog_partition);
    flash_store_erase(&boatlog_partition);
    boat_log_reset();
    test_latitude = 0;
    test_longitude = 0;
    test_timestamp = 0;
}

void log_read_init() {
    flash_read_init(&boatlog_partition, &log_read, read_cache);
}

void read_delta(int size, int32_t *value) {
    switch (size) {
    case SIZE_EMPTY:
        return;

    case SIZE_SMALL:
        *value += (int8_t) flash_read_uint8(&log_read);
        return;

    case SIZE_MEDIUM:
        *value += (int16_t) flash_read_uint16(&log_read);
        return;

    case SIZE_FULL:
        *value = flash_read_uint32(&log_read);
        return;

    default:
        TEST_FAIL_MESSAGE("unknown delta size");
        return;
    }
}

void get_reconstructed_record() {
    uint16_t delta;
    int16_t signed_delta;
    uint8_t temperature;

    TEST_ASSERT_TRUE(log_read.offset < boatlog_partition.bytes_stored);


    memset(&log_record, 0, sizeof(log_record));
    uint8_t marker = flash_read_uint8(&log_read);

    TEST_ASSERT_EQUAL(LOG_RECORD_MARKER, marker);

    uint8_t header = flash_read_uint8(&log_read);

    if (header & LOG_HEADER_TIME_ONE_SECOND) {
        test_timestamp += 1;
    } else {
        read_delta((header & LOG_HEADER_TIME_MASK) >> LOG_HEADER_TIME_SHIFT, (int32_t *)&test_timestamp);
    }

    uint8_t gps_header = flash_read_uint8(&log_read);
    read_delta((gps_header & LOG_GPS_HEADER_LAT_MASK) >> LOG_GPS_HEADER_LAT_SHIFT, &test_latitude);
    read_delta((gps_header & LOG_GPS_HEADER_LON_MASK) >> LOG_GPS_HEADER_LON_SHIFT, &test_longitude);

    log_record.timestamp = test_timestamp;
    log_record.latitude  = test_latitude;
    log_record.longitude = test_longitude;

    log_record.high_res = (gps_header & LOG_GPS_HEADER_HIGH_RES) != 0;

    if (gps_header & LOG_GPS_HEADER_SPEED_HEADING) {
        log_record.heading = flash_read_uint16(&log_read);
        log_record.speed = flash_read_uint16(&log_read);
    }

    if (gps_header & LOG_GPS_HEADER_ACCEL_HEADING) {
        log_record.accel_heading = flash_read_uint16(&log_read);
    }

    if (gps_header & LOG_GPS_HEADER_EXTENDED) {
        uint8_t ext_header = flash_read_uint8(&log_read);

        TEST_ASSERT_EQUAL(0, ext_header & ~(LOG_GPS_EXT_HEADER_PRECISION|LOG_GPS_EXT_HEADER_ACCEL_RAW));

        if (ext_header & LOG_GPS_EXT_HEADER_PRECISION) {
            log_record.satellites = flash_read_uint8(&log_read);
            log_record.hdop = flash_read_uint8(&log_read);
            log_record.vdop = flash_read_uint8(&log_read);
            log_record.pdop = flash_read_uint8(&log_read);
        }


        if (ext_header & LOG_GPS_EXT_HEADER_ACCEL_RAW) {
            log_record.accel_x = flash_read_uint16(&log_read);
            log_record.accel_y = flash_read_uint16(&log_read);
            log_record.accel_z = flash_read_uint16(&log_read);
            log_record.mag_x = flash_read_uint16(&log_read);
            log_record.mag_y = flash_read_uint16(&log_read);
            log_record.mag_z = flash_read_uint16(&log_read);
        }
    }

    if ((header & LOG_HEADER_TYPE_MASK) == LOG_HEADER_TYPE_SENSOR_REMOTE) {
        log_record.sensor_type = flash_read_uint8(&log_read);
        flash_read_block(&log_read, log_record.sensor_uid, sizeof(board_uid_t));

        if (log_record.sensor_type == SENSOR_PROTOTYPE) {
            log_record.sensor_length = flash_read_uint8(&log_read);
            flash_read_block(&log_read, log_record.sensor_data, log_record.sensor_length);
        } else {
            log_record.sensor_length = 2;
            log_record.temperature = flash_read_uint16(&log_read);
        }
    }

}

void test_boat_log_gps_RecordBasicCoords() {
    int32_t lat = 1000000, lon = -1000000;

    // start at 1,1 (1000000, 1000000) and move ~11 meters (+10) every second

    for (int i = 0; i < 10000; i++) {
        rtc_write(BASE_TIME + i);
        boat_log_gps(lat + (-4*i), lon + (10*i), 0, NULL);
    }

    log_read_init();

    for (int i = 0; i < 10000; i++) {
        get_reconstructed_record();
        TEST_ASSERT_EQUAL_UINT32(BASE_TIME + i, log_record.timestamp);
        TEST_ASSERT_EQUAL_INT32(lat + (-4*i), log_record.latitude);
        TEST_ASSERT_EQUAL_INT32(lon + (10*i), log_record.longitude);
    }
}

void test_boat_log_gps_IgnoreSmallDelta() {
    int32_t lat = 1000000, lon = -1000000;
    int32_t drift = 1;

    for (int i = 0; i < 10000; i++) {
        rtc_write(BASE_TIME + i);
        boat_log_gps(lat + drift, lon + drift, 0, NULL);
        drift = -drift;
    }

    log_read_init();

    // There should only be 1 record
    TEST_ASSERT_EQUAL_INT(15, boatlog_partition.bytes_stored);

    get_reconstructed_record();
    TEST_ASSERT_EQUAL_UINT32(BASE_TIME, log_record.timestamp);
    TEST_ASSERT_EQUAL_INT32(1000001, log_record.latitude);
    TEST_ASSERT_EQUAL_INT32(-999999, log_record.longitude);
}

void test_boat_log_gps_RecordMediumAndSmallDeltas() {
    int32_t lat = 1000000, lon = -1000000;

    for (int i = 0; i < 1000; i++) {
        rtc_write(BASE_TIME + i);
        boat_log_gps(lat + (i*10), lon + (i*300), 0, NULL);
    }

    log_read_init();

    TEST_ASSERT_EQUAL_INT(6243, boatlog_partition.bytes_stored);

    for (int i = 0; i < 1000; i++) {
        get_reconstructed_record();
        TEST_ASSERT_EQUAL_UINT32(BASE_TIME + i, log_record.timestamp);
        TEST_ASSERT_EQUAL_INT32(lat + (i*10), log_record.latitude);
        TEST_ASSERT_EQUAL_INT32(lon + (i*300), log_record.longitude);
    }
}

void test_boat_log_gps_RecordHighRes() {
    boat_log_gps(1000, 1000, LOG_ATTR_HIGH_RES, NULL);
    boat_log_gps(2000, -1000, 0, NULL);

    log_read_init();
    get_reconstructed_record();
    TEST_ASSERT_EQUAL(1, log_record.high_res);
    get_reconstructed_record();
    TEST_ASSERT_EQUAL(0, log_record.high_res);
}

void log_attr(uint16_t flags, log_attribute_t *attr) {
    boat_log_gps(1000000, -1000000, flags, attr);
}

void read_record() {
    log_read_init();
    get_reconstructed_record();
    TEST_ASSERT_EQUAL(1000000, log_record.latitude);
    TEST_ASSERT_EQUAL(-1000000, log_record.longitude);
}

void test_boat_log_gps_RecordPrecision() {
    log_attribute_t attr = { .satellites = 5, .vdop = 10, .hdop = 20, .pdop = 30 };
    log_attr(LOG_ATTR_PRECISION, &attr);
    read_record();
    TEST_ASSERT_EQUAL_UINT16(5, log_record.satellites);
    TEST_ASSERT_EQUAL_UINT16(10, log_record.vdop);
    TEST_ASSERT_EQUAL_UINT16(20, log_record.hdop);
    TEST_ASSERT_EQUAL_UINT16(30, log_record.pdop);
}

void test_boat_log_gps_RecordHeading() {
    log_attribute_t attr = { .heading = 280, .speed = 777 };

    log_attr(LOG_ATTR_SPEED_HEADING, &attr);
    read_record();
    TEST_ASSERT_EQUAL_UINT16(280, log_record.heading);
    TEST_ASSERT_EQUAL_UINT16(777, log_record.speed);
}


void test_boat_log_gps_RecordAccelHeading() {
    log_attribute_t attr = { .accel_heading = 175 };

    log_attr(LOG_ATTR_ACCEL_HEADING, &attr);
    read_record();
    TEST_ASSERT_EQUAL_UINT16(175, log_record.accel_heading);
}

void test_boat_log_gps_RecordAccelRaw() {
    log_attribute_t attr = { .accel_x = 1000, .accel_y = -2000, .accel_z = 3000, .mag_x = -4000, .mag_y = 5000, .mag_z = 6000 };

    log_attr(LOG_ATTR_ACCEL_RAW, &attr);
    read_record();
    TEST_ASSERT_EQUAL_INT16(1000, log_record.accel_x);
    TEST_ASSERT_EQUAL_INT16(-2000, log_record.accel_y);
    TEST_ASSERT_EQUAL_INT16(3000, log_record.accel_z);
    TEST_ASSERT_EQUAL_INT16(-4000, log_record.mag_x);
    TEST_ASSERT_EQUAL_INT16(5000, log_record.mag_y);
    TEST_ASSERT_EQUAL_INT16(6000, log_record.mag_z);
}

void test_boat_log_sensor_Record() {
    log_sensor_t sensor = {
        .sensor_type = SENSOR_THERMISTOR,
        .sensor_uid = { 0, 1, 2, 3, 4, 5, 6, 7, 8, 9 },
        .sensor_data = { 0x1, 0x2 },
        .sensor_length = 2
    };

    boat_log_gps(1000000, -1000000, 0, NULL);   // need to prime the gps locations
    boat_log_sensor(&sensor);
    read_record();

    get_reconstructed_record();

    TEST_ASSERT_EQUAL(1000000, log_record.latitude);    // should have the same location
    TEST_ASSERT_EQUAL(-1000000, log_record.longitude);
    TEST_ASSERT_EQUAL(SENSOR_THERMISTOR, log_record.sensor_type);
    TEST_ASSERT_EQUAL(513, log_record.temperature);
}

void test_boat_log_sensor_DontRecordDuplicates() {
    log_sensor_t sensor = {
        .sensor_type = SENSOR_THERMISTOR,
        .sensor_uid = { 0, 1, 2, 3, 4, 5, 6, 7, 8, 9 },
        .sensor_data = { 0x1, 0x2 },
        .sensor_length = 2
    };

    boat_log_gps(1000000, -1000000, 0, NULL);   // need to prime the gps locations

    for (int t = 0; t < 4; t++) {
        boat_log_sensor(&sensor);
    }

    TEST_ASSERT_EQUAL_INT(32, boatlog_partition.bytes_stored);
}

void test_boat_log_gps_RecordEverything() {
    log_attribute_t attr = {
        .satellites = 5, .vdop = 10, .hdop = 20, .pdop = 30,
        .heading = 280, .speed = 777,
        .accel_heading = 175,
        .accel_x = 1000, .accel_y = -2000, .accel_z = 3000,
        .mag_x = -4000, .mag_y = 5000, .mag_z = 6000
    };

    log_attr(LOG_ATTR_PRECISION | LOG_ATTR_SPEED_HEADING | LOG_ATTR_ACCEL_HEADING | LOG_ATTR_ACCEL_RAW, &attr);
    read_record();

    TEST_ASSERT_EQUAL_UINT16(5, log_record.satellites);
    TEST_ASSERT_EQUAL_UINT16(10, log_record.vdop);
    TEST_ASSERT_EQUAL_UINT16(20, log_record.hdop);
    TEST_ASSERT_EQUAL_UINT16(30, log_record.pdop);
    TEST_ASSERT_EQUAL_UINT16(280, log_record.heading);
    TEST_ASSERT_EQUAL_UINT16(777, log_record.speed);
    TEST_ASSERT_EQUAL_UINT16(175, log_record.accel_heading);
    TEST_ASSERT_EQUAL_INT16(1000, log_record.accel_x);
    TEST_ASSERT_EQUAL_INT16(-2000, log_record.accel_y);
    TEST_ASSERT_EQUAL_INT16(3000, log_record.accel_z);
    TEST_ASSERT_EQUAL_INT16(-4000, log_record.mag_x);
    TEST_ASSERT_EQUAL_INT16(5000, log_record.mag_y);
    TEST_ASSERT_EQUAL_INT16(6000, log_record.mag_z);

}

int main(int argc, char **argv) {
    UnityBegin("boat_log.c");
    RUN_TEST(test_boat_log_gps_RecordBasicCoords, 10);
    RUN_TEST(test_boat_log_gps_IgnoreSmallDelta, 20);
    RUN_TEST(test_boat_log_gps_RecordMediumAndSmallDeltas, 30);
    RUN_TEST(test_boat_log_gps_RecordHighRes, 50);
    RUN_TEST(test_boat_log_gps_RecordPrecision, 60);
    RUN_TEST(test_boat_log_gps_RecordHeading, 70);
    RUN_TEST(test_boat_log_gps_RecordAccelHeading, 80);
    RUN_TEST(test_boat_log_gps_RecordAccelRaw, 90);
    RUN_TEST(test_boat_log_sensor_Record, 100);
    RUN_TEST(test_boat_log_sensor_DontRecordDuplicates, 110);
    RUN_TEST(test_boat_log_gps_RecordEverything, 120);
    UnityEnd();
}

#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include <stdlib.h>

#include "datetime.h"
#include "sun.h"
#include "unity.h"

uint32_t utc_time;


void display_timestamp(char *name, uint32_t time) {
    datetime_t dt;
    seconds_to_datetime(time, &dt);

    printf("%s UTC %d/%d/%d %2.2d:%2.2d:%2.2d\n", name, dt.year, dt.month, dt.day, dt.hour, dt.minute, dt.second);
}

// LEB 43.631428, -72.305823 - 11/30/2014 UTC -5
// Actual Time	7:01 AM EST	16:14 PM EST
// Civil Twilight	6:29 AM EST	16:46 PM EST
// Nautical Twilight	5:53 AM EST	17:21 PM EST
// Astronomical Twilight	5:19 AM EST	17:56 PM EST

extern uint32_t  sunrise, sunset;
void test_sun_position_ForLEB() {
    datetime_t dt;

    sun_position(utc_time, 43.631428, -72.305823);
    seconds_to_datetime(sunrise, &dt);
    TEST_ASSERT_EQUAL_INT(2014, dt.year);
    TEST_ASSERT_EQUAL_INT(12, dt.month);
    TEST_ASSERT_EQUAL_INT(1, dt.day);
    TEST_ASSERT_EQUAL_INT(12, dt.hour);
    TEST_ASSERT_EQUAL_INT(2, dt.minute);
    TEST_ASSERT_EQUAL_INT(26, dt.second);

    seconds_to_datetime(sunset, &dt);
    TEST_ASSERT_EQUAL_INT(2014, dt.year);
    TEST_ASSERT_EQUAL_INT(11, dt.month);
    TEST_ASSERT_EQUAL_INT(30, dt.day);
    TEST_ASSERT_EQUAL_INT(21, dt.hour);
    TEST_ASSERT_EQUAL_INT(14, dt.minute);
    TEST_ASSERT_EQUAL_INT(5, dt.second);
}

// ROR 7.367614, 134.538811 12/1/2014 UTC +9
// Actual Time	6:58 AM CHUT	18:42 PM CHUT
// Civil Twilight	6:36 AM CHUT	19:04 PM CHUT
// Nautical Twilight	6:10 AM CHUT	19:30 PM CHUT
// Astronomical Twilight	5:44 AM CHUT	19:56 PM CHUT

void test_sun_position_ForROR() {
    datetime_t dt;

    sun_position(utc_time, 7.367614, 134.538811);

    seconds_to_datetime(sunrise, &dt);
    TEST_ASSERT_EQUAL_INT(2014, dt.year);
    TEST_ASSERT_EQUAL_INT(11, dt.month);
    TEST_ASSERT_EQUAL_INT(30, dt.day);
    TEST_ASSERT_EQUAL_INT(20, dt.hour);
    TEST_ASSERT_EQUAL_INT(58, dt.minute);
    TEST_ASSERT_EQUAL_INT(46, dt.second);

    seconds_to_datetime(sunset, &dt);
    TEST_ASSERT_EQUAL_INT(2014, dt.year);
    TEST_ASSERT_EQUAL_INT(12, dt.month);
    TEST_ASSERT_EQUAL_INT(1, dt.day);
    TEST_ASSERT_EQUAL_INT(8, dt.hour);
    TEST_ASSERT_EQUAL_INT(42, dt.minute);
    TEST_ASSERT_EQUAL_INT(33, dt.second);
}

// Greenwich 51.483298, -0.007466 UTC+0 12/1/2014
// Actual Time	7:43 AM GMT	15:54 PM GMT
// Civil Twilight	7:03 AM GMT	16:33 PM GMT
// Nautical Twilight	6:21 AM GMT	17:15 PM GMT
// Astronomical Twilight	5:41 AM GMT	17:56 PM GMT

void test_sun_position_ForGreenwich() {
    datetime_t dt;

    sun_position(utc_time, 51.483298, 0);

    seconds_to_datetime(sunrise, &dt);
    TEST_ASSERT_EQUAL_INT(2014, dt.year);
    TEST_ASSERT_EQUAL_INT(12, dt.month);
    TEST_ASSERT_EQUAL_INT(1, dt.day);
    TEST_ASSERT_EQUAL_INT(7, dt.hour);
    TEST_ASSERT_EQUAL_INT(42, dt.minute);
    TEST_ASSERT_EQUAL_INT(39, dt.second);

    seconds_to_datetime(sunset, &dt);
    TEST_ASSERT_EQUAL_INT(2014, dt.year);
    TEST_ASSERT_EQUAL_INT(12, dt.month);
    TEST_ASSERT_EQUAL_INT(1, dt.day);
    TEST_ASSERT_EQUAL_INT(15, dt.hour);
    TEST_ASSERT_EQUAL_INT(54, dt.minute);
    TEST_ASSERT_EQUAL_INT(47, dt.second);
}

int main(int argc, char **argv) {
    datetime_t dt = { .year = 2014, .month = 12, .day = 1, .hour = 2, .minute = 30, .second = 0 };

    utc_time = datetime_to_seconds(&dt);

    UnityBegin("sun.c");
    RUN_TEST(test_sun_position_ForLEB, 10);
    RUN_TEST(test_sun_position_ForROR, 20);
    RUN_TEST(test_sun_position_ForGreenwich, 30);
    exit(UnityEnd());
}

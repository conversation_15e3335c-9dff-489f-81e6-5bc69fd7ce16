#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include <stdlib.h>
#include "unity.h"

#include "pelagic.h"
#include "cmsis_os.h"
#include "bnet.h"
#include "bnet_sensor.h"
#include "rtc_api.h"
#include "radio_harness.h"
#include "system_file.h"

enum {
    TEST_NODE_ID = 3,
    TEST_TEMP_VALUE = 0xabcd
};

uint8_t bnet_receive_buffer[128], bnet_send_buffer[128];

volatile bool bnet_packet_watch = false;
volatile bool bnet_sensor_timed_out = false;

#define VMS_UID_HEX "\xf1\xf2\xf3\xf4\xf5\xf6\xf7\xf8\xf9\xfa"
#define VMS_WRONG_UID "\xff\xff\xff\xf4\xf5\xf6\xf7\xf8\xf9\xfa"
#define SENSOR_UID_HEX "\x11\x12\x13\x14\x15\x16\x17\x18\x19\x1a"
#define SENSOR_WRONG_UID_HEX "\x10\x10\x13\x14\x15\x16\x17\x18\x19\x1a"

#define DATA_HEX "\x00\x01\x02\x03\x04\x05\x06\x07\x08\x09\x0a\x0b\x0c\x0d\x0e\x0f"

board_uid_t board_uid = { 0x11,0x12,0x13,0x14,0x15,0x16,0x17,0x18,0x19,0x1a };
board_uid_t vms_uid = { 0xf1, 0xf2, 0xf3, 0xf4, 0xf5, 0xf6, 0xf7, 0xf8, 0xf9, 0xfa };

uint8_t radio_captured[8192];

const uint32_t build_target_version = 10;
const uint32_t build_timestamp = 0xf00dfeed;

const char build_tag[] = "test-1.0";

bool firmware_erase_called = false;
void firmware_erase() {
    firmware_erase_called = true;
}

bool firmware_flush_called = false;
void firmware_flush() {
    firmware_flush_called = true;
}

void firmware_write(uint8_t *data, int length) { }

void bnet_delay() { }

void bnet_sensor_create_timer() {

}

void bnet_sensor_timer_stop() {

}

void radio_set_rx_mode(int mode) {
    
}

void setUp() {
    sys_file_clear();
    uid_copy(SYS_REG_FILE->host_uid, vms_uid);
    radio_harness_init();
    memset(radio_captured, 0, sizeof(radio_captured));
    radio_harness_capture(radio_captured);
    firmware_erase_called = false;
    firmware_flush_called = false;
}
radio_harness_read_t avail_stream[] = {
    { .bytes = 22, .data = "\xd0" SENSOR_UID_HEX VMS_UID_HEX "\x01"}
};

void test_bnet_sensor_firmware_check_Avail() {
    bool result;

    radio_harness_set_read(avail_stream, ARRAY_SIZE(avail_stream));
    result = bnet_sensor_firmware_check();
    TEST_ASSERT_EQUAL(true, result);

    TEST_ASSERT_EQUAL(27, radio_harness_captured_bytes);
    TEST_ASSERT_EQUAL_UINT8(BNET_TYPE_FIRMWARE_CHECK, radio_captured[0]);
    TEST_ASSERT_EQUAL_UINT8_ARRAY(vms_uid, &radio_captured[1], sizeof(board_uid_t));
    TEST_ASSERT_EQUAL_UINT8_ARRAY(board_uid, &radio_captured[11], sizeof(board_uid_t));
    TEST_ASSERT_EQUAL_UINT8(BNET_DEVICE_SENSOR, radio_captured[21]);
    TEST_ASSERT_EQUAL_UINT8(build_target_version, radio_captured[22]);
    TEST_ASSERT_EQUAL_UINT8((build_timestamp & 0xff), radio_captured[23]);
    TEST_ASSERT_EQUAL_UINT8(((build_timestamp >> 8) & 0xff), radio_captured[24]);
    TEST_ASSERT_EQUAL_UINT8(((build_timestamp >> 16) & 0xff), radio_captured[25]);
    TEST_ASSERT_EQUAL_UINT8(((build_timestamp >> 24) & 0xff), radio_captured[26]);
}

radio_harness_read_t not_avail_stream[] = {
    { .bytes = 22, .data = "\xd0" SENSOR_UID_HEX VMS_UID_HEX "\x00"}
};


void test_bnet_sensor_firmware_check_NotAvail() {
    bool result;

    radio_harness_set_read(not_avail_stream, ARRAY_SIZE(not_avail_stream));
    result = bnet_sensor_firmware_check();
    TEST_ASSERT_EQUAL(false, result);
}

radio_harness_read_t wrong_uid_stream[BNET_SENSOR_REPORT_TRIES+1] = {
    { .bytes = 22, .data = "\xd0\x03" VMS_WRONG_UID "\x01"}
};

void test_bnet_sensor_firmware_check_IgnoreWrongUID() {
    bool result;

    radio_harness_set_read(wrong_uid_stream, ARRAY_SIZE(wrong_uid_stream));

    result = bnet_sensor_firmware_check();
    TEST_ASSERT_EQUAL(false, result);
}

radio_harness_read_t wrong_type_stream[BNET_SENSOR_REPORT_TRIES+1] = {
    { .bytes = 13, .data = "\xff\x04" VMS_UID_HEX "\x01"}
};

void test_bnet_sensor_firmware_check_IgnoreWrongType() {
    bool result;

    radio_harness_set_read(wrong_type_stream, ARRAY_SIZE(wrong_type_stream));

    result = bnet_sensor_firmware_check();
    TEST_ASSERT_EQUAL(false, result);
}

radio_harness_read_t download_success_stream[] = {
    { .bytes = 42, .data = "\xd1" SENSOR_UID_HEX VMS_UID_HEX "\x00\x00\x00\x00\x10" DATA_HEX },
    { .bytes = 26, .data = "\xd1" SENSOR_UID_HEX VMS_UID_HEX "\x10\x00\x00\x00\x00" }
};

void check_firmware_send(uint8_t *data, uint32_t offset) {
    TEST_ASSERT_EQUAL_UINT8(BNET_TYPE_FIRMWARE_SEND, data[0]);
    TEST_ASSERT_EQUAL_UINT8_ARRAY(vms_uid, &data[1], sizeof(board_uid_t));
    TEST_ASSERT_EQUAL_UINT8_ARRAY(board_uid, &data[11], sizeof(board_uid_t));
    TEST_ASSERT_EQUAL_UINT8(offset & 0xff, data[21]);
    TEST_ASSERT_EQUAL_UINT8((offset >> 8) & 0xff, data[22]);
    TEST_ASSERT_EQUAL_UINT8((offset >> 16) & 0xff, data[23]);
    TEST_ASSERT_EQUAL_UINT8((offset >> 24) & 0xff, data[24]);
}

void test_bnet_sensor_firmware_download_Success() {
    bool result;

    radio_harness_set_read(download_success_stream, ARRAY_SIZE(download_success_stream));
    result = bnet_sensor_firmware_download();
    TEST_ASSERT_EQUAL(true, result);

    TEST_ASSERT_EQUAL(25 * 2, radio_harness_captured_bytes);
    check_firmware_send(radio_captured, 0);
    check_firmware_send(&radio_captured[25], 16);
}

radio_harness_read_t download_not_avail_stream[BNET_SENSOR_FIRMWARE_TRIES+1] = {
    { .bytes = 26, .data = "\xd1" SENSOR_UID_HEX VMS_UID_HEX "\x00\x00\x00\x00\x00" }
};


void test_bnet_sensor_firmware_download_NotAvail() {
    bool result;

    radio_harness_set_read(download_not_avail_stream, ARRAY_SIZE(download_not_avail_stream));
    result = bnet_sensor_firmware_download();
    TEST_ASSERT_EQUAL(false, result);
}

radio_harness_read_t download_wrong_sensor_uid_stream[BNET_SENSOR_FIRMWARE_TRIES+3] = {
    { .bytes = 42, .data = "\xd1" SENSOR_WRONG_UID_HEX VMS_UID_HEX "\x00\x00\x00\x00\x10" DATA_HEX },
    { .bytes = 26, .data = "\xd1" SENSOR_WRONG_UID_HEX VMS_UID_HEX "\x10\x00\x00\x00\x00" }
};

void test_bnet_sensor_firmware_download_IgnoreWrongSensorUID() {
    bool result;

    radio_harness_set_read(download_wrong_sensor_uid_stream, ARRAY_SIZE(download_wrong_sensor_uid_stream));

    result = bnet_sensor_firmware_download();
    TEST_ASSERT_EQUAL(false, result);
}

radio_harness_read_t download_wrong_type_stream[BNET_SENSOR_FIRMWARE_TRIES+3] = {
    { .bytes = 42, .data = "\xff" SENSOR_UID_HEX VMS_UID_HEX "\x00\x00\x00\x00\x10" DATA_HEX },
    { .bytes = 26, .data = "\xff" SENSOR_UID_HEX VMS_UID_HEX "\x10\x00\x00\x00\x00" }
};

void test_bnet_sensor_firmware_download_IgnoreWrongType() {
    bool result;

    radio_harness_set_read(download_wrong_type_stream, ARRAY_SIZE(download_wrong_type_stream));

    result = bnet_sensor_firmware_download();
    TEST_ASSERT_EQUAL(false, result);
}

int main(int argc, char **argv) {
    UnityBegin("bnet_sensor_firmware.c");
    RUN_TEST(test_bnet_sensor_firmware_check_Avail, 10);
    RUN_TEST(test_bnet_sensor_firmware_check_NotAvail, 20);
    RUN_TEST(test_bnet_sensor_firmware_check_IgnoreWrongUID, 40);
    RUN_TEST(test_bnet_sensor_firmware_check_IgnoreWrongType, 50);
    RUN_TEST(test_bnet_sensor_firmware_download_Success, 60);
    RUN_TEST(test_bnet_sensor_firmware_download_NotAvail, 70);
    RUN_TEST(test_bnet_sensor_firmware_download_IgnoreWrongSensorUID, 80);
    RUN_TEST(test_bnet_sensor_firmware_download_IgnoreWrongType, 90);
    exit(UnityEnd());
}

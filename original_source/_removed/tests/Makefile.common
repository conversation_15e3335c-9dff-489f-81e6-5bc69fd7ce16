QUIET ?= 1
ifeq ($(QUIET),1)
  AD=@
else
  AD=
endif

SRC += unity.c

TESTDIR := $(abspath ..)
TESTAPP := $(TESTDIR)/tests/$(TEST)

BOATOS := $(abspath ../..)

VPATH=$(TESTDIR)/$(TEST):$(TESTDIR)/harness:$(BOATOS)/common:$(BOATOS)/vms:$(BOATOS)/bnet:$(TESTDIR)/unity

OBJDIR = $(TESTDIR)/objs/$(TEST)
TESTAPP= $(TESTDIR)/tests/$(TEST)

IMAGEDIR = $(BOATOS)/images
IMAGEBASE = $(IMAGEDIR)/$(PROJECT_TARGET)

OBJS = $(SRC:%.c=$(OBJDIR)/%.o)

DEPENDENCIES = $(OBJS:%.o=%.d)

INCLUDE_PATHS= -I$(BOATOS)/rtx -I$(BOATOS)/cpu -I$(BOATOS)/devices -I$(BOATOS)/shell -I$(BOATOS)/vms -I$(BOATOS)/sensor -I$(BOATOS)/bnet -I$(BOATOS)/common -I$(TESTDIR)/harness -I$(TESTDIR)/unity

CC      = gcc
CFLAGS += -std=gnu99 -DDEVICE_VERSION=10 -DTARGET_TEST -DTARGET_VMS -D__CMSIS_RTOS -MP -MD -Werror -Wno-format-invalid-specifier -g $(INCLUDE_PATHS)

.PHONY: clean

all:
	@echo "Please run make from the top level test directory."

build: $(TESTAPP)

clean:
	rm -rf $(OBJDIR) tests

$(TESTAPP): | $(TESTDIR)/tests

$(TESTDIR)/tests:
	mkdir -p $@

$(OBJS): | $(OBJDIR)

$(OBJDIR):
	mkdir -p $@

$(OBJDIR)/%.o: %.c
	@echo "Compiling $(subst $(TESTDIR)/,,$(abspath $<))"
	$(AD)$(CC) -c $(CFLAGS) -o $@ $<

$(TESTAPP): $(OBJS)
	@echo "Linking $(TESTAPP)"
	$(CC) -o $@ $(OBJS)

-include $(DEPENDENCIES)

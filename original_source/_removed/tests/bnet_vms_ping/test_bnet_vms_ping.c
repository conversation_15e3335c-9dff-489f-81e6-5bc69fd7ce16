#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include <stdlib.h>
#include "unity.h"

#include "pelagic.h"
#include "cmsis_os.h"
#include "bnet.h"
#include "rtc_api.h"
#include "radio_harness.h"


board_uid_t board_uid = { 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a };
board_uid_t other_uid = { 0xa0, 0xa1, 0xa2, 0xa3, 0xa4, 0xa5, 0xa6, 0xa7, 0xa8, 0xa9 };
uint8_t radio_captured[4096];

volatile bool bnet_packet_watch = false;

bool board_info_have_imei;
imei_t board_info_imei = { 0xd0, 0xd1, 0xd2, 0xd3, 0xd4, 0xd5, 0xd6, 0x7 };

uint8_t bnet_send_buffer[128];

void setUp() {
    radio_harness_init();
    radio_harness_capture(radio_captured);
    board_info_have_imei = false;
    memset(bnet_send_buffer, 0, sizeof(bnet_send_buffer));
}

void bnet_delay() {
}

void test_bnet_process_ping_BroadcastSuccess() {
    bnet_ping_t ping;

    ping.type = BNET_TYPE_PING;
    ping.ping_type = BNET_BROADCAST;
    ping.sequence = 22;
    uid_copy(ping.from_uid, other_uid);

    bnet_process_ping(&ping, sizeof(bnet_ping_t));

    TEST_ASSERT_EQUAL(25, radio_harness_captured_bytes);
    TEST_ASSERT_EQUAL_UINT8(BNET_TYPE_PONG, radio_captured[0]);
    TEST_ASSERT_EQUAL_UINT8(BNET_TYPE_UID, radio_captured[1]);
    TEST_ASSERT_EQUAL_UINT8_ARRAY(other_uid, &radio_captured[2], sizeof(board_uid_t));
    TEST_ASSERT_EQUAL_UINT8_ARRAY(board_uid, &radio_captured[12], sizeof(board_uid_t));
    TEST_ASSERT_EQUAL_UINT8(22, radio_captured[22]);
}

void test_bnet_process_ping_IMEISuccess() {
    uint8_t ping_data[128];
    bnet_ping_t *ping = (bnet_ping_t *) ping_data;

    ping->type = BNET_TYPE_PING;
    ping->ping_type = BNET_TYPE_IMEI;
    ping->sequence = 22;
    uid_copy(ping->from_uid, other_uid);
    imei_copy(ping->destination, board_info_imei);
    board_info_have_imei = true;

    bnet_process_ping(ping, sizeof(bnet_ping_t) + sizeof(imei_t));

    TEST_ASSERT_EQUAL(33, radio_harness_captured_bytes);
    TEST_ASSERT_EQUAL_UINT8(BNET_TYPE_PONG, radio_captured[0]);
    TEST_ASSERT_EQUAL_UINT8(BNET_TYPE_IMEI, radio_captured[1]);
    TEST_ASSERT_EQUAL_UINT8_ARRAY(other_uid, &radio_captured[2], sizeof(board_uid_t));
    TEST_ASSERT_EQUAL_UINT8_ARRAY(board_uid, &radio_captured[12], sizeof(board_uid_t));
    TEST_ASSERT_EQUAL_UINT8(22, radio_captured[22]);
    TEST_ASSERT_EQUAL_UINT8_ARRAY(board_info_imei, &radio_captured[25], sizeof(imei_t));
}

void test_bnet_process_ping_PartialIMEISuccess() {
    uint8_t ping_data[128];
    bnet_ping_t *ping = (bnet_ping_t *) ping_data;
    imei_t partial_imei = { 0x0, 0x0, 0x0, 0x0, 0x0, 0xd5, 0xd6, 0x7 };

    ping->type = BNET_TYPE_PING;
    ping->ping_type = BNET_TYPE_IMEI;
    ping->sequence = 22;
    uid_copy(ping->from_uid, other_uid);
    imei_copy(ping->destination, board_info_imei);
    board_info_have_imei = true;

    bnet_process_ping(ping, sizeof(bnet_ping_t) + sizeof(imei_t));

    TEST_ASSERT_EQUAL(33, radio_harness_captured_bytes);
    TEST_ASSERT_EQUAL_UINT8(BNET_TYPE_PONG, radio_captured[0]);
    TEST_ASSERT_EQUAL_UINT8(BNET_TYPE_IMEI, radio_captured[1]);
    TEST_ASSERT_EQUAL_UINT8_ARRAY(other_uid, &radio_captured[2], sizeof(board_uid_t));
    TEST_ASSERT_EQUAL_UINT8_ARRAY(board_uid, &radio_captured[12], sizeof(board_uid_t));
    TEST_ASSERT_EQUAL_UINT8(22, radio_captured[22]);
    TEST_ASSERT_EQUAL_UINT8_ARRAY(board_info_imei, &radio_captured[25], sizeof(imei_t));
}

void test_bnet_process_ping_UIDSuccess() {
    uint8_t ping_data[128];
    bnet_ping_t *ping = (bnet_ping_t *) ping_data;

    ping->type = BNET_TYPE_PING;
    ping->ping_type = BNET_TYPE_UID;
    ping->sequence = 22;
    uid_copy(ping->from_uid, other_uid);
    uid_set_me(ping->destination);

    bnet_process_ping(ping, sizeof(bnet_ping_t) + sizeof(board_uid_t));

    TEST_ASSERT_EQUAL(25, radio_harness_captured_bytes);
    TEST_ASSERT_EQUAL_UINT8(BNET_TYPE_PONG, radio_captured[0]);
    TEST_ASSERT_EQUAL_UINT8(BNET_TYPE_UID, radio_captured[1]);
    TEST_ASSERT_EQUAL_UINT8_ARRAY(other_uid, &radio_captured[2], sizeof(board_uid_t));
    TEST_ASSERT_EQUAL_UINT8_ARRAY(board_uid, &radio_captured[12], sizeof(board_uid_t));
    TEST_ASSERT_EQUAL_UINT8(22, radio_captured[22]);
}

void check_for_no_response() {
    TEST_ASSERT_EQUAL(0, radio_harness_captured_bytes);
}

void test_bnet_process_ping_IgnoreWrongByteCount() {
    uint8_t ping_data[128];
    bnet_ping_t *ping = (bnet_ping_t *) ping_data;

    ping->type = BNET_TYPE_PING;
    ping->ping_type = BNET_BROADCAST;
    ping->sequence = 22;
    uid_copy(ping->from_uid, other_uid);

    bnet_process_ping(ping, sizeof(bnet_ping_t) - 1);
    check_for_no_response();

    ping->ping_type = BNET_TYPE_UID;
    uid_set_me(ping->destination);
    bnet_process_ping(ping, sizeof(bnet_ping_t) + sizeof(board_uid_t) - 1);
    check_for_no_response();

    ping->ping_type = BNET_TYPE_IMEI;
    imei_copy(ping->destination, board_info_imei);
    board_info_have_imei = true;

    bnet_process_ping(ping, sizeof(bnet_ping_t) + sizeof(imei_t) - 1);
    check_for_no_response();
}

void test_bnet_process_ping_IgnoreWrongUID() {
    uint8_t ping_data[128];
    bnet_ping_t *ping = (bnet_ping_t *) ping_data;

    ping->type = BNET_TYPE_PING;
    ping->ping_type = BNET_TYPE_UID;
    ping->sequence = 22;
    uid_copy(ping->from_uid, other_uid);
    memset(ping->destination, 0xff, sizeof(board_uid_t));

    bnet_process_ping(ping, sizeof(bnet_ping_t) + sizeof(board_uid_t));
    check_for_no_response();
}

void test_bnet_process_ping_IgnoreWrongIMEI() {
    uint8_t ping_data[128];
    bnet_ping_t *ping = (bnet_ping_t *) ping_data;

    ping->type = BNET_TYPE_PING;
    ping->ping_type = BNET_TYPE_IMEI;
    ping->sequence = 22;
    uid_copy(ping->from_uid, other_uid);
    memset(ping->destination, 0xff, sizeof(imei_t));
    board_info_have_imei = true;

    bnet_process_ping(ping, sizeof(bnet_ping_t) + sizeof(imei_t));
    check_for_no_response();
}

int main(int argc, char **argv) {
    UnityBegin("bnet_vms_ping.c");
    RUN_TEST(test_bnet_process_ping_BroadcastSuccess, 10);
    RUN_TEST(test_bnet_process_ping_UIDSuccess, 20);
    RUN_TEST(test_bnet_process_ping_IMEISuccess, 30);
    RUN_TEST(test_bnet_process_ping_PartialIMEISuccess, 31);
    RUN_TEST(test_bnet_process_ping_IgnoreWrongByteCount, 40);
    RUN_TEST(test_bnet_process_ping_IgnoreWrongUID, 50);
    RUN_TEST(test_bnet_process_ping_IgnoreWrongIMEI, 60);

    exit(UnityEnd());
}

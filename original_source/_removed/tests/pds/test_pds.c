#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include <stdlib.h>
#include "unity.h"

#include "pelagic.h"
#include "flash_store.h"
#include "modem.h"
#include "modem_bodytrace.h"
#include "modem_harness.h"
#include "stats.h"
#include "firmware.h"
#include "modem.h"
#include "modem_log.h"
#include "winbond_flash.h"
#include "kinetis_flash.h"
#include "pds.h"
#include "compress.h"


extern uint8_t kinetis_store[];

modem_log_t *modem_log = NULL;

enum {
    TEST_PARTITION_SIZE = (64*1024)
};

shared_buffer_t the_buffer;
uint8_t staging_buffer[4096];
uint8_t compress_buffer[COMPRESS_BUFFER_SIZE + 200];

void test_pds_send_log_Success() {
    modem_result_t result;
    uint32_t timestamp;
    uint8_t data[20];
    uint8_t buffer[8192];

    flash_partition_t partition = {
        .name           = "test-partition",
        .is_raw         = 0,
        .offset         = 0,
        .size           = TEST_PARTITION_SIZE,
        .staging_buffer = staging_buffer,
        .device         = &winbond_flash_device
    };

    for (int i = 0; i < sizeof(data); i++)
        data[i] = i;

    modem_harness_init();

    winbond_flash_init();
    flash_store_setup(&partition);
    flash_store_write(&partition, data, sizeof(data));

    TEST_ASSERT_EQUAL_UINT32(partition.bytes_stored, sizeof(data));

    timestamp = 0x12345678;

    rtc_write(timestamp);
    modem_harness_capture(buffer);

    result = pds_send_log(&partition, PDS_TYPE_EVENT_LOG, PDS_EVENT_LOG_VERSION, NULL);

    TEST_ASSERT_EQUAL_INT(MODEM_SUCCESS, result);
    TEST_ASSERT_EQUAL_INT(modem_captured_bytes, 23);

    // Inspect the buffer to see if the packet was constructed correctly
    TEST_ASSERT_EQUAL_UINT8(PDS_TYPE_EVENT_LOG, buffer[0]);
    TEST_ASSERT_EQUAL_UINT8(PDS_CHUNK_VERSION, buffer[1]);
    TEST_ASSERT_EQUAL_UINT8(0, buffer[2]);

    for (int i = 0; i < sizeof(data); i++) {
        TEST_ASSERT_EQUAL_UINT8(data[i], buffer[3+i]);
    }

    TEST_ASSERT_EQUAL_UINT32(0, partition.bytes_stored);
}

uint8_t compressed_result[] = {
	0xff, 0xd5, 0x40, 0x1f, 0x00, 0x7c, 0x01, 0xf0, 0x07, 0xc0,
	0x1f, 0x00, 0x7c, 0x01, 0xf0, 0x07, 0xc0, 0x1f, 0x00, 0x7c,
	0x01, 0xf0, 0x07, 0xc0, 0x15 };

void test_pds_send_log_CompressAndSuccess() {
    bool result;
    uint32_t timestamp;
    uint8_t data[200];
    uint8_t buffer[8192];

    flash_partition_t partition = {
        .name           = "test-partition",
        .is_raw         = 0,
        .offset         = 0,
        .size           = TEST_PARTITION_SIZE,
        .staging_buffer = staging_buffer,
        .device         = &winbond_flash_device
    };

    for (int i = 0; i < sizeof(data); i++)
        data[i] = (i & 1) ? 0x55 : 0xff;

    modem_harness_init();

    winbond_flash_init();
    flash_store_setup(&partition);
    flash_store_write(&partition, data, sizeof(data));

    timestamp = 0x12345678;

    rtc_write(timestamp);
    modem_harness_capture(buffer);

    result = pds_send_log(&partition, PDS_TYPE_EVENT_LOG, PDS_EVENT_LOG_VERSION, NULL);

    TEST_ASSERT_EQUAL_INT(true, result);
    TEST_ASSERT_EQUAL_INT(28, modem_captured_bytes);

    // Inspect the buffer to see if the packet was constructed correctly
    TEST_ASSERT_EQUAL_UINT8(PDS_TYPE_EVENT_LOG, buffer[0]);
    TEST_ASSERT_EQUAL_UINT8(PDS_CHUNK_VERSION, buffer[1]);
    TEST_ASSERT_EQUAL_UINT8(PDS_CHUNK_FLAG_COMPRESSED, buffer[2]);

    TEST_ASSERT_EQUAL_UINT8_ARRAY(compressed_result, &buffer[3], sizeof(compressed_result));
}

void test_pds_send_log_Failure() {
    bool result;
    uint32_t timestamp;
    uint8_t data[20];
    uint8_t buffer[8192];

    flash_partition_t partition = {
        .name           = "test-partition",
        .is_raw         = 0,
        .offset         = 0,
        .size           = TEST_PARTITION_SIZE,
        .staging_buffer = staging_buffer,
        .device         = &winbond_flash_device
    };

    for (int i = 0; i < sizeof(data); i++)
        data[i] = i;

    winbond_flash_init();
    flash_store_setup(&partition);
    flash_store_write(&partition, data, sizeof(data));
    timestamp = 0x12345678;

    rtc_write(timestamp);
    modem_harness_response = MODEM_ERR_TIMEOUT;

    result = pds_send_log(&partition, PDS_TYPE_EVENT_LOG, PDS_EVENT_LOG_VERSION, NULL);
    TEST_ASSERT_EQUAL_INT(false, result);
}


int main(int argc, char **argv) {
    UnityBegin("pds.c");
    RUN_TEST(test_pds_send_log_Success, 10);
    RUN_TEST(test_pds_send_log_CompressAndSuccess, 20);
    RUN_TEST(test_pds_send_log_Failure, 30);
    exit(UnityEnd());
}

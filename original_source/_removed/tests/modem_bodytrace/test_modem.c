#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include <stdlib.h>
#include "unity.h"

#include "pelagic.h"
#include "flash_store.h"
#include "serial_harness.h"
#include "stats.h"
#include "modem.h"
#include "modem_bodytrace.h"
#include "modem_log.h"
#include "winbond_flash.h"
#include "kinetis_flash.h"
#include "event_harness.h"

extern uint8_t kinetis_store[];

shared_buffer_t the_buffer;

modem_log_t *modem_log = NULL;

void setUp() {
    event_harness_init();
    modem_init();
}

void tearDown() {
    serial_teardown(GSM_PORT);
}

void test_modem_crc_okay_GoodCRC() {
    uint8_t packet[16];

    for (int i = 1; i < 13; i++)
        packet[i] = i;

    packet[13] = 0x56;
    packet[14] = 0x56;
    TEST_ASSERT_EQUAL_INT(1, modem_crc_okay("good crc", packet, 16));
}

void test_modem_crc_okay_BadCRC() {
    uint8_t packet[16];

    for (int i = 1; i < 13; i++)
        packet[i] = i;

    packet[13] = 0xff;
    packet[14] = 0xff;
    TEST_ASSERT_EQUAL_INT(0, modem_crc_okay("bad crc", packet, 16));
}

serial_stream_t good_response_stream = {
    .count = 1,
    .sequences = {
      { .data = "\x3e\x42\x31\x80\x3b",  .bytes = 5 }
  }
};


void test_modem_receive_ack_response_GoodResponse() {
    modem_result_t result;
    jmp_buf env;

    serial_stream(GSM_PORT, &good_response_stream, NULL, &env);

    if (setjmp(env)) {
        TEST_FAIL_MESSAGE("routine read too much");
        return;
    }

    result = modem_receive_ack_response("read chunk", MODEM_CMD_READ_CHUNK);

    TEST_ASSERT_EQUAL_INT(MODEM_SUCCESS, result);
}

serial_stream_t bad_response_stream = {
    .count = 3,
    .sequences = {
      { .data = NULL,  .bytes = 0 },  // First test - timeout
      { .data = "\x3e\x42\xff\xff\x3b",  .bytes = 5 },  // Second - bad CRC
      { .data = "\x3e\x00\x00\x00\x3b",  .bytes = 5 }   // Third - wrong command
  }
};

void test_modem_receive_ack_response_NoResponse() {
    modem_result_t result;
    jmp_buf env;

    if (setjmp(env)) {
        TEST_FAIL_MESSAGE("routine read too much");
        return;
    }

    serial_stream(GSM_PORT, &bad_response_stream, NULL, &env);
    TEST_ASSERT_EQUAL_INT(MODEM_ERR_TIMEOUT, modem_receive_ack_response("read chunk", MODEM_CMD_READ_CHUNK));
    TEST_ASSERT_EQUAL_INT(MODEM_ERR_CRC, modem_receive_ack_response("read chunk", MODEM_CMD_READ_CHUNK));
    TEST_ASSERT_EQUAL_INT(MODEM_ERR_MISMATCH, modem_receive_ack_response("read chunk", MODEM_CMD_READ_CHUNK));
}


void test_modem_send_packet() {
    uint8_t packet[12];
    char    buffer[8192];

    for (int i = 0; i < 12; i++)
        packet[i] = i+1;

    serial_capture(GSM_PORT, buffer);
    modem_send_packet(packet, 12);

    TEST_ASSERT_EQUAL_UINT8(BT_PACKET_PRELUDE, buffer[0]);

    for (int i = 0; i < 12; i++) {
        TEST_ASSERT_EQUAL_UINT8(packet[i], buffer[i+1]);    // data send
    }

    TEST_ASSERT_EQUAL_UINT8(0x56, buffer[13]);     // CRC
    TEST_ASSERT_EQUAL_UINT8(0x56, buffer[14]);     // CRC
    TEST_ASSERT_EQUAL_UINT8(BT_PACKET_TRAILER, buffer[15]);
}

serial_stream_t send_data_simple_stream = {
    .count = 2,
    .sequences = {
      { .data = "\x3e\x40\xf0\x01\x3b",  .bytes = 5 },  // MODEM_CMD_SEND_DATA ack
      { .data = "\x3e\x02\x04\x01\x02\x03\x04\x05\x06\x07\x08\x09\x2f\x4e\x3b",  .bytes = 15 },  // Modem Status - sent packet
  }
};


void test_modem_send_data_Simple() {
    modem_result_t result;
    jmp_buf env;
    uint8_t packet[12];

    for (int i = 0; i < 12; i++)
        packet[i] = i+1;

    if (setjmp(env)) {
        TEST_FAIL_MESSAGE("read more than expected");
        return;
    }

    serial_stream(GSM_PORT, &send_data_simple_stream, NULL, &env);
    result = modem_send_data(packet, 12, MODEM_WAIT);
    TEST_ASSERT_EQUAL_INT(MODEM_SUCCESS, result);
}

serial_stream_t send_data_failure_stream = {
    .count = 2,
    .sequences = {
      { .data = "\x3e\x40\xf0\x01\x3b",  .bytes = 5 },  // MODEM_CMD_SEND_DATA ack
      { .data = "\x3e\x02\x84\x01\x02\x03\x04\x05\x06\x07\x08\x09\xc7\x49\x3b",  .bytes = 15 },  // Error connecting to network
  }
};

void test_modem_send_data_Failure() {
    modem_result_t result;
    jmp_buf env;
    uint8_t packet[12];

    for (int i = 0; i < 12; i++)
        packet[i] = i+1;

    if (setjmp(env)) {
        TEST_FAIL_MESSAGE("read more than expected");
        return;
    }

    serial_stream(GSM_PORT, &send_data_failure_stream, NULL, &env);
    result = modem_send_data(packet, 12, MODEM_WAIT);

    TEST_ASSERT_EQUAL_INT(MODEM_ERR_FAULT, result);
}

serial_stream_t modem_status_good_stream = {
    .count = 1,
    .sequences = {
      { .data = "\x3e\x02\x04\x01\x02\x03\x04\x05\x06\x07\x08\x09\x2f\x4e\x3b",  .bytes = 15 },  // Modem Status - sent packet
  }
};

void test_modem_status_Good() {
    modem_result_t result;
    modem_status_t status;
    jmp_buf env;

    if (setjmp(env)) {
        TEST_FAIL_MESSAGE("read more than expected");
        return;
    }

    serial_stream(GSM_PORT, &modem_status_good_stream, NULL, &env);

    result = modem_get_status(&status);

    TEST_ASSERT_EQUAL_INT(MODEM_SUCCESS, result);

    TEST_ASSERT_EQUAL_UINT8(4, status.state);
    TEST_ASSERT_EQUAL_UINT16(0x0102, status.voltage);
    TEST_ASSERT_EQUAL_UINT16(0x0405, status.adc);
    TEST_ASSERT_EQUAL_UINT8(0x06, status.rssi);
    TEST_ASSERT_EQUAL_UINT8(0x07, status.signal_strength);
    TEST_ASSERT_EQUAL_UINT8(0x08, status.provisioned);
    TEST_ASSERT_EQUAL_UINT8(0x09, status.temperature);
}

serial_stream_t modem_status_failure_stream = {
    .count = 5,
    .sequences = {
      { .data = NULL,  .bytes = 0 },  // First failure - timeout
      { .data = NULL,  .bytes = 0 },  // First failure - 2nd timeout
      { .data = NULL,  .bytes = 0 },  // First failure - 3rd timeout
      { .data = "\x3e\x02\x04\x01\x02\x03\x04\x05\x06\x07\x08\x09\xFF\xFF\x3b",  .bytes = 15 },  // Second failure - bad CRC
      { .data = "\x3e\x01\x04\x01\x02\x03\x04\x05\x06\x07\x08\x09\x6b\x41\x3b",  .bytes = 15 },  // Second failure - command mismatch
  }
};

void test_modem_status_Failure() {
    modem_result_t result;
    modem_status_t status;
    jmp_buf env;

    if (setjmp(env)) {
        TEST_FAIL_MESSAGE("read more than expected");
        return;
    }

    serial_stream(GSM_PORT, &modem_status_failure_stream, NULL, &env);

    result = modem_get_status(&status);
    TEST_ASSERT_EQUAL_INT(MODEM_ERR_TIMEOUT, result);
    TEST_ASSERT_EQUAL_INT(true, event_harness_have_entry(EVT_BT_READ_TIMEOUT));

    event_harness_init();
    result = modem_get_status(&status);
    TEST_ASSERT_EQUAL_INT(MODEM_ERR_CRC, result);
    TEST_ASSERT_EQUAL_INT(true, event_harness_have_entry(EVT_BT_CRC_ERR));

    event_harness_init();
    result = modem_get_status(&status);
    TEST_ASSERT_EQUAL_INT(MODEM_ERR_MISMATCH, result);
}

serial_stream_t message_count_stream = {
    .count = 1,
    .sequences = {
        { .data = "\x3e\x03\x00\x04\x00\x00\x00\x46\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x83\xf1\x3b",  .bytes = 23 },  // Message count response
  }
};

void test_modem_get_message_count_Success() {
    uint32_t msg_count = 0;
    modem_result_t result;
    jmp_buf env;
    uint8_t msg_status_cmd[] = { 0x3c, 0x03, 0x01, 0x40, 0x3b };
    uint8_t buffer[8192];


    if (setjmp(env)) {
        TEST_FAIL_MESSAGE("read more than expected");
        return;
    }

    serial_stream(GSM_PORT, &message_count_stream, NULL, &env);
    serial_capture(GSM_PORT, buffer);

    result = modem_get_message_count(&msg_count);

    TEST_ASSERT_EQUAL_INT(MODEM_SUCCESS, result);
    TEST_ASSERT_EQUAL_INT(4, msg_count);
}

serial_stream_t message_count_failure_stream = {
    .count = 2,
    .sequences = {
        { .data = NULL,  .bytes = 0 },  // timeout
        { .data = NULL,  .bytes = 0 },  // timeout
    }
};

void test_modem_get_message_count_Failure() {
    uint32_t msg_count = -1;
    modem_result_t result;
    jmp_buf env;


    if (setjmp(env)) {
        TEST_FAIL_MESSAGE("read more than expected");
        return;
    }

    serial_stream(GSM_PORT, &message_count_failure_stream, NULL, &env);
    result = modem_get_message_count(&msg_count);
    TEST_ASSERT_EQUAL_INT(MODEM_ERR_TIMEOUT, result);
    TEST_ASSERT_EQUAL_INT(true, event_harness_have_entry(EVT_BT_READ_TIMEOUT));

}

serial_stream_t read_chunk_success_stream = {
    .count = 3,
    .sequences = {
        { .data = "\x3e\x42\x00\x00\x00\x10\x00\x00\x00\x02", .bytes = 10 },
        { .data = "\x00\x01\x02\x03\x04\x05\x06\x07\x08\x09\x0a\x0b\x0c\x0d\x0e\x0f", .bytes=16 },
        { .data = "\x81\x44\x3b",  .bytes = 3 },  // Message count response
  }
};

void test_modem_read_chunk_Success() {
    uint32_t bytes = 0, remaining = 0;
    modem_result_t result;
    jmp_buf env;
    uint8_t read_chunk_cmd[] = { 0x3c,  0x42,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x40,  0x53,  0x29,  0x3b };
    uint8_t buffer[8192];

    if (setjmp(env)) {
        TEST_FAIL_MESSAGE("read more than expected");
        return;
    }

    serial_stream(GSM_PORT, &read_chunk_success_stream, NULL, &env);
    serial_capture(GSM_PORT, buffer);

    result = modem_read_chunk(buffer, 0, 16, &bytes, &remaining);

    TEST_ASSERT_EQUAL_INT(MODEM_SUCCESS, result);
    TEST_ASSERT_EQUAL_UINT32(16, bytes);
    TEST_ASSERT_EQUAL_UINT32(2, remaining);

    for (int i = 0; i < 16; i++) {
        TEST_ASSERT_EQUAL_UINT8(i, buffer[i]);
    }
}

serial_stream_t read_chunk_fail_stream = {
    .count = 4,
    .sequences = {
        // CRC failure
        { .data = "\x3e\x42\x00\x00\x00\x10\x00\x00\x00\x02", .bytes = 10 },
        { .data = "\x00\x01\x02\x03\x04\x05\x06\x07\x08\x09\x0a\x0b\x0c\x0d\x0e\x0f", .bytes=16 },
        { .data = "\x44\x44\x3b",  .bytes = 3 },  // Message count response
        // Timeout
        { .data = 0, .bytes = 0 },
  }
};

void test_modem_read_chunk_Failure() {
    uint32_t bytes = 0, remaining = 0;
    modem_result_t result;
    jmp_buf env;
    uint8_t read_chunk_cmd[] = { 0x3c,  0x42,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x40,  0x53,  0x29,  0x3b };
    uint8_t buffer[8192];

    if (setjmp(env)) {
        TEST_FAIL_MESSAGE("read more than expected");
        return;
    }

    serial_stream(GSM_PORT, &read_chunk_fail_stream, NULL, &env);
    serial_capture(GSM_PORT, buffer);

    result = modem_read_chunk(buffer, 0, 16, &bytes, &remaining);
    TEST_ASSERT_EQUAL_INT(MODEM_ERR_CRC, result);
    TEST_ASSERT_EQUAL_INT(true, event_harness_have_entry(EVT_BT_CRC_ERR));

    event_harness_init();

    result = modem_read_chunk(buffer, 0, 16, &bytes, &remaining);
    TEST_ASSERT_EQUAL_INT(MODEM_ERR_TIMEOUT, result);
    TEST_ASSERT_EQUAL_INT(true, event_harness_have_entry(EVT_BT_READ_CHUNK_ERR));

}

serial_stream_t read_chunk_toobig_stream = {
    .count = 1,
    .sequences = {
        { .data = "\x3e\x42\x00\x00\xff\xff\x00\x00\x00\x02", .bytes = 10 },
  }
};

void test_modem_read_chunk_TooBig() {
    uint32_t bytes = 0, remaining = 0;
    modem_result_t result;
    jmp_buf env;
    uint8_t read_chunk_cmd[] = { 0x3c,  0x42,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x40,  0x53,  0x29,  0x3b };
    uint8_t buffer[8192];

    if (setjmp(env)) {
        TEST_FAIL_MESSAGE("read more than expected");
        return;
    }

    serial_stream(GSM_PORT, &read_chunk_toobig_stream, NULL, &env);
    serial_capture(GSM_PORT, buffer);

    result = modem_read_chunk(buffer, 0, 16, &bytes, &remaining);
    TEST_ASSERT_EQUAL_INT(MODEM_ERR_TOO_BIG, result);
    TEST_ASSERT_EQUAL_INT(true, event_harness_have_entry(EVT_BT_READ_CHUNK_TOO_BIG));
}

serial_stream_t ping_success_stream = {
    .count = 1,
    .sequences = {
        { .data = "\x3c\x00\x00\x00\x3b", .bytes = 5 },
  }
};

void test_modem_ping_Success() {
    modem_result_t result;
    jmp_buf env;

    serial_stream(GSM_PORT, &ping_success_stream, NULL, &env);
    result = modem_ping();

    TEST_ASSERT_EQUAL(MODEM_SUCCESS, result);
}

serial_stream_t ping_failure_stream = {
    .count = 2,
    .sequences = {
        { .data = "\x3c\x00\xee\xdd\x3b", .bytes = 5 },
        { .data = NULL, .bytes = 0 },
  }
};

void test_modem_ping_Failure() {
    modem_result_t result;
    jmp_buf env;

    if (setjmp(env)) {
        TEST_FAIL_MESSAGE("read more than expected");
        return;
    }

    serial_stream(GSM_PORT, &ping_failure_stream, NULL, &env);

    result = modem_ping();
    TEST_ASSERT_EQUAL(MODEM_ERR_CRC, result);

    result = modem_ping();
    TEST_ASSERT_EQUAL(MODEM_ERR_TIMEOUT, result);
}

int main(int argc, char **argv) {
    UnityBegin("modem_bodytrace.c");
    RUN_TEST(test_modem_crc_okay_GoodCRC, 10);
    RUN_TEST(test_modem_crc_okay_BadCRC, 20);
    RUN_TEST(test_modem_receive_ack_response_GoodResponse, 30);
    RUN_TEST(test_modem_receive_ack_response_NoResponse, 40);
    RUN_TEST(test_modem_send_packet, 50);
    RUN_TEST(test_modem_send_data_Simple, 60);
    RUN_TEST(test_modem_send_data_Failure, 70);
    RUN_TEST(test_modem_status_Good, 80);
    RUN_TEST(test_modem_status_Failure, 90);
    RUN_TEST(test_modem_read_chunk_TooBig, 91);
    RUN_TEST(test_modem_get_message_count_Success, 150);
    RUN_TEST(test_modem_get_message_count_Failure, 160);
    RUN_TEST(test_modem_read_chunk_Success, 170);
    RUN_TEST(test_modem_read_chunk_Failure, 180);
    RUN_TEST(test_modem_ping_Success, 190);
    RUN_TEST(test_modem_ping_Failure, 200);
    exit(UnityEnd());
}

#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include <stdlib.h>

#include "unity.h"

#include "pelagic-types.h"
#include "cmsis_os.h"
#include "flash_store.h"
#include "winbond_flash.h"

#define TEST_PARTITION_SIZE (64*1024)


//
// TODO : NEED TO TEST FOR A LOOPED BUFFER AND FLASH_STORE_INIT() setups staging_buffer correctly.
//

extern uint8_t winbond_store[];

uint8_t staging_buffer[4096];

void setUp() {
    winbond_flash_init();
}

void test_flash_store_StructSizes() {
    TEST_ASSERT_EQUAL_INT(FLASH_PAGE_SIZE, sizeof(flash_page_t));
    TEST_ASSERT_EQUAL_INT(16, sizeof(flash_page_header_t));
    TEST_ASSERT_EQUAL_INT(2, sizeof(flash_page_footer_t));
    TEST_ASSERT_EQUAL_INT(FLASH_DATA_SIZE, sizeof(flash_page_t) - (sizeof(flash_page_header_t) + sizeof(flash_page_footer_t)));
    TEST_ASSERT_EQUAL_INT(FREE_MARKER_OFFSET, &((flash_page_header_t *) 0)->free_marker);
}

void test_flash_store_setup_SetupRaw(void) {
    flash_partition_t raw = {
        .is_raw         = 1,
        .staging_offset = 12,
        .staging_page   = 1234,
        .device         = &winbond_flash_device
    };


    flash_store_setup(&raw);

    TEST_ASSERT_EQUAL_INT(0, raw.staging_offset);
    TEST_ASSERT_EQUAL_INT(0, raw.staging_page);
}


void test_flash_store_setup_FlashUninitialized (void) {
    flash_partition_t normal = {
        .name           = "test-partition",
        .is_raw         = 0,
        .offset         = 0,
        .size           = TEST_PARTITION_SIZE,
        .staging_buffer = staging_buffer,
        .device         = &winbond_flash_device
    };

    flash_store_setup(&normal);

    TEST_ASSERT_EQUAL_INT(0, normal.staging_offset);
    TEST_ASSERT_EQUAL_INT(0, normal.staging_page);
    TEST_ASSERT_EQUAL_INT(0, normal.is_full);
}

void test_flash_store_setup_FlashInitialized(void) {
    flash_partition_t normal = {
        .name           = "test-partition",
        .is_raw         = 0,
        .offset         = 0,
        .size           = TEST_PARTITION_SIZE,
        .staging_buffer = staging_buffer,
        .device         = &winbond_flash_device
    };

    flash_page_t page;

    page.header.magic = FLASH_STORE_MAGIC;
    page.header.size = 20;
    page.header.free_marker = NOT_FREE_MARKER;
    page.header.sequence = 11;

    winbond_flash_write(0, &page, sizeof(page));
    flash_store_setup(&normal);

    TEST_ASSERT_EQUAL_INT(0, normal.staging_offset);
    TEST_ASSERT_EQUAL_INT(1, normal.staging_page);
    TEST_ASSERT_EQUAL_INT(1, normal.pages_used);
    TEST_ASSERT_EQUAL_INT(20, normal.bytes_stored);
    TEST_ASSERT_EQUAL_INT(false, normal.is_full);
}

void test_flash_store_erase(void) {
    flash_partition_t part = {
        .name           = "test-partition",
        .is_raw         = 0,
        .offset         = 4096,
        .size           = TEST_PARTITION_SIZE,
        .staging_buffer = staging_buffer,
        .staging_page   = 1,
        .device         = &winbond_flash_device
    };
    uint8_t page[FLASH_PAGE_SIZE];

    memset(page, 0xaa, 256);
    winbond_flash_write(0, page, sizeof(page));
    winbond_flash_write(4096, page, sizeof(page));
    flash_store_erase(&part);
    winbond_flash_read(0, page, sizeof(page));

    for (int i = 0; i < 256; i++) {
        TEST_ASSERT_EQUAL(0xaa, page[i]);
    }

    winbond_flash_read(4096, page, sizeof(page));
    for (int i = 0; i < 256; i++) {
        TEST_ASSERT_EQUAL(0xff, page[i]);
    }
}

void test_flash_store_write_StayWithinStagingBuffer(void) {
    flash_partition_t part = {
        .name            = "test-partition",
        .is_raw          = 0,
        .offset          = 0,
        .size            = TEST_PARTITION_SIZE,
        .staging_buffer  = staging_buffer,
        .staging_offset  = 0,
        .staging_page    = 0,
        .device          = &winbond_flash_device
    };

    uint8_t page[200];

    for (int i = 0; i < 200; i++) {
        page[i] = i;
    }

    flash_store_setup(&part);
    flash_store_write(&part, page, 200);

    TEST_ASSERT_EQUAL_INT(200, part.staging_offset);
    TEST_ASSERT_EQUAL_INT(0, part.pages_used);
    TEST_ASSERT_EQUAL_INT(200, part.bytes_stored);

    for (int i = 0; i < 200; i++) {
        TEST_ASSERT_EQUAL_INT(i, part.staging_buffer[i + sizeof(flash_page_header_t)]);
    }
}


void test_flash_store_write_FlushStagingBuffer(void) {
    int bytes;
    flash_partition_t part = {
        .name           = "test-partition",
        .is_raw         = 0,
        .offset         = 0,
        .size           = TEST_PARTITION_SIZE,
        .staging_buffer = staging_buffer,
        .staging_offset = 0,
        .staging_page   = 0,
        .device         = &winbond_flash_device
    };
    flash_page_header_t *header;
    uint8_t data[600];

    for (int i = 0; i < sizeof(data); i++) {
        data[i] = i;
    }

    flash_store_setup(&part);

    flash_store_write(&part, data, sizeof(data));

    TEST_ASSERT_EQUAL_INT(2, part.pages_used);
    TEST_ASSERT_EQUAL_INT(sizeof(data), part.bytes_stored);
    TEST_ASSERT_EQUAL_INT(2, part.staging_page);
    TEST_ASSERT_EQUAL_INT(124, part.staging_offset);

    // check the two pages that should have been writing to the flash

    for (int page = 0; page < 2; page++) {
        header = (flash_page_header_t *)&winbond_store[page * 256];

        TEST_ASSERT_EQUAL_UINT16(FLASH_STORE_MAGIC, header->magic);
        TEST_ASSERT_EQUAL_UINT8(NOT_FREE_MARKER, header->free_marker);
        TEST_ASSERT_EQUAL_UINT8(FLASH_DATA_SIZE, header->size);

        for (int i = 0; i < FLASH_DATA_SIZE; i++) {
            TEST_ASSERT_EQUAL_INT((uint8_t)((page * FLASH_DATA_SIZE) + i), winbond_store[((page * 256) + sizeof(flash_page_header_t)) + i]);
        }
    }

    // the 3rd page (page #2) should be left erased -  aka all 0xff
    header = (flash_page_header_t *)&winbond_store[2 * 256];

    TEST_ASSERT_EQUAL_UINT16(0xffff, header->magic);
    TEST_ASSERT_EQUAL_UINT8(0xff, header->size);

    // Check the contents of the staging buffer

    int v = 476;

    flash_page_t *stage_page = (flash_page_t *)part.staging_buffer;
    for (int i = 0; i < 124; i++, v++) {
        TEST_ASSERT_EQUAL_UINT8((uint8_t)v, stage_page->data[i]);
    }
}

void test_flash_store_write_CircularBufferLoop(void) {
    int bytes;
    flash_partition_t part = {
        .name           = "test-partition",
        .is_raw         = 0,
        .offset         = 0,
        .size           = 8192, // 2 sectors, 32 pages..
        .staging_buffer = staging_buffer,
        .staging_offset = 0,
        .staging_page   = 0,
        .device         = &winbond_flash_device
    };
    flash_page_t *page;
    uint8_t data[FLASH_DATA_SIZE];

    for (int i = 1; i < sizeof(data); i++) {
        data[i] = i;
    }

    data[0] = 0xAA;

    flash_store_setup(&part);

    // loop around once.. and partial page..
    for (int loop = 0; loop < 34; loop++) {
        data[FLASH_DATA_SIZE-1] = loop;
        flash_store_write(&part, data, sizeof(data));
    }

    TEST_ASSERT_EQUAL_INT(18, part.pages_used);
    TEST_ASSERT_EQUAL_INT(18 * FLASH_DATA_SIZE, part.bytes_stored);
    TEST_ASSERT_EQUAL_INT(2, part.staging_page);
    TEST_ASSERT_EQUAL_INT(0, part.staging_offset);
    TEST_ASSERT_EQUAL_INT(16, part.page_read);
    TEST_ASSERT_EQUAL_INT(35, part.sequence);

    // Check data.. The first two pages should be loops # 32 & 33

    for (int loop = 0; loop < 2; loop++) {
        page = (flash_page_t *)&winbond_store[loop * FLASH_PAGE_SIZE];
        TEST_ASSERT_EQUAL_UINT32(loop + 33, page->header.sequence);
        TEST_ASSERT_EQUAL_UINT8(0xAA, page->data[0]);
        TEST_ASSERT_EQUAL_UINT8(loop + 32, page->data[FLASH_DATA_SIZE-1]);
    }

    // Next 12 pages should be erased

    for (int loop = 0; loop < 14; loop++) {
        int page_num = loop + 2;
        page = (flash_page_t *)&winbond_store[page_num * FLASH_PAGE_SIZE];

        for (int byte = 0; byte < FLASH_DATA_SIZE; byte++)
            TEST_ASSERT_EQUAL_UINT8(0xff, page->data[byte]);
    }

    // The remaining 16 should be loops 16 thru 31

    for (int loop = 0; loop < 16; loop++) {
        int page_num = loop + 16;

        page = (flash_page_t *)&winbond_store[page_num * FLASH_PAGE_SIZE];

        TEST_ASSERT_EQUAL_UINT32(page_num + 1, page->header.sequence);
        TEST_ASSERT_EQUAL_UINT8(NOT_FREE_MARKER, page->header.free_marker);
        TEST_ASSERT_EQUAL_UINT8(page_num, page->data[FLASH_DATA_SIZE-1]);
    }
}

void test_flash_store_setup_ExistingCircularBuffer(void) {
    int bytes;
    flash_partition_t part = {
        .name           = "test-partition",
        .is_raw         = 0,
        .offset         = 0,
        .size           = 8192, // 2 sectors, 32 pages..
        .staging_buffer = staging_buffer,
        .staging_offset = 0,
        .staging_page   = 0,
        .device         = &winbond_flash_device
    };

    flash_page_t *page;
    uint8_t data[FLASH_DATA_SIZE];

    for (int i = 1; i <= sizeof(data); i++) {
        data[i] = i;
    }

    data[0] = 0xAA;

    flash_store_setup(&part);

    // loop around once.. and partial page..
    for (int loop = 0; loop < 34; loop++) {
        data[FLASH_DATA_SIZE-1] = loop;
        flash_store_write(&part, data, sizeof(data));
    }

    flash_store_flush(&part);

    flash_store_init(&part);

    TEST_ASSERT_EQUAL_INT(18, part.pages_used);
    TEST_ASSERT_EQUAL_INT(18 * FLASH_DATA_SIZE, part.bytes_stored);
    TEST_ASSERT_EQUAL_INT(2, part.staging_page);
    TEST_ASSERT_EQUAL_INT(0, part.staging_offset);
    TEST_ASSERT_EQUAL_INT(16, part.page_read);
    TEST_ASSERT_EQUAL_INT(35, part.sequence);

}

void test_flash_store_mark_read_PagesMarkedFree() {
    int bytes;
    flash_partition_t part = {
        .name           = "test-partition",
        .is_raw         = 0,
        .offset         = 0,
        .size           = 8192, // 2 sectors, 32 pages..
        .staging_buffer = staging_buffer,
        .staging_offset = 0,
        .staging_page   = 0,
        .device         = &winbond_flash_device
    };

    flash_page_t *page;
    flash_page_header_t header;
    uint8_t data[FLASH_DATA_SIZE];

    for (int i = 1; i <= sizeof(data); i++) {
        data[i] = i;
    }

    data[0] = 0xAA;

    flash_store_setup(&part);

    // loop around once.. and partial page..
    for (int loop = 0; loop < 34; loop++) {
        data[FLASH_DATA_SIZE-1] = loop;
        flash_store_write(&part, data, sizeof(data));
    }

    flash_store_page_mark_free(&part, 16);   // Mark the first free page

    TEST_ASSERT_EQUAL_INT(17, part.pages_used);
    TEST_ASSERT_EQUAL_INT(17 * FLASH_DATA_SIZE, part.bytes_stored);
    TEST_ASSERT_EQUAL_INT(17, part.page_read);

    part.device->read(part.offset + (FLASH_PAGE_SIZE * 16), &header, sizeof(header));
    TEST_ASSERT_EQUAL_UINT8(FREE_MARKER, header.free_marker);
}

void test_flash_store_mark_read_PagesMarkedFreeInStaging() {
    int bytes;
    flash_partition_t part = {
        .name           = "test-partition",
        .is_raw         = 0,
        .offset         = 0,
        .size           = 4096,
        .staging_buffer = staging_buffer,
        .staging_offset = 0,
        .staging_page   = 0,
        .device         = &winbond_flash_device
    };

    flash_page_t *page;
    flash_page_header_t header;
    uint8_t data[FLASH_DATA_SIZE];

    for (int i = 1; i <= sizeof(data); i++) {
        data[i] = i;
    }

    flash_store_setup(&part);

    for (int loop = 0; loop < 2; loop++)
        flash_store_write(&part, data, sizeof(data));

    flash_store_write(&part, data, sizeof(data) / 2);

    TEST_ASSERT_EQUAL_INT(part.staging_offset, sizeof(data) / 2);

    flash_store_page_mark_free(&part, 2);   // Mark the first free page

    TEST_ASSERT_EQUAL_INT(2, part.pages_used);
    TEST_ASSERT_EQUAL_INT(0, part.staging_offset);
}

void test_flash_store_mark_read_PagesMarkAllFreeWithFlashRead() {
    int bytes;
    flash_partition_t part = {
        .name           = "test-partition",
        .is_raw         = 0,
        .offset         = 0,
        .size           = 8192, // 2 sectors, 32 pages..
        .staging_buffer = staging_buffer,
        .staging_offset = 0,
        .staging_page   = 0,
        .device         = &winbond_flash_device
    };

    flash_page_t *page;
    flash_read_t read;
    flash_page_header_t header;
    uint8_t data[FLASH_DATA_SIZE], read_cache[FLASH_PAGE_SIZE];
    int pages_read = 0;
    uint32_t page_num;

    for (int i = 1; i <= sizeof(data); i++) {
        data[i] = i;
    }

    data[0] = 0xAA;

    flash_store_setup(&part);

    // loop around once.. and partial page..
    for (int loop = 0; loop < 34; loop++) {
        data[FLASH_DATA_SIZE-1] = loop;
        flash_store_write(&part, data, sizeof(data));
    }

    flash_read_init(&part, &read, read_cache);

    while (!read.is_eof) {
        uint32_t bytes = flash_read_page(&read, data, &page_num);
        if (bytes == 0)
            break;

        TEST_ASSERT_EQUAL_UINT32(FLASH_DATA_SIZE, bytes);
        pages_read++;

        TEST_ASSERT(pages_read <= 18);
        flash_store_page_mark_free(&part, page_num);
    }

    TEST_ASSERT_EQUAL_UINT32(18, pages_read);

    TEST_ASSERT_EQUAL_UINT32(0, part.bytes_stored);
    TEST_ASSERT_EQUAL_UINT32(0, part.pages_used);
    TEST_ASSERT_EQUAL_UINT32(35, part.sequence);
    TEST_ASSERT_EQUAL_UINT32(2, part.page_read);
    TEST_ASSERT_EQUAL_UINT32(2, part.staging_page);
}

void test_flash_store_mark_read_PageMarkFreeInChunks() {
    int bytes;
    flash_partition_t part = {
        .name           = "test-partition",
        .is_raw         = 0,
        .offset         = 0,
        .size           = 16*1024,
        .staging_buffer = staging_buffer,
        .staging_offset = 0,
        .staging_page   = 0,
        .device         = &winbond_flash_device
    };

    flash_page_t *page;
    flash_read_t read;
    flash_page_header_t header;
    uint8_t data[FLASH_DATA_SIZE], read_cache[FLASH_PAGE_SIZE];
    int pages_read = 0, page_chunk = 0;
    uint32_t page_num;

    for (int i = 0; i < sizeof(data); i++) {
        data[i] = i;
    }


    flash_store_setup(&part);

    for (int loop = 0; loop < 32; loop++) {
        for (int i = 0; i < 16; i++)
            flash_store_write(&part, data, 15);
    }

    flash_read_init(&part, &read, read_cache);

    pages_read = 0;
    while (!read.is_eof) {
        uint32_t bytes, page, pages[4];
        for (page = 0; page < 4; page++) {
            bytes = flash_read_page(&read, data, &pages[page]);
            if (bytes == 0)
                break;

            pages_read++;
            TEST_ASSERT(pages_read <= 35);
        }

        for (int i = 0; i < page; i++)
            flash_store_page_mark_free(&part, pages[i]);
    }

    flash_store_setup(&part);

    TEST_ASSERT_EQUAL_UINT32(35, pages_read);

    TEST_ASSERT_EQUAL_UINT32(0, part.bytes_stored);
    TEST_ASSERT_EQUAL_UINT32(0, part.pages_used);
    TEST_ASSERT_EQUAL_UINT32(35, part.sequence);
    TEST_ASSERT_EQUAL_UINT32(34, part.page_read);
    TEST_ASSERT_EQUAL_UINT32(34, part.staging_page);

    for (int loop = 0; loop < 33; loop++) {
        data[FLASH_DATA_SIZE-1] = loop;
        flash_store_write(&part, data, sizeof(data));
    }

    flash_read_init(&part, &read, read_cache);

    pages_read = 0;

    while (!read.is_eof) {
        uint32_t bytes, page, pages[4];
        for (page = 0; page < 4; page++) {
            bytes = flash_read_page(&read, data, &pages[page]);
            if (bytes == 0)
                break;

            TEST_ASSERT_EQUAL_UINT32(FLASH_DATA_SIZE, bytes);
            pages_read++;
            TEST_ASSERT(pages_read <= 33);
        }

        for (int i = 0; i < page; i++)
            flash_store_page_mark_free(&part, pages[i]);
    }
    TEST_ASSERT_EQUAL_UINT32(0, part.bytes_stored);
    TEST_ASSERT_EQUAL_UINT32(0, part.pages_used);
    TEST_ASSERT_EQUAL_UINT32(68, part.sequence);
    TEST_ASSERT_EQUAL_UINT32(3, part.page_read);
    TEST_ASSERT_EQUAL_UINT32(3, part.staging_page);

}

void test_flash_store_write_RawFlushStagingBuffer(void) {
    flash_partition_t part = {
        .name           = "test-partition",
        .is_raw         = true,
        .offset         = 0,
        .size           = TEST_PARTITION_SIZE,
        .staging_buffer = staging_buffer,
        .staging_offset = 0,
        .staging_page   = 0,
        .device         = &winbond_flash_device
    };
    uint8_t data[600];

    for (int i = 0; i < sizeof(data); i++) {
        data[i] = i;
    }

    flash_store_setup(&part);
    TEST_ASSERT_EQUAL_INT(FLASH_SUCCESS, flash_store_write(&part, data, sizeof(data)));

    TEST_ASSERT_EQUAL_INT(false, part.is_full);
    TEST_ASSERT_EQUAL_INT(2, part.pages_used);
    TEST_ASSERT_EQUAL_INT(sizeof(data), part.bytes_stored);
    TEST_ASSERT_EQUAL_INT(88, part.staging_offset);
    TEST_ASSERT_EQUAL_INT(2, part.staging_page);

    // check the two pages that should have been writing to the flash

    for (int page = 0; page < 2; page++) {
        for (int i = 0; i < 256; i++) {
            TEST_ASSERT_EQUAL_INT((uint8_t)((page * 256) + i), winbond_store[(page * 256) + i]);
        }
    }

    for (int i = 0; i < 256; i++) {
        TEST_ASSERT_EQUAL_INT(0xff, winbond_store[(2 * 256) + i]);
    }

    // Check the contents of the staging buffer

    for (int i = 0; i < 88; i++) {
        TEST_ASSERT_EQUAL_INT((uint8_t)i, part.staging_buffer[i]);
    }
}

void test_flash_store_partial_okay_Success() {
    flash_partition_t part = {
        .name           = "test-partition",
        .is_raw         = 0,
        .offset         = 0,
        .size           = 16*1024,
        .staging_buffer = staging_buffer,
        .staging_offset = 0,
        .staging_page   = 0,
        .device         = &winbond_flash_device
    };

    uint8_t data[FLASH_DATA_SIZE];

    //
    // if beginning of the page (byte 0), then a partial record
    // should not be recommended..
    flash_store_setup(&part);
    TEST_ASSERT_EQUAL_INT(false, flash_store_partial_okay(&part, 10));
    flash_store_write(&part, data, 10);
    TEST_ASSERT_EQUAL_INT(true, flash_store_partial_okay(&part, 10));

    // Test for page spaning writes..
    flash_store_setup(&part);
    flash_store_write(&part, data, FLASH_DATA_SIZE-4);
    TEST_ASSERT_EQUAL_INT(true, flash_store_partial_okay(&part, 4));
    TEST_ASSERT_EQUAL_INT(false, flash_store_partial_okay(&part, 5));

}


int main() {
    UnityBegin(__FILE__);
    RUN_TEST(test_flash_store_StructSizes, 1);
    RUN_TEST(test_flash_store_setup_SetupRaw, 10);
    RUN_TEST(test_flash_store_setup_FlashUninitialized, 20);
    RUN_TEST(test_flash_store_setup_FlashInitialized, 30);
    RUN_TEST(test_flash_store_erase, 50);
    RUN_TEST(test_flash_store_write_StayWithinStagingBuffer, 60);
    RUN_TEST(test_flash_store_write_FlushStagingBuffer, 70);
    RUN_TEST(test_flash_store_write_RawFlushStagingBuffer, 80);
    RUN_TEST(test_flash_store_write_CircularBufferLoop, 90);
    RUN_TEST(test_flash_store_setup_ExistingCircularBuffer, 100);
    RUN_TEST(test_flash_store_mark_read_PagesMarkedFree, 110);
    RUN_TEST(test_flash_store_mark_read_PagesMarkedFreeInStaging, 120);
    RUN_TEST(test_flash_store_mark_read_PagesMarkAllFreeWithFlashRead, 130);
    RUN_TEST(test_flash_store_mark_read_PageMarkFreeInChunks, 140);
    RUN_TEST(test_flash_store_partial_okay_Success, 150);
    exit(UnityEnd());
}

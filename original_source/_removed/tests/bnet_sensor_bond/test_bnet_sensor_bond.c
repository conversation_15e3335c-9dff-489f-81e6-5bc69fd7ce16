#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include <stdlib.h>
#include "unity.h"

#include "pelagic.h"
#include "cmsis_os.h"
#include "bnet.h"
#include "bnet_sensor.h"
#include "rtc_api.h"
#include "radio_harness.h"
#include "system_file.h"
#include "firmware.h"

uint8_t bnet_receive_buffer[128];

volatile bool bnet_packet_watch = false;

board_uid_t board_uid = { 0x11,0x12,0x13,0x14,0x15,0x16,0x17,0x18,0x19,0x1a };
board_uid_t vms_uid = { 0xf1, 0xf2, 0xf3, 0xf4, 0xf5, 0xf6, 0xf7, 0xf8, 0xf9, 0xfa };

uint8_t radio_captured[8192];

uint16_t sensor_slept;

void setUp() {
    sys_file_clear();
    radio_harness_init();
    radio_harness_capture(radio_captured);
}

void bnet_sensor_check_firmware() {

}

bool bnet_sensor_report(bool reeval) {
    return true;
}

bool bnet_sensor_firmware_check() { return false;}
bool bnet_sensor_firmware_download() { return false; }
bool bnet_sensor_firmware_updated() { return false; }
void firmware_update() { }

radio_harness_read_t success_stream[] = {
    {
        .bytes = 21,
        .data = "\x92\x11\x12\x13\x14\x15\x16\x17\x18\x19\x1a\xf1\xf2\xf3\xf4\xf5\xf6\xf7\xf8\xf9\xfa"
    }
};

void test_bnet_sensor_bond_Success() {
    bool result;

    radio_harness_set_read(success_stream, ARRAY_SIZE(success_stream));
    result = bnet_sensor_bond();
    TEST_ASSERT_EQUAL(true, result);
    TEST_ASSERT_EQUAL(11, radio_harness_captured_bytes);
    TEST_ASSERT_EQUAL(BNET_TYPE_SENSOR_BOND, radio_captured[0]);
    TEST_ASSERT_EQUAL_UINT8_ARRAY(board_uid, &radio_captured[1], sizeof(board_uid_t));
    TEST_ASSERT_EQUAL_UINT8_ARRAY(vms_uid, SYS_REG_FILE->host_uid, sizeof(board_uid_t));
    TEST_ASSERT_EQUAL(true, SYS_REG_FILE->have_host);
}

radio_harness_read_t higher_power_stream[] = {
    { .bytes = 0  },
    { .bytes = 0  },
    { .bytes = 0  },
    { .bytes = 0  },
    { .bytes = 0  },
    { .bytes = 0  },
    {
        .bytes = 21,
        .data = "\x92\x11\x12\x13\x14\x15\x16\x17\x18\x19\x1a\xf1\xf2\xf3\xf4\xf5\xf6\xf7\xf8\xf9\xfa"
    }
};

void test_bnet_sensor_bond_HigherPower() {
    bool result;

    radio_harness_set_read(higher_power_stream, ARRAY_SIZE(higher_power_stream));
    result = bnet_sensor_bond();
    TEST_ASSERT_EQUAL(true, result);
    TEST_ASSERT_EQUAL(6, SYS_REG_FILE->radio_power);
}

radio_harness_read_t request_timeout_stream[20];

void test_bnet_sensor_bond_Timeout() {
    bool result;

    radio_harness_set_read(request_timeout_stream, ARRAY_SIZE(request_timeout_stream));
    result = bnet_sensor_bond();

    TEST_ASSERT_EQUAL(false, result);
    TEST_ASSERT_EQUAL(false, SYS_REG_FILE->have_host);
}

int main(int argc, char **argv) {
    UnityBegin("bnet_sensor_bond.c");
    RUN_TEST(test_bnet_sensor_bond_Success, 10);
    RUN_TEST(test_bnet_sensor_bond_HigherPower,20);
    RUN_TEST(test_bnet_sensor_bond_Timeout, 30);
    exit(UnityEnd());
}

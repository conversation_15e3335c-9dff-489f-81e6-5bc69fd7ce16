
TESTS=flash_store flash_read
TESTS += nap
TESTS += datetime sun nmea
TESTS += boat_log
#TESTS += power_manage
TESTS += modem_bodytrace
TESTS += pds pds_firmware pds_message
TESTS += bnet_vms_old bnet_vms_sensor bnet_vms_firmware bnet_vms_ping
TESTS += bnet_sensor_bond bnet_sensor_report bnet_sensor_firmware


# TODO: get these working again
# TESTS += event boat_log cell_signal_log gps_thread bnet_vms

.PHONY: $(TESTS) clean

all:
	@echo "make something?"

runall:
	make $(TESTS:%=%-run)

$(TESTS):
		cd $@; make build

%-run:
	make $(patsubst %-run,%,$@)
	./tests/$(patsubst %-run,%,$@)

%-debug:
	make $(patsubst %-debug,%,$@)
	lldb ./tests/$(patsubst %-debug,%,$@)

clean:
	rm -rf objs tests

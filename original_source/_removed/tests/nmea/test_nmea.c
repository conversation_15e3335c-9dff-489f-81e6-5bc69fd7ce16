#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <string.h>
#include <math.h>
#include "unity.h"
#include "pelagic-types.h"
#include "nmea.h"

nmea_result_t parse_sentence(nmea_t *nmea, const char *sentence) {
    nmea_result_t result;

    while (*sentence) {
        result = nmea_fusedata(nmea, *sentence++);

        if (result != NMEA_NONE)
            return result;
    }

    return NMEA_NONE;
}

void test_nmea_fusedata_ValidGPRMC() {
    nmea_t  nmea;

    nmea_init(&nmea);
    TEST_ASSERT_EQUAL_INT(NMEA_GPRMC, parse_sentence(&nmea, "$GPRMC,032437.000,A,4339.3206,N,07212.2396,W,0.01,0.00,021214,,,A*7A\r\n"));

    TEST_ASSERT_DOUBLE_WITHIN(0.000001, 43.655343,  nmea.latitude);
    TEST_ASSERT_DOUBLE_WITHIN(0.000001, -72.203993, nmea.longitude);
    TEST_ASSERT_EQUAL_INT(2014, nmea.year);
    TEST_ASSERT_EQUAL_INT(12, nmea.month);
    TEST_ASSERT_EQUAL_INT(2, nmea.day);
    TEST_ASSERT_EQUAL_INT(3, nmea.hour);
    TEST_ASSERT_EQUAL_INT(24, nmea.minute);
    TEST_ASSERT_EQUAL_INT(37, nmea.second);
    TEST_ASSERT_FLOAT_WITHIN(0.001, 0.0054, nmea.speed);
    TEST_ASSERT_EQUAL_FLOAT(0.0, nmea.heading);
}

void test_nmea_fusedata_BadGPRMC() {
    nmea_t  nmea;

    nmea_init(&nmea);
    TEST_ASSERT_EQUAL_INT(NMEA_CRC_ERR, parse_sentence(&nmea, "$GPRMC,032437.000,A,21214,,,A*7A\r\n"));
}

void test_nmea_fusedata_ValidPMTK() {
    nmea_t  nmea;
    nmea_result_t result;

    nmea_init(&nmea);
    result = parse_sentence(&nmea, "$PMTK001,604,3*32\r\n");

    TEST_ASSERT_EQUAL_INT(NMEA_PMTK, result);
    TEST_ASSERT_EQUAL_INT(2, nmea.word_idx);
    TEST_ASSERT_EQUAL_STRING("$PMTK001", nmea.words[0]);
    TEST_ASSERT_EQUAL_STRING("604", nmea.words[1]);
}

void test_nmea_fusedata_BadPMTK() {
    nmea_t  nmea;
    nmea_result_t result;

    nmea_init(&nmea);
    result = parse_sentence(&nmea, "$PMTK001,,3*32\r\n");

    TEST_ASSERT_EQUAL_INT(NMEA_CRC_ERR, result);
}

void test_nmea_fusedata_ValidGPGGA() {
    nmea_t nmea;
    nmea_result_t result;

    nmea_init(&nmea);
    result = parse_sentence(&nmea, "$GPGGA,042033.000,4339.3187,N,07212.2364,W,1,6,1.11,364.8,M,-32.6,M,,*69\r\n");

    TEST_ASSERT_EQUAL_INT(NMEA_GPGGA, result);
    TEST_ASSERT_DOUBLE_WITHIN(0.000001, 43.655312, nmea.latitude);
    TEST_ASSERT_DOUBLE_WITHIN(0.000001, -72.203940, nmea.longitude);
    TEST_ASSERT_EQUAL_INT(6, nmea.satellites_used);
    TEST_ASSERT_EQUAL_FLOAT(364.799988, nmea.altitude);
}

void test_nmea_fusedata_BadGPGGA() {
    nmea_t nmea;
    nmea_result_t result;

    nmea_init(&nmea);
    result = parse_sentence(&nmea, "$GPGGA6,M,,*6\r\n");

    TEST_ASSERT_EQUAL_INT(NMEA_CRC_ERR, result);
}

void test_nmea_fusedataValidGPGSA() {
    nmea_t nmea;
    nmea_result_t result;

    nmea_init(&nmea);
    result = parse_sentence(&nmea, "$GNGSA,A,3,13,09,28,32,04,24,01,10,23,20,02,,1.1,0.6,0.9*20\r\n");

    TEST_ASSERT_EQUAL_INT(NMEA_GPGSA, result);
    TEST_ASSERT_EQUAL_FLOAT(1.1, nmea.pdop);
    TEST_ASSERT_EQUAL_FLOAT(0.6, nmea.hdop);
    TEST_ASSERT_EQUAL_FLOAT(0.9, nmea.vdop);
}

int main(int argc, char **argv) {
    UnityBegin("nmea.c");
    RUN_TEST(test_nmea_fusedata_ValidGPRMC, 10);
    RUN_TEST(test_nmea_fusedata_BadGPRMC, 20);
    RUN_TEST(test_nmea_fusedata_ValidPMTK, 30);
    RUN_TEST(test_nmea_fusedata_BadPMTK, 40);
    RUN_TEST(test_nmea_fusedata_ValidGPGGA, 50);
    RUN_TEST(test_nmea_fusedata_BadGPGGA, 60);
    RUN_TEST(test_nmea_fusedataValidGPGSA, 70);
    exit(UnityEnd());
}

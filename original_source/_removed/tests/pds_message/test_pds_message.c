#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include <stdlib.h>
#include "unity.h"

#include "pelagic.h"
#include "flash_store.h"
#include "winbond_flash.h"
#include "kinetis_flash.h"
#include "modem.h"
#include "modem_bodytrace.h"
#include "modem_harness.h"
#include "event_harness.h"
#include "stats.h"
#include "firmware.h"
#include "pds.h"

#if 0
extern uint8_t kinetis_store[];


enum {
    FIRMWARE_SENSOR_PART_SIZE = (64*1024),

    FIRMWARE_PART_OFFSET      = FIRMWARE_SENSOR_PART_SIZE,
    FIRMWARE_PART_SIZE        = (128*1024),
};
#endif

shared_buffer_t the_buffer;


firmware_header_t firmware_sensor_header;

board_uid_t board_uid = { 0, 1, 2, 3, 4, 5, 6, 7, 8, 9 };

bool pds_settings_process_called = false;
bool firmware_sensor_init_called = false;
bool pds_firmware_acknowledge_called = false;
char *pds_settings_process_expect_data = NULL;
int pds_settings_process_expect_size = 0;
bool firmware_update_called = false;

struct {
    uint8_t device;
    uint8_t version;
    uint32_t buildstamp;
    board_uid_t uid;
    uint16_t crc;
} pds_firmware_acknowledge_args;

void setUp() {
    firmware_sensor_init_called = false;
    pds_firmware_acknowledge_called = false;
    pds_settings_process_called = false;
    pds_settings_process_expect_data = NULL;
    pds_settings_process_expect_size = 0;
    firmware_update_called = false;

    event_harness_init();
    modem_harness_init();
    modem_harness_message_count = 0;
    modem_harness_bomb_on_read_chunk = false;

    winbond_flash_init();
    flash_store_setup(&firmware_sensor_partition);
    flash_store_setup(&firmware_partition);
}

void firmware_sensor_init() {
    firmware_sensor_init_called = true;
}

void pds_firmware_acknowledge(uint8_t device, uint8_t version, uint32_t buildstamp, uint8_t *uid, uint16_t crc) {
    pds_firmware_acknowledge_called = true;
    pds_firmware_acknowledge_args.device = device;
    pds_firmware_acknowledge_args.version = version;
    pds_firmware_acknowledge_args.buildstamp = buildstamp;
    pds_firmware_acknowledge_args.crc = crc;

    memcpy(pds_firmware_acknowledge_args.uid, uid, sizeof(board_uid_t));
}


void pds_settings_process(pds_settings_incoming_msg_t *msg, uint32_t bytes) {
    pds_settings_process_called = true;

    if (pds_settings_process_expect_data == NULL)
        TEST_FAIL();

    TEST_ASSERT_EQUAL_UINT32(pds_settings_process_expect_size, bytes);
    TEST_ASSERT_EQUAL_UINT8_ARRAY((uint8_t *)pds_settings_process_expect_data, msg->data, bytes-2);
}


void firmware_update() {
    firmware_update_called = true;
}

void test_pds_check_message_None_Success() {
    modem_harness_bomb_on_read_chunk = true;

    // Should not fail or call modem_read_chunk
    pds_check_messages();
}

modem_harness_message_t settings_messages[] = {
    { .data = "\2\1expected message", .size = 18 }
};

void test_pds_check_message_Settings_Success() {
    modem_harness_set_messages(settings_messages, ARRAY_SIZE(settings_messages));

    pds_settings_process_expect_data = "expected message";
    pds_settings_process_expect_size = 18;

    pds_check_messages();

    TEST_ASSERT_EQUAL_INT(true, pds_settings_process_called);
}

void test_check_firmware(flash_partition_t *part, uint8_t device_hardware) {
    flash_read_t read;
    firmware_header_t header;
    uint8_t cache[512];
    uint8_t firmware[10];

    flash_read_init(part, &read, cache);

    TEST_ASSERT_EQUAL_INT(22, part->bytes_stored);
    TEST_ASSERT_EQUAL_INT(sizeof(firmware_header_t), flash_read_block(&read, &header, sizeof(header)));
    TEST_ASSERT_EQUAL_UINT32(10, header.length);
    TEST_ASSERT_EQUAL_UINT16(0x3412, header.crc16);
    TEST_ASSERT_EQUAL_UINT8(device_hardware, header.device_hardware);
    TEST_ASSERT_EQUAL_UINT8(6, header.device_version);
    TEST_ASSERT_EQUAL_UINT32(0x78563412, header.buildstamp);
    TEST_ASSERT_EQUAL_INT(sizeof(firmware), flash_read_block(&read, firmware, sizeof(firmware)));

    for (int i = 0; i < sizeof(firmware); i++)
        TEST_ASSERT_EQUAL_UINT8(i, firmware[i]);
}

modem_harness_message_t firmware_vms_message[] = {
    {
        .data = "\xfe\x0a\x00\x00\x00\x12\x34\x01\x06\x12\x34\x56\x78\x00\x01\x02\x03\x04\x05\x06\x07\x08\x09",
        .size = 23
    }
};

void test_pds_check_message_FirmwareVMS_Success() {
    modem_harness_set_messages(firmware_vms_message, ARRAY_SIZE(firmware_vms_message));

    pds_check_messages();

    TEST_ASSERT_EQUAL_UINT8(true, firmware_update_called);

    test_check_firmware(&firmware_partition, DEVICE_VMS);
}

modem_harness_message_t firmware_sensor_message[] = {
    {
        .data = "\xfe\x0a\x00\x00\x00\x12\x34\x02\x06\x12\x34\x56\x78\x00\x01\x02\x03\x04\x05\x06\x07\x08\x09",
        .size = 23
    }
};

void test_pds_check_message_FirmwareSensor_Success() {
    modem_harness_set_messages(firmware_sensor_message, ARRAY_SIZE(firmware_sensor_message));

    pds_check_messages();

    TEST_ASSERT_EQUAL_UINT8(true, firmware_sensor_init_called);

    test_check_firmware(&firmware_sensor_partition, DEVICE_SENSOR);
}

modem_harness_message_t firmware_unknown_message[] = {
    {
        .data = "\xfe\x0a\x00\x00\x00\x12\x34\xdd\x06\x12\x34\x56\x78\x00\x01\x02\x03\x04\x05\x06\x07\x08\x09",
        .size = 23
    }
};

void test_pds_check_message_FirmwareUnknown() {
    modem_harness_set_messages(firmware_unknown_message, ARRAY_SIZE(firmware_unknown_message));

    pds_check_messages();

    TEST_ASSERT_EQUAL_UINT8(false, firmware_update_called);
    TEST_ASSERT_EQUAL_UINT8(false, firmware_sensor_init_called);
    TEST_ASSERT_EQUAL_INT(true, event_harness_have_entry(EVT_PDS_UNKNOWN_DEVICE_TYPE));
}

int main(int argc, char **argv) {
    UnityBegin("pds_message.c");
    RUN_TEST(test_pds_check_message_None_Success, 10);
    RUN_TEST(test_pds_check_message_Settings_Success, 20);
    RUN_TEST(test_pds_check_message_FirmwareVMS_Success, 30);
    RUN_TEST(test_pds_check_message_FirmwareSensor_Success, 40);
    RUN_TEST(test_pds_check_message_FirmwareUnknown, 50);
    exit(UnityEnd());
}

#include "unity.h"
#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include <stdlib.h>


#include "datetime.h"

void test_seconds_to_datetime_AtEpoch() {
    datetime_t dt;

    seconds_to_datetime(0, &dt);

    TEST_ASSERT_EQUAL_INT(dt.year, EPOCH_YEAR);
    TEST_ASSERT_EQUAL_INT(dt.month, 1);
    TEST_ASSERT_EQUAL_INT(dt.day, 1);
    TEST_ASSERT_EQUAL_INT(dt.hour, 0);
    TEST_ASSERT_EQUAL_INT(dt.minute, 0);
    TEST_ASSERT_EQUAL_INT(dt.second, 0);
}

void test_seconds_to_datetime_OnDate() {
    datetime_t dt;

    seconds_to_datetime(43113330, &dt);

    TEST_ASSERT_EQUAL_INT(dt.year, 2015);
    TEST_ASSERT_EQUAL_INT(dt.month, 5);
    TEST_ASSERT_EQUAL_INT(dt.day, 14);
    TEST_ASSERT_EQUAL_INT(dt.hour, 23);
    TEST_ASSERT_EQUAL_INT(dt.minute, 55);
    TEST_ASSERT_EQUAL_INT(dt.second, 30);

}

void test_date_to_seconds_AtEpoch() {
    uint32_t seconds;

    seconds = date_to_seconds(EPOCH_YEAR, 1, 1);
    TEST_ASSERT_EQUAL_INT(seconds, 0);
}

void test_date_to_seconds_OnDate() {
    uint32_t seconds;

    seconds = date_to_seconds(2015, 5, 14);
    TEST_ASSERT_EQUAL_INT(seconds, 43027200);
}

void test_datetime_to_seconds_AtEpoch() {
    uint32_t seconds;
    datetime_t dt = {
        .year = 2014, .month = 1, .day = 1, .hour = 0, .minute = 0, .second = 0
    };

    seconds = datetime_to_seconds(&dt);
    TEST_ASSERT_EQUAL_INT(seconds, 0);
}

void test_datetime_to_seconds_OnDate() {
    uint32_t seconds;
    datetime_t dt = {
        .year = 2015, .month = 5, .day = 14, .hour = 23, .minute = 55, .second = 30
    };

    seconds = datetime_to_seconds(&dt);
    TEST_ASSERT_EQUAL_INT(seconds, 43113330);
}

void test_timezone_offset_Success() {
    int32_t offset;
    offset = timezone_offset(-72.305823);

    // Lebanon is -4.82 hours
    TEST_ASSERT_EQUAL_INT32(-17353, offset);

    // ROR airport, +8.9691 hours
    offset = timezone_offset(134.538811);
    TEST_ASSERT_EQUAL_INT32(32289, offset);

    // UTC
    offset = timezone_offset(0);
    TEST_ASSERT_EQUAL_INT32(0, offset);
}

int main(int argc, char **argv) {
    UnityBegin(__FILE__);
    RUN_TEST(test_seconds_to_datetime_AtEpoch, 10);
    RUN_TEST(test_seconds_to_datetime_OnDate, 20);
    RUN_TEST(test_date_to_seconds_AtEpoch, 30);
    RUN_TEST(test_date_to_seconds_OnDate, 40);
    RUN_TEST(test_datetime_to_seconds_AtEpoch, 50);
    RUN_TEST(test_datetime_to_seconds_OnDate, 60);
    RUN_TEST(test_timezone_offset_Success, 70);
    exit(UnityEnd());
}

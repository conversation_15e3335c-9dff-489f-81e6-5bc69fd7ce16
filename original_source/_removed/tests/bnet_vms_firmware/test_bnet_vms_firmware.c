#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include <stdlib.h>
#include "unity.h"

#include "pelagic.h"
#include "cmsis_os.h"
#include "bnet.h"
#include "boat_log.h"
#include "flash_store.h"
#include "firmware.h"
#include "rtc_api.h"

#include "radio_harness.h"

enum {
    TIME_NOW = 0xdeafb00b
};

firmware_header_t firmware_sensor_header = {
    .length = 20,
    .crc16 = 0xabcd,
    .buildstamp = 111,
    .device_hardware = DEVICE_SENSOR,
    .device_version = 5,
};

flash_read_t firmware_sensor_read;

board_uid_t board_uid = { 1,2,3,4,5,6,7,8,9 };
board_uid_t sensor_uid = { 0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18, 0x19 };

uint8_t firmware_data[20] = { 0, 1, 2 , 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19};

uint8_t firmware_cache[256];

uint8_t radio_captured[8192];

extern volatile bool time_acquired;

volatile bool bnet_sensor_watch;

void boat_log_sensor(log_sensor_t *sensor) {
}

void setUp() {
    radio_harness_init();
    radio_harness_capture(radio_captured);

    time_acquired        = true;
    firmware_sensor_header.buildstamp = 111;
    firmware_sensor_partition.is_busy = false;

    firmware_sensor_partition.bytes_stored = 0;
    firmware_sensor_partition.pages_used = 0;
    firmware_sensor_partition.staging_offset = 0;

    flash_store_setup(&firmware_sensor_partition);
    flash_store_write(&firmware_sensor_partition, (void *) &firmware_sensor_header, sizeof(firmware_sensor_header));
    flash_store_write(&firmware_sensor_partition, firmware_data, 20);
    flash_read_init(&firmware_sensor_partition, &firmware_sensor_read, firmware_cache);

    rtc_write(TIME_NOW);
}

void create_firmware_check_packet(uint8_t *packet) {
    // Check for firmware available
    packet[0] = BNET_TYPE_FIRMWARE_CHECK;
    uid_set_me(&packet[1]); // from_uid
    uid_copy(&packet[11],sensor_uid);
    packet[21] = DEVICE_SENSOR; // device_type
    packet[22] = 05;            // device_version
    packet[23] = 0;             // buildstamp
    packet[24] = 0x12;
    packet[25] = 0;
    packet[26] = 0;
}

void check_firmware_response(uint8_t avail) {
    TEST_ASSERT_EQUAL_INT(22, radio_harness_captured_bytes);
    TEST_ASSERT_EQUAL_UINT8(BNET_TYPE_FIRMWARE_CHECK_ACK, radio_captured[0]);
    TEST_ASSERT_EQUAL_UINT8_ARRAY(sensor_uid, &radio_captured[1], sizeof(board_uid_t));
    TEST_ASSERT_EQUAL_UINT8_ARRAY(board_uid, &radio_captured[11], sizeof(board_uid_t));
    TEST_ASSERT_EQUAL_UINT8(avail, radio_captured[21]);
}

void test_bnet_process_firmware_check_FirmwareAvail() {
    uint8_t packet[80];

    create_firmware_check_packet(packet);
    firmware_sensor_header.buildstamp = 0xff00;

    bnet_process_firmware_check(packet, sizeof(bnet_firmware_check_t));
    check_firmware_response(true);
}


void test_bnet_process_firmware_check_FirmwareNone() {
    uint8_t packet[80];

    create_firmware_check_packet(packet);
    firmware_sensor_header.buildstamp = 0;
    bnet_process_firmware_check(packet, sizeof(bnet_firmware_check_t));
    check_firmware_response(false);
}

void test_bnet_process_firmware_check_BusyPartition() {
    uint8_t packet[80];

    create_firmware_check_packet(packet);
    firmware_sensor_header.buildstamp = 0xfffff;
    firmware_sensor_partition.is_busy = true;
    bnet_process_firmware_check(packet, sizeof(bnet_firmware_check_t));
    TEST_ASSERT_EQUAL_INT(0, radio_harness_captured_bytes);
}

void test_bnet_process_firmware_check_BadPacket() {
    uint8_t packet[80];

    create_firmware_check_packet(packet);
    firmware_sensor_header.buildstamp = 0xfffff;
    bnet_process_firmware_check(packet, 15);
    TEST_ASSERT_EQUAL_INT(0, radio_harness_captured_bytes);
}

void create_firmware_send_packet(uint8_t *packet, uint32_t offset) {
    packet[0] = BNET_TYPE_FIRMWARE_SEND; // type
    uid_set_me(&packet[1]); // from_uid
    uid_copy(&packet[11], sensor_uid);
    packet[21] = offset & 0xff;
    packet[22] = (offset >> 8) & 0xff;
    packet[23] = (offset >> 16) & 0xff;
    packet[24] = (offset >> 24) & 0xff;
}

void test_bnet_process_firmware_send_Success() {
    uint8_t packet[32];

    create_firmware_send_packet(packet, 0);
    bnet_process_firmware_send(packet, 25);

    TEST_ASSERT_EQUAL_INT(58, radio_harness_captured_bytes);

    TEST_ASSERT_EQUAL_UINT8(BNET_TYPE_FIRMWARE_SEND_ACK, radio_captured[0]); // type
    TEST_ASSERT_EQUAL_UINT8_ARRAY(sensor_uid, &radio_captured[1], sizeof(board_uid_t)); // board uid
    TEST_ASSERT_EQUAL_UINT8_ARRAY(board_uid, &radio_captured[11], sizeof(board_uid_t)); // board uid
    TEST_ASSERT_EQUAL_UINT8(0, radio_captured[21]); // offset
    TEST_ASSERT_EQUAL_UINT8(0, radio_captured[22]);
    TEST_ASSERT_EQUAL_UINT8(0, radio_captured[23]);
    TEST_ASSERT_EQUAL_UINT8(0, radio_captured[24]);
    TEST_ASSERT_EQUAL_UINT8(32, radio_captured[25]); // length
    TEST_ASSERT_EQUAL_UINT8_ARRAY((uint8_t *)&firmware_sensor_header,
                &radio_captured[26], sizeof(firmware_sensor_header));
    TEST_ASSERT_EQUAL_UINT8_ARRAY(firmware_data, &radio_captured[38], 20);

    radio_harness_captured_bytes = 0;
    // second send should be EOF.
    create_firmware_send_packet(packet, 32);
    bnet_process_firmware_send(packet, 25);
    TEST_ASSERT_EQUAL_INT(26, radio_harness_captured_bytes);
    TEST_ASSERT_EQUAL_UINT8(BNET_TYPE_FIRMWARE_SEND_ACK, radio_captured[0]); // type
    TEST_ASSERT_EQUAL_UINT8_ARRAY(sensor_uid, &radio_captured[1], sizeof(board_uid_t)); // board uid
    TEST_ASSERT_EQUAL_UINT8_ARRAY(board_uid, &radio_captured[11], sizeof(board_uid_t)); // board uid
    TEST_ASSERT_EQUAL_UINT8(32, radio_captured[21]); // offset
    TEST_ASSERT_EQUAL_UINT8(0, radio_captured[22]);
    TEST_ASSERT_EQUAL_UINT8(0, radio_captured[23]);
    TEST_ASSERT_EQUAL_UINT8(0, radio_captured[24]);
    TEST_ASSERT_EQUAL_UINT8(0, radio_captured[25]); // length
}

void test_bnet_process_firmware_send_OffsetToLarge() {
    uint8_t packet[32];

    create_firmware_send_packet(packet, 0xffff);
    bnet_process_firmware_send(packet, 25);
    TEST_ASSERT_EQUAL_INT(26, radio_harness_captured_bytes);
    TEST_ASSERT_EQUAL_UINT8(BNET_TYPE_FIRMWARE_SEND_ACK, radio_captured[0]); // type
    TEST_ASSERT_EQUAL_UINT8_ARRAY(sensor_uid, &radio_captured[1], sizeof(board_uid_t)); // board uid
    TEST_ASSERT_EQUAL_UINT8_ARRAY(board_uid, &radio_captured[11], sizeof(board_uid_t)); // board uid
    TEST_ASSERT_EQUAL_UINT8(0xff, radio_captured[21]); // offset
    TEST_ASSERT_EQUAL_UINT8(0xff, radio_captured[22]);
    TEST_ASSERT_EQUAL_UINT8(0, radio_captured[23]);
    TEST_ASSERT_EQUAL_UINT8(0, radio_captured[24]);
    TEST_ASSERT_EQUAL_UINT8(0, radio_captured[25]); // length
}

void test_bnet_process_firmware_send_Fail() {
    uint8_t packet[32];

    // improper size
    bnet_process_firmware_send(packet, 6);
    TEST_ASSERT_EQUAL_INT(0, radio_harness_captured_bytes);

    // not destination
    create_firmware_send_packet(packet, 0);
    memset(&packet[1], 0xff, sizeof(board_uid_t));
    bnet_process_firmware_send(packet, 25);
    TEST_ASSERT_EQUAL_INT(0, radio_harness_captured_bytes);

    // partition busy
    firmware_sensor_partition.is_busy = true;
    create_firmware_send_packet(packet, 0);
    bnet_process_firmware_send(packet, 25);
    TEST_ASSERT_EQUAL_INT(0, radio_harness_captured_bytes);
    firmware_sensor_partition.is_busy = false;
}

int main(int argc, char **argv) {
    UnityBegin("bnet_vms_firmware.c");
    RUN_TEST(test_bnet_process_firmware_check_FirmwareAvail, 10);
    RUN_TEST(test_bnet_process_firmware_check_FirmwareNone, 20);
    RUN_TEST(test_bnet_process_firmware_check_BusyPartition, 30);
    RUN_TEST(test_bnet_process_firmware_check_BadPacket, 40);
    RUN_TEST(test_bnet_process_firmware_send_Success, 50);
    RUN_TEST(test_bnet_process_firmware_send_OffsetToLarge, 60);
    RUN_TEST(test_bnet_process_firmware_send_Fail, 70);
    exit(UnityEnd());
}

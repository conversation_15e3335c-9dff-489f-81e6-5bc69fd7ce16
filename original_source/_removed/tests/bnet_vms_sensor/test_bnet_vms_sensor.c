#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include <stdlib.h>
#include "unity.h"

#include "pelagic.h"
#include "cmsis_os.h"
#include "bnet.h"
#include "bnet_bond.h"
#include "rtc_api.h"
#include "radio_harness.h"
#include "winbond_flash.h"
#include "sensor_types.h"

board_uid_t board_uid = { 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a };
board_uid_t sensor_uid = { 0xa0, 0xa1, 0xa2, 0xa3, 0xa4, 0xa5, 0xa6, 0xa7, 0xa8, 0xa9 };
uint8_t radio_captured[4096];

bool board_have_imei = false;

volatile bool bnet_packet_watch = false, bnet_sensor_watch = false;
bool boat_log_record_sensor_called = false;
uint8_t log_sensor_type = 0;
uint8_t log_sensor_data[2];

void setUp() {
    rtc_write(1);
    radio_harness_init();
    radio_harness_capture(radio_captured);
    boat_log_record_sensor_called = false;
}

void boat_log_sensor(log_sensor_t *sensor) {
    boat_log_record_sensor_called = true;
    log_sensor_type = sensor->sensor_type;
    memcpy(log_sensor_data, sensor->sensor_data, sizeof(log_sensor_data));
}

void check_ack(uint8_t type) {
    TEST_ASSERT_EQUAL(21, radio_harness_captured_bytes);
    TEST_ASSERT_EQUAL_UINT8(type, radio_captured[0]);
    TEST_ASSERT_EQUAL_UINT8_ARRAY(sensor_uid, &radio_captured[1], sizeof(board_uid_t));
    TEST_ASSERT_EQUAL_UINT8_ARRAY(board_uid, &radio_captured[11], sizeof(board_uid_t));
}

void test_bnet_process_sensor_report_Success() {
    bnet_sensor_report_t report;

    report.type = BNET_TYPE_SENSOR_REPORT;
    uid_copy(report.from_uid, sensor_uid);
    uid_set_me(report.to_uid);
    report.sensor_type = SENSOR_THERMISTOR;
    report.sensor_data[0] = 0x44;
    report.sensor_data[1] = 0x55;

    bnet_process_sensor_report(&report, sizeof(report));

    check_ack(BNET_TYPE_SENSOR_REPORT_ACK);
    TEST_ASSERT_EQUAL(true, boat_log_record_sensor_called);
    TEST_ASSERT_EQUAL(SENSOR_THERMISTOR, log_sensor_type);
    TEST_ASSERT_EQUAL(0x44, log_sensor_data[0]);
    TEST_ASSERT_EQUAL(0x55, log_sensor_data[1]);
}

void check_for_no_response() {
    TEST_ASSERT_EQUAL(0, radio_harness_captured_bytes);
    TEST_ASSERT_EQUAL(false, boat_log_record_sensor_called);
}

void test_bnet_process_sensor_report_IgnoreWrongByteCount() {
    bnet_sensor_report_t report;

    // Wrong size
    report.type = BNET_TYPE_SENSOR_REPORT;
    bnet_process_sensor_report(&report, 1);
    check_for_no_response();
}

void test_bnet_process_sensor_report_IgnoreWrongUID() {
    bnet_sensor_report_t report;

    // Wrong UID
    report.type = BNET_TYPE_SENSOR_REPORT;
    uid_copy(report.from_uid, sensor_uid);
    memset(report.to_uid, 0xaa, sizeof(report.to_uid));
    bnet_process_sensor_report(&report, sizeof(report));
    check_for_no_response();
}

void test_bnet_process_sensor_bond_Success() {
    bnet_sensor_bond_req_t req;

    req.type = BNET_TYPE_SENSOR_BOND;
    uid_copy(req.from_uid, sensor_uid);

    bnet_process_sensor_bond(&req, sizeof(req));
    check_ack(BNET_TYPE_SENSOR_BOND_ACK);
}

void test_bnet_process_sensor_bond_WrongByteCount() {
    bnet_sensor_bond_req_t req;

    req.type = BNET_TYPE_SENSOR_BOND;
    uid_copy(req.from_uid, sensor_uid);
    bnet_process_sensor_bond(&req, sizeof(req) - 1);
    TEST_ASSERT_EQUAL(0, radio_harness_captured_bytes);
}

int main(int argc, char **argv) {
    UnityBegin("bnet_vms_sensor.c");
    RUN_TEST(test_bnet_process_sensor_report_Success, 10);
    RUN_TEST(test_bnet_process_sensor_report_IgnoreWrongByteCount, 20);
    RUN_TEST(test_bnet_process_sensor_report_IgnoreWrongUID, 30);
    RUN_TEST(test_bnet_process_sensor_bond_Success, 50);
    RUN_TEST(test_bnet_process_sensor_bond_WrongByteCount, 60);
    exit(UnityEnd());
}

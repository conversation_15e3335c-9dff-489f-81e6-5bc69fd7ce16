#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include <stdlib.h>

#include "pelagic-types.h"
#include "power.h"
#include "unity.h"

extern uint16_t battery_out, battery_in, battery_count;

void test_power_compute_average_Success() {
    uint16_t range[12];

    for (int i = 0; i < 13*24; i++) {
        power_compute_average(30000 + (50*i));
    }

    TEST_ASSERT_EQUAL(38375, battery_avg);
    TEST_ASSERT_EQUAL(12*24, battery_count);
}

int main(int argc, char **argv) {
    UnityBegin("power.c");
    RUN_TEST(test_power_compute_average_Success, 10);
    exit(UnityEnd());
}

#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include <stdlib.h>
#include "unity.h"

#include "pelagic.h"
#include "datetime.h"
#include "nap.h"
#include "settings.h"

bool night_time_value;

osThreadId reboot_tid;
volatile bool gps_acquired_fix = true;
volatile double gps_current_lon;
settings_t settings;

uint32_t sunrise, sunset;

bool is_nighttime() {
    return night_time_value;
}

void setUp() {
    uint32_t secs;

    // Time is in UTC
    datetime_t dt = {
        .year   = 2015,
        .month  = 9,
        .day    = 1,
        .hour   = 11,
        .minute = 30,
        .second = 0
    };

    memset((void *)&settings, 0, sizeof(settings));

    time_acquired = true;
    night_time_value = false;

    secs = datetime_to_seconds(&dt);
    rtc_write(secs);
    settings.nap.mode = NAP_MODE_OFF;

    // sunrise is 9/2 7:30
    sunrise = secs + (20 * 3600);
    // sunset is 9/1 5:30
    sunset = secs + (6 * 3600);

    // and offset for west coast to make sure timezone offset works
    gps_current_lon = -122.6994067;
}


void test_nap_ModeTimeSuccess() {
    settings.nap.mode       = NAP_MODE_TIME;
    settings.nap.minute_start = 10*60;
    settings.nap.minute_end   = 14*60;

    TEST_ASSERT_EQUAL(true, is_nap_time());
}

void test_nap_ModeTimeNotNow() {
    settings.nap.mode       = NAP_MODE_TIME;
    settings.nap.minute_start = 14*60;
    settings.nap.minute_end   = 20*60;

    TEST_ASSERT_EQUAL(false, is_nap_time());
}

void test_nap_ModeNightSuccess() {
    settings.nap.mode       = NAP_MODE_NIGHT;
    night_time_value = true;

    TEST_ASSERT_EQUAL(true, is_nap_time());
}

void test_nap_ModeNightNotNow() {
    settings.nap.mode       = NAP_MODE_NIGHT;
    night_time_value = false;

    TEST_ASSERT_EQUAL(false, is_nap_time());
}

void test_nap_ModeDaySuccess() {
    settings.nap.mode       = NAP_MODE_DAY;
    night_time_value = false;

    TEST_ASSERT_EQUAL(true, is_nap_time());
}

void test_nap_ModeDayNotNow() {
    settings.nap.mode       = NAP_MODE_DAY;
    night_time_value = true;

    TEST_ASSERT_EQUAL(false, is_nap_time());
}

void test_nap_duration_Day() {
    settings.nap.mode = NAP_MODE_DAY;

    TEST_ASSERT_EQUAL_UINT32(6 * 3600, nap_duration());
}

void test_nap_duration_Night() {
    settings.nap.mode = NAP_MODE_NIGHT;

    TEST_ASSERT_EQUAL_UINT32(20 * 3600, nap_duration());
}

void test_nap_duration_Time() {
    settings.nap.mode = NAP_MODE_TIME;

    settings.nap.minute_start = 11*60;
    settings.nap.minute_end   = 13*60;

    // 1.5 hours
    TEST_ASSERT_EQUAL_UINT32(5400, nap_duration());
}

int main(int argc, char **argv) {
    UnityBegin("nap.c");
    RUN_TEST(test_nap_ModeTimeSuccess, 10);
    RUN_TEST(test_nap_ModeTimeNotNow, 20);
    RUN_TEST(test_nap_ModeNightSuccess, 30);
    RUN_TEST(test_nap_ModeNightNotNow, 40);
    RUN_TEST(test_nap_ModeDaySuccess, 30);
    RUN_TEST(test_nap_ModeDayNotNow, 40);
    RUN_TEST(test_nap_duration_Day, 50);
    RUN_TEST(test_nap_duration_Night, 60);
    RUN_TEST(test_nap_duration_Time, 70);
    exit(UnityEnd());
}

#include "unity.h"
#include <stdio.h>
#include <stdint.h>
#include <string.h>

#include "flash_store.h"
#include "cell_signal.h"

typedef struct {
    int32_t  timestamp;
    uint32_t  latitude;
    int32_t  longitude;
    uint8_t signal;
    uint8_t rssi;
    uint8_t network;
} cell_log_t;

static int32_t test_latitude = 0, test_longitude = 0;
static uint32_t test_timestamp = 0;

static uint8_t record_cache[8192];

void setup_reconstructed_log(flash_read_t *read) {
    flash_read_init(&cell_signal_partition, read, record_cache);
}

void get_reconstructed_record(flash_read_t *log_read, cell_log_t *record) {
    uint16_t delta;
    int16_t signed_delta;
    uint8_t temp;

    TEST_ASSERT_TRUE(log_read->count < cell_signal_partition.stored);

    record->timestamp = flash_read_uint32(log_read);
    record->latitude = flash_read_uint32(log_read);
    record->longitude = flash_read_uint32(log_read);
    record->signal = flash_read_uint8(log_read);
    record->rssi = flash_read_uint8(log_read);
    record->network = flash_read_uint8(log_read);
}


void setup_cell_signal() {
    flash_store_erase(&cell_signal_partition);
}

void test_boat_log_gps_RecordSignal() {
    int32_t lat = 1000000, lon = -1000000;
    uint32_t timestamp = 1;
    flash_read_t log_read;
    cell_log_t log_record;
    uint8_t signal, rssi, network;

    setup_cell_signal();
    
    for (int i = 0; i < 10000; i++) {
        signal = (i % 100);
        rssi = (i % 10);
        network = ((i % 2) == 0);
        cell_signal_record(timestamp + i, lat + i, lon + i, signal, rssi, network);
    }

    // now reconstruct the record to make sure everything matches.
    setup_reconstructed_log(&log_read);    

    for (int i = 0; i < 10000; i++) {
        signal = (i % 100);
        rssi = (i % 10);
        network = ((i % 2) == 0);

        if (signal == 0)    // cell signal only records areas that have signal strength
            continue;

        get_reconstructed_record(&log_read, &log_record);
        
        TEST_ASSERT_EQUAL_UINT32(timestamp + i, log_record.timestamp);
        TEST_ASSERT_EQUAL_INT32(lat + i, log_record.latitude);
        TEST_ASSERT_EQUAL_INT32(lon + i, log_record.longitude);
        TEST_ASSERT_EQUAL_INT8(signal, log_record.signal);
        TEST_ASSERT_EQUAL_INT8(rssi, log_record.rssi);
        TEST_ASSERT_EQUAL_INT(network, log_record.network);
    }
}

int main(int argc, char **argv) {
    UnityBegin("cell_signal.c");
    RUN_TEST(test_boat_log_gps_RecordSignal, 10);
    UnityEnd();
}

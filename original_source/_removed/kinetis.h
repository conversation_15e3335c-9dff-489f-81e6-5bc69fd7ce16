/*
** ###################################################################
**     Processors:          MKL46Z256VLH4
**                          MKL46Z128VLH4
**                          MKL46Z256VLL4
**                          MKL46Z128VLL4
**                          MKL46Z256VMC4
**                          MKL46Z128VMC4
**
**     Compilers:           ARM Compiler
**                          Freescale C/C++ for Embedded ARM
**                          GNU C Compiler
**                          IAR ANSI C/C++ Compiler for ARM
**
**     Reference manual:    KL46P121M48SF4RM, Rev.2, Dec 2012
**     Version:             rev. 2.2, 2013-04-12
**
**     Abstract:
**         CMSIS Peripheral Access Layer for MKL46Z4
**
**     Copyright: 1997 - 2013 Freescale, Inc. All Rights Reserved.
**
**     http:                 www.freescale.com
**     mail:                 <EMAIL>
**
**     Revisions:
**     - rev. 1.0 (2012-10-16)
**         Initial version.
**     - rev. 2.0 (2012-12-12)
**         Update to reference manual rev. 1.
**     - rev. 2.1 (2013-04-05)
**         Changed start of doxygen comment.
**     - rev. 2.2 (2013-04-12)
**         SystemInit function fixed for clock configuration 1.
**         Name of the interrupt num. 31 updated to reflect proper function.
**
** ###################################################################
*/

/*!
 * @file MKL46Z4.h
 * @version 2.2
 * @date 2013-04-12
 * @brief CMSIS Peripheral Access Layer for MKL46Z4
 *
 * CMSIS Peripheral Access Layer for MKL46Z4
 */

#if !defined(MKL46Z4_H_)
#define MKL46Z4_H_                               /**< Symbol preventing repeated inclusion */

/** Memory map major version (memory maps with equal major version number are
 * compatible) */
#define MCU_MEM_MAP_VERSION 0x0200u
/** Memory map minor version */
#define MCU_MEM_MAP_VERSION_MINOR 0x0002u


/* ----------------------------------------------------------------------------
   -- Interrupt vector numbers
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup Interrupt_vector_numbers Interrupt vector numbers
 * @{
 */

/** Interrupt Number Definitions */
typedef enum IRQn {
        /* Core interrupts */
        NonMaskableInt_IRQn          = -14,              /**< Non Maskable Interrupt */
        HardFault_IRQn               = -13,              /**< Cortex-M0 SV Hard Fault Interrupt */
        SVCall_IRQn                  = -5,               /**< Cortex-M0 SV Call Interrupt */
        PendSV_IRQn                  = -2,               /**< Cortex-M0 Pend SV Interrupt */
        SysTick_IRQn                 = -1,               /**< Cortex-M0 System Tick Interrupt */

        /* Device specific interrupts */
        DMA0_IRQn                    = 0,                /**< DMA channel 0 transfer complete/error interrupt */
        DMA1_IRQn                    = 1,                /**< DMA channel 1 transfer complete/error interrupt */
        DMA2_IRQn                    = 2,                /**< DMA channel 2 transfer complete/error interrupt */
        DMA3_IRQn                    = 3,                /**< DMA channel 3 transfer complete/error interrupt */
        Reserved20_IRQn              = 4,                /**< Reserved interrupt 20 */
        FTFA_IRQn                    = 5,                /**< FTFA command complete/read collision interrupt */
        LVD_LVW_IRQn                 = 6,                /**< Low Voltage Detect, Low Voltage Warning */
        LLW_IRQn                     = 7,                /**< Low Leakage Wakeup */
        I2C0_IRQn                    = 8,                /**< I2C0 interrupt */
        I2C1_IRQn                    = 9,                /**< I2C0 interrupt 25 */
        SPI0_IRQn                    = 10,               /**< SPI0 interrupt */
        SPI1_IRQn                    = 11,               /**< SPI1 interrupt */
        UART0_IRQn                   = 12,               /**< UART0 status/error interrupt */
        UART1_IRQn                   = 13,               /**< UART1 status/error interrupt */
        UART2_IRQn                   = 14,               /**< UART2 status/error interrupt */
        ADC0_IRQn                    = 15,               /**< ADC0 interrupt */
        CMP0_IRQn                    = 16,               /**< CMP0 interrupt */
        TPM0_IRQn                    = 17,               /**< TPM0 fault, overflow and channels interrupt */
        TPM1_IRQn                    = 18,               /**< TPM1 fault, overflow and channels interrupt */
        TPM2_IRQn                    = 19,               /**< TPM2 fault, overflow and channels interrupt */
        RTC_IRQn                     = 20,               /**< RTC interrupt */
        RTC_Seconds_IRQn             = 21,               /**< RTC seconds interrupt */
        PIT_IRQn                     = 22,               /**< PIT timer interrupt */
        I2S0_IRQn                    = 23,               /**< I2S0 transmit interrupt */
        USB0_IRQn                    = 24,               /**< USB0 interrupt */
        DAC0_IRQn                    = 25,               /**< DAC0 interrupt */
        TSI0_IRQn                    = 26,               /**< TSI0 interrupt */
        MCG_IRQn                     = 27,               /**< MCG interrupt */
        LPTimer_IRQn                 = 28,               /**< LPTimer interrupt */
        LCD_IRQn                     = 29,               /**< Segment LCD Interrupt */
        PORTA_IRQn                   = 30,               /**< Port A interrupt */
        PORTC_PORTD_IRQn             = 31                /**< Port C and port D interrupt */
} IRQn_Type;

/*!
 * @}
 */ /* end of group Interrupt_vector_numbers */


/* ----------------------------------------------------------------------------
   -- Cortex M0 Core Configuration
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup Cortex_Core_Configuration Cortex M0 Core Configuration
 * @{
 */

#define __CM0PLUS_REV                  0x0000    /**< Core revision r0p0 */
#define __MPU_PRESENT                  0         /**< Defines if an MPU is present or not */
#define __VTOR_PRESENT                 1         /**< Defines if an MPU is present or not */
#define __NVIC_PRIO_BITS               2         /**< Number of priority bits implemented in the NVIC */
#define __Vendor_SysTickConfig         0         /**< Vendor specific implementation of SysTickConfig is defined */

#include "core_cm0plus.h"              /* Core Peripheral Access Layer */

/*!
 * @}
 */ /* end of group Cortex_Core_Configuration */


/* ----------------------------------------------------------------------------
   -- Device Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup Peripheral_access_layer Device Peripheral Access Layer
 * @{
 */


/*
** Start of section using anonymous unions
*/

#if defined(__ARMCC_VERSION)
#pragma push
#pragma anon_unions
#elif defined(__CWCC__)
#pragma push
#pragma cpp_extensions on
#elif defined(__GNUC__)
/* anonymous unions are enabled by default */
#elif defined(__IAR_SYSTEMS_ICC__)
#pragma language=extended
#else
#error Not supported compiler type
#endif

/* ----------------------------------------------------------------------------
   -- ADC Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup ADC_Peripheral_Access_Layer ADC Peripheral Access Layer
 * @{
 */

/** ADC - Register Layout Typedef */
typedef struct {
        __IO uint32_t SC1[2];                            /**< ADC Status and Control Registers 1, array offset: 0x0, array step: 0x4 */
        __IO uint32_t CFG1;                              /**< ADC Configuration Register 1, offset: 0x8 */
        __IO uint32_t CFG2;                              /**< ADC Configuration Register 2, offset: 0xC */
        __I  uint32_t R[2];                              /**< ADC Data Result Register, array offset: 0x10, array step: 0x4 */
        __IO uint32_t CV1;                               /**< Compare Value Registers, offset: 0x18 */
        __IO uint32_t CV2;                               /**< Compare Value Registers, offset: 0x1C */
        __IO uint32_t SC2;                               /**< Status and Control Register 2, offset: 0x20 */
        __IO uint32_t SC3;                               /**< Status and Control Register 3, offset: 0x24 */
        __IO uint32_t OFS;                               /**< ADC Offset Correction Register, offset: 0x28 */
        __IO uint32_t PG;                                /**< ADC Plus-Side Gain Register, offset: 0x2C */
        __IO uint32_t MG;                                /**< ADC Minus-Side Gain Register, offset: 0x30 */
        __IO uint32_t CLPD;                              /**< ADC Plus-Side General Calibration Value Register, offset: 0x34 */
        __IO uint32_t CLPS;                              /**< ADC Plus-Side General Calibration Value Register, offset: 0x38 */
        __IO uint32_t CLP4;                              /**< ADC Plus-Side General Calibration Value Register, offset: 0x3C */
        __IO uint32_t CLP3;                              /**< ADC Plus-Side General Calibration Value Register, offset: 0x40 */
        __IO uint32_t CLP2;                              /**< ADC Plus-Side General Calibration Value Register, offset: 0x44 */
        __IO uint32_t CLP1;                              /**< ADC Plus-Side General Calibration Value Register, offset: 0x48 */
        __IO uint32_t CLP0;                              /**< ADC Plus-Side General Calibration Value Register, offset: 0x4C */
        uint8_t RESERVED_0[4];
        __IO uint32_t CLMD;                              /**< ADC Minus-Side General Calibration Value Register, offset: 0x54 */
        __IO uint32_t CLMS;                              /**< ADC Minus-Side General Calibration Value Register, offset: 0x58 */
        __IO uint32_t CLM4;                              /**< ADC Minus-Side General Calibration Value Register, offset: 0x5C */
        __IO uint32_t CLM3;                              /**< ADC Minus-Side General Calibration Value Register, offset: 0x60 */
        __IO uint32_t CLM2;                              /**< ADC Minus-Side General Calibration Value Register, offset: 0x64 */
        __IO uint32_t CLM1;                              /**< ADC Minus-Side General Calibration Value Register, offset: 0x68 */
        __IO uint32_t CLM0;                              /**< ADC Minus-Side General Calibration Value Register, offset: 0x6C */
} ADC_Type;

/* ----------------------------------------------------------------------------
   -- ADC Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup ADC_Register_Masks ADC Register Masks
 * @{
 */

/* SC1 Bit Fields */
#define ADC_SC1_ADCH_MASK                        0x1Fu
#define ADC_SC1_ADCH_SHIFT                       0
#define ADC_SC1_ADCH(x)                          (((uint32_t)(((uint32_t)(x))<<ADC_SC1_ADCH_SHIFT))&ADC_SC1_ADCH_MASK)
#define ADC_SC1_DIFF_MASK                        0x20u
#define ADC_SC1_DIFF_SHIFT                       5
#define ADC_SC1_AIEN_MASK                        0x40u
#define ADC_SC1_AIEN_SHIFT                       6
#define ADC_SC1_COCO_MASK                        0x80u
#define ADC_SC1_COCO_SHIFT                       7
/* CFG1 Bit Fields */
#define ADC_CFG1_ADICLK_MASK                     0x3u
#define ADC_CFG1_ADICLK_SHIFT                    0
#define ADC_CFG1_ADICLK(x)                       (((uint32_t)(((uint32_t)(x))<<ADC_CFG1_ADICLK_SHIFT))&ADC_CFG1_ADICLK_MASK)
#define ADC_CFG1_MODE_MASK                       0xCu
#define ADC_CFG1_MODE_SHIFT                      2
#define ADC_CFG1_MODE(x)                         (((uint32_t)(((uint32_t)(x))<<ADC_CFG1_MODE_SHIFT))&ADC_CFG1_MODE_MASK)
#define ADC_CFG1_ADLSMP_MASK                     0x10u
#define ADC_CFG1_ADLSMP_SHIFT                    4
#define ADC_CFG1_ADIV_MASK                       0x60u
#define ADC_CFG1_ADIV_SHIFT                      5
#define ADC_CFG1_ADIV(x)                         (((uint32_t)(((uint32_t)(x))<<ADC_CFG1_ADIV_SHIFT))&ADC_CFG1_ADIV_MASK)
#define ADC_CFG1_ADLPC_MASK                      0x80u
#define ADC_CFG1_ADLPC_SHIFT                     7
/* CFG2 Bit Fields */
#define ADC_CFG2_ADLSTS_MASK                     0x3u
#define ADC_CFG2_ADLSTS_SHIFT                    0
#define ADC_CFG2_ADLSTS(x)                       (((uint32_t)(((uint32_t)(x))<<ADC_CFG2_ADLSTS_SHIFT))&ADC_CFG2_ADLSTS_MASK)
#define ADC_CFG2_ADHSC_MASK                      0x4u
#define ADC_CFG2_ADHSC_SHIFT                     2
#define ADC_CFG2_ADACKEN_MASK                    0x8u
#define ADC_CFG2_ADACKEN_SHIFT                   3
#define ADC_CFG2_MUXSEL_MASK                     0x10u
#define ADC_CFG2_MUXSEL_SHIFT                    4
/* R Bit Fields */
#define ADC_R_D_MASK                             0xFFFFu
#define ADC_R_D_SHIFT                            0
#define ADC_R_D(x)                               (((uint32_t)(((uint32_t)(x))<<ADC_R_D_SHIFT))&ADC_R_D_MASK)
/* CV1 Bit Fields */
#define ADC_CV1_CV_MASK                          0xFFFFu
#define ADC_CV1_CV_SHIFT                         0
#define ADC_CV1_CV(x)                            (((uint32_t)(((uint32_t)(x))<<ADC_CV1_CV_SHIFT))&ADC_CV1_CV_MASK)
/* CV2 Bit Fields */
#define ADC_CV2_CV_MASK                          0xFFFFu
#define ADC_CV2_CV_SHIFT                         0
#define ADC_CV2_CV(x)                            (((uint32_t)(((uint32_t)(x))<<ADC_CV2_CV_SHIFT))&ADC_CV2_CV_MASK)
/* SC2 Bit Fields */
#define ADC_SC2_REFSEL_MASK                      0x3u
#define ADC_SC2_REFSEL_SHIFT                     0
#define ADC_SC2_REFSEL(x)                        (((uint32_t)(((uint32_t)(x))<<ADC_SC2_REFSEL_SHIFT))&ADC_SC2_REFSEL_MASK)
#define ADC_SC2_DMAEN_MASK                       0x4u
#define ADC_SC2_DMAEN_SHIFT                      2
#define ADC_SC2_ACREN_MASK                       0x8u
#define ADC_SC2_ACREN_SHIFT                      3
#define ADC_SC2_ACFGT_MASK                       0x10u
#define ADC_SC2_ACFGT_SHIFT                      4
#define ADC_SC2_ACFE_MASK                        0x20u
#define ADC_SC2_ACFE_SHIFT                       5
#define ADC_SC2_ADTRG_MASK                       0x40u
#define ADC_SC2_ADTRG_SHIFT                      6
#define ADC_SC2_ADACT_MASK                       0x80u
#define ADC_SC2_ADACT_SHIFT                      7
/* SC3 Bit Fields */
#define ADC_SC3_AVGS_MASK                        0x3u
#define ADC_SC3_AVGS_SHIFT                       0
#define ADC_SC3_AVGS(x)                          (((uint32_t)(((uint32_t)(x))<<ADC_SC3_AVGS_SHIFT))&ADC_SC3_AVGS_MASK)
#define ADC_SC3_AVGE_MASK                        0x4u
#define ADC_SC3_AVGE_SHIFT                       2
#define ADC_SC3_ADCO_MASK                        0x8u
#define ADC_SC3_ADCO_SHIFT                       3
#define ADC_SC3_CALF_MASK                        0x40u
#define ADC_SC3_CALF_SHIFT                       6
#define ADC_SC3_CAL_MASK                         0x80u
#define ADC_SC3_CAL_SHIFT                        7
/* OFS Bit Fields */
#define ADC_OFS_OFS_MASK                         0xFFFFu
#define ADC_OFS_OFS_SHIFT                        0
#define ADC_OFS_OFS(x)                           (((uint32_t)(((uint32_t)(x))<<ADC_OFS_OFS_SHIFT))&ADC_OFS_OFS_MASK)
/* PG Bit Fields */
#define ADC_PG_PG_MASK                           0xFFFFu
#define ADC_PG_PG_SHIFT                          0
#define ADC_PG_PG(x)                             (((uint32_t)(((uint32_t)(x))<<ADC_PG_PG_SHIFT))&ADC_PG_PG_MASK)
/* MG Bit Fields */
#define ADC_MG_MG_MASK                           0xFFFFu
#define ADC_MG_MG_SHIFT                          0
#define ADC_MG_MG(x)                             (((uint32_t)(((uint32_t)(x))<<ADC_MG_MG_SHIFT))&ADC_MG_MG_MASK)
/* CLPD Bit Fields */
#define ADC_CLPD_CLPD_MASK                       0x3Fu
#define ADC_CLPD_CLPD_SHIFT                      0
#define ADC_CLPD_CLPD(x)                         (((uint32_t)(((uint32_t)(x))<<ADC_CLPD_CLPD_SHIFT))&ADC_CLPD_CLPD_MASK)
/* CLPS Bit Fields */
#define ADC_CLPS_CLPS_MASK                       0x3Fu
#define ADC_CLPS_CLPS_SHIFT                      0
#define ADC_CLPS_CLPS(x)                         (((uint32_t)(((uint32_t)(x))<<ADC_CLPS_CLPS_SHIFT))&ADC_CLPS_CLPS_MASK)
/* CLP4 Bit Fields */
#define ADC_CLP4_CLP4_MASK                       0x3FFu
#define ADC_CLP4_CLP4_SHIFT                      0
#define ADC_CLP4_CLP4(x)                         (((uint32_t)(((uint32_t)(x))<<ADC_CLP4_CLP4_SHIFT))&ADC_CLP4_CLP4_MASK)
/* CLP3 Bit Fields */
#define ADC_CLP3_CLP3_MASK                       0x1FFu
#define ADC_CLP3_CLP3_SHIFT                      0
#define ADC_CLP3_CLP3(x)                         (((uint32_t)(((uint32_t)(x))<<ADC_CLP3_CLP3_SHIFT))&ADC_CLP3_CLP3_MASK)
/* CLP2 Bit Fields */
#define ADC_CLP2_CLP2_MASK                       0xFFu
#define ADC_CLP2_CLP2_SHIFT                      0
#define ADC_CLP2_CLP2(x)                         (((uint32_t)(((uint32_t)(x))<<ADC_CLP2_CLP2_SHIFT))&ADC_CLP2_CLP2_MASK)
/* CLP1 Bit Fields */
#define ADC_CLP1_CLP1_MASK                       0x7Fu
#define ADC_CLP1_CLP1_SHIFT                      0
#define ADC_CLP1_CLP1(x)                         (((uint32_t)(((uint32_t)(x))<<ADC_CLP1_CLP1_SHIFT))&ADC_CLP1_CLP1_MASK)
/* CLP0 Bit Fields */
#define ADC_CLP0_CLP0_MASK                       0x3Fu
#define ADC_CLP0_CLP0_SHIFT                      0
#define ADC_CLP0_CLP0(x)                         (((uint32_t)(((uint32_t)(x))<<ADC_CLP0_CLP0_SHIFT))&ADC_CLP0_CLP0_MASK)
/* CLMD Bit Fields */
#define ADC_CLMD_CLMD_MASK                       0x3Fu
#define ADC_CLMD_CLMD_SHIFT                      0
#define ADC_CLMD_CLMD(x)                         (((uint32_t)(((uint32_t)(x))<<ADC_CLMD_CLMD_SHIFT))&ADC_CLMD_CLMD_MASK)
/* CLMS Bit Fields */
#define ADC_CLMS_CLMS_MASK                       0x3Fu
#define ADC_CLMS_CLMS_SHIFT                      0
#define ADC_CLMS_CLMS(x)                         (((uint32_t)(((uint32_t)(x))<<ADC_CLMS_CLMS_SHIFT))&ADC_CLMS_CLMS_MASK)
/* CLM4 Bit Fields */
#define ADC_CLM4_CLM4_MASK                       0x3FFu
#define ADC_CLM4_CLM4_SHIFT                      0
#define ADC_CLM4_CLM4(x)                         (((uint32_t)(((uint32_t)(x))<<ADC_CLM4_CLM4_SHIFT))&ADC_CLM4_CLM4_MASK)
/* CLM3 Bit Fields */
#define ADC_CLM3_CLM3_MASK                       0x1FFu
#define ADC_CLM3_CLM3_SHIFT                      0
#define ADC_CLM3_CLM3(x)                         (((uint32_t)(((uint32_t)(x))<<ADC_CLM3_CLM3_SHIFT))&ADC_CLM3_CLM3_MASK)
/* CLM2 Bit Fields */
#define ADC_CLM2_CLM2_MASK                       0xFFu
#define ADC_CLM2_CLM2_SHIFT                      0
#define ADC_CLM2_CLM2(x)                         (((uint32_t)(((uint32_t)(x))<<ADC_CLM2_CLM2_SHIFT))&ADC_CLM2_CLM2_MASK)
/* CLM1 Bit Fields */
#define ADC_CLM1_CLM1_MASK                       0x7Fu
#define ADC_CLM1_CLM1_SHIFT                      0
#define ADC_CLM1_CLM1(x)                         (((uint32_t)(((uint32_t)(x))<<ADC_CLM1_CLM1_SHIFT))&ADC_CLM1_CLM1_MASK)
/* CLM0 Bit Fields */
#define ADC_CLM0_CLM0_MASK                       0x3Fu
#define ADC_CLM0_CLM0_SHIFT                      0
#define ADC_CLM0_CLM0(x)                         (((uint32_t)(((uint32_t)(x))<<ADC_CLM0_CLM0_SHIFT))&ADC_CLM0_CLM0_MASK)

/*!
 * @}
 */ /* end of group ADC_Register_Masks */


/* ADC - Peripheral instance base addresses */
/** Peripheral ADC0 base address */
#define ADC0_BASE                                (0x4003B000u)
/** Peripheral ADC0 base pointer */
#define ADC0                                     ((ADC_Type *)ADC0_BASE)
/** Array initializer of ADC peripheral base pointers */
#define ADC_BASES                                { ADC0 }

/*!
 * @}
 */ /* end of group ADC_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- CMP Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup CMP_Peripheral_Access_Layer CMP Peripheral Access Layer
 * @{
 */

/** CMP - Register Layout Typedef */
typedef struct {
        __IO uint8_t CR0;                                /**< CMP Control Register 0, offset: 0x0 */
        __IO uint8_t CR1;                                /**< CMP Control Register 1, offset: 0x1 */
        __IO uint8_t FPR;                                /**< CMP Filter Period Register, offset: 0x2 */
        __IO uint8_t SCR;                                /**< CMP Status and Control Register, offset: 0x3 */
        __IO uint8_t DACCR;                              /**< DAC Control Register, offset: 0x4 */
        __IO uint8_t MUXCR;                              /**< MUX Control Register, offset: 0x5 */
} CMP_Type;

/* ----------------------------------------------------------------------------
   -- CMP Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup CMP_Register_Masks CMP Register Masks
 * @{
 */

/* CR0 Bit Fields */
#define CMP_CR0_HYSTCTR_MASK                     0x3u
#define CMP_CR0_HYSTCTR_SHIFT                    0
#define CMP_CR0_HYSTCTR(x)                       (((uint8_t)(((uint8_t)(x))<<CMP_CR0_HYSTCTR_SHIFT))&CMP_CR0_HYSTCTR_MASK)
#define CMP_CR0_FILTER_CNT_MASK                  0x70u
#define CMP_CR0_FILTER_CNT_SHIFT                 4
#define CMP_CR0_FILTER_CNT(x)                    (((uint8_t)(((uint8_t)(x))<<CMP_CR0_FILTER_CNT_SHIFT))&CMP_CR0_FILTER_CNT_MASK)
/* CR1 Bit Fields */
#define CMP_CR1_EN_MASK                          0x1u
#define CMP_CR1_EN_SHIFT                         0
#define CMP_CR1_OPE_MASK                         0x2u
#define CMP_CR1_OPE_SHIFT                        1
#define CMP_CR1_COS_MASK                         0x4u
#define CMP_CR1_COS_SHIFT                        2
#define CMP_CR1_INV_MASK                         0x8u
#define CMP_CR1_INV_SHIFT                        3
#define CMP_CR1_PMODE_MASK                       0x10u
#define CMP_CR1_PMODE_SHIFT                      4
#define CMP_CR1_TRIGM_MASK                       0x20u
#define CMP_CR1_TRIGM_SHIFT                      5
#define CMP_CR1_WE_MASK                          0x40u
#define CMP_CR1_WE_SHIFT                         6
#define CMP_CR1_SE_MASK                          0x80u
#define CMP_CR1_SE_SHIFT                         7
/* FPR Bit Fields */
#define CMP_FPR_FILT_PER_MASK                    0xFFu
#define CMP_FPR_FILT_PER_SHIFT                   0
#define CMP_FPR_FILT_PER(x)                      (((uint8_t)(((uint8_t)(x))<<CMP_FPR_FILT_PER_SHIFT))&CMP_FPR_FILT_PER_MASK)
/* SCR Bit Fields */
#define CMP_SCR_COUT_MASK                        0x1u
#define CMP_SCR_COUT_SHIFT                       0
#define CMP_SCR_CFF_MASK                         0x2u
#define CMP_SCR_CFF_SHIFT                        1
#define CMP_SCR_CFR_MASK                         0x4u
#define CMP_SCR_CFR_SHIFT                        2
#define CMP_SCR_IEF_MASK                         0x8u
#define CMP_SCR_IEF_SHIFT                        3
#define CMP_SCR_IER_MASK                         0x10u
#define CMP_SCR_IER_SHIFT                        4
#define CMP_SCR_DMAEN_MASK                       0x40u
#define CMP_SCR_DMAEN_SHIFT                      6
/* DACCR Bit Fields */
#define CMP_DACCR_VOSEL_MASK                     0x3Fu
#define CMP_DACCR_VOSEL_SHIFT                    0
#define CMP_DACCR_VOSEL(x)                       (((uint8_t)(((uint8_t)(x))<<CMP_DACCR_VOSEL_SHIFT))&CMP_DACCR_VOSEL_MASK)
#define CMP_DACCR_VRSEL_MASK                     0x40u
#define CMP_DACCR_VRSEL_SHIFT                    6
#define CMP_DACCR_DACEN_MASK                     0x80u
#define CMP_DACCR_DACEN_SHIFT                    7
/* MUXCR Bit Fields */
#define CMP_MUXCR_MSEL_MASK                      0x7u
#define CMP_MUXCR_MSEL_SHIFT                     0
#define CMP_MUXCR_MSEL(x)                        (((uint8_t)(((uint8_t)(x))<<CMP_MUXCR_MSEL_SHIFT))&CMP_MUXCR_MSEL_MASK)
#define CMP_MUXCR_PSEL_MASK                      0x38u
#define CMP_MUXCR_PSEL_SHIFT                     3
#define CMP_MUXCR_PSEL(x)                        (((uint8_t)(((uint8_t)(x))<<CMP_MUXCR_PSEL_SHIFT))&CMP_MUXCR_PSEL_MASK)
#define CMP_MUXCR_PSTM_MASK                      0x80u
#define CMP_MUXCR_PSTM_SHIFT                     7

/*!
 * @}
 */ /* end of group CMP_Register_Masks */


/* CMP - Peripheral instance base addresses */
/** Peripheral CMP0 base address */
#define CMP0_BASE                                (0x40073000u)
/** Peripheral CMP0 base pointer */
#define CMP0                                     ((CMP_Type *)CMP0_BASE)
/** Array initializer of CMP peripheral base pointers */
#define CMP_BASES                                { CMP0 }

/*!
 * @}
 */ /* end of group CMP_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- DAC Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup DAC_Peripheral_Access_Layer DAC Peripheral Access Layer
 * @{
 */

/** DAC - Register Layout Typedef */
typedef struct {
        struct {                                         /* offset: 0x0, array step: 0x2 */
                __IO uint8_t DATL;                               /**< DAC Data Low Register, array offset: 0x0, array step: 0x2 */
                __IO uint8_t DATH;                               /**< DAC Data High Register, array offset: 0x1, array step: 0x2 */
        } DAT[2];
        uint8_t RESERVED_0[28];
        __IO uint8_t SR;                                 /**< DAC Status Register, offset: 0x20 */
        __IO uint8_t C0;                                 /**< DAC Control Register, offset: 0x21 */
        __IO uint8_t C1;                                 /**< DAC Control Register 1, offset: 0x22 */
        __IO uint8_t C2;                                 /**< DAC Control Register 2, offset: 0x23 */
} DAC_Type;

/* ----------------------------------------------------------------------------
   -- DAC Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup DAC_Register_Masks DAC Register Masks
 * @{
 */

/* DATL Bit Fields */
#define DAC_DATL_DATA0_MASK                      0xFFu
#define DAC_DATL_DATA0_SHIFT                     0
#define DAC_DATL_DATA0(x)                        (((uint8_t)(((uint8_t)(x))<<DAC_DATL_DATA0_SHIFT))&DAC_DATL_DATA0_MASK)
/* DATH Bit Fields */
#define DAC_DATH_DATA1_MASK                      0xFu
#define DAC_DATH_DATA1_SHIFT                     0
#define DAC_DATH_DATA1(x)                        (((uint8_t)(((uint8_t)(x))<<DAC_DATH_DATA1_SHIFT))&DAC_DATH_DATA1_MASK)
/* SR Bit Fields */
#define DAC_SR_DACBFRPBF_MASK                    0x1u
#define DAC_SR_DACBFRPBF_SHIFT                   0
#define DAC_SR_DACBFRPTF_MASK                    0x2u
#define DAC_SR_DACBFRPTF_SHIFT                   1
/* C0 Bit Fields */
#define DAC_C0_DACBBIEN_MASK                     0x1u
#define DAC_C0_DACBBIEN_SHIFT                    0
#define DAC_C0_DACBTIEN_MASK                     0x2u
#define DAC_C0_DACBTIEN_SHIFT                    1
#define DAC_C0_LPEN_MASK                         0x8u
#define DAC_C0_LPEN_SHIFT                        3
#define DAC_C0_DACSWTRG_MASK                     0x10u
#define DAC_C0_DACSWTRG_SHIFT                    4
#define DAC_C0_DACTRGSEL_MASK                    0x20u
#define DAC_C0_DACTRGSEL_SHIFT                   5
#define DAC_C0_DACRFS_MASK                       0x40u
#define DAC_C0_DACRFS_SHIFT                      6
#define DAC_C0_DACEN_MASK                        0x80u
#define DAC_C0_DACEN_SHIFT                       7
/* C1 Bit Fields */
#define DAC_C1_DACBFEN_MASK                      0x1u
#define DAC_C1_DACBFEN_SHIFT                     0
#define DAC_C1_DACBFMD_MASK                      0x4u
#define DAC_C1_DACBFMD_SHIFT                     2
#define DAC_C1_DMAEN_MASK                        0x80u
#define DAC_C1_DMAEN_SHIFT                       7
/* C2 Bit Fields */
#define DAC_C2_DACBFUP_MASK                      0x1u
#define DAC_C2_DACBFUP_SHIFT                     0
#define DAC_C2_DACBFRP_MASK                      0x10u
#define DAC_C2_DACBFRP_SHIFT                     4

/*!
 * @}
 */ /* end of group DAC_Register_Masks */


/* DAC - Peripheral instance base addresses */
/** Peripheral DAC0 base address */
#define DAC0_BASE                                (0x4003F000u)
/** Peripheral DAC0 base pointer */
#define DAC0                                     ((DAC_Type *)DAC0_BASE)
/** Array initializer of DAC peripheral base pointers */
#define DAC_BASES                                { DAC0 }

/*!
 * @}
 */ /* end of group DAC_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- DMA Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup DMA_Peripheral_Access_Layer DMA Peripheral Access Layer
 * @{
 */

/** DMA - Register Layout Typedef */
typedef struct {
        uint8_t RESERVED_0[256];
        struct DMA_Channel {                                         /* offset: 0x100, array step: 0x10 */
                __IO uint32_t SAR;                               /**< Source Address Register, array offset: 0x100, array step: 0x10 */
                __IO uint32_t DAR;                               /**< Destination Address Register, array offset: 0x104, array step: 0x10 */
                union {                                          /* offset: 0x108, array step: 0x10 */
                        __IO uint32_t DSR_BCR;                           /**< DMA Status Register / Byte Count Register, array offset: 0x108, array step: 0x10 */
                        struct {                                         /* offset: 0x108, array step: 0x10 */
                                uint8_t RESERVED_0[3];
                                __IO uint8_t DSR;                                /**< DMA_DSR0 register...DMA_DSR3 register., array offset: 0x10B, array step: 0x10 */
                        } DMA_DSR_ACCESS8BIT;
                };
                __IO uint32_t DCR;                               /**< DMA Control Register, array offset: 0x10C, array step: 0x10 */
        } DMA[4];
} DMA_Type;

/* ----------------------------------------------------------------------------
   -- DMA Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup DMA_Register_Masks DMA Register Masks
 * @{
 */

/* SAR Bit Fields */
#define DMA_SAR_SAR_MASK                         0xFFFFFFFFu
#define DMA_SAR_SAR_SHIFT                        0
#define DMA_SAR_SAR(x)                           (((uint32_t)(((uint32_t)(x))<<DMA_SAR_SAR_SHIFT))&DMA_SAR_SAR_MASK)
/* DAR Bit Fields */
#define DMA_DAR_DAR_MASK                         0xFFFFFFFFu
#define DMA_DAR_DAR_SHIFT                        0
#define DMA_DAR_DAR(x)                           (((uint32_t)(((uint32_t)(x))<<DMA_DAR_DAR_SHIFT))&DMA_DAR_DAR_MASK)
/* DSR_BCR Bit Fields */
#define DMA_DSR_BCR_BCR_MASK                     0xFFFFFFu
#define DMA_DSR_BCR_BCR_SHIFT                    0
#define DMA_DSR_BCR_BCR(x)                       (((uint32_t)(((uint32_t)(x))<<DMA_DSR_BCR_BCR_SHIFT))&DMA_DSR_BCR_BCR_MASK)
#define DMA_DSR_BCR_DONE_MASK                    0x1000000u
#define DMA_DSR_BCR_DONE_SHIFT                   24
#define DMA_DSR_BCR_BSY_MASK                     0x2000000u
#define DMA_DSR_BCR_BSY_SHIFT                    25
#define DMA_DSR_BCR_REQ_MASK                     0x4000000u
#define DMA_DSR_BCR_REQ_SHIFT                    26
#define DMA_DSR_BCR_BED_MASK                     0x10000000u
#define DMA_DSR_BCR_BED_SHIFT                    28
#define DMA_DSR_BCR_BES_MASK                     0x20000000u
#define DMA_DSR_BCR_BES_SHIFT                    29
#define DMA_DSR_BCR_CE_MASK                      0x40000000u
#define DMA_DSR_BCR_CE_SHIFT                     30
/* DCR Bit Fields */
#define DMA_DCR_LCH2_MASK                        0x3u
#define DMA_DCR_LCH2_SHIFT                       0
#define DMA_DCR_LCH2(x)                          (((uint32_t)(((uint32_t)(x))<<DMA_DCR_LCH2_SHIFT))&DMA_DCR_LCH2_MASK)
#define DMA_DCR_LCH1_MASK                        0xCu
#define DMA_DCR_LCH1_SHIFT                       2
#define DMA_DCR_LCH1(x)                          (((uint32_t)(((uint32_t)(x))<<DMA_DCR_LCH1_SHIFT))&DMA_DCR_LCH1_MASK)
#define DMA_DCR_LINKCC_MASK                      0x30u
#define DMA_DCR_LINKCC_SHIFT                     4
#define DMA_DCR_LINKCC(x)                        (((uint32_t)(((uint32_t)(x))<<DMA_DCR_LINKCC_SHIFT))&DMA_DCR_LINKCC_MASK)
#define DMA_DCR_D_REQ_MASK                       0x80u
#define DMA_DCR_D_REQ_SHIFT                      7
#define DMA_DCR_DMOD_MASK                        0xF00u
#define DMA_DCR_DMOD_SHIFT                       8
#define DMA_DCR_DMOD(x)                          (((uint32_t)(((uint32_t)(x))<<DMA_DCR_DMOD_SHIFT))&DMA_DCR_DMOD_MASK)
#define DMA_DCR_SMOD_MASK                        0xF000u
#define DMA_DCR_SMOD_SHIFT                       12
#define DMA_DCR_SMOD(x)                          (((uint32_t)(((uint32_t)(x))<<DMA_DCR_SMOD_SHIFT))&DMA_DCR_SMOD_MASK)
#define DMA_DCR_START_MASK                       0x10000u
#define DMA_DCR_START_SHIFT                      16
#define DMA_DCR_DSIZE_MASK                       0x60000u
#define DMA_DCR_DSIZE_SHIFT                      17
#define DMA_DCR_DSIZE(x)                         (((uint32_t)(((uint32_t)(x))<<DMA_DCR_DSIZE_SHIFT))&DMA_DCR_DSIZE_MASK)
#define DMA_DCR_DINC_MASK                        0x80000u
#define DMA_DCR_DINC_SHIFT                       19
#define DMA_DCR_SSIZE_MASK                       0x300000u
#define DMA_DCR_SSIZE_SHIFT                      20
#define DMA_DCR_SSIZE(x)                         (((uint32_t)(((uint32_t)(x))<<DMA_DCR_SSIZE_SHIFT))&DMA_DCR_SSIZE_MASK)
#define DMA_DCR_SINC_MASK                        0x400000u
#define DMA_DCR_SINC_SHIFT                       22
#define DMA_DCR_EADREQ_MASK                      0x800000u
#define DMA_DCR_EADREQ_SHIFT                     23
#define DMA_DCR_AA_MASK                          0x10000000u
#define DMA_DCR_AA_SHIFT                         28
#define DMA_DCR_CS_MASK                          0x20000000u
#define DMA_DCR_CS_SHIFT                         29
#define DMA_DCR_ERQ_MASK                         0x40000000u
#define DMA_DCR_ERQ_SHIFT                        30
#define DMA_DCR_EINT_MASK                        0x80000000u
#define DMA_DCR_EINT_SHIFT                       31

/*!
 * @}
 */ /* end of group DMA_Register_Masks */


/* DMA - Peripheral instance base addresses */
/** Peripheral DMA base address */
#define DMA_BASE                                 (0x40008000u)
/** Peripheral DMA base pointer */
#define DMA0                                     ((DMA_Type *)DMA_BASE)
/** Array initializer of DMA peripheral base pointers */
#define DMA_BASES                                { DMA0 }

/*!
 * @}
 */ /* end of group DMA_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- DMAMUX Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup DMAMUX_Peripheral_Access_Layer DMAMUX Peripheral Access Layer
 * @{
 */

/** DMAMUX - Register Layout Typedef */
typedef struct {
        __IO uint8_t CHCFG[4];                           /**< Channel Configuration register, array offset: 0x0, array step: 0x1 */
} DMAMUX_Type;

/* ----------------------------------------------------------------------------
   -- DMAMUX Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup DMAMUX_Register_Masks DMAMUX Register Masks
 * @{
 */

/* CHCFG Bit Fields */
#define DMAMUX_CHCFG_SOURCE_MASK                 0x3Fu
#define DMAMUX_CHCFG_SOURCE_SHIFT                0
#define DMAMUX_CHCFG_SOURCE(x)                   (((uint8_t)(((uint8_t)(x))<<DMAMUX_CHCFG_SOURCE_SHIFT))&DMAMUX_CHCFG_SOURCE_MASK)
#define DMAMUX_CHCFG_TRIG_MASK                   0x40u
#define DMAMUX_CHCFG_TRIG_SHIFT                  6
#define DMAMUX_CHCFG_ENBL_MASK                   0x80u
#define DMAMUX_CHCFG_ENBL_SHIFT                  7

/*!
 * @}
 */ /* end of group DMAMUX_Register_Masks */


/* DMAMUX - Peripheral instance base addresses */
/** Peripheral DMAMUX0 base address */
#define DMAMUX0_BASE                             (0x40021000u)
/** Peripheral DMAMUX0 base pointer */
#define DMAMUX0                                  ((DMAMUX_Type *)DMAMUX0_BASE)
/** Array initializer of DMAMUX peripheral base pointers */
#define DMAMUX_BASES                             { DMAMUX0 }

/*!
 * @}
 */ /* end of group DMAMUX_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- FGPIO Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup FGPIO_Peripheral_Access_Layer FGPIO Peripheral Access Layer
 * @{
 */

/** FGPIO - Register Layout Typedef */
typedef struct {
        __IO uint32_t PDOR;                              /**< Port Data Output Register, offset: 0x0 */
        __O  uint32_t PSOR;                              /**< Port Set Output Register, offset: 0x4 */
        __O  uint32_t PCOR;                              /**< Port Clear Output Register, offset: 0x8 */
        __O  uint32_t PTOR;                              /**< Port Toggle Output Register, offset: 0xC */
        __I  uint32_t PDIR;                              /**< Port Data Input Register, offset: 0x10 */
        __IO uint32_t PDDR;                              /**< Port Data Direction Register, offset: 0x14 */
} FGPIO_Type;

/* ----------------------------------------------------------------------------
   -- FGPIO Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup FGPIO_Register_Masks FGPIO Register Masks
 * @{
 */

/* PDOR Bit Fields */
#define FGPIO_PDOR_PDO_MASK                      0xFFFFFFFFu
#define FGPIO_PDOR_PDO_SHIFT                     0
#define FGPIO_PDOR_PDO(x)                        (((uint32_t)(((uint32_t)(x))<<FGPIO_PDOR_PDO_SHIFT))&FGPIO_PDOR_PDO_MASK)
/* PSOR Bit Fields */
#define FGPIO_PSOR_PTSO_MASK                     0xFFFFFFFFu
#define FGPIO_PSOR_PTSO_SHIFT                    0
#define FGPIO_PSOR_PTSO(x)                       (((uint32_t)(((uint32_t)(x))<<FGPIO_PSOR_PTSO_SHIFT))&FGPIO_PSOR_PTSO_MASK)
/* PCOR Bit Fields */
#define FGPIO_PCOR_PTCO_MASK                     0xFFFFFFFFu
#define FGPIO_PCOR_PTCO_SHIFT                    0
#define FGPIO_PCOR_PTCO(x)                       (((uint32_t)(((uint32_t)(x))<<FGPIO_PCOR_PTCO_SHIFT))&FGPIO_PCOR_PTCO_MASK)
/* PTOR Bit Fields */
#define FGPIO_PTOR_PTTO_MASK                     0xFFFFFFFFu
#define FGPIO_PTOR_PTTO_SHIFT                    0
#define FGPIO_PTOR_PTTO(x)                       (((uint32_t)(((uint32_t)(x))<<FGPIO_PTOR_PTTO_SHIFT))&FGPIO_PTOR_PTTO_MASK)
/* PDIR Bit Fields */
#define FGPIO_PDIR_PDI_MASK                      0xFFFFFFFFu
#define FGPIO_PDIR_PDI_SHIFT                     0
#define FGPIO_PDIR_PDI(x)                        (((uint32_t)(((uint32_t)(x))<<FGPIO_PDIR_PDI_SHIFT))&FGPIO_PDIR_PDI_MASK)
/* PDDR Bit Fields */
#define FGPIO_PDDR_PDD_MASK                      0xFFFFFFFFu
#define FGPIO_PDDR_PDD_SHIFT                     0
#define FGPIO_PDDR_PDD(x)                        (((uint32_t)(((uint32_t)(x))<<FGPIO_PDDR_PDD_SHIFT))&FGPIO_PDDR_PDD_MASK)

/*!
 * @}
 */ /* end of group FGPIO_Register_Masks */


/* FGPIO - Peripheral instance base addresses */
/** Peripheral FPTA base address */
#define FPTA_BASE                                (0xF80FF000u)
/** Peripheral FPTA base pointer */
#define FPTA                                     ((FGPIO_Type *)FPTA_BASE)
/** Peripheral FPTB base address */
#define FPTB_BASE                                (0xF80FF040u)
/** Peripheral FPTB base pointer */
#define FPTB                                     ((FGPIO_Type *)FPTB_BASE)
/** Peripheral FPTC base address */
#define FPTC_BASE                                (0xF80FF080u)
/** Peripheral FPTC base pointer */
#define FPTC                                     ((FGPIO_Type *)FPTC_BASE)
/** Peripheral FPTD base address */
#define FPTD_BASE                                (0xF80FF0C0u)
/** Peripheral FPTD base pointer */
#define FPTD                                     ((FGPIO_Type *)FPTD_BASE)
/** Peripheral FPTE base address */
#define FPTE_BASE                                (0xF80FF100u)
/** Peripheral FPTE base pointer */
#define FPTE                                     ((FGPIO_Type *)FPTE_BASE)
/** Array initializer of FGPIO peripheral base pointers */
#define FGPIO_BASES                              { FPTA, FPTB, FPTC, FPTD, FPTE }

/*!
 * @}
 */ /* end of group FGPIO_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- FTFA Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup FTFA_Peripheral_Access_Layer FTFA Peripheral Access Layer
 * @{
 */

/** FTFA - Register Layout Typedef */
typedef struct {
        __IO uint8_t FSTAT;                              /**< Flash Status Register, offset: 0x0 */
        __IO uint8_t FCNFG;                              /**< Flash Configuration Register, offset: 0x1 */
        __I  uint8_t FSEC;                               /**< Flash Security Register, offset: 0x2 */
        __I  uint8_t FOPT;                               /**< Flash Option Register, offset: 0x3 */
        __IO uint8_t FCCOB3;                             /**< Flash Common Command Object Registers, offset: 0x4 */
        __IO uint8_t FCCOB2;                             /**< Flash Common Command Object Registers, offset: 0x5 */
        __IO uint8_t FCCOB1;                             /**< Flash Common Command Object Registers, offset: 0x6 */
        __IO uint8_t FCCOB0;                             /**< Flash Common Command Object Registers, offset: 0x7 */
        __IO uint8_t FCCOB7;                             /**< Flash Common Command Object Registers, offset: 0x8 */
        __IO uint8_t FCCOB6;                             /**< Flash Common Command Object Registers, offset: 0x9 */
        __IO uint8_t FCCOB5;                             /**< Flash Common Command Object Registers, offset: 0xA */
        __IO uint8_t FCCOB4;                             /**< Flash Common Command Object Registers, offset: 0xB */
        __IO uint8_t FCCOBB;                             /**< Flash Common Command Object Registers, offset: 0xC */
        __IO uint8_t FCCOBA;                             /**< Flash Common Command Object Registers, offset: 0xD */
        __IO uint8_t FCCOB9;                             /**< Flash Common Command Object Registers, offset: 0xE */
        __IO uint8_t FCCOB8;                             /**< Flash Common Command Object Registers, offset: 0xF */
        __IO uint8_t FPROT3;                             /**< Program Flash Protection Registers, offset: 0x10 */
        __IO uint8_t FPROT2;                             /**< Program Flash Protection Registers, offset: 0x11 */
        __IO uint8_t FPROT1;                             /**< Program Flash Protection Registers, offset: 0x12 */
        __IO uint8_t FPROT0;                             /**< Program Flash Protection Registers, offset: 0x13 */
} FTFA_Type;

/* ----------------------------------------------------------------------------
   -- FTFA Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup FTFA_Register_Masks FTFA Register Masks
 * @{
 */

/* FSTAT Bit Fields */
#define FTFA_FSTAT_MGSTAT0_MASK                  0x1u
#define FTFA_FSTAT_MGSTAT0_SHIFT                 0
#define FTFA_FSTAT_FPVIOL_MASK                   0x10u
#define FTFA_FSTAT_FPVIOL_SHIFT                  4
#define FTFA_FSTAT_ACCERR_MASK                   0x20u
#define FTFA_FSTAT_ACCERR_SHIFT                  5
#define FTFA_FSTAT_RDCOLERR_MASK                 0x40u
#define FTFA_FSTAT_RDCOLERR_SHIFT                6
#define FTFA_FSTAT_CCIF_MASK                     0x80u
#define FTFA_FSTAT_CCIF_SHIFT                    7
/* FCNFG Bit Fields */
#define FTFA_FCNFG_ERSSUSP_MASK                  0x10u
#define FTFA_FCNFG_ERSSUSP_SHIFT                 4
#define FTFA_FCNFG_ERSAREQ_MASK                  0x20u
#define FTFA_FCNFG_ERSAREQ_SHIFT                 5
#define FTFA_FCNFG_RDCOLLIE_MASK                 0x40u
#define FTFA_FCNFG_RDCOLLIE_SHIFT                6
#define FTFA_FCNFG_CCIE_MASK                     0x80u
#define FTFA_FCNFG_CCIE_SHIFT                    7
/* FSEC Bit Fields */
#define FTFA_FSEC_SEC_MASK                       0x3u
#define FTFA_FSEC_SEC_SHIFT                      0
#define FTFA_FSEC_SEC(x)                         (((uint8_t)(((uint8_t)(x))<<FTFA_FSEC_SEC_SHIFT))&FTFA_FSEC_SEC_MASK)
#define FTFA_FSEC_FSLACC_MASK                    0xCu
#define FTFA_FSEC_FSLACC_SHIFT                   2
#define FTFA_FSEC_FSLACC(x)                      (((uint8_t)(((uint8_t)(x))<<FTFA_FSEC_FSLACC_SHIFT))&FTFA_FSEC_FSLACC_MASK)
#define FTFA_FSEC_MEEN_MASK                      0x30u
#define FTFA_FSEC_MEEN_SHIFT                     4
#define FTFA_FSEC_MEEN(x)                        (((uint8_t)(((uint8_t)(x))<<FTFA_FSEC_MEEN_SHIFT))&FTFA_FSEC_MEEN_MASK)
#define FTFA_FSEC_KEYEN_MASK                     0xC0u
#define FTFA_FSEC_KEYEN_SHIFT                    6
#define FTFA_FSEC_KEYEN(x)                       (((uint8_t)(((uint8_t)(x))<<FTFA_FSEC_KEYEN_SHIFT))&FTFA_FSEC_KEYEN_MASK)
/* FOPT Bit Fields */
#define FTFA_FOPT_OPT_MASK                       0xFFu
#define FTFA_FOPT_OPT_SHIFT                      0
#define FTFA_FOPT_OPT(x)                         (((uint8_t)(((uint8_t)(x))<<FTFA_FOPT_OPT_SHIFT))&FTFA_FOPT_OPT_MASK)
/* FCCOB3 Bit Fields */
#define FTFA_FCCOB3_CCOBn_MASK                   0xFFu
#define FTFA_FCCOB3_CCOBn_SHIFT                  0
#define FTFA_FCCOB3_CCOBn(x)                     (((uint8_t)(((uint8_t)(x))<<FTFA_FCCOB3_CCOBn_SHIFT))&FTFA_FCCOB3_CCOBn_MASK)
/* FCCOB2 Bit Fields */
#define FTFA_FCCOB2_CCOBn_MASK                   0xFFu
#define FTFA_FCCOB2_CCOBn_SHIFT                  0
#define FTFA_FCCOB2_CCOBn(x)                     (((uint8_t)(((uint8_t)(x))<<FTFA_FCCOB2_CCOBn_SHIFT))&FTFA_FCCOB2_CCOBn_MASK)
/* FCCOB1 Bit Fields */
#define FTFA_FCCOB1_CCOBn_MASK                   0xFFu
#define FTFA_FCCOB1_CCOBn_SHIFT                  0
#define FTFA_FCCOB1_CCOBn(x)                     (((uint8_t)(((uint8_t)(x))<<FTFA_FCCOB1_CCOBn_SHIFT))&FTFA_FCCOB1_CCOBn_MASK)
/* FCCOB0 Bit Fields */
#define FTFA_FCCOB0_CCOBn_MASK                   0xFFu
#define FTFA_FCCOB0_CCOBn_SHIFT                  0
#define FTFA_FCCOB0_CCOBn(x)                     (((uint8_t)(((uint8_t)(x))<<FTFA_FCCOB0_CCOBn_SHIFT))&FTFA_FCCOB0_CCOBn_MASK)
/* FCCOB7 Bit Fields */
#define FTFA_FCCOB7_CCOBn_MASK                   0xFFu
#define FTFA_FCCOB7_CCOBn_SHIFT                  0
#define FTFA_FCCOB7_CCOBn(x)                     (((uint8_t)(((uint8_t)(x))<<FTFA_FCCOB7_CCOBn_SHIFT))&FTFA_FCCOB7_CCOBn_MASK)
/* FCCOB6 Bit Fields */
#define FTFA_FCCOB6_CCOBn_MASK                   0xFFu
#define FTFA_FCCOB6_CCOBn_SHIFT                  0
#define FTFA_FCCOB6_CCOBn(x)                     (((uint8_t)(((uint8_t)(x))<<FTFA_FCCOB6_CCOBn_SHIFT))&FTFA_FCCOB6_CCOBn_MASK)
/* FCCOB5 Bit Fields */
#define FTFA_FCCOB5_CCOBn_MASK                   0xFFu
#define FTFA_FCCOB5_CCOBn_SHIFT                  0
#define FTFA_FCCOB5_CCOBn(x)                     (((uint8_t)(((uint8_t)(x))<<FTFA_FCCOB5_CCOBn_SHIFT))&FTFA_FCCOB5_CCOBn_MASK)
/* FCCOB4 Bit Fields */
#define FTFA_FCCOB4_CCOBn_MASK                   0xFFu
#define FTFA_FCCOB4_CCOBn_SHIFT                  0
#define FTFA_FCCOB4_CCOBn(x)                     (((uint8_t)(((uint8_t)(x))<<FTFA_FCCOB4_CCOBn_SHIFT))&FTFA_FCCOB4_CCOBn_MASK)
/* FCCOBB Bit Fields */
#define FTFA_FCCOBB_CCOBn_MASK                   0xFFu
#define FTFA_FCCOBB_CCOBn_SHIFT                  0
#define FTFA_FCCOBB_CCOBn(x)                     (((uint8_t)(((uint8_t)(x))<<FTFA_FCCOBB_CCOBn_SHIFT))&FTFA_FCCOBB_CCOBn_MASK)
/* FCCOBA Bit Fields */
#define FTFA_FCCOBA_CCOBn_MASK                   0xFFu
#define FTFA_FCCOBA_CCOBn_SHIFT                  0
#define FTFA_FCCOBA_CCOBn(x)                     (((uint8_t)(((uint8_t)(x))<<FTFA_FCCOBA_CCOBn_SHIFT))&FTFA_FCCOBA_CCOBn_MASK)
/* FCCOB9 Bit Fields */
#define FTFA_FCCOB9_CCOBn_MASK                   0xFFu
#define FTFA_FCCOB9_CCOBn_SHIFT                  0
#define FTFA_FCCOB9_CCOBn(x)                     (((uint8_t)(((uint8_t)(x))<<FTFA_FCCOB9_CCOBn_SHIFT))&FTFA_FCCOB9_CCOBn_MASK)
/* FCCOB8 Bit Fields */
#define FTFA_FCCOB8_CCOBn_MASK                   0xFFu
#define FTFA_FCCOB8_CCOBn_SHIFT                  0
#define FTFA_FCCOB8_CCOBn(x)                     (((uint8_t)(((uint8_t)(x))<<FTFA_FCCOB8_CCOBn_SHIFT))&FTFA_FCCOB8_CCOBn_MASK)
/* FPROT3 Bit Fields */
#define FTFA_FPROT3_PROT_MASK                    0xFFu
#define FTFA_FPROT3_PROT_SHIFT                   0
#define FTFA_FPROT3_PROT(x)                      (((uint8_t)(((uint8_t)(x))<<FTFA_FPROT3_PROT_SHIFT))&FTFA_FPROT3_PROT_MASK)
/* FPROT2 Bit Fields */
#define FTFA_FPROT2_PROT_MASK                    0xFFu
#define FTFA_FPROT2_PROT_SHIFT                   0
#define FTFA_FPROT2_PROT(x)                      (((uint8_t)(((uint8_t)(x))<<FTFA_FPROT2_PROT_SHIFT))&FTFA_FPROT2_PROT_MASK)
/* FPROT1 Bit Fields */
#define FTFA_FPROT1_PROT_MASK                    0xFFu
#define FTFA_FPROT1_PROT_SHIFT                   0
#define FTFA_FPROT1_PROT(x)                      (((uint8_t)(((uint8_t)(x))<<FTFA_FPROT1_PROT_SHIFT))&FTFA_FPROT1_PROT_MASK)
/* FPROT0 Bit Fields */
#define FTFA_FPROT0_PROT_MASK                    0xFFu
#define FTFA_FPROT0_PROT_SHIFT                   0
#define FTFA_FPROT0_PROT(x)                      (((uint8_t)(((uint8_t)(x))<<FTFA_FPROT0_PROT_SHIFT))&FTFA_FPROT0_PROT_MASK)

/*!
 * @}
 */ /* end of group FTFA_Register_Masks */


/* FTFA - Peripheral instance base addresses */
/** Peripheral FTFA base address */
#define FTFA_BASE                                (0x40020000u)
/** Peripheral FTFA base pointer */
#define FTFA                                     ((FTFA_Type *)FTFA_BASE)
/** Array initializer of FTFA peripheral base pointers */
#define FTFA_BASES                               { FTFA }

/*!
 * @}
 */ /* end of group FTFA_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- GPIO Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup GPIO_Peripheral_Access_Layer GPIO Peripheral Access Layer
 * @{
 */

/** GPIO - Register Layout Typedef */
typedef struct {
        __IO uint32_t PDOR;                              /**< Port Data Output Register, offset: 0x0 */
        __O  uint32_t PSOR;                              /**< Port Set Output Register, offset: 0x4 */
        __O  uint32_t PCOR;                              /**< Port Clear Output Register, offset: 0x8 */
        __O  uint32_t PTOR;                              /**< Port Toggle Output Register, offset: 0xC */
        __I  uint32_t PDIR;                              /**< Port Data Input Register, offset: 0x10 */
        __IO uint32_t PDDR;                              /**< Port Data Direction Register, offset: 0x14 */
} GPIO_Type;

/* ----------------------------------------------------------------------------
   -- GPIO Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup GPIO_Register_Masks GPIO Register Masks
 * @{
 */

/* PDOR Bit Fields */
#define GPIO_PDOR_PDO_MASK                       0xFFFFFFFFu
#define GPIO_PDOR_PDO_SHIFT                      0
#define GPIO_PDOR_PDO(x)                         (((uint32_t)(((uint32_t)(x))<<GPIO_PDOR_PDO_SHIFT))&GPIO_PDOR_PDO_MASK)
/* PSOR Bit Fields */
#define GPIO_PSOR_PTSO_MASK                      0xFFFFFFFFu
#define GPIO_PSOR_PTSO_SHIFT                     0
#define GPIO_PSOR_PTSO(x)                        (((uint32_t)(((uint32_t)(x))<<GPIO_PSOR_PTSO_SHIFT))&GPIO_PSOR_PTSO_MASK)
/* PCOR Bit Fields */
#define GPIO_PCOR_PTCO_MASK                      0xFFFFFFFFu
#define GPIO_PCOR_PTCO_SHIFT                     0
#define GPIO_PCOR_PTCO(x)                        (((uint32_t)(((uint32_t)(x))<<GPIO_PCOR_PTCO_SHIFT))&GPIO_PCOR_PTCO_MASK)
/* PTOR Bit Fields */
#define GPIO_PTOR_PTTO_MASK                      0xFFFFFFFFu
#define GPIO_PTOR_PTTO_SHIFT                     0
#define GPIO_PTOR_PTTO(x)                        (((uint32_t)(((uint32_t)(x))<<GPIO_PTOR_PTTO_SHIFT))&GPIO_PTOR_PTTO_MASK)
/* PDIR Bit Fields */
#define GPIO_PDIR_PDI_MASK                       0xFFFFFFFFu
#define GPIO_PDIR_PDI_SHIFT                      0
#define GPIO_PDIR_PDI(x)                         (((uint32_t)(((uint32_t)(x))<<GPIO_PDIR_PDI_SHIFT))&GPIO_PDIR_PDI_MASK)
/* PDDR Bit Fields */
#define GPIO_PDDR_PDD_MASK                       0xFFFFFFFFu
#define GPIO_PDDR_PDD_SHIFT                      0
#define GPIO_PDDR_PDD(x)                         (((uint32_t)(((uint32_t)(x))<<GPIO_PDDR_PDD_SHIFT))&GPIO_PDDR_PDD_MASK)

/*!
 * @}
 */ /* end of group GPIO_Register_Masks */


/* GPIO - Peripheral instance base addresses */
/** Peripheral PTA base address */
#define PTA_BASE                                 (0x400FF000u)
/** Peripheral PTA base pointer */
#define PTA                                      ((GPIO_Type *)PTA_BASE)
/** Peripheral PTB base address */
#define PTB_BASE                                 (0x400FF040u)
/** Peripheral PTB base pointer */
#define PTB                                      ((GPIO_Type *)PTB_BASE)
/** Peripheral PTC base address */
#define PTC_BASE                                 (0x400FF080u)
/** Peripheral PTC base pointer */
#define PTC                                      ((GPIO_Type *)PTC_BASE)
/** Peripheral PTD base address */
#define PTD_BASE                                 (0x400FF0C0u)
/** Peripheral PTD base pointer */
#define PTD                                      ((GPIO_Type *)PTD_BASE)
/** Peripheral PTE base address */
#define PTE_BASE                                 (0x400FF100u)
/** Peripheral PTE base pointer */
#define PTE                                      ((GPIO_Type *)PTE_BASE)
/** Array initializer of GPIO peripheral base pointers */
#define GPIO_BASES                               { PTA, PTB, PTC, PTD, PTE }

/*!
 * @}
 */ /* end of group GPIO_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- I2C Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup I2C_Peripheral_Access_Layer I2C Peripheral Access Layer
 * @{
 */

/** I2C - Register Layout Typedef */
typedef struct {
        __IO uint8_t A1;                                 /**< I2C Address Register 1, offset: 0x0 */
        __IO uint8_t F;                                  /**< I2C Frequency Divider register, offset: 0x1 */
        __IO uint8_t C1;                                 /**< I2C Control Register 1, offset: 0x2 */
        __IO uint8_t S;                                  /**< I2C Status register, offset: 0x3 */
        __IO uint8_t D;                                  /**< I2C Data I/O register, offset: 0x4 */
        __IO uint8_t C2;                                 /**< I2C Control Register 2, offset: 0x5 */
        __IO uint8_t FLT;                                /**< I2C Programmable Input Glitch Filter register, offset: 0x6 */
        __IO uint8_t RA;                                 /**< I2C Range Address register, offset: 0x7 */
        __IO uint8_t SMB;                                /**< I2C SMBus Control and Status register, offset: 0x8 */
        __IO uint8_t A2;                                 /**< I2C Address Register 2, offset: 0x9 */
        __IO uint8_t SLTH;                               /**< I2C SCL Low Timeout Register High, offset: 0xA */
        __IO uint8_t SLTL;                               /**< I2C SCL Low Timeout Register Low, offset: 0xB */
} I2C_Type;

/* ----------------------------------------------------------------------------
   -- I2C Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup I2C_Register_Masks I2C Register Masks
 * @{
 */

/* A1 Bit Fields */
#define I2C_A1_AD_MASK                           0xFEu
#define I2C_A1_AD_SHIFT                          1
#define I2C_A1_AD(x)                             (((uint8_t)(((uint8_t)(x))<<I2C_A1_AD_SHIFT))&I2C_A1_AD_MASK)
/* F Bit Fields */
#define I2C_F_ICR_MASK                           0x3Fu
#define I2C_F_ICR_SHIFT                          0
#define I2C_F_ICR(x)                             (((uint8_t)(((uint8_t)(x))<<I2C_F_ICR_SHIFT))&I2C_F_ICR_MASK)
#define I2C_F_MULT_MASK                          0xC0u
#define I2C_F_MULT_SHIFT                         6
#define I2C_F_MULT(x)                            (((uint8_t)(((uint8_t)(x))<<I2C_F_MULT_SHIFT))&I2C_F_MULT_MASK)
/* C1 Bit Fields */
#define I2C_C1_DMAEN_MASK                        0x1u
#define I2C_C1_DMAEN_SHIFT                       0
#define I2C_C1_WUEN_MASK                         0x2u
#define I2C_C1_WUEN_SHIFT                        1
#define I2C_C1_RSTA_MASK                         0x4u
#define I2C_C1_RSTA_SHIFT                        2
#define I2C_C1_TXAK_MASK                         0x8u
#define I2C_C1_TXAK_SHIFT                        3
#define I2C_C1_TX_MASK                           0x10u
#define I2C_C1_TX_SHIFT                          4
#define I2C_C1_MST_MASK                          0x20u
#define I2C_C1_MST_SHIFT                         5
#define I2C_C1_IICIE_MASK                        0x40u
#define I2C_C1_IICIE_SHIFT                       6
#define I2C_C1_IICEN_MASK                        0x80u
#define I2C_C1_IICEN_SHIFT                       7
/* S Bit Fields */
#define I2C_S_RXAK_MASK                          0x1u
#define I2C_S_RXAK_SHIFT                         0
#define I2C_S_IICIF_MASK                         0x2u
#define I2C_S_IICIF_SHIFT                        1
#define I2C_S_SRW_MASK                           0x4u
#define I2C_S_SRW_SHIFT                          2
#define I2C_S_RAM_MASK                           0x8u
#define I2C_S_RAM_SHIFT                          3
#define I2C_S_ARBL_MASK                          0x10u
#define I2C_S_ARBL_SHIFT                         4
#define I2C_S_BUSY_MASK                          0x20u
#define I2C_S_BUSY_SHIFT                         5
#define I2C_S_IAAS_MASK                          0x40u
#define I2C_S_IAAS_SHIFT                         6
#define I2C_S_TCF_MASK                           0x80u
#define I2C_S_TCF_SHIFT                          7
/* D Bit Fields */
#define I2C_D_DATA_MASK                          0xFFu
#define I2C_D_DATA_SHIFT                         0
#define I2C_D_DATA(x)                            (((uint8_t)(((uint8_t)(x))<<I2C_D_DATA_SHIFT))&I2C_D_DATA_MASK)
/* C2 Bit Fields */
#define I2C_C2_AD_MASK                           0x7u
#define I2C_C2_AD_SHIFT                          0
#define I2C_C2_AD(x)                             (((uint8_t)(((uint8_t)(x))<<I2C_C2_AD_SHIFT))&I2C_C2_AD_MASK)
#define I2C_C2_RMEN_MASK                         0x8u
#define I2C_C2_RMEN_SHIFT                        3
#define I2C_C2_SBRC_MASK                         0x10u
#define I2C_C2_SBRC_SHIFT                        4
#define I2C_C2_HDRS_MASK                         0x20u
#define I2C_C2_HDRS_SHIFT                        5
#define I2C_C2_ADEXT_MASK                        0x40u
#define I2C_C2_ADEXT_SHIFT                       6
#define I2C_C2_GCAEN_MASK                        0x80u
#define I2C_C2_GCAEN_SHIFT                       7
/* FLT Bit Fields */
#define I2C_FLT_FLT_MASK                         0x1Fu
#define I2C_FLT_FLT_SHIFT                        0
#define I2C_FLT_FLT(x)                           (((uint8_t)(((uint8_t)(x))<<I2C_FLT_FLT_SHIFT))&I2C_FLT_FLT_MASK)
#define I2C_FLT_STOPIE_MASK                      0x20u
#define I2C_FLT_STOPIE_SHIFT                     5
#define I2C_FLT_STOPF_MASK                       0x40u
#define I2C_FLT_STOPF_SHIFT                      6
#define I2C_FLT_SHEN_MASK                        0x80u
#define I2C_FLT_SHEN_SHIFT                       7
/* RA Bit Fields */
#define I2C_RA_RAD_MASK                          0xFEu
#define I2C_RA_RAD_SHIFT                         1
#define I2C_RA_RAD(x)                            (((uint8_t)(((uint8_t)(x))<<I2C_RA_RAD_SHIFT))&I2C_RA_RAD_MASK)
/* SMB Bit Fields */
#define I2C_SMB_SHTF2IE_MASK                     0x1u
#define I2C_SMB_SHTF2IE_SHIFT                    0
#define I2C_SMB_SHTF2_MASK                       0x2u
#define I2C_SMB_SHTF2_SHIFT                      1
#define I2C_SMB_SHTF1_MASK                       0x4u
#define I2C_SMB_SHTF1_SHIFT                      2
#define I2C_SMB_SLTF_MASK                        0x8u
#define I2C_SMB_SLTF_SHIFT                       3
#define I2C_SMB_TCKSEL_MASK                      0x10u
#define I2C_SMB_TCKSEL_SHIFT                     4
#define I2C_SMB_SIICAEN_MASK                     0x20u
#define I2C_SMB_SIICAEN_SHIFT                    5
#define I2C_SMB_ALERTEN_MASK                     0x40u
#define I2C_SMB_ALERTEN_SHIFT                    6
#define I2C_SMB_FACK_MASK                        0x80u
#define I2C_SMB_FACK_SHIFT                       7
/* A2 Bit Fields */
#define I2C_A2_SAD_MASK                          0xFEu
#define I2C_A2_SAD_SHIFT                         1
#define I2C_A2_SAD(x)                            (((uint8_t)(((uint8_t)(x))<<I2C_A2_SAD_SHIFT))&I2C_A2_SAD_MASK)
/* SLTH Bit Fields */
#define I2C_SLTH_SSLT_MASK                       0xFFu
#define I2C_SLTH_SSLT_SHIFT                      0
#define I2C_SLTH_SSLT(x)                         (((uint8_t)(((uint8_t)(x))<<I2C_SLTH_SSLT_SHIFT))&I2C_SLTH_SSLT_MASK)
/* SLTL Bit Fields */
#define I2C_SLTL_SSLT_MASK                       0xFFu
#define I2C_SLTL_SSLT_SHIFT                      0
#define I2C_SLTL_SSLT(x)                         (((uint8_t)(((uint8_t)(x))<<I2C_SLTL_SSLT_SHIFT))&I2C_SLTL_SSLT_MASK)

/*!
 * @}
 */ /* end of group I2C_Register_Masks */


/* I2C - Peripheral instance base addresses */
/** Peripheral I2C0 base address */
#define I2C0_BASE                                (0x40066000u)
/** Peripheral I2C0 base pointer */
#define I2C0                                     ((I2C_Type *)I2C0_BASE)
/** Peripheral I2C1 base address */
#define I2C1_BASE                                (0x40067000u)
/** Peripheral I2C1 base pointer */
#define I2C1                                     ((I2C_Type *)I2C1_BASE)
/** Array initializer of I2C peripheral base pointers */
#define I2C_BASES                                { I2C0, I2C1 }

/*!
 * @}
 */ /* end of group I2C_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- I2S Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup I2S_Peripheral_Access_Layer I2S Peripheral Access Layer
 * @{
 */

/** I2S - Register Layout Typedef */
typedef struct {
        __IO uint32_t TCSR;                              /**< SAI Transmit Control Register, offset: 0x0 */
        uint8_t RESERVED_0[4];
        __IO uint32_t TCR2;                              /**< SAI Transmit Configuration 2 Register, offset: 0x8 */
        __IO uint32_t TCR3;                              /**< SAI Transmit Configuration 3 Register, offset: 0xC */
        __IO uint32_t TCR4;                              /**< SAI Transmit Configuration 4 Register, offset: 0x10 */
        __IO uint32_t TCR5;                              /**< SAI Transmit Configuration 5 Register, offset: 0x14 */
        uint8_t RESERVED_1[8];
        __O  uint32_t TDR[1];                            /**< SAI Transmit Data Register, array offset: 0x20, array step: 0x4 */
        uint8_t RESERVED_2[60];
        __IO uint32_t TMR;                               /**< SAI Transmit Mask Register, offset: 0x60 */
        uint8_t RESERVED_3[28];
        __IO uint32_t RCSR;                              /**< SAI Receive Control Register, offset: 0x80 */
        uint8_t RESERVED_4[4];
        __IO uint32_t RCR2;                              /**< SAI Receive Configuration 2 Register, offset: 0x88 */
        __IO uint32_t RCR3;                              /**< SAI Receive Configuration 3 Register, offset: 0x8C */
        __IO uint32_t RCR4;                              /**< SAI Receive Configuration 4 Register, offset: 0x90 */
        __IO uint32_t RCR5;                              /**< SAI Receive Configuration 5 Register, offset: 0x94 */
        uint8_t RESERVED_5[8];
        __I  uint32_t RDR[1];                            /**< SAI Receive Data Register, array offset: 0xA0, array step: 0x4 */
        uint8_t RESERVED_6[60];
        __IO uint32_t RMR;                               /**< SAI Receive Mask Register, offset: 0xE0 */
        uint8_t RESERVED_7[28];
        __IO uint32_t MCR;                               /**< SAI MCLK Control Register, offset: 0x100 */
        __IO uint32_t MDR;                               /**< SAI MCLK Divide Register, offset: 0x104 */
} I2S_Type;

/* ----------------------------------------------------------------------------
   -- I2S Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup I2S_Register_Masks I2S Register Masks
 * @{
 */

/* TCSR Bit Fields */
#define I2S_TCSR_FWDE_MASK                       0x2u
#define I2S_TCSR_FWDE_SHIFT                      1
#define I2S_TCSR_FWIE_MASK                       0x200u
#define I2S_TCSR_FWIE_SHIFT                      9
#define I2S_TCSR_FEIE_MASK                       0x400u
#define I2S_TCSR_FEIE_SHIFT                      10
#define I2S_TCSR_SEIE_MASK                       0x800u
#define I2S_TCSR_SEIE_SHIFT                      11
#define I2S_TCSR_WSIE_MASK                       0x1000u
#define I2S_TCSR_WSIE_SHIFT                      12
#define I2S_TCSR_FWF_MASK                        0x20000u
#define I2S_TCSR_FWF_SHIFT                       17
#define I2S_TCSR_FEF_MASK                        0x40000u
#define I2S_TCSR_FEF_SHIFT                       18
#define I2S_TCSR_SEF_MASK                        0x80000u
#define I2S_TCSR_SEF_SHIFT                       19
#define I2S_TCSR_WSF_MASK                        0x100000u
#define I2S_TCSR_WSF_SHIFT                       20
#define I2S_TCSR_SR_MASK                         0x1000000u
#define I2S_TCSR_SR_SHIFT                        24
#define I2S_TCSR_FR_MASK                         0x2000000u
#define I2S_TCSR_FR_SHIFT                        25
#define I2S_TCSR_BCE_MASK                        0x10000000u
#define I2S_TCSR_BCE_SHIFT                       28
#define I2S_TCSR_DBGE_MASK                       0x20000000u
#define I2S_TCSR_DBGE_SHIFT                      29
#define I2S_TCSR_STOPE_MASK                      0x40000000u
#define I2S_TCSR_STOPE_SHIFT                     30
#define I2S_TCSR_TE_MASK                         0x80000000u
#define I2S_TCSR_TE_SHIFT                        31
/* TCR2 Bit Fields */
#define I2S_TCR2_DIV_MASK                        0xFFu
#define I2S_TCR2_DIV_SHIFT                       0
#define I2S_TCR2_DIV(x)                          (((uint32_t)(((uint32_t)(x))<<I2S_TCR2_DIV_SHIFT))&I2S_TCR2_DIV_MASK)
#define I2S_TCR2_BCD_MASK                        0x1000000u
#define I2S_TCR2_BCD_SHIFT                       24
#define I2S_TCR2_BCP_MASK                        0x2000000u
#define I2S_TCR2_BCP_SHIFT                       25
#define I2S_TCR2_CLKMODE_MASK                    0xC000000u
#define I2S_TCR2_CLKMODE_SHIFT                   26
#define I2S_TCR2_CLKMODE(x)                      (((uint32_t)(((uint32_t)(x))<<I2S_TCR2_CLKMODE_SHIFT))&I2S_TCR2_CLKMODE_MASK)
/* TCR3 Bit Fields */
#define I2S_TCR3_WDFL_MASK                       0x1u
#define I2S_TCR3_WDFL_SHIFT                      0
#define I2S_TCR3_TCE_MASK                        0x10000u
#define I2S_TCR3_TCE_SHIFT                       16
/* TCR4 Bit Fields */
#define I2S_TCR4_FSD_MASK                        0x1u
#define I2S_TCR4_FSD_SHIFT                       0
#define I2S_TCR4_FSP_MASK                        0x2u
#define I2S_TCR4_FSP_SHIFT                       1
#define I2S_TCR4_FSE_MASK                        0x8u
#define I2S_TCR4_FSE_SHIFT                       3
#define I2S_TCR4_MF_MASK                         0x10u
#define I2S_TCR4_MF_SHIFT                        4
#define I2S_TCR4_SYWD_MASK                       0x1F00u
#define I2S_TCR4_SYWD_SHIFT                      8
#define I2S_TCR4_SYWD(x)                         (((uint32_t)(((uint32_t)(x))<<I2S_TCR4_SYWD_SHIFT))&I2S_TCR4_SYWD_MASK)
#define I2S_TCR4_FRSZ_MASK                       0x10000u
#define I2S_TCR4_FRSZ_SHIFT                      16
/* TCR5 Bit Fields */
#define I2S_TCR5_FBT_MASK                        0x1F00u
#define I2S_TCR5_FBT_SHIFT                       8
#define I2S_TCR5_FBT(x)                          (((uint32_t)(((uint32_t)(x))<<I2S_TCR5_FBT_SHIFT))&I2S_TCR5_FBT_MASK)
#define I2S_TCR5_W0W_MASK                        0x1F0000u
#define I2S_TCR5_W0W_SHIFT                       16
#define I2S_TCR5_W0W(x)                          (((uint32_t)(((uint32_t)(x))<<I2S_TCR5_W0W_SHIFT))&I2S_TCR5_W0W_MASK)
#define I2S_TCR5_WNW_MASK                        0x1F000000u
#define I2S_TCR5_WNW_SHIFT                       24
#define I2S_TCR5_WNW(x)                          (((uint32_t)(((uint32_t)(x))<<I2S_TCR5_WNW_SHIFT))&I2S_TCR5_WNW_MASK)
/* TDR Bit Fields */
#define I2S_TDR_TDR_MASK                         0xFFFFFFFFu
#define I2S_TDR_TDR_SHIFT                        0
#define I2S_TDR_TDR(x)                           (((uint32_t)(((uint32_t)(x))<<I2S_TDR_TDR_SHIFT))&I2S_TDR_TDR_MASK)
/* TMR Bit Fields */
#define I2S_TMR_TWM_MASK                         0x3u
#define I2S_TMR_TWM_SHIFT                        0
#define I2S_TMR_TWM(x)                           (((uint32_t)(((uint32_t)(x))<<I2S_TMR_TWM_SHIFT))&I2S_TMR_TWM_MASK)
/* RCSR Bit Fields */
#define I2S_RCSR_FWDE_MASK                       0x2u
#define I2S_RCSR_FWDE_SHIFT                      1
#define I2S_RCSR_FWIE_MASK                       0x200u
#define I2S_RCSR_FWIE_SHIFT                      9
#define I2S_RCSR_FEIE_MASK                       0x400u
#define I2S_RCSR_FEIE_SHIFT                      10
#define I2S_RCSR_SEIE_MASK                       0x800u
#define I2S_RCSR_SEIE_SHIFT                      11
#define I2S_RCSR_WSIE_MASK                       0x1000u
#define I2S_RCSR_WSIE_SHIFT                      12
#define I2S_RCSR_FWF_MASK                        0x20000u
#define I2S_RCSR_FWF_SHIFT                       17
#define I2S_RCSR_FEF_MASK                        0x40000u
#define I2S_RCSR_FEF_SHIFT                       18
#define I2S_RCSR_SEF_MASK                        0x80000u
#define I2S_RCSR_SEF_SHIFT                       19
#define I2S_RCSR_WSF_MASK                        0x100000u
#define I2S_RCSR_WSF_SHIFT                       20
#define I2S_RCSR_SR_MASK                         0x1000000u
#define I2S_RCSR_SR_SHIFT                        24
#define I2S_RCSR_FR_MASK                         0x2000000u
#define I2S_RCSR_FR_SHIFT                        25
#define I2S_RCSR_BCE_MASK                        0x10000000u
#define I2S_RCSR_BCE_SHIFT                       28
#define I2S_RCSR_DBGE_MASK                       0x20000000u
#define I2S_RCSR_DBGE_SHIFT                      29
#define I2S_RCSR_STOPE_MASK                      0x40000000u
#define I2S_RCSR_STOPE_SHIFT                     30
#define I2S_RCSR_RE_MASK                         0x80000000u
#define I2S_RCSR_RE_SHIFT                        31
/* RCR2 Bit Fields */
#define I2S_RCR2_DIV_MASK                        0xFFu
#define I2S_RCR2_DIV_SHIFT                       0
#define I2S_RCR2_DIV(x)                          (((uint32_t)(((uint32_t)(x))<<I2S_RCR2_DIV_SHIFT))&I2S_RCR2_DIV_MASK)
#define I2S_RCR2_BCD_MASK                        0x1000000u
#define I2S_RCR2_BCD_SHIFT                       24
#define I2S_RCR2_BCP_MASK                        0x2000000u
#define I2S_RCR2_BCP_SHIFT                       25
#define I2S_RCR2_CLKMODE_MASK                    0xC000000u
#define I2S_RCR2_CLKMODE_SHIFT                   26
#define I2S_RCR2_CLKMODE(x)                      (((uint32_t)(((uint32_t)(x))<<I2S_RCR2_CLKMODE_SHIFT))&I2S_RCR2_CLKMODE_MASK)
/* RCR3 Bit Fields */
#define I2S_RCR3_WDFL_MASK                       0x1u
#define I2S_RCR3_WDFL_SHIFT                      0
#define I2S_RCR3_RCE_MASK                        0x10000u
#define I2S_RCR3_RCE_SHIFT                       16
/* RCR4 Bit Fields */
#define I2S_RCR4_FSD_MASK                        0x1u
#define I2S_RCR4_FSD_SHIFT                       0
#define I2S_RCR4_FSP_MASK                        0x2u
#define I2S_RCR4_FSP_SHIFT                       1
#define I2S_RCR4_FSE_MASK                        0x8u
#define I2S_RCR4_FSE_SHIFT                       3
#define I2S_RCR4_MF_MASK                         0x10u
#define I2S_RCR4_MF_SHIFT                        4
#define I2S_RCR4_SYWD_MASK                       0x1F00u
#define I2S_RCR4_SYWD_SHIFT                      8
#define I2S_RCR4_SYWD(x)                         (((uint32_t)(((uint32_t)(x))<<I2S_RCR4_SYWD_SHIFT))&I2S_RCR4_SYWD_MASK)
#define I2S_RCR4_FRSZ_MASK                       0x10000u
#define I2S_RCR4_FRSZ_SHIFT                      16
/* RCR5 Bit Fields */
#define I2S_RCR5_FBT_MASK                        0x1F00u
#define I2S_RCR5_FBT_SHIFT                       8
#define I2S_RCR5_FBT(x)                          (((uint32_t)(((uint32_t)(x))<<I2S_RCR5_FBT_SHIFT))&I2S_RCR5_FBT_MASK)
#define I2S_RCR5_W0W_MASK                        0x1F0000u
#define I2S_RCR5_W0W_SHIFT                       16
#define I2S_RCR5_W0W(x)                          (((uint32_t)(((uint32_t)(x))<<I2S_RCR5_W0W_SHIFT))&I2S_RCR5_W0W_MASK)
#define I2S_RCR5_WNW_MASK                        0x1F000000u
#define I2S_RCR5_WNW_SHIFT                       24
#define I2S_RCR5_WNW(x)                          (((uint32_t)(((uint32_t)(x))<<I2S_RCR5_WNW_SHIFT))&I2S_RCR5_WNW_MASK)
/* RDR Bit Fields */
#define I2S_RDR_RDR_MASK                         0xFFFFFFFFu
#define I2S_RDR_RDR_SHIFT                        0
#define I2S_RDR_RDR(x)                           (((uint32_t)(((uint32_t)(x))<<I2S_RDR_RDR_SHIFT))&I2S_RDR_RDR_MASK)
/* RMR Bit Fields */
#define I2S_RMR_RWM_MASK                         0x3u
#define I2S_RMR_RWM_SHIFT                        0
#define I2S_RMR_RWM(x)                           (((uint32_t)(((uint32_t)(x))<<I2S_RMR_RWM_SHIFT))&I2S_RMR_RWM_MASK)
/* MCR Bit Fields */
#define I2S_MCR_MICS_MASK                        0x3000000u
#define I2S_MCR_MICS_SHIFT                       24
#define I2S_MCR_MICS(x)                          (((uint32_t)(((uint32_t)(x))<<I2S_MCR_MICS_SHIFT))&I2S_MCR_MICS_MASK)
#define I2S_MCR_MOE_MASK                         0x40000000u
#define I2S_MCR_MOE_SHIFT                        30
#define I2S_MCR_DUF_MASK                         0x80000000u
#define I2S_MCR_DUF_SHIFT                        31
/* MDR Bit Fields */
#define I2S_MDR_DIVIDE_MASK                      0xFFFu
#define I2S_MDR_DIVIDE_SHIFT                     0
#define I2S_MDR_DIVIDE(x)                        (((uint32_t)(((uint32_t)(x))<<I2S_MDR_DIVIDE_SHIFT))&I2S_MDR_DIVIDE_MASK)
#define I2S_MDR_FRACT_MASK                       0xFF000u
#define I2S_MDR_FRACT_SHIFT                      12
#define I2S_MDR_FRACT(x)                         (((uint32_t)(((uint32_t)(x))<<I2S_MDR_FRACT_SHIFT))&I2S_MDR_FRACT_MASK)

/*!
 * @}
 */ /* end of group I2S_Register_Masks */


/* I2S - Peripheral instance base addresses */
/** Peripheral I2S0 base address */
#define I2S0_BASE                                (0x4002F000u)
/** Peripheral I2S0 base pointer */
#define I2S0                                     ((I2S_Type *)I2S0_BASE)
/** Array initializer of I2S peripheral base pointers */
#define I2S_BASES                                { I2S0 }

/*!
 * @}
 */ /* end of group I2S_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- LCD Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup LCD_Peripheral_Access_Layer LCD Peripheral Access Layer
 * @{
 */

/** LCD - Register Layout Typedef */
typedef struct {
        __IO uint32_t GCR;                               /**< LCD General Control Register, offset: 0x0 */
        __IO uint32_t AR;                                /**< LCD Auxiliary Register, offset: 0x4 */
        __IO uint32_t FDCR;                              /**< LCD Fault Detect Control Register, offset: 0x8 */
        __IO uint32_t FDSR;                              /**< LCD Fault Detect Status Register, offset: 0xC */
        __IO uint32_t PEN[2];                            /**< LCD Pin Enable register, array offset: 0x10, array step: 0x4 */
        __IO uint32_t BPEN[2];                           /**< LCD Back Plane Enable register, array offset: 0x18, array step: 0x4 */
        union {                                          /* offset: 0x20 */
                __IO uint32_t WF[16];                            /**< LCD Waveform register, array offset: 0x20, array step: 0x4 */
                __IO uint8_t WF8B[64];                           /**< LCD Waveform Register 0...LCD Waveform Register 63., array offset: 0x20, array step: 0x1 */
        };
} LCD_Type;

/* ----------------------------------------------------------------------------
   -- LCD Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup LCD_Register_Masks LCD Register Masks
 * @{
 */

/* GCR Bit Fields */
#define LCD_GCR_DUTY_MASK                        0x7u
#define LCD_GCR_DUTY_SHIFT                       0
#define LCD_GCR_DUTY(x)                          (((uint32_t)(((uint32_t)(x))<<LCD_GCR_DUTY_SHIFT))&LCD_GCR_DUTY_MASK)
#define LCD_GCR_LCLK_MASK                        0x38u
#define LCD_GCR_LCLK_SHIFT                       3
#define LCD_GCR_LCLK(x)                          (((uint32_t)(((uint32_t)(x))<<LCD_GCR_LCLK_SHIFT))&LCD_GCR_LCLK_MASK)
#define LCD_GCR_SOURCE_MASK                      0x40u
#define LCD_GCR_SOURCE_SHIFT                     6
#define LCD_GCR_LCDEN_MASK                       0x80u
#define LCD_GCR_LCDEN_SHIFT                      7
#define LCD_GCR_LCDSTP_MASK                      0x100u
#define LCD_GCR_LCDSTP_SHIFT                     8
#define LCD_GCR_LCDDOZE_MASK                     0x200u
#define LCD_GCR_LCDDOZE_SHIFT                    9
#define LCD_GCR_FFR_MASK                         0x400u
#define LCD_GCR_FFR_SHIFT                        10
#define LCD_GCR_ALTSOURCE_MASK                   0x800u
#define LCD_GCR_ALTSOURCE_SHIFT                  11
#define LCD_GCR_ALTDIV_MASK                      0x3000u
#define LCD_GCR_ALTDIV_SHIFT                     12
#define LCD_GCR_ALTDIV(x)                        (((uint32_t)(((uint32_t)(x))<<LCD_GCR_ALTDIV_SHIFT))&LCD_GCR_ALTDIV_MASK)
#define LCD_GCR_FDCIEN_MASK                      0x4000u
#define LCD_GCR_FDCIEN_SHIFT                     14
#define LCD_GCR_PADSAFE_MASK                     0x8000u
#define LCD_GCR_PADSAFE_SHIFT                    15
#define LCD_GCR_VSUPPLY_MASK                     0x20000u
#define LCD_GCR_VSUPPLY_SHIFT                    17
#define LCD_GCR_LADJ_MASK                        0x300000u
#define LCD_GCR_LADJ_SHIFT                       20
#define LCD_GCR_LADJ(x)                          (((uint32_t)(((uint32_t)(x))<<LCD_GCR_LADJ_SHIFT))&LCD_GCR_LADJ_MASK)
#define LCD_GCR_CPSEL_MASK                       0x800000u
#define LCD_GCR_CPSEL_SHIFT                      23
#define LCD_GCR_RVTRIM_MASK                      0xF000000u
#define LCD_GCR_RVTRIM_SHIFT                     24
#define LCD_GCR_RVTRIM(x)                        (((uint32_t)(((uint32_t)(x))<<LCD_GCR_RVTRIM_SHIFT))&LCD_GCR_RVTRIM_MASK)
#define LCD_GCR_RVEN_MASK                        0x80000000u
#define LCD_GCR_RVEN_SHIFT                       31
/* AR Bit Fields */
#define LCD_AR_BRATE_MASK                        0x7u
#define LCD_AR_BRATE_SHIFT                       0
#define LCD_AR_BRATE(x)                          (((uint32_t)(((uint32_t)(x))<<LCD_AR_BRATE_SHIFT))&LCD_AR_BRATE_MASK)
#define LCD_AR_BMODE_MASK                        0x8u
#define LCD_AR_BMODE_SHIFT                       3
#define LCD_AR_BLANK_MASK                        0x20u
#define LCD_AR_BLANK_SHIFT                       5
#define LCD_AR_ALT_MASK                          0x40u
#define LCD_AR_ALT_SHIFT                         6
#define LCD_AR_BLINK_MASK                        0x80u
#define LCD_AR_BLINK_SHIFT                       7
/* FDCR Bit Fields */
#define LCD_FDCR_FDPINID_MASK                    0x3Fu
#define LCD_FDCR_FDPINID_SHIFT                   0
#define LCD_FDCR_FDPINID(x)                      (((uint32_t)(((uint32_t)(x))<<LCD_FDCR_FDPINID_SHIFT))&LCD_FDCR_FDPINID_MASK)
#define LCD_FDCR_FDBPEN_MASK                     0x40u
#define LCD_FDCR_FDBPEN_SHIFT                    6
#define LCD_FDCR_FDEN_MASK                       0x80u
#define LCD_FDCR_FDEN_SHIFT                      7
#define LCD_FDCR_FDSWW_MASK                      0xE00u
#define LCD_FDCR_FDSWW_SHIFT                     9
#define LCD_FDCR_FDSWW(x)                        (((uint32_t)(((uint32_t)(x))<<LCD_FDCR_FDSWW_SHIFT))&LCD_FDCR_FDSWW_MASK)
#define LCD_FDCR_FDPRS_MASK                      0x7000u
#define LCD_FDCR_FDPRS_SHIFT                     12
#define LCD_FDCR_FDPRS(x)                        (((uint32_t)(((uint32_t)(x))<<LCD_FDCR_FDPRS_SHIFT))&LCD_FDCR_FDPRS_MASK)
/* FDSR Bit Fields */
#define LCD_FDSR_FDCNT_MASK                      0xFFu
#define LCD_FDSR_FDCNT_SHIFT                     0
#define LCD_FDSR_FDCNT(x)                        (((uint32_t)(((uint32_t)(x))<<LCD_FDSR_FDCNT_SHIFT))&LCD_FDSR_FDCNT_MASK)
#define LCD_FDSR_FDCF_MASK                       0x8000u
#define LCD_FDSR_FDCF_SHIFT                      15
/* PEN Bit Fields */
#define LCD_PEN_PEN_MASK                         0xFFFFFFFFu
#define LCD_PEN_PEN_SHIFT                        0
#define LCD_PEN_PEN(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_PEN_PEN_SHIFT))&LCD_PEN_PEN_MASK)
/* BPEN Bit Fields */
#define LCD_BPEN_BPEN_MASK                       0xFFFFFFFFu
#define LCD_BPEN_BPEN_SHIFT                      0
#define LCD_BPEN_BPEN(x)                         (((uint32_t)(((uint32_t)(x))<<LCD_BPEN_BPEN_SHIFT))&LCD_BPEN_BPEN_MASK)
/* WF Bit Fields */
#define LCD_WF_WF0_MASK                          0xFFu
#define LCD_WF_WF0_SHIFT                         0
#define LCD_WF_WF0(x)                            (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF0_SHIFT))&LCD_WF_WF0_MASK)
#define LCD_WF_WF60_MASK                         0xFFu
#define LCD_WF_WF60_SHIFT                        0
#define LCD_WF_WF60(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF60_SHIFT))&LCD_WF_WF60_MASK)
#define LCD_WF_WF56_MASK                         0xFFu
#define LCD_WF_WF56_SHIFT                        0
#define LCD_WF_WF56(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF56_SHIFT))&LCD_WF_WF56_MASK)
#define LCD_WF_WF52_MASK                         0xFFu
#define LCD_WF_WF52_SHIFT                        0
#define LCD_WF_WF52(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF52_SHIFT))&LCD_WF_WF52_MASK)
#define LCD_WF_WF4_MASK                          0xFFu
#define LCD_WF_WF4_SHIFT                         0
#define LCD_WF_WF4(x)                            (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF4_SHIFT))&LCD_WF_WF4_MASK)
#define LCD_WF_WF48_MASK                         0xFFu
#define LCD_WF_WF48_SHIFT                        0
#define LCD_WF_WF48(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF48_SHIFT))&LCD_WF_WF48_MASK)
#define LCD_WF_WF44_MASK                         0xFFu
#define LCD_WF_WF44_SHIFT                        0
#define LCD_WF_WF44(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF44_SHIFT))&LCD_WF_WF44_MASK)
#define LCD_WF_WF40_MASK                         0xFFu
#define LCD_WF_WF40_SHIFT                        0
#define LCD_WF_WF40(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF40_SHIFT))&LCD_WF_WF40_MASK)
#define LCD_WF_WF8_MASK                          0xFFu
#define LCD_WF_WF8_SHIFT                         0
#define LCD_WF_WF8(x)                            (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF8_SHIFT))&LCD_WF_WF8_MASK)
#define LCD_WF_WF36_MASK                         0xFFu
#define LCD_WF_WF36_SHIFT                        0
#define LCD_WF_WF36(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF36_SHIFT))&LCD_WF_WF36_MASK)
#define LCD_WF_WF32_MASK                         0xFFu
#define LCD_WF_WF32_SHIFT                        0
#define LCD_WF_WF32(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF32_SHIFT))&LCD_WF_WF32_MASK)
#define LCD_WF_WF28_MASK                         0xFFu
#define LCD_WF_WF28_SHIFT                        0
#define LCD_WF_WF28(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF28_SHIFT))&LCD_WF_WF28_MASK)
#define LCD_WF_WF12_MASK                         0xFFu
#define LCD_WF_WF12_SHIFT                        0
#define LCD_WF_WF12(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF12_SHIFT))&LCD_WF_WF12_MASK)
#define LCD_WF_WF24_MASK                         0xFFu
#define LCD_WF_WF24_SHIFT                        0
#define LCD_WF_WF24(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF24_SHIFT))&LCD_WF_WF24_MASK)
#define LCD_WF_WF20_MASK                         0xFFu
#define LCD_WF_WF20_SHIFT                        0
#define LCD_WF_WF20(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF20_SHIFT))&LCD_WF_WF20_MASK)
#define LCD_WF_WF16_MASK                         0xFFu
#define LCD_WF_WF16_SHIFT                        0
#define LCD_WF_WF16(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF16_SHIFT))&LCD_WF_WF16_MASK)
#define LCD_WF_WF5_MASK                          0xFF00u
#define LCD_WF_WF5_SHIFT                         8
#define LCD_WF_WF5(x)                            (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF5_SHIFT))&LCD_WF_WF5_MASK)
#define LCD_WF_WF49_MASK                         0xFF00u
#define LCD_WF_WF49_SHIFT                        8
#define LCD_WF_WF49(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF49_SHIFT))&LCD_WF_WF49_MASK)
#define LCD_WF_WF45_MASK                         0xFF00u
#define LCD_WF_WF45_SHIFT                        8
#define LCD_WF_WF45(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF45_SHIFT))&LCD_WF_WF45_MASK)
#define LCD_WF_WF61_MASK                         0xFF00u
#define LCD_WF_WF61_SHIFT                        8
#define LCD_WF_WF61(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF61_SHIFT))&LCD_WF_WF61_MASK)
#define LCD_WF_WF25_MASK                         0xFF00u
#define LCD_WF_WF25_SHIFT                        8
#define LCD_WF_WF25(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF25_SHIFT))&LCD_WF_WF25_MASK)
#define LCD_WF_WF17_MASK                         0xFF00u
#define LCD_WF_WF17_SHIFT                        8
#define LCD_WF_WF17(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF17_SHIFT))&LCD_WF_WF17_MASK)
#define LCD_WF_WF41_MASK                         0xFF00u
#define LCD_WF_WF41_SHIFT                        8
#define LCD_WF_WF41(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF41_SHIFT))&LCD_WF_WF41_MASK)
#define LCD_WF_WF13_MASK                         0xFF00u
#define LCD_WF_WF13_SHIFT                        8
#define LCD_WF_WF13(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF13_SHIFT))&LCD_WF_WF13_MASK)
#define LCD_WF_WF57_MASK                         0xFF00u
#define LCD_WF_WF57_SHIFT                        8
#define LCD_WF_WF57(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF57_SHIFT))&LCD_WF_WF57_MASK)
#define LCD_WF_WF53_MASK                         0xFF00u
#define LCD_WF_WF53_SHIFT                        8
#define LCD_WF_WF53(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF53_SHIFT))&LCD_WF_WF53_MASK)
#define LCD_WF_WF37_MASK                         0xFF00u
#define LCD_WF_WF37_SHIFT                        8
#define LCD_WF_WF37(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF37_SHIFT))&LCD_WF_WF37_MASK)
#define LCD_WF_WF9_MASK                          0xFF00u
#define LCD_WF_WF9_SHIFT                         8
#define LCD_WF_WF9(x)                            (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF9_SHIFT))&LCD_WF_WF9_MASK)
#define LCD_WF_WF1_MASK                          0xFF00u
#define LCD_WF_WF1_SHIFT                         8
#define LCD_WF_WF1(x)                            (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF1_SHIFT))&LCD_WF_WF1_MASK)
#define LCD_WF_WF29_MASK                         0xFF00u
#define LCD_WF_WF29_SHIFT                        8
#define LCD_WF_WF29(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF29_SHIFT))&LCD_WF_WF29_MASK)
#define LCD_WF_WF33_MASK                         0xFF00u
#define LCD_WF_WF33_SHIFT                        8
#define LCD_WF_WF33(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF33_SHIFT))&LCD_WF_WF33_MASK)
#define LCD_WF_WF21_MASK                         0xFF00u
#define LCD_WF_WF21_SHIFT                        8
#define LCD_WF_WF21(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF21_SHIFT))&LCD_WF_WF21_MASK)
#define LCD_WF_WF26_MASK                         0xFF0000u
#define LCD_WF_WF26_SHIFT                        16
#define LCD_WF_WF26(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF26_SHIFT))&LCD_WF_WF26_MASK)
#define LCD_WF_WF46_MASK                         0xFF0000u
#define LCD_WF_WF46_SHIFT                        16
#define LCD_WF_WF46(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF46_SHIFT))&LCD_WF_WF46_MASK)
#define LCD_WF_WF6_MASK                          0xFF0000u
#define LCD_WF_WF6_SHIFT                         16
#define LCD_WF_WF6(x)                            (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF6_SHIFT))&LCD_WF_WF6_MASK)
#define LCD_WF_WF42_MASK                         0xFF0000u
#define LCD_WF_WF42_SHIFT                        16
#define LCD_WF_WF42(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF42_SHIFT))&LCD_WF_WF42_MASK)
#define LCD_WF_WF18_MASK                         0xFF0000u
#define LCD_WF_WF18_SHIFT                        16
#define LCD_WF_WF18(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF18_SHIFT))&LCD_WF_WF18_MASK)
#define LCD_WF_WF38_MASK                         0xFF0000u
#define LCD_WF_WF38_SHIFT                        16
#define LCD_WF_WF38(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF38_SHIFT))&LCD_WF_WF38_MASK)
#define LCD_WF_WF22_MASK                         0xFF0000u
#define LCD_WF_WF22_SHIFT                        16
#define LCD_WF_WF22(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF22_SHIFT))&LCD_WF_WF22_MASK)
#define LCD_WF_WF34_MASK                         0xFF0000u
#define LCD_WF_WF34_SHIFT                        16
#define LCD_WF_WF34(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF34_SHIFT))&LCD_WF_WF34_MASK)
#define LCD_WF_WF50_MASK                         0xFF0000u
#define LCD_WF_WF50_SHIFT                        16
#define LCD_WF_WF50(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF50_SHIFT))&LCD_WF_WF50_MASK)
#define LCD_WF_WF14_MASK                         0xFF0000u
#define LCD_WF_WF14_SHIFT                        16
#define LCD_WF_WF14(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF14_SHIFT))&LCD_WF_WF14_MASK)
#define LCD_WF_WF54_MASK                         0xFF0000u
#define LCD_WF_WF54_SHIFT                        16
#define LCD_WF_WF54(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF54_SHIFT))&LCD_WF_WF54_MASK)
#define LCD_WF_WF2_MASK                          0xFF0000u
#define LCD_WF_WF2_SHIFT                         16
#define LCD_WF_WF2(x)                            (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF2_SHIFT))&LCD_WF_WF2_MASK)
#define LCD_WF_WF58_MASK                         0xFF0000u
#define LCD_WF_WF58_SHIFT                        16
#define LCD_WF_WF58(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF58_SHIFT))&LCD_WF_WF58_MASK)
#define LCD_WF_WF30_MASK                         0xFF0000u
#define LCD_WF_WF30_SHIFT                        16
#define LCD_WF_WF30(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF30_SHIFT))&LCD_WF_WF30_MASK)
#define LCD_WF_WF62_MASK                         0xFF0000u
#define LCD_WF_WF62_SHIFT                        16
#define LCD_WF_WF62(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF62_SHIFT))&LCD_WF_WF62_MASK)
#define LCD_WF_WF10_MASK                         0xFF0000u
#define LCD_WF_WF10_SHIFT                        16
#define LCD_WF_WF10(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF10_SHIFT))&LCD_WF_WF10_MASK)
#define LCD_WF_WF63_MASK                         0xFF000000u
#define LCD_WF_WF63_SHIFT                        24
#define LCD_WF_WF63(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF63_SHIFT))&LCD_WF_WF63_MASK)
#define LCD_WF_WF59_MASK                         0xFF000000u
#define LCD_WF_WF59_SHIFT                        24
#define LCD_WF_WF59(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF59_SHIFT))&LCD_WF_WF59_MASK)
#define LCD_WF_WF55_MASK                         0xFF000000u
#define LCD_WF_WF55_SHIFT                        24
#define LCD_WF_WF55(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF55_SHIFT))&LCD_WF_WF55_MASK)
#define LCD_WF_WF3_MASK                          0xFF000000u
#define LCD_WF_WF3_SHIFT                         24
#define LCD_WF_WF3(x)                            (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF3_SHIFT))&LCD_WF_WF3_MASK)
#define LCD_WF_WF51_MASK                         0xFF000000u
#define LCD_WF_WF51_SHIFT                        24
#define LCD_WF_WF51(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF51_SHIFT))&LCD_WF_WF51_MASK)
#define LCD_WF_WF47_MASK                         0xFF000000u
#define LCD_WF_WF47_SHIFT                        24
#define LCD_WF_WF47(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF47_SHIFT))&LCD_WF_WF47_MASK)
#define LCD_WF_WF43_MASK                         0xFF000000u
#define LCD_WF_WF43_SHIFT                        24
#define LCD_WF_WF43(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF43_SHIFT))&LCD_WF_WF43_MASK)
#define LCD_WF_WF7_MASK                          0xFF000000u
#define LCD_WF_WF7_SHIFT                         24
#define LCD_WF_WF7(x)                            (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF7_SHIFT))&LCD_WF_WF7_MASK)
#define LCD_WF_WF39_MASK                         0xFF000000u
#define LCD_WF_WF39_SHIFT                        24
#define LCD_WF_WF39(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF39_SHIFT))&LCD_WF_WF39_MASK)
#define LCD_WF_WF35_MASK                         0xFF000000u
#define LCD_WF_WF35_SHIFT                        24
#define LCD_WF_WF35(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF35_SHIFT))&LCD_WF_WF35_MASK)
#define LCD_WF_WF31_MASK                         0xFF000000u
#define LCD_WF_WF31_SHIFT                        24
#define LCD_WF_WF31(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF31_SHIFT))&LCD_WF_WF31_MASK)
#define LCD_WF_WF11_MASK                         0xFF000000u
#define LCD_WF_WF11_SHIFT                        24
#define LCD_WF_WF11(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF11_SHIFT))&LCD_WF_WF11_MASK)
#define LCD_WF_WF27_MASK                         0xFF000000u
#define LCD_WF_WF27_SHIFT                        24
#define LCD_WF_WF27(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF27_SHIFT))&LCD_WF_WF27_MASK)
#define LCD_WF_WF23_MASK                         0xFF000000u
#define LCD_WF_WF23_SHIFT                        24
#define LCD_WF_WF23(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF23_SHIFT))&LCD_WF_WF23_MASK)
#define LCD_WF_WF19_MASK                         0xFF000000u
#define LCD_WF_WF19_SHIFT                        24
#define LCD_WF_WF19(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF19_SHIFT))&LCD_WF_WF19_MASK)
#define LCD_WF_WF15_MASK                         0xFF000000u
#define LCD_WF_WF15_SHIFT                        24
#define LCD_WF_WF15(x)                           (((uint32_t)(((uint32_t)(x))<<LCD_WF_WF15_SHIFT))&LCD_WF_WF15_MASK)
/* WF8B Bit Fields */
#define LCD_WF8B_BPALCD0_MASK                    0x1u
#define LCD_WF8B_BPALCD0_SHIFT                   0
#define LCD_WF8B_BPALCD63_MASK                   0x1u
#define LCD_WF8B_BPALCD63_SHIFT                  0
#define LCD_WF8B_BPALCD62_MASK                   0x1u
#define LCD_WF8B_BPALCD62_SHIFT                  0
#define LCD_WF8B_BPALCD61_MASK                   0x1u
#define LCD_WF8B_BPALCD61_SHIFT                  0
#define LCD_WF8B_BPALCD60_MASK                   0x1u
#define LCD_WF8B_BPALCD60_SHIFT                  0
#define LCD_WF8B_BPALCD59_MASK                   0x1u
#define LCD_WF8B_BPALCD59_SHIFT                  0
#define LCD_WF8B_BPALCD58_MASK                   0x1u
#define LCD_WF8B_BPALCD58_SHIFT                  0
#define LCD_WF8B_BPALCD57_MASK                   0x1u
#define LCD_WF8B_BPALCD57_SHIFT                  0
#define LCD_WF8B_BPALCD1_MASK                    0x1u
#define LCD_WF8B_BPALCD1_SHIFT                   0
#define LCD_WF8B_BPALCD56_MASK                   0x1u
#define LCD_WF8B_BPALCD56_SHIFT                  0
#define LCD_WF8B_BPALCD55_MASK                   0x1u
#define LCD_WF8B_BPALCD55_SHIFT                  0
#define LCD_WF8B_BPALCD54_MASK                   0x1u
#define LCD_WF8B_BPALCD54_SHIFT                  0
#define LCD_WF8B_BPALCD53_MASK                   0x1u
#define LCD_WF8B_BPALCD53_SHIFT                  0
#define LCD_WF8B_BPALCD52_MASK                   0x1u
#define LCD_WF8B_BPALCD52_SHIFT                  0
#define LCD_WF8B_BPALCD51_MASK                   0x1u
#define LCD_WF8B_BPALCD51_SHIFT                  0
#define LCD_WF8B_BPALCD50_MASK                   0x1u
#define LCD_WF8B_BPALCD50_SHIFT                  0
#define LCD_WF8B_BPALCD2_MASK                    0x1u
#define LCD_WF8B_BPALCD2_SHIFT                   0
#define LCD_WF8B_BPALCD49_MASK                   0x1u
#define LCD_WF8B_BPALCD49_SHIFT                  0
#define LCD_WF8B_BPALCD48_MASK                   0x1u
#define LCD_WF8B_BPALCD48_SHIFT                  0
#define LCD_WF8B_BPALCD47_MASK                   0x1u
#define LCD_WF8B_BPALCD47_SHIFT                  0
#define LCD_WF8B_BPALCD46_MASK                   0x1u
#define LCD_WF8B_BPALCD46_SHIFT                  0
#define LCD_WF8B_BPALCD45_MASK                   0x1u
#define LCD_WF8B_BPALCD45_SHIFT                  0
#define LCD_WF8B_BPALCD44_MASK                   0x1u
#define LCD_WF8B_BPALCD44_SHIFT                  0
#define LCD_WF8B_BPALCD43_MASK                   0x1u
#define LCD_WF8B_BPALCD43_SHIFT                  0
#define LCD_WF8B_BPALCD3_MASK                    0x1u
#define LCD_WF8B_BPALCD3_SHIFT                   0
#define LCD_WF8B_BPALCD42_MASK                   0x1u
#define LCD_WF8B_BPALCD42_SHIFT                  0
#define LCD_WF8B_BPALCD41_MASK                   0x1u
#define LCD_WF8B_BPALCD41_SHIFT                  0
#define LCD_WF8B_BPALCD40_MASK                   0x1u
#define LCD_WF8B_BPALCD40_SHIFT                  0
#define LCD_WF8B_BPALCD39_MASK                   0x1u
#define LCD_WF8B_BPALCD39_SHIFT                  0
#define LCD_WF8B_BPALCD38_MASK                   0x1u
#define LCD_WF8B_BPALCD38_SHIFT                  0
#define LCD_WF8B_BPALCD37_MASK                   0x1u
#define LCD_WF8B_BPALCD37_SHIFT                  0
#define LCD_WF8B_BPALCD36_MASK                   0x1u
#define LCD_WF8B_BPALCD36_SHIFT                  0
#define LCD_WF8B_BPALCD4_MASK                    0x1u
#define LCD_WF8B_BPALCD4_SHIFT                   0
#define LCD_WF8B_BPALCD35_MASK                   0x1u
#define LCD_WF8B_BPALCD35_SHIFT                  0
#define LCD_WF8B_BPALCD34_MASK                   0x1u
#define LCD_WF8B_BPALCD34_SHIFT                  0
#define LCD_WF8B_BPALCD33_MASK                   0x1u
#define LCD_WF8B_BPALCD33_SHIFT                  0
#define LCD_WF8B_BPALCD32_MASK                   0x1u
#define LCD_WF8B_BPALCD32_SHIFT                  0
#define LCD_WF8B_BPALCD31_MASK                   0x1u
#define LCD_WF8B_BPALCD31_SHIFT                  0
#define LCD_WF8B_BPALCD30_MASK                   0x1u
#define LCD_WF8B_BPALCD30_SHIFT                  0
#define LCD_WF8B_BPALCD29_MASK                   0x1u
#define LCD_WF8B_BPALCD29_SHIFT                  0
#define LCD_WF8B_BPALCD5_MASK                    0x1u
#define LCD_WF8B_BPALCD5_SHIFT                   0
#define LCD_WF8B_BPALCD28_MASK                   0x1u
#define LCD_WF8B_BPALCD28_SHIFT                  0
#define LCD_WF8B_BPALCD27_MASK                   0x1u
#define LCD_WF8B_BPALCD27_SHIFT                  0
#define LCD_WF8B_BPALCD26_MASK                   0x1u
#define LCD_WF8B_BPALCD26_SHIFT                  0
#define LCD_WF8B_BPALCD25_MASK                   0x1u
#define LCD_WF8B_BPALCD25_SHIFT                  0
#define LCD_WF8B_BPALCD24_MASK                   0x1u
#define LCD_WF8B_BPALCD24_SHIFT                  0
#define LCD_WF8B_BPALCD23_MASK                   0x1u
#define LCD_WF8B_BPALCD23_SHIFT                  0
#define LCD_WF8B_BPALCD22_MASK                   0x1u
#define LCD_WF8B_BPALCD22_SHIFT                  0
#define LCD_WF8B_BPALCD6_MASK                    0x1u
#define LCD_WF8B_BPALCD6_SHIFT                   0
#define LCD_WF8B_BPALCD21_MASK                   0x1u
#define LCD_WF8B_BPALCD21_SHIFT                  0
#define LCD_WF8B_BPALCD20_MASK                   0x1u
#define LCD_WF8B_BPALCD20_SHIFT                  0
#define LCD_WF8B_BPALCD19_MASK                   0x1u
#define LCD_WF8B_BPALCD19_SHIFT                  0
#define LCD_WF8B_BPALCD18_MASK                   0x1u
#define LCD_WF8B_BPALCD18_SHIFT                  0
#define LCD_WF8B_BPALCD17_MASK                   0x1u
#define LCD_WF8B_BPALCD17_SHIFT                  0
#define LCD_WF8B_BPALCD16_MASK                   0x1u
#define LCD_WF8B_BPALCD16_SHIFT                  0
#define LCD_WF8B_BPALCD15_MASK                   0x1u
#define LCD_WF8B_BPALCD15_SHIFT                  0
#define LCD_WF8B_BPALCD7_MASK                    0x1u
#define LCD_WF8B_BPALCD7_SHIFT                   0
#define LCD_WF8B_BPALCD14_MASK                   0x1u
#define LCD_WF8B_BPALCD14_SHIFT                  0
#define LCD_WF8B_BPALCD13_MASK                   0x1u
#define LCD_WF8B_BPALCD13_SHIFT                  0
#define LCD_WF8B_BPALCD12_MASK                   0x1u
#define LCD_WF8B_BPALCD12_SHIFT                  0
#define LCD_WF8B_BPALCD11_MASK                   0x1u
#define LCD_WF8B_BPALCD11_SHIFT                  0
#define LCD_WF8B_BPALCD10_MASK                   0x1u
#define LCD_WF8B_BPALCD10_SHIFT                  0
#define LCD_WF8B_BPALCD9_MASK                    0x1u
#define LCD_WF8B_BPALCD9_SHIFT                   0
#define LCD_WF8B_BPALCD8_MASK                    0x1u
#define LCD_WF8B_BPALCD8_SHIFT                   0
#define LCD_WF8B_BPBLCD1_MASK                    0x2u
#define LCD_WF8B_BPBLCD1_SHIFT                   1
#define LCD_WF8B_BPBLCD32_MASK                   0x2u
#define LCD_WF8B_BPBLCD32_SHIFT                  1
#define LCD_WF8B_BPBLCD30_MASK                   0x2u
#define LCD_WF8B_BPBLCD30_SHIFT                  1
#define LCD_WF8B_BPBLCD60_MASK                   0x2u
#define LCD_WF8B_BPBLCD60_SHIFT                  1
#define LCD_WF8B_BPBLCD24_MASK                   0x2u
#define LCD_WF8B_BPBLCD24_SHIFT                  1
#define LCD_WF8B_BPBLCD28_MASK                   0x2u
#define LCD_WF8B_BPBLCD28_SHIFT                  1
#define LCD_WF8B_BPBLCD23_MASK                   0x2u
#define LCD_WF8B_BPBLCD23_SHIFT                  1
#define LCD_WF8B_BPBLCD48_MASK                   0x2u
#define LCD_WF8B_BPBLCD48_SHIFT                  1
#define LCD_WF8B_BPBLCD10_MASK                   0x2u
#define LCD_WF8B_BPBLCD10_SHIFT                  1
#define LCD_WF8B_BPBLCD15_MASK                   0x2u
#define LCD_WF8B_BPBLCD15_SHIFT                  1
#define LCD_WF8B_BPBLCD36_MASK                   0x2u
#define LCD_WF8B_BPBLCD36_SHIFT                  1
#define LCD_WF8B_BPBLCD44_MASK                   0x2u
#define LCD_WF8B_BPBLCD44_SHIFT                  1
#define LCD_WF8B_BPBLCD62_MASK                   0x2u
#define LCD_WF8B_BPBLCD62_SHIFT                  1
#define LCD_WF8B_BPBLCD53_MASK                   0x2u
#define LCD_WF8B_BPBLCD53_SHIFT                  1
#define LCD_WF8B_BPBLCD22_MASK                   0x2u
#define LCD_WF8B_BPBLCD22_SHIFT                  1
#define LCD_WF8B_BPBLCD47_MASK                   0x2u
#define LCD_WF8B_BPBLCD47_SHIFT                  1
#define LCD_WF8B_BPBLCD33_MASK                   0x2u
#define LCD_WF8B_BPBLCD33_SHIFT                  1
#define LCD_WF8B_BPBLCD2_MASK                    0x2u
#define LCD_WF8B_BPBLCD2_SHIFT                   1
#define LCD_WF8B_BPBLCD49_MASK                   0x2u
#define LCD_WF8B_BPBLCD49_SHIFT                  1
#define LCD_WF8B_BPBLCD0_MASK                    0x2u
#define LCD_WF8B_BPBLCD0_SHIFT                   1
#define LCD_WF8B_BPBLCD55_MASK                   0x2u
#define LCD_WF8B_BPBLCD55_SHIFT                  1
#define LCD_WF8B_BPBLCD56_MASK                   0x2u
#define LCD_WF8B_BPBLCD56_SHIFT                  1
#define LCD_WF8B_BPBLCD21_MASK                   0x2u
#define LCD_WF8B_BPBLCD21_SHIFT                  1
#define LCD_WF8B_BPBLCD6_MASK                    0x2u
#define LCD_WF8B_BPBLCD6_SHIFT                   1
#define LCD_WF8B_BPBLCD29_MASK                   0x2u
#define LCD_WF8B_BPBLCD29_SHIFT                  1
#define LCD_WF8B_BPBLCD25_MASK                   0x2u
#define LCD_WF8B_BPBLCD25_SHIFT                  1
#define LCD_WF8B_BPBLCD8_MASK                    0x2u
#define LCD_WF8B_BPBLCD8_SHIFT                   1
#define LCD_WF8B_BPBLCD54_MASK                   0x2u
#define LCD_WF8B_BPBLCD54_SHIFT                  1
#define LCD_WF8B_BPBLCD38_MASK                   0x2u
#define LCD_WF8B_BPBLCD38_SHIFT                  1
#define LCD_WF8B_BPBLCD43_MASK                   0x2u
#define LCD_WF8B_BPBLCD43_SHIFT                  1
#define LCD_WF8B_BPBLCD20_MASK                   0x2u
#define LCD_WF8B_BPBLCD20_SHIFT                  1
#define LCD_WF8B_BPBLCD9_MASK                    0x2u
#define LCD_WF8B_BPBLCD9_SHIFT                   1
#define LCD_WF8B_BPBLCD7_MASK                    0x2u
#define LCD_WF8B_BPBLCD7_SHIFT                   1
#define LCD_WF8B_BPBLCD50_MASK                   0x2u
#define LCD_WF8B_BPBLCD50_SHIFT                  1
#define LCD_WF8B_BPBLCD40_MASK                   0x2u
#define LCD_WF8B_BPBLCD40_SHIFT                  1
#define LCD_WF8B_BPBLCD63_MASK                   0x2u
#define LCD_WF8B_BPBLCD63_SHIFT                  1
#define LCD_WF8B_BPBLCD26_MASK                   0x2u
#define LCD_WF8B_BPBLCD26_SHIFT                  1
#define LCD_WF8B_BPBLCD12_MASK                   0x2u
#define LCD_WF8B_BPBLCD12_SHIFT                  1
#define LCD_WF8B_BPBLCD19_MASK                   0x2u
#define LCD_WF8B_BPBLCD19_SHIFT                  1
#define LCD_WF8B_BPBLCD34_MASK                   0x2u
#define LCD_WF8B_BPBLCD34_SHIFT                  1
#define LCD_WF8B_BPBLCD39_MASK                   0x2u
#define LCD_WF8B_BPBLCD39_SHIFT                  1
#define LCD_WF8B_BPBLCD59_MASK                   0x2u
#define LCD_WF8B_BPBLCD59_SHIFT                  1
#define LCD_WF8B_BPBLCD61_MASK                   0x2u
#define LCD_WF8B_BPBLCD61_SHIFT                  1
#define LCD_WF8B_BPBLCD37_MASK                   0x2u
#define LCD_WF8B_BPBLCD37_SHIFT                  1
#define LCD_WF8B_BPBLCD31_MASK                   0x2u
#define LCD_WF8B_BPBLCD31_SHIFT                  1
#define LCD_WF8B_BPBLCD58_MASK                   0x2u
#define LCD_WF8B_BPBLCD58_SHIFT                  1
#define LCD_WF8B_BPBLCD18_MASK                   0x2u
#define LCD_WF8B_BPBLCD18_SHIFT                  1
#define LCD_WF8B_BPBLCD45_MASK                   0x2u
#define LCD_WF8B_BPBLCD45_SHIFT                  1
#define LCD_WF8B_BPBLCD27_MASK                   0x2u
#define LCD_WF8B_BPBLCD27_SHIFT                  1
#define LCD_WF8B_BPBLCD14_MASK                   0x2u
#define LCD_WF8B_BPBLCD14_SHIFT                  1
#define LCD_WF8B_BPBLCD51_MASK                   0x2u
#define LCD_WF8B_BPBLCD51_SHIFT                  1
#define LCD_WF8B_BPBLCD52_MASK                   0x2u
#define LCD_WF8B_BPBLCD52_SHIFT                  1
#define LCD_WF8B_BPBLCD4_MASK                    0x2u
#define LCD_WF8B_BPBLCD4_SHIFT                   1
#define LCD_WF8B_BPBLCD35_MASK                   0x2u
#define LCD_WF8B_BPBLCD35_SHIFT                  1
#define LCD_WF8B_BPBLCD17_MASK                   0x2u
#define LCD_WF8B_BPBLCD17_SHIFT                  1
#define LCD_WF8B_BPBLCD41_MASK                   0x2u
#define LCD_WF8B_BPBLCD41_SHIFT                  1
#define LCD_WF8B_BPBLCD11_MASK                   0x2u
#define LCD_WF8B_BPBLCD11_SHIFT                  1
#define LCD_WF8B_BPBLCD46_MASK                   0x2u
#define LCD_WF8B_BPBLCD46_SHIFT                  1
#define LCD_WF8B_BPBLCD57_MASK                   0x2u
#define LCD_WF8B_BPBLCD57_SHIFT                  1
#define LCD_WF8B_BPBLCD42_MASK                   0x2u
#define LCD_WF8B_BPBLCD42_SHIFT                  1
#define LCD_WF8B_BPBLCD5_MASK                    0x2u
#define LCD_WF8B_BPBLCD5_SHIFT                   1
#define LCD_WF8B_BPBLCD3_MASK                    0x2u
#define LCD_WF8B_BPBLCD3_SHIFT                   1
#define LCD_WF8B_BPBLCD16_MASK                   0x2u
#define LCD_WF8B_BPBLCD16_SHIFT                  1
#define LCD_WF8B_BPBLCD13_MASK                   0x2u
#define LCD_WF8B_BPBLCD13_SHIFT                  1
#define LCD_WF8B_BPCLCD10_MASK                   0x4u
#define LCD_WF8B_BPCLCD10_SHIFT                  2
#define LCD_WF8B_BPCLCD55_MASK                   0x4u
#define LCD_WF8B_BPCLCD55_SHIFT                  2
#define LCD_WF8B_BPCLCD2_MASK                    0x4u
#define LCD_WF8B_BPCLCD2_SHIFT                   2
#define LCD_WF8B_BPCLCD23_MASK                   0x4u
#define LCD_WF8B_BPCLCD23_SHIFT                  2
#define LCD_WF8B_BPCLCD48_MASK                   0x4u
#define LCD_WF8B_BPCLCD48_SHIFT                  2
#define LCD_WF8B_BPCLCD24_MASK                   0x4u
#define LCD_WF8B_BPCLCD24_SHIFT                  2
#define LCD_WF8B_BPCLCD60_MASK                   0x4u
#define LCD_WF8B_BPCLCD60_SHIFT                  2
#define LCD_WF8B_BPCLCD47_MASK                   0x4u
#define LCD_WF8B_BPCLCD47_SHIFT                  2
#define LCD_WF8B_BPCLCD22_MASK                   0x4u
#define LCD_WF8B_BPCLCD22_SHIFT                  2
#define LCD_WF8B_BPCLCD8_MASK                    0x4u
#define LCD_WF8B_BPCLCD8_SHIFT                   2
#define LCD_WF8B_BPCLCD21_MASK                   0x4u
#define LCD_WF8B_BPCLCD21_SHIFT                  2
#define LCD_WF8B_BPCLCD49_MASK                   0x4u
#define LCD_WF8B_BPCLCD49_SHIFT                  2
#define LCD_WF8B_BPCLCD25_MASK                   0x4u
#define LCD_WF8B_BPCLCD25_SHIFT                  2
#define LCD_WF8B_BPCLCD1_MASK                    0x4u
#define LCD_WF8B_BPCLCD1_SHIFT                   2
#define LCD_WF8B_BPCLCD20_MASK                   0x4u
#define LCD_WF8B_BPCLCD20_SHIFT                  2
#define LCD_WF8B_BPCLCD50_MASK                   0x4u
#define LCD_WF8B_BPCLCD50_SHIFT                  2
#define LCD_WF8B_BPCLCD19_MASK                   0x4u
#define LCD_WF8B_BPCLCD19_SHIFT                  2
#define LCD_WF8B_BPCLCD26_MASK                   0x4u
#define LCD_WF8B_BPCLCD26_SHIFT                  2
#define LCD_WF8B_BPCLCD59_MASK                   0x4u
#define LCD_WF8B_BPCLCD59_SHIFT                  2
#define LCD_WF8B_BPCLCD61_MASK                   0x4u
#define LCD_WF8B_BPCLCD61_SHIFT                  2
#define LCD_WF8B_BPCLCD46_MASK                   0x4u
#define LCD_WF8B_BPCLCD46_SHIFT                  2
#define LCD_WF8B_BPCLCD18_MASK                   0x4u
#define LCD_WF8B_BPCLCD18_SHIFT                  2
#define LCD_WF8B_BPCLCD5_MASK                    0x4u
#define LCD_WF8B_BPCLCD5_SHIFT                   2
#define LCD_WF8B_BPCLCD63_MASK                   0x4u
#define LCD_WF8B_BPCLCD63_SHIFT                  2
#define LCD_WF8B_BPCLCD27_MASK                   0x4u
#define LCD_WF8B_BPCLCD27_SHIFT                  2
#define LCD_WF8B_BPCLCD17_MASK                   0x4u
#define LCD_WF8B_BPCLCD17_SHIFT                  2
#define LCD_WF8B_BPCLCD51_MASK                   0x4u
#define LCD_WF8B_BPCLCD51_SHIFT                  2
#define LCD_WF8B_BPCLCD9_MASK                    0x4u
#define LCD_WF8B_BPCLCD9_SHIFT                   2
#define LCD_WF8B_BPCLCD54_MASK                   0x4u
#define LCD_WF8B_BPCLCD54_SHIFT                  2
#define LCD_WF8B_BPCLCD15_MASK                   0x4u
#define LCD_WF8B_BPCLCD15_SHIFT                  2
#define LCD_WF8B_BPCLCD16_MASK                   0x4u
#define LCD_WF8B_BPCLCD16_SHIFT                  2
#define LCD_WF8B_BPCLCD14_MASK                   0x4u
#define LCD_WF8B_BPCLCD14_SHIFT                  2
#define LCD_WF8B_BPCLCD32_MASK                   0x4u
#define LCD_WF8B_BPCLCD32_SHIFT                  2
#define LCD_WF8B_BPCLCD28_MASK                   0x4u
#define LCD_WF8B_BPCLCD28_SHIFT                  2
#define LCD_WF8B_BPCLCD53_MASK                   0x4u
#define LCD_WF8B_BPCLCD53_SHIFT                  2
#define LCD_WF8B_BPCLCD33_MASK                   0x4u
#define LCD_WF8B_BPCLCD33_SHIFT                  2
#define LCD_WF8B_BPCLCD0_MASK                    0x4u
#define LCD_WF8B_BPCLCD0_SHIFT                   2
#define LCD_WF8B_BPCLCD43_MASK                   0x4u
#define LCD_WF8B_BPCLCD43_SHIFT                  2
#define LCD_WF8B_BPCLCD7_MASK                    0x4u
#define LCD_WF8B_BPCLCD7_SHIFT                   2
#define LCD_WF8B_BPCLCD4_MASK                    0x4u
#define LCD_WF8B_BPCLCD4_SHIFT                   2
#define LCD_WF8B_BPCLCD34_MASK                   0x4u
#define LCD_WF8B_BPCLCD34_SHIFT                  2
#define LCD_WF8B_BPCLCD29_MASK                   0x4u
#define LCD_WF8B_BPCLCD29_SHIFT                  2
#define LCD_WF8B_BPCLCD45_MASK                   0x4u
#define LCD_WF8B_BPCLCD45_SHIFT                  2
#define LCD_WF8B_BPCLCD57_MASK                   0x4u
#define LCD_WF8B_BPCLCD57_SHIFT                  2
#define LCD_WF8B_BPCLCD42_MASK                   0x4u
#define LCD_WF8B_BPCLCD42_SHIFT                  2
#define LCD_WF8B_BPCLCD35_MASK                   0x4u
#define LCD_WF8B_BPCLCD35_SHIFT                  2
#define LCD_WF8B_BPCLCD13_MASK                   0x4u
#define LCD_WF8B_BPCLCD13_SHIFT                  2
#define LCD_WF8B_BPCLCD36_MASK                   0x4u
#define LCD_WF8B_BPCLCD36_SHIFT                  2
#define LCD_WF8B_BPCLCD30_MASK                   0x4u
#define LCD_WF8B_BPCLCD30_SHIFT                  2
#define LCD_WF8B_BPCLCD52_MASK                   0x4u
#define LCD_WF8B_BPCLCD52_SHIFT                  2
#define LCD_WF8B_BPCLCD58_MASK                   0x4u
#define LCD_WF8B_BPCLCD58_SHIFT                  2
#define LCD_WF8B_BPCLCD41_MASK                   0x4u
#define LCD_WF8B_BPCLCD41_SHIFT                  2
#define LCD_WF8B_BPCLCD37_MASK                   0x4u
#define LCD_WF8B_BPCLCD37_SHIFT                  2
#define LCD_WF8B_BPCLCD3_MASK                    0x4u
#define LCD_WF8B_BPCLCD3_SHIFT                   2
#define LCD_WF8B_BPCLCD12_MASK                   0x4u
#define LCD_WF8B_BPCLCD12_SHIFT                  2
#define LCD_WF8B_BPCLCD11_MASK                   0x4u
#define LCD_WF8B_BPCLCD11_SHIFT                  2
#define LCD_WF8B_BPCLCD38_MASK                   0x4u
#define LCD_WF8B_BPCLCD38_SHIFT                  2
#define LCD_WF8B_BPCLCD44_MASK                   0x4u
#define LCD_WF8B_BPCLCD44_SHIFT                  2
#define LCD_WF8B_BPCLCD31_MASK                   0x4u
#define LCD_WF8B_BPCLCD31_SHIFT                  2
#define LCD_WF8B_BPCLCD40_MASK                   0x4u
#define LCD_WF8B_BPCLCD40_SHIFT                  2
#define LCD_WF8B_BPCLCD62_MASK                   0x4u
#define LCD_WF8B_BPCLCD62_SHIFT                  2
#define LCD_WF8B_BPCLCD56_MASK                   0x4u
#define LCD_WF8B_BPCLCD56_SHIFT                  2
#define LCD_WF8B_BPCLCD39_MASK                   0x4u
#define LCD_WF8B_BPCLCD39_SHIFT                  2
#define LCD_WF8B_BPCLCD6_MASK                    0x4u
#define LCD_WF8B_BPCLCD6_SHIFT                   2
#define LCD_WF8B_BPDLCD47_MASK                   0x8u
#define LCD_WF8B_BPDLCD47_SHIFT                  3
#define LCD_WF8B_BPDLCD23_MASK                   0x8u
#define LCD_WF8B_BPDLCD23_SHIFT                  3
#define LCD_WF8B_BPDLCD48_MASK                   0x8u
#define LCD_WF8B_BPDLCD48_SHIFT                  3
#define LCD_WF8B_BPDLCD24_MASK                   0x8u
#define LCD_WF8B_BPDLCD24_SHIFT                  3
#define LCD_WF8B_BPDLCD15_MASK                   0x8u
#define LCD_WF8B_BPDLCD15_SHIFT                  3
#define LCD_WF8B_BPDLCD22_MASK                   0x8u
#define LCD_WF8B_BPDLCD22_SHIFT                  3
#define LCD_WF8B_BPDLCD60_MASK                   0x8u
#define LCD_WF8B_BPDLCD60_SHIFT                  3
#define LCD_WF8B_BPDLCD10_MASK                   0x8u
#define LCD_WF8B_BPDLCD10_SHIFT                  3
#define LCD_WF8B_BPDLCD21_MASK                   0x8u
#define LCD_WF8B_BPDLCD21_SHIFT                  3
#define LCD_WF8B_BPDLCD49_MASK                   0x8u
#define LCD_WF8B_BPDLCD49_SHIFT                  3
#define LCD_WF8B_BPDLCD1_MASK                    0x8u
#define LCD_WF8B_BPDLCD1_SHIFT                   3
#define LCD_WF8B_BPDLCD25_MASK                   0x8u
#define LCD_WF8B_BPDLCD25_SHIFT                  3
#define LCD_WF8B_BPDLCD20_MASK                   0x8u
#define LCD_WF8B_BPDLCD20_SHIFT                  3
#define LCD_WF8B_BPDLCD2_MASK                    0x8u
#define LCD_WF8B_BPDLCD2_SHIFT                   3
#define LCD_WF8B_BPDLCD55_MASK                   0x8u
#define LCD_WF8B_BPDLCD55_SHIFT                  3
#define LCD_WF8B_BPDLCD59_MASK                   0x8u
#define LCD_WF8B_BPDLCD59_SHIFT                  3
#define LCD_WF8B_BPDLCD5_MASK                    0x8u
#define LCD_WF8B_BPDLCD5_SHIFT                   3
#define LCD_WF8B_BPDLCD19_MASK                   0x8u
#define LCD_WF8B_BPDLCD19_SHIFT                  3
#define LCD_WF8B_BPDLCD6_MASK                    0x8u
#define LCD_WF8B_BPDLCD6_SHIFT                   3
#define LCD_WF8B_BPDLCD26_MASK                   0x8u
#define LCD_WF8B_BPDLCD26_SHIFT                  3
#define LCD_WF8B_BPDLCD0_MASK                    0x8u
#define LCD_WF8B_BPDLCD0_SHIFT                   3
#define LCD_WF8B_BPDLCD50_MASK                   0x8u
#define LCD_WF8B_BPDLCD50_SHIFT                  3
#define LCD_WF8B_BPDLCD46_MASK                   0x8u
#define LCD_WF8B_BPDLCD46_SHIFT                  3
#define LCD_WF8B_BPDLCD18_MASK                   0x8u
#define LCD_WF8B_BPDLCD18_SHIFT                  3
#define LCD_WF8B_BPDLCD61_MASK                   0x8u
#define LCD_WF8B_BPDLCD61_SHIFT                  3
#define LCD_WF8B_BPDLCD9_MASK                    0x8u
#define LCD_WF8B_BPDLCD9_SHIFT                   3
#define LCD_WF8B_BPDLCD17_MASK                   0x8u
#define LCD_WF8B_BPDLCD17_SHIFT                  3
#define LCD_WF8B_BPDLCD27_MASK                   0x8u
#define LCD_WF8B_BPDLCD27_SHIFT                  3
#define LCD_WF8B_BPDLCD53_MASK                   0x8u
#define LCD_WF8B_BPDLCD53_SHIFT                  3
#define LCD_WF8B_BPDLCD51_MASK                   0x8u
#define LCD_WF8B_BPDLCD51_SHIFT                  3
#define LCD_WF8B_BPDLCD54_MASK                   0x8u
#define LCD_WF8B_BPDLCD54_SHIFT                  3
#define LCD_WF8B_BPDLCD13_MASK                   0x8u
#define LCD_WF8B_BPDLCD13_SHIFT                  3
#define LCD_WF8B_BPDLCD16_MASK                   0x8u
#define LCD_WF8B_BPDLCD16_SHIFT                  3
#define LCD_WF8B_BPDLCD32_MASK                   0x8u
#define LCD_WF8B_BPDLCD32_SHIFT                  3
#define LCD_WF8B_BPDLCD14_MASK                   0x8u
#define LCD_WF8B_BPDLCD14_SHIFT                  3
#define LCD_WF8B_BPDLCD28_MASK                   0x8u
#define LCD_WF8B_BPDLCD28_SHIFT                  3
#define LCD_WF8B_BPDLCD43_MASK                   0x8u
#define LCD_WF8B_BPDLCD43_SHIFT                  3
#define LCD_WF8B_BPDLCD4_MASK                    0x8u
#define LCD_WF8B_BPDLCD4_SHIFT                   3
#define LCD_WF8B_BPDLCD45_MASK                   0x8u
#define LCD_WF8B_BPDLCD45_SHIFT                  3
#define LCD_WF8B_BPDLCD8_MASK                    0x8u
#define LCD_WF8B_BPDLCD8_SHIFT                   3
#define LCD_WF8B_BPDLCD62_MASK                   0x8u
#define LCD_WF8B_BPDLCD62_SHIFT                  3
#define LCD_WF8B_BPDLCD33_MASK                   0x8u
#define LCD_WF8B_BPDLCD33_SHIFT                  3
#define LCD_WF8B_BPDLCD34_MASK                   0x8u
#define LCD_WF8B_BPDLCD34_SHIFT                  3
#define LCD_WF8B_BPDLCD29_MASK                   0x8u
#define LCD_WF8B_BPDLCD29_SHIFT                  3
#define LCD_WF8B_BPDLCD58_MASK                   0x8u
#define LCD_WF8B_BPDLCD58_SHIFT                  3
#define LCD_WF8B_BPDLCD57_MASK                   0x8u
#define LCD_WF8B_BPDLCD57_SHIFT                  3
#define LCD_WF8B_BPDLCD42_MASK                   0x8u
#define LCD_WF8B_BPDLCD42_SHIFT                  3
#define LCD_WF8B_BPDLCD35_MASK                   0x8u
#define LCD_WF8B_BPDLCD35_SHIFT                  3
#define LCD_WF8B_BPDLCD52_MASK                   0x8u
#define LCD_WF8B_BPDLCD52_SHIFT                  3
#define LCD_WF8B_BPDLCD7_MASK                    0x8u
#define LCD_WF8B_BPDLCD7_SHIFT                   3
#define LCD_WF8B_BPDLCD36_MASK                   0x8u
#define LCD_WF8B_BPDLCD36_SHIFT                  3
#define LCD_WF8B_BPDLCD30_MASK                   0x8u
#define LCD_WF8B_BPDLCD30_SHIFT                  3
#define LCD_WF8B_BPDLCD41_MASK                   0x8u
#define LCD_WF8B_BPDLCD41_SHIFT                  3
#define LCD_WF8B_BPDLCD37_MASK                   0x8u
#define LCD_WF8B_BPDLCD37_SHIFT                  3
#define LCD_WF8B_BPDLCD44_MASK                   0x8u
#define LCD_WF8B_BPDLCD44_SHIFT                  3
#define LCD_WF8B_BPDLCD63_MASK                   0x8u
#define LCD_WF8B_BPDLCD63_SHIFT                  3
#define LCD_WF8B_BPDLCD38_MASK                   0x8u
#define LCD_WF8B_BPDLCD38_SHIFT                  3
#define LCD_WF8B_BPDLCD56_MASK                   0x8u
#define LCD_WF8B_BPDLCD56_SHIFT                  3
#define LCD_WF8B_BPDLCD40_MASK                   0x8u
#define LCD_WF8B_BPDLCD40_SHIFT                  3
#define LCD_WF8B_BPDLCD31_MASK                   0x8u
#define LCD_WF8B_BPDLCD31_SHIFT                  3
#define LCD_WF8B_BPDLCD12_MASK                   0x8u
#define LCD_WF8B_BPDLCD12_SHIFT                  3
#define LCD_WF8B_BPDLCD39_MASK                   0x8u
#define LCD_WF8B_BPDLCD39_SHIFT                  3
#define LCD_WF8B_BPDLCD3_MASK                    0x8u
#define LCD_WF8B_BPDLCD3_SHIFT                   3
#define LCD_WF8B_BPDLCD11_MASK                   0x8u
#define LCD_WF8B_BPDLCD11_SHIFT                  3
#define LCD_WF8B_BPELCD12_MASK                   0x10u
#define LCD_WF8B_BPELCD12_SHIFT                  4
#define LCD_WF8B_BPELCD39_MASK                   0x10u
#define LCD_WF8B_BPELCD39_SHIFT                  4
#define LCD_WF8B_BPELCD3_MASK                    0x10u
#define LCD_WF8B_BPELCD3_SHIFT                   4
#define LCD_WF8B_BPELCD38_MASK                   0x10u
#define LCD_WF8B_BPELCD38_SHIFT                  4
#define LCD_WF8B_BPELCD40_MASK                   0x10u
#define LCD_WF8B_BPELCD40_SHIFT                  4
#define LCD_WF8B_BPELCD37_MASK                   0x10u
#define LCD_WF8B_BPELCD37_SHIFT                  4
#define LCD_WF8B_BPELCD41_MASK                   0x10u
#define LCD_WF8B_BPELCD41_SHIFT                  4
#define LCD_WF8B_BPELCD36_MASK                   0x10u
#define LCD_WF8B_BPELCD36_SHIFT                  4
#define LCD_WF8B_BPELCD8_MASK                    0x10u
#define LCD_WF8B_BPELCD8_SHIFT                   4
#define LCD_WF8B_BPELCD35_MASK                   0x10u
#define LCD_WF8B_BPELCD35_SHIFT                  4
#define LCD_WF8B_BPELCD42_MASK                   0x10u
#define LCD_WF8B_BPELCD42_SHIFT                  4
#define LCD_WF8B_BPELCD34_MASK                   0x10u
#define LCD_WF8B_BPELCD34_SHIFT                  4
#define LCD_WF8B_BPELCD33_MASK                   0x10u
#define LCD_WF8B_BPELCD33_SHIFT                  4
#define LCD_WF8B_BPELCD11_MASK                   0x10u
#define LCD_WF8B_BPELCD11_SHIFT                  4
#define LCD_WF8B_BPELCD43_MASK                   0x10u
#define LCD_WF8B_BPELCD43_SHIFT                  4
#define LCD_WF8B_BPELCD32_MASK                   0x10u
#define LCD_WF8B_BPELCD32_SHIFT                  4
#define LCD_WF8B_BPELCD31_MASK                   0x10u
#define LCD_WF8B_BPELCD31_SHIFT                  4
#define LCD_WF8B_BPELCD44_MASK                   0x10u
#define LCD_WF8B_BPELCD44_SHIFT                  4
#define LCD_WF8B_BPELCD30_MASK                   0x10u
#define LCD_WF8B_BPELCD30_SHIFT                  4
#define LCD_WF8B_BPELCD29_MASK                   0x10u
#define LCD_WF8B_BPELCD29_SHIFT                  4
#define LCD_WF8B_BPELCD7_MASK                    0x10u
#define LCD_WF8B_BPELCD7_SHIFT                   4
#define LCD_WF8B_BPELCD45_MASK                   0x10u
#define LCD_WF8B_BPELCD45_SHIFT                  4
#define LCD_WF8B_BPELCD28_MASK                   0x10u
#define LCD_WF8B_BPELCD28_SHIFT                  4
#define LCD_WF8B_BPELCD2_MASK                    0x10u
#define LCD_WF8B_BPELCD2_SHIFT                   4
#define LCD_WF8B_BPELCD27_MASK                   0x10u
#define LCD_WF8B_BPELCD27_SHIFT                  4
#define LCD_WF8B_BPELCD46_MASK                   0x10u
#define LCD_WF8B_BPELCD46_SHIFT                  4
#define LCD_WF8B_BPELCD26_MASK                   0x10u
#define LCD_WF8B_BPELCD26_SHIFT                  4
#define LCD_WF8B_BPELCD10_MASK                   0x10u
#define LCD_WF8B_BPELCD10_SHIFT                  4
#define LCD_WF8B_BPELCD13_MASK                   0x10u
#define LCD_WF8B_BPELCD13_SHIFT                  4
#define LCD_WF8B_BPELCD25_MASK                   0x10u
#define LCD_WF8B_BPELCD25_SHIFT                  4
#define LCD_WF8B_BPELCD5_MASK                    0x10u
#define LCD_WF8B_BPELCD5_SHIFT                   4
#define LCD_WF8B_BPELCD24_MASK                   0x10u
#define LCD_WF8B_BPELCD24_SHIFT                  4
#define LCD_WF8B_BPELCD47_MASK                   0x10u
#define LCD_WF8B_BPELCD47_SHIFT                  4
#define LCD_WF8B_BPELCD23_MASK                   0x10u
#define LCD_WF8B_BPELCD23_SHIFT                  4
#define LCD_WF8B_BPELCD22_MASK                   0x10u
#define LCD_WF8B_BPELCD22_SHIFT                  4
#define LCD_WF8B_BPELCD48_MASK                   0x10u
#define LCD_WF8B_BPELCD48_SHIFT                  4
#define LCD_WF8B_BPELCD21_MASK                   0x10u
#define LCD_WF8B_BPELCD21_SHIFT                  4
#define LCD_WF8B_BPELCD49_MASK                   0x10u
#define LCD_WF8B_BPELCD49_SHIFT                  4
#define LCD_WF8B_BPELCD20_MASK                   0x10u
#define LCD_WF8B_BPELCD20_SHIFT                  4
#define LCD_WF8B_BPELCD19_MASK                   0x10u
#define LCD_WF8B_BPELCD19_SHIFT                  4
#define LCD_WF8B_BPELCD9_MASK                    0x10u
#define LCD_WF8B_BPELCD9_SHIFT                   4
#define LCD_WF8B_BPELCD50_MASK                   0x10u
#define LCD_WF8B_BPELCD50_SHIFT                  4
#define LCD_WF8B_BPELCD18_MASK                   0x10u
#define LCD_WF8B_BPELCD18_SHIFT                  4
#define LCD_WF8B_BPELCD6_MASK                    0x10u
#define LCD_WF8B_BPELCD6_SHIFT                   4
#define LCD_WF8B_BPELCD17_MASK                   0x10u
#define LCD_WF8B_BPELCD17_SHIFT                  4
#define LCD_WF8B_BPELCD51_MASK                   0x10u
#define LCD_WF8B_BPELCD51_SHIFT                  4
#define LCD_WF8B_BPELCD16_MASK                   0x10u
#define LCD_WF8B_BPELCD16_SHIFT                  4
#define LCD_WF8B_BPELCD56_MASK                   0x10u
#define LCD_WF8B_BPELCD56_SHIFT                  4
#define LCD_WF8B_BPELCD57_MASK                   0x10u
#define LCD_WF8B_BPELCD57_SHIFT                  4
#define LCD_WF8B_BPELCD52_MASK                   0x10u
#define LCD_WF8B_BPELCD52_SHIFT                  4
#define LCD_WF8B_BPELCD1_MASK                    0x10u
#define LCD_WF8B_BPELCD1_SHIFT                   4
#define LCD_WF8B_BPELCD58_MASK                   0x10u
#define LCD_WF8B_BPELCD58_SHIFT                  4
#define LCD_WF8B_BPELCD59_MASK                   0x10u
#define LCD_WF8B_BPELCD59_SHIFT                  4
#define LCD_WF8B_BPELCD53_MASK                   0x10u
#define LCD_WF8B_BPELCD53_SHIFT                  4
#define LCD_WF8B_BPELCD14_MASK                   0x10u
#define LCD_WF8B_BPELCD14_SHIFT                  4
#define LCD_WF8B_BPELCD0_MASK                    0x10u
#define LCD_WF8B_BPELCD0_SHIFT                   4
#define LCD_WF8B_BPELCD60_MASK                   0x10u
#define LCD_WF8B_BPELCD60_SHIFT                  4
#define LCD_WF8B_BPELCD15_MASK                   0x10u
#define LCD_WF8B_BPELCD15_SHIFT                  4
#define LCD_WF8B_BPELCD61_MASK                   0x10u
#define LCD_WF8B_BPELCD61_SHIFT                  4
#define LCD_WF8B_BPELCD54_MASK                   0x10u
#define LCD_WF8B_BPELCD54_SHIFT                  4
#define LCD_WF8B_BPELCD62_MASK                   0x10u
#define LCD_WF8B_BPELCD62_SHIFT                  4
#define LCD_WF8B_BPELCD63_MASK                   0x10u
#define LCD_WF8B_BPELCD63_SHIFT                  4
#define LCD_WF8B_BPELCD55_MASK                   0x10u
#define LCD_WF8B_BPELCD55_SHIFT                  4
#define LCD_WF8B_BPELCD4_MASK                    0x10u
#define LCD_WF8B_BPELCD4_SHIFT                   4
#define LCD_WF8B_BPFLCD13_MASK                   0x20u
#define LCD_WF8B_BPFLCD13_SHIFT                  5
#define LCD_WF8B_BPFLCD39_MASK                   0x20u
#define LCD_WF8B_BPFLCD39_SHIFT                  5
#define LCD_WF8B_BPFLCD55_MASK                   0x20u
#define LCD_WF8B_BPFLCD55_SHIFT                  5
#define LCD_WF8B_BPFLCD47_MASK                   0x20u
#define LCD_WF8B_BPFLCD47_SHIFT                  5
#define LCD_WF8B_BPFLCD63_MASK                   0x20u
#define LCD_WF8B_BPFLCD63_SHIFT                  5
#define LCD_WF8B_BPFLCD43_MASK                   0x20u
#define LCD_WF8B_BPFLCD43_SHIFT                  5
#define LCD_WF8B_BPFLCD5_MASK                    0x20u
#define LCD_WF8B_BPFLCD5_SHIFT                   5
#define LCD_WF8B_BPFLCD62_MASK                   0x20u
#define LCD_WF8B_BPFLCD62_SHIFT                  5
#define LCD_WF8B_BPFLCD14_MASK                   0x20u
#define LCD_WF8B_BPFLCD14_SHIFT                  5
#define LCD_WF8B_BPFLCD24_MASK                   0x20u
#define LCD_WF8B_BPFLCD24_SHIFT                  5
#define LCD_WF8B_BPFLCD54_MASK                   0x20u
#define LCD_WF8B_BPFLCD54_SHIFT                  5
#define LCD_WF8B_BPFLCD15_MASK                   0x20u
#define LCD_WF8B_BPFLCD15_SHIFT                  5
#define LCD_WF8B_BPFLCD32_MASK                   0x20u
#define LCD_WF8B_BPFLCD32_SHIFT                  5
#define LCD_WF8B_BPFLCD61_MASK                   0x20u
#define LCD_WF8B_BPFLCD61_SHIFT                  5
#define LCD_WF8B_BPFLCD25_MASK                   0x20u
#define LCD_WF8B_BPFLCD25_SHIFT                  5
#define LCD_WF8B_BPFLCD60_MASK                   0x20u
#define LCD_WF8B_BPFLCD60_SHIFT                  5
#define LCD_WF8B_BPFLCD41_MASK                   0x20u
#define LCD_WF8B_BPFLCD41_SHIFT                  5
#define LCD_WF8B_BPFLCD33_MASK                   0x20u
#define LCD_WF8B_BPFLCD33_SHIFT                  5
#define LCD_WF8B_BPFLCD53_MASK                   0x20u
#define LCD_WF8B_BPFLCD53_SHIFT                  5
#define LCD_WF8B_BPFLCD59_MASK                   0x20u
#define LCD_WF8B_BPFLCD59_SHIFT                  5
#define LCD_WF8B_BPFLCD0_MASK                    0x20u
#define LCD_WF8B_BPFLCD0_SHIFT                   5
#define LCD_WF8B_BPFLCD46_MASK                   0x20u
#define LCD_WF8B_BPFLCD46_SHIFT                  5
#define LCD_WF8B_BPFLCD58_MASK                   0x20u
#define LCD_WF8B_BPFLCD58_SHIFT                  5
#define LCD_WF8B_BPFLCD26_MASK                   0x20u
#define LCD_WF8B_BPFLCD26_SHIFT                  5
#define LCD_WF8B_BPFLCD36_MASK                   0x20u
#define LCD_WF8B_BPFLCD36_SHIFT                  5
#define LCD_WF8B_BPFLCD10_MASK                   0x20u
#define LCD_WF8B_BPFLCD10_SHIFT                  5
#define LCD_WF8B_BPFLCD52_MASK                   0x20u
#define LCD_WF8B_BPFLCD52_SHIFT                  5
#define LCD_WF8B_BPFLCD57_MASK                   0x20u
#define LCD_WF8B_BPFLCD57_SHIFT                  5
#define LCD_WF8B_BPFLCD27_MASK                   0x20u
#define LCD_WF8B_BPFLCD27_SHIFT                  5
#define LCD_WF8B_BPFLCD11_MASK                   0x20u
#define LCD_WF8B_BPFLCD11_SHIFT                  5
#define LCD_WF8B_BPFLCD56_MASK                   0x20u
#define LCD_WF8B_BPFLCD56_SHIFT                  5
#define LCD_WF8B_BPFLCD1_MASK                    0x20u
#define LCD_WF8B_BPFLCD1_SHIFT                   5
#define LCD_WF8B_BPFLCD8_MASK                    0x20u
#define LCD_WF8B_BPFLCD8_SHIFT                   5
#define LCD_WF8B_BPFLCD40_MASK                   0x20u
#define LCD_WF8B_BPFLCD40_SHIFT                  5
#define LCD_WF8B_BPFLCD51_MASK                   0x20u
#define LCD_WF8B_BPFLCD51_SHIFT                  5
#define LCD_WF8B_BPFLCD16_MASK                   0x20u
#define LCD_WF8B_BPFLCD16_SHIFT                  5
#define LCD_WF8B_BPFLCD45_MASK                   0x20u
#define LCD_WF8B_BPFLCD45_SHIFT                  5
#define LCD_WF8B_BPFLCD6_MASK                    0x20u
#define LCD_WF8B_BPFLCD6_SHIFT                   5
#define LCD_WF8B_BPFLCD17_MASK                   0x20u
#define LCD_WF8B_BPFLCD17_SHIFT                  5
#define LCD_WF8B_BPFLCD28_MASK                   0x20u
#define LCD_WF8B_BPFLCD28_SHIFT                  5
#define LCD_WF8B_BPFLCD42_MASK                   0x20u
#define LCD_WF8B_BPFLCD42_SHIFT                  5
#define LCD_WF8B_BPFLCD29_MASK                   0x20u
#define LCD_WF8B_BPFLCD29_SHIFT                  5
#define LCD_WF8B_BPFLCD50_MASK                   0x20u
#define LCD_WF8B_BPFLCD50_SHIFT                  5
#define LCD_WF8B_BPFLCD18_MASK                   0x20u
#define LCD_WF8B_BPFLCD18_SHIFT                  5
#define LCD_WF8B_BPFLCD34_MASK                   0x20u
#define LCD_WF8B_BPFLCD34_SHIFT                  5
#define LCD_WF8B_BPFLCD19_MASK                   0x20u
#define LCD_WF8B_BPFLCD19_SHIFT                  5
#define LCD_WF8B_BPFLCD2_MASK                    0x20u
#define LCD_WF8B_BPFLCD2_SHIFT                   5
#define LCD_WF8B_BPFLCD9_MASK                    0x20u
#define LCD_WF8B_BPFLCD9_SHIFT                   5
#define LCD_WF8B_BPFLCD3_MASK                    0x20u
#define LCD_WF8B_BPFLCD3_SHIFT                   5
#define LCD_WF8B_BPFLCD37_MASK                   0x20u
#define LCD_WF8B_BPFLCD37_SHIFT                  5
#define LCD_WF8B_BPFLCD49_MASK                   0x20u
#define LCD_WF8B_BPFLCD49_SHIFT                  5
#define LCD_WF8B_BPFLCD20_MASK                   0x20u
#define LCD_WF8B_BPFLCD20_SHIFT                  5
#define LCD_WF8B_BPFLCD44_MASK                   0x20u
#define LCD_WF8B_BPFLCD44_SHIFT                  5
#define LCD_WF8B_BPFLCD30_MASK                   0x20u
#define LCD_WF8B_BPFLCD30_SHIFT                  5
#define LCD_WF8B_BPFLCD21_MASK                   0x20u
#define LCD_WF8B_BPFLCD21_SHIFT                  5
#define LCD_WF8B_BPFLCD35_MASK                   0x20u
#define LCD_WF8B_BPFLCD35_SHIFT                  5
#define LCD_WF8B_BPFLCD4_MASK                    0x20u
#define LCD_WF8B_BPFLCD4_SHIFT                   5
#define LCD_WF8B_BPFLCD31_MASK                   0x20u
#define LCD_WF8B_BPFLCD31_SHIFT                  5
#define LCD_WF8B_BPFLCD48_MASK                   0x20u
#define LCD_WF8B_BPFLCD48_SHIFT                  5
#define LCD_WF8B_BPFLCD7_MASK                    0x20u
#define LCD_WF8B_BPFLCD7_SHIFT                   5
#define LCD_WF8B_BPFLCD22_MASK                   0x20u
#define LCD_WF8B_BPFLCD22_SHIFT                  5
#define LCD_WF8B_BPFLCD38_MASK                   0x20u
#define LCD_WF8B_BPFLCD38_SHIFT                  5
#define LCD_WF8B_BPFLCD12_MASK                   0x20u
#define LCD_WF8B_BPFLCD12_SHIFT                  5
#define LCD_WF8B_BPFLCD23_MASK                   0x20u
#define LCD_WF8B_BPFLCD23_SHIFT                  5
#define LCD_WF8B_BPGLCD14_MASK                   0x40u
#define LCD_WF8B_BPGLCD14_SHIFT                  6
#define LCD_WF8B_BPGLCD55_MASK                   0x40u
#define LCD_WF8B_BPGLCD55_SHIFT                  6
#define LCD_WF8B_BPGLCD63_MASK                   0x40u
#define LCD_WF8B_BPGLCD63_SHIFT                  6
#define LCD_WF8B_BPGLCD15_MASK                   0x40u
#define LCD_WF8B_BPGLCD15_SHIFT                  6
#define LCD_WF8B_BPGLCD62_MASK                   0x40u
#define LCD_WF8B_BPGLCD62_SHIFT                  6
#define LCD_WF8B_BPGLCD54_MASK                   0x40u
#define LCD_WF8B_BPGLCD54_SHIFT                  6
#define LCD_WF8B_BPGLCD61_MASK                   0x40u
#define LCD_WF8B_BPGLCD61_SHIFT                  6
#define LCD_WF8B_BPGLCD60_MASK                   0x40u
#define LCD_WF8B_BPGLCD60_SHIFT                  6
#define LCD_WF8B_BPGLCD59_MASK                   0x40u
#define LCD_WF8B_BPGLCD59_SHIFT                  6
#define LCD_WF8B_BPGLCD53_MASK                   0x40u
#define LCD_WF8B_BPGLCD53_SHIFT                  6
#define LCD_WF8B_BPGLCD58_MASK                   0x40u
#define LCD_WF8B_BPGLCD58_SHIFT                  6
#define LCD_WF8B_BPGLCD0_MASK                    0x40u
#define LCD_WF8B_BPGLCD0_SHIFT                   6
#define LCD_WF8B_BPGLCD57_MASK                   0x40u
#define LCD_WF8B_BPGLCD57_SHIFT                  6
#define LCD_WF8B_BPGLCD52_MASK                   0x40u
#define LCD_WF8B_BPGLCD52_SHIFT                  6
#define LCD_WF8B_BPGLCD7_MASK                    0x40u
#define LCD_WF8B_BPGLCD7_SHIFT                   6
#define LCD_WF8B_BPGLCD56_MASK                   0x40u
#define LCD_WF8B_BPGLCD56_SHIFT                  6
#define LCD_WF8B_BPGLCD6_MASK                    0x40u
#define LCD_WF8B_BPGLCD6_SHIFT                   6
#define LCD_WF8B_BPGLCD51_MASK                   0x40u
#define LCD_WF8B_BPGLCD51_SHIFT                  6
#define LCD_WF8B_BPGLCD16_MASK                   0x40u
#define LCD_WF8B_BPGLCD16_SHIFT                  6
#define LCD_WF8B_BPGLCD1_MASK                    0x40u
#define LCD_WF8B_BPGLCD1_SHIFT                   6
#define LCD_WF8B_BPGLCD17_MASK                   0x40u
#define LCD_WF8B_BPGLCD17_SHIFT                  6
#define LCD_WF8B_BPGLCD50_MASK                   0x40u
#define LCD_WF8B_BPGLCD50_SHIFT                  6
#define LCD_WF8B_BPGLCD18_MASK                   0x40u
#define LCD_WF8B_BPGLCD18_SHIFT                  6
#define LCD_WF8B_BPGLCD19_MASK                   0x40u
#define LCD_WF8B_BPGLCD19_SHIFT                  6
#define LCD_WF8B_BPGLCD8_MASK                    0x40u
#define LCD_WF8B_BPGLCD8_SHIFT                   6
#define LCD_WF8B_BPGLCD49_MASK                   0x40u
#define LCD_WF8B_BPGLCD49_SHIFT                  6
#define LCD_WF8B_BPGLCD20_MASK                   0x40u
#define LCD_WF8B_BPGLCD20_SHIFT                  6
#define LCD_WF8B_BPGLCD9_MASK                    0x40u
#define LCD_WF8B_BPGLCD9_SHIFT                   6
#define LCD_WF8B_BPGLCD21_MASK                   0x40u
#define LCD_WF8B_BPGLCD21_SHIFT                  6
#define LCD_WF8B_BPGLCD13_MASK                   0x40u
#define LCD_WF8B_BPGLCD13_SHIFT                  6
#define LCD_WF8B_BPGLCD48_MASK                   0x40u
#define LCD_WF8B_BPGLCD48_SHIFT                  6
#define LCD_WF8B_BPGLCD22_MASK                   0x40u
#define LCD_WF8B_BPGLCD22_SHIFT                  6
#define LCD_WF8B_BPGLCD5_MASK                    0x40u
#define LCD_WF8B_BPGLCD5_SHIFT                   6
#define LCD_WF8B_BPGLCD47_MASK                   0x40u
#define LCD_WF8B_BPGLCD47_SHIFT                  6
#define LCD_WF8B_BPGLCD23_MASK                   0x40u
#define LCD_WF8B_BPGLCD23_SHIFT                  6
#define LCD_WF8B_BPGLCD24_MASK                   0x40u
#define LCD_WF8B_BPGLCD24_SHIFT                  6
#define LCD_WF8B_BPGLCD25_MASK                   0x40u
#define LCD_WF8B_BPGLCD25_SHIFT                  6
#define LCD_WF8B_BPGLCD46_MASK                   0x40u
#define LCD_WF8B_BPGLCD46_SHIFT                  6
#define LCD_WF8B_BPGLCD26_MASK                   0x40u
#define LCD_WF8B_BPGLCD26_SHIFT                  6
#define LCD_WF8B_BPGLCD27_MASK                   0x40u
#define LCD_WF8B_BPGLCD27_SHIFT                  6
#define LCD_WF8B_BPGLCD10_MASK                   0x40u
#define LCD_WF8B_BPGLCD10_SHIFT                  6
#define LCD_WF8B_BPGLCD45_MASK                   0x40u
#define LCD_WF8B_BPGLCD45_SHIFT                  6
#define LCD_WF8B_BPGLCD28_MASK                   0x40u
#define LCD_WF8B_BPGLCD28_SHIFT                  6
#define LCD_WF8B_BPGLCD29_MASK                   0x40u
#define LCD_WF8B_BPGLCD29_SHIFT                  6
#define LCD_WF8B_BPGLCD4_MASK                    0x40u
#define LCD_WF8B_BPGLCD4_SHIFT                   6
#define LCD_WF8B_BPGLCD44_MASK                   0x40u
#define LCD_WF8B_BPGLCD44_SHIFT                  6
#define LCD_WF8B_BPGLCD30_MASK                   0x40u
#define LCD_WF8B_BPGLCD30_SHIFT                  6
#define LCD_WF8B_BPGLCD2_MASK                    0x40u
#define LCD_WF8B_BPGLCD2_SHIFT                   6
#define LCD_WF8B_BPGLCD31_MASK                   0x40u
#define LCD_WF8B_BPGLCD31_SHIFT                  6
#define LCD_WF8B_BPGLCD43_MASK                   0x40u
#define LCD_WF8B_BPGLCD43_SHIFT                  6
#define LCD_WF8B_BPGLCD32_MASK                   0x40u
#define LCD_WF8B_BPGLCD32_SHIFT                  6
#define LCD_WF8B_BPGLCD33_MASK                   0x40u
#define LCD_WF8B_BPGLCD33_SHIFT                  6
#define LCD_WF8B_BPGLCD42_MASK                   0x40u
#define LCD_WF8B_BPGLCD42_SHIFT                  6
#define LCD_WF8B_BPGLCD34_MASK                   0x40u
#define LCD_WF8B_BPGLCD34_SHIFT                  6
#define LCD_WF8B_BPGLCD11_MASK                   0x40u
#define LCD_WF8B_BPGLCD11_SHIFT                  6
#define LCD_WF8B_BPGLCD35_MASK                   0x40u
#define LCD_WF8B_BPGLCD35_SHIFT                  6
#define LCD_WF8B_BPGLCD12_MASK                   0x40u
#define LCD_WF8B_BPGLCD12_SHIFT                  6
#define LCD_WF8B_BPGLCD41_MASK                   0x40u
#define LCD_WF8B_BPGLCD41_SHIFT                  6
#define LCD_WF8B_BPGLCD36_MASK                   0x40u
#define LCD_WF8B_BPGLCD36_SHIFT                  6
#define LCD_WF8B_BPGLCD3_MASK                    0x40u
#define LCD_WF8B_BPGLCD3_SHIFT                   6
#define LCD_WF8B_BPGLCD37_MASK                   0x40u
#define LCD_WF8B_BPGLCD37_SHIFT                  6
#define LCD_WF8B_BPGLCD40_MASK                   0x40u
#define LCD_WF8B_BPGLCD40_SHIFT                  6
#define LCD_WF8B_BPGLCD38_MASK                   0x40u
#define LCD_WF8B_BPGLCD38_SHIFT                  6
#define LCD_WF8B_BPGLCD39_MASK                   0x40u
#define LCD_WF8B_BPGLCD39_SHIFT                  6
#define LCD_WF8B_BPHLCD63_MASK                   0x80u
#define LCD_WF8B_BPHLCD63_SHIFT                  7
#define LCD_WF8B_BPHLCD62_MASK                   0x80u
#define LCD_WF8B_BPHLCD62_SHIFT                  7
#define LCD_WF8B_BPHLCD61_MASK                   0x80u
#define LCD_WF8B_BPHLCD61_SHIFT                  7
#define LCD_WF8B_BPHLCD60_MASK                   0x80u
#define LCD_WF8B_BPHLCD60_SHIFT                  7
#define LCD_WF8B_BPHLCD59_MASK                   0x80u
#define LCD_WF8B_BPHLCD59_SHIFT                  7
#define LCD_WF8B_BPHLCD58_MASK                   0x80u
#define LCD_WF8B_BPHLCD58_SHIFT                  7
#define LCD_WF8B_BPHLCD57_MASK                   0x80u
#define LCD_WF8B_BPHLCD57_SHIFT                  7
#define LCD_WF8B_BPHLCD0_MASK                    0x80u
#define LCD_WF8B_BPHLCD0_SHIFT                   7
#define LCD_WF8B_BPHLCD56_MASK                   0x80u
#define LCD_WF8B_BPHLCD56_SHIFT                  7
#define LCD_WF8B_BPHLCD55_MASK                   0x80u
#define LCD_WF8B_BPHLCD55_SHIFT                  7
#define LCD_WF8B_BPHLCD54_MASK                   0x80u
#define LCD_WF8B_BPHLCD54_SHIFT                  7
#define LCD_WF8B_BPHLCD53_MASK                   0x80u
#define LCD_WF8B_BPHLCD53_SHIFT                  7
#define LCD_WF8B_BPHLCD52_MASK                   0x80u
#define LCD_WF8B_BPHLCD52_SHIFT                  7
#define LCD_WF8B_BPHLCD51_MASK                   0x80u
#define LCD_WF8B_BPHLCD51_SHIFT                  7
#define LCD_WF8B_BPHLCD50_MASK                   0x80u
#define LCD_WF8B_BPHLCD50_SHIFT                  7
#define LCD_WF8B_BPHLCD1_MASK                    0x80u
#define LCD_WF8B_BPHLCD1_SHIFT                   7
#define LCD_WF8B_BPHLCD49_MASK                   0x80u
#define LCD_WF8B_BPHLCD49_SHIFT                  7
#define LCD_WF8B_BPHLCD48_MASK                   0x80u
#define LCD_WF8B_BPHLCD48_SHIFT                  7
#define LCD_WF8B_BPHLCD47_MASK                   0x80u
#define LCD_WF8B_BPHLCD47_SHIFT                  7
#define LCD_WF8B_BPHLCD46_MASK                   0x80u
#define LCD_WF8B_BPHLCD46_SHIFT                  7
#define LCD_WF8B_BPHLCD45_MASK                   0x80u
#define LCD_WF8B_BPHLCD45_SHIFT                  7
#define LCD_WF8B_BPHLCD44_MASK                   0x80u
#define LCD_WF8B_BPHLCD44_SHIFT                  7
#define LCD_WF8B_BPHLCD43_MASK                   0x80u
#define LCD_WF8B_BPHLCD43_SHIFT                  7
#define LCD_WF8B_BPHLCD2_MASK                    0x80u
#define LCD_WF8B_BPHLCD2_SHIFT                   7
#define LCD_WF8B_BPHLCD42_MASK                   0x80u
#define LCD_WF8B_BPHLCD42_SHIFT                  7
#define LCD_WF8B_BPHLCD41_MASK                   0x80u
#define LCD_WF8B_BPHLCD41_SHIFT                  7
#define LCD_WF8B_BPHLCD40_MASK                   0x80u
#define LCD_WF8B_BPHLCD40_SHIFT                  7
#define LCD_WF8B_BPHLCD39_MASK                   0x80u
#define LCD_WF8B_BPHLCD39_SHIFT                  7
#define LCD_WF8B_BPHLCD38_MASK                   0x80u
#define LCD_WF8B_BPHLCD38_SHIFT                  7
#define LCD_WF8B_BPHLCD37_MASK                   0x80u
#define LCD_WF8B_BPHLCD37_SHIFT                  7
#define LCD_WF8B_BPHLCD36_MASK                   0x80u
#define LCD_WF8B_BPHLCD36_SHIFT                  7
#define LCD_WF8B_BPHLCD3_MASK                    0x80u
#define LCD_WF8B_BPHLCD3_SHIFT                   7
#define LCD_WF8B_BPHLCD35_MASK                   0x80u
#define LCD_WF8B_BPHLCD35_SHIFT                  7
#define LCD_WF8B_BPHLCD34_MASK                   0x80u
#define LCD_WF8B_BPHLCD34_SHIFT                  7
#define LCD_WF8B_BPHLCD33_MASK                   0x80u
#define LCD_WF8B_BPHLCD33_SHIFT                  7
#define LCD_WF8B_BPHLCD32_MASK                   0x80u
#define LCD_WF8B_BPHLCD32_SHIFT                  7
#define LCD_WF8B_BPHLCD31_MASK                   0x80u
#define LCD_WF8B_BPHLCD31_SHIFT                  7
#define LCD_WF8B_BPHLCD30_MASK                   0x80u
#define LCD_WF8B_BPHLCD30_SHIFT                  7
#define LCD_WF8B_BPHLCD29_MASK                   0x80u
#define LCD_WF8B_BPHLCD29_SHIFT                  7
#define LCD_WF8B_BPHLCD4_MASK                    0x80u
#define LCD_WF8B_BPHLCD4_SHIFT                   7
#define LCD_WF8B_BPHLCD28_MASK                   0x80u
#define LCD_WF8B_BPHLCD28_SHIFT                  7
#define LCD_WF8B_BPHLCD27_MASK                   0x80u
#define LCD_WF8B_BPHLCD27_SHIFT                  7
#define LCD_WF8B_BPHLCD26_MASK                   0x80u
#define LCD_WF8B_BPHLCD26_SHIFT                  7
#define LCD_WF8B_BPHLCD25_MASK                   0x80u
#define LCD_WF8B_BPHLCD25_SHIFT                  7
#define LCD_WF8B_BPHLCD24_MASK                   0x80u
#define LCD_WF8B_BPHLCD24_SHIFT                  7
#define LCD_WF8B_BPHLCD23_MASK                   0x80u
#define LCD_WF8B_BPHLCD23_SHIFT                  7
#define LCD_WF8B_BPHLCD22_MASK                   0x80u
#define LCD_WF8B_BPHLCD22_SHIFT                  7
#define LCD_WF8B_BPHLCD5_MASK                    0x80u
#define LCD_WF8B_BPHLCD5_SHIFT                   7
#define LCD_WF8B_BPHLCD21_MASK                   0x80u
#define LCD_WF8B_BPHLCD21_SHIFT                  7
#define LCD_WF8B_BPHLCD20_MASK                   0x80u
#define LCD_WF8B_BPHLCD20_SHIFT                  7
#define LCD_WF8B_BPHLCD19_MASK                   0x80u
#define LCD_WF8B_BPHLCD19_SHIFT                  7
#define LCD_WF8B_BPHLCD18_MASK                   0x80u
#define LCD_WF8B_BPHLCD18_SHIFT                  7
#define LCD_WF8B_BPHLCD17_MASK                   0x80u
#define LCD_WF8B_BPHLCD17_SHIFT                  7
#define LCD_WF8B_BPHLCD16_MASK                   0x80u
#define LCD_WF8B_BPHLCD16_SHIFT                  7
#define LCD_WF8B_BPHLCD15_MASK                   0x80u
#define LCD_WF8B_BPHLCD15_SHIFT                  7
#define LCD_WF8B_BPHLCD6_MASK                    0x80u
#define LCD_WF8B_BPHLCD6_SHIFT                   7
#define LCD_WF8B_BPHLCD14_MASK                   0x80u
#define LCD_WF8B_BPHLCD14_SHIFT                  7
#define LCD_WF8B_BPHLCD13_MASK                   0x80u
#define LCD_WF8B_BPHLCD13_SHIFT                  7
#define LCD_WF8B_BPHLCD12_MASK                   0x80u
#define LCD_WF8B_BPHLCD12_SHIFT                  7
#define LCD_WF8B_BPHLCD11_MASK                   0x80u
#define LCD_WF8B_BPHLCD11_SHIFT                  7
#define LCD_WF8B_BPHLCD10_MASK                   0x80u
#define LCD_WF8B_BPHLCD10_SHIFT                  7
#define LCD_WF8B_BPHLCD9_MASK                    0x80u
#define LCD_WF8B_BPHLCD9_SHIFT                   7
#define LCD_WF8B_BPHLCD8_MASK                    0x80u
#define LCD_WF8B_BPHLCD8_SHIFT                   7
#define LCD_WF8B_BPHLCD7_MASK                    0x80u
#define LCD_WF8B_BPHLCD7_SHIFT                   7

/*!
 * @}
 */ /* end of group LCD_Register_Masks */


/* LCD - Peripheral instance base addresses */
/** Peripheral LCD base address */
#define LCD_BASE                                 (0x40053000u)
/** Peripheral LCD base pointer */
#define LCD                                      ((LCD_Type *)LCD_BASE)
/** Array initializer of LCD peripheral base pointers */
#define LCD_BASES                                { LCD }

/*!
 * @}
 */ /* end of group LCD_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- LLWU Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup LLWU_Peripheral_Access_Layer LLWU Peripheral Access Layer
 * @{
 */

/** LLWU - Register Layout Typedef */
typedef struct {
        __IO uint8_t PE1;                                /**< LLWU Pin Enable 1 register, offset: 0x0 */
        __IO uint8_t PE2;                                /**< LLWU Pin Enable 2 register, offset: 0x1 */
        __IO uint8_t PE3;                                /**< LLWU Pin Enable 3 register, offset: 0x2 */
        __IO uint8_t PE4;                                /**< LLWU Pin Enable 4 register, offset: 0x3 */
        __IO uint8_t ME;                                 /**< LLWU Module Enable register, offset: 0x4 */
        __IO uint8_t F1;                                 /**< LLWU Flag 1 register, offset: 0x5 */
        __IO uint8_t F2;                                 /**< LLWU Flag 2 register, offset: 0x6 */
        __I  uint8_t F3;                                 /**< LLWU Flag 3 register, offset: 0x7 */
        __IO uint8_t FILT1;                              /**< LLWU Pin Filter 1 register, offset: 0x8 */
        __IO uint8_t FILT2;                              /**< LLWU Pin Filter 2 register, offset: 0x9 */
} LLWU_Type;

/* ----------------------------------------------------------------------------
   -- LLWU Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup LLWU_Register_Masks LLWU Register Masks
 * @{
 */

/* PE1 Bit Fields */
#define LLWU_PE1_WUPE0_MASK                      0x3u
#define LLWU_PE1_WUPE0_SHIFT                     0
#define LLWU_PE1_WUPE0(x)                        (((uint8_t)(((uint8_t)(x))<<LLWU_PE1_WUPE0_SHIFT))&LLWU_PE1_WUPE0_MASK)
#define LLWU_PE1_WUPE1_MASK                      0xCu
#define LLWU_PE1_WUPE1_SHIFT                     2
#define LLWU_PE1_WUPE1(x)                        (((uint8_t)(((uint8_t)(x))<<LLWU_PE1_WUPE1_SHIFT))&LLWU_PE1_WUPE1_MASK)
#define LLWU_PE1_WUPE2_MASK                      0x30u
#define LLWU_PE1_WUPE2_SHIFT                     4
#define LLWU_PE1_WUPE2(x)                        (((uint8_t)(((uint8_t)(x))<<LLWU_PE1_WUPE2_SHIFT))&LLWU_PE1_WUPE2_MASK)
#define LLWU_PE1_WUPE3_MASK                      0xC0u
#define LLWU_PE1_WUPE3_SHIFT                     6
#define LLWU_PE1_WUPE3(x)                        (((uint8_t)(((uint8_t)(x))<<LLWU_PE1_WUPE3_SHIFT))&LLWU_PE1_WUPE3_MASK)
/* PE2 Bit Fields */
#define LLWU_PE2_WUPE4_MASK                      0x3u
#define LLWU_PE2_WUPE4_SHIFT                     0
#define LLWU_PE2_WUPE4(x)                        (((uint8_t)(((uint8_t)(x))<<LLWU_PE2_WUPE4_SHIFT))&LLWU_PE2_WUPE4_MASK)
#define LLWU_PE2_WUPE5_MASK                      0xCu
#define LLWU_PE2_WUPE5_SHIFT                     2
#define LLWU_PE2_WUPE5(x)                        (((uint8_t)(((uint8_t)(x))<<LLWU_PE2_WUPE5_SHIFT))&LLWU_PE2_WUPE5_MASK)
#define LLWU_PE2_WUPE6_MASK                      0x30u
#define LLWU_PE2_WUPE6_SHIFT                     4
#define LLWU_PE2_WUPE6(x)                        (((uint8_t)(((uint8_t)(x))<<LLWU_PE2_WUPE6_SHIFT))&LLWU_PE2_WUPE6_MASK)
#define LLWU_PE2_WUPE7_MASK                      0xC0u
#define LLWU_PE2_WUPE7_SHIFT                     6
#define LLWU_PE2_WUPE7(x)                        (((uint8_t)(((uint8_t)(x))<<LLWU_PE2_WUPE7_SHIFT))&LLWU_PE2_WUPE7_MASK)
/* PE3 Bit Fields */
#define LLWU_PE3_WUPE8_MASK                      0x3u
#define LLWU_PE3_WUPE8_SHIFT                     0
#define LLWU_PE3_WUPE8(x)                        (((uint8_t)(((uint8_t)(x))<<LLWU_PE3_WUPE8_SHIFT))&LLWU_PE3_WUPE8_MASK)
#define LLWU_PE3_WUPE9_MASK                      0xCu
#define LLWU_PE3_WUPE9_SHIFT                     2
#define LLWU_PE3_WUPE9(x)                        (((uint8_t)(((uint8_t)(x))<<LLWU_PE3_WUPE9_SHIFT))&LLWU_PE3_WUPE9_MASK)
#define LLWU_PE3_WUPE10_MASK                     0x30u
#define LLWU_PE3_WUPE10_SHIFT                    4
#define LLWU_PE3_WUPE10(x)                       (((uint8_t)(((uint8_t)(x))<<LLWU_PE3_WUPE10_SHIFT))&LLWU_PE3_WUPE10_MASK)
#define LLWU_PE3_WUPE11_MASK                     0xC0u
#define LLWU_PE3_WUPE11_SHIFT                    6
#define LLWU_PE3_WUPE11(x)                       (((uint8_t)(((uint8_t)(x))<<LLWU_PE3_WUPE11_SHIFT))&LLWU_PE3_WUPE11_MASK)
/* PE4 Bit Fields */
#define LLWU_PE4_WUPE12_MASK                     0x3u
#define LLWU_PE4_WUPE12_SHIFT                    0
#define LLWU_PE4_WUPE12(x)                       (((uint8_t)(((uint8_t)(x))<<LLWU_PE4_WUPE12_SHIFT))&LLWU_PE4_WUPE12_MASK)
#define LLWU_PE4_WUPE13_MASK                     0xCu
#define LLWU_PE4_WUPE13_SHIFT                    2
#define LLWU_PE4_WUPE13(x)                       (((uint8_t)(((uint8_t)(x))<<LLWU_PE4_WUPE13_SHIFT))&LLWU_PE4_WUPE13_MASK)
#define LLWU_PE4_WUPE14_MASK                     0x30u
#define LLWU_PE4_WUPE14_SHIFT                    4
#define LLWU_PE4_WUPE14(x)                       (((uint8_t)(((uint8_t)(x))<<LLWU_PE4_WUPE14_SHIFT))&LLWU_PE4_WUPE14_MASK)
#define LLWU_PE4_WUPE15_MASK                     0xC0u
#define LLWU_PE4_WUPE15_SHIFT                    6
#define LLWU_PE4_WUPE15(x)                       (((uint8_t)(((uint8_t)(x))<<LLWU_PE4_WUPE15_SHIFT))&LLWU_PE4_WUPE15_MASK)
/* ME Bit Fields */
#define LLWU_ME_WUME0_MASK                       0x1u
#define LLWU_ME_WUME0_SHIFT                      0
#define LLWU_ME_WUME1_MASK                       0x2u
#define LLWU_ME_WUME1_SHIFT                      1
#define LLWU_ME_WUME2_MASK                       0x4u
#define LLWU_ME_WUME2_SHIFT                      2
#define LLWU_ME_WUME3_MASK                       0x8u
#define LLWU_ME_WUME3_SHIFT                      3
#define LLWU_ME_WUME4_MASK                       0x10u
#define LLWU_ME_WUME4_SHIFT                      4
#define LLWU_ME_WUME5_MASK                       0x20u
#define LLWU_ME_WUME5_SHIFT                      5
#define LLWU_ME_WUME6_MASK                       0x40u
#define LLWU_ME_WUME6_SHIFT                      6
#define LLWU_ME_WUME7_MASK                       0x80u
#define LLWU_ME_WUME7_SHIFT                      7
/* F1 Bit Fields */
#define LLWU_F1_WUF0_MASK                        0x1u
#define LLWU_F1_WUF0_SHIFT                       0
#define LLWU_F1_WUF1_MASK                        0x2u
#define LLWU_F1_WUF1_SHIFT                       1
#define LLWU_F1_WUF2_MASK                        0x4u
#define LLWU_F1_WUF2_SHIFT                       2
#define LLWU_F1_WUF3_MASK                        0x8u
#define LLWU_F1_WUF3_SHIFT                       3
#define LLWU_F1_WUF4_MASK                        0x10u
#define LLWU_F1_WUF4_SHIFT                       4
#define LLWU_F1_WUF5_MASK                        0x20u
#define LLWU_F1_WUF5_SHIFT                       5
#define LLWU_F1_WUF6_MASK                        0x40u
#define LLWU_F1_WUF6_SHIFT                       6
#define LLWU_F1_WUF7_MASK                        0x80u
#define LLWU_F1_WUF7_SHIFT                       7
/* F2 Bit Fields */
#define LLWU_F2_WUF8_MASK                        0x1u
#define LLWU_F2_WUF8_SHIFT                       0
#define LLWU_F2_WUF9_MASK                        0x2u
#define LLWU_F2_WUF9_SHIFT                       1
#define LLWU_F2_WUF10_MASK                       0x4u
#define LLWU_F2_WUF10_SHIFT                      2
#define LLWU_F2_WUF11_MASK                       0x8u
#define LLWU_F2_WUF11_SHIFT                      3
#define LLWU_F2_WUF12_MASK                       0x10u
#define LLWU_F2_WUF12_SHIFT                      4
#define LLWU_F2_WUF13_MASK                       0x20u
#define LLWU_F2_WUF13_SHIFT                      5
#define LLWU_F2_WUF14_MASK                       0x40u
#define LLWU_F2_WUF14_SHIFT                      6
#define LLWU_F2_WUF15_MASK                       0x80u
#define LLWU_F2_WUF15_SHIFT                      7
/* F3 Bit Fields */
#define LLWU_F3_MWUF0_MASK                       0x1u
#define LLWU_F3_MWUF0_SHIFT                      0
#define LLWU_F3_MWUF1_MASK                       0x2u
#define LLWU_F3_MWUF1_SHIFT                      1
#define LLWU_F3_MWUF2_MASK                       0x4u
#define LLWU_F3_MWUF2_SHIFT                      2
#define LLWU_F3_MWUF3_MASK                       0x8u
#define LLWU_F3_MWUF3_SHIFT                      3
#define LLWU_F3_MWUF4_MASK                       0x10u
#define LLWU_F3_MWUF4_SHIFT                      4
#define LLWU_F3_MWUF5_MASK                       0x20u
#define LLWU_F3_MWUF5_SHIFT                      5
#define LLWU_F3_MWUF6_MASK                       0x40u
#define LLWU_F3_MWUF6_SHIFT                      6
#define LLWU_F3_MWUF7_MASK                       0x80u
#define LLWU_F3_MWUF7_SHIFT                      7
/* FILT1 Bit Fields */
#define LLWU_FILT1_FILTSEL_MASK                  0xFu
#define LLWU_FILT1_FILTSEL_SHIFT                 0
#define LLWU_FILT1_FILTSEL(x)                    (((uint8_t)(((uint8_t)(x))<<LLWU_FILT1_FILTSEL_SHIFT))&LLWU_FILT1_FILTSEL_MASK)
#define LLWU_FILT1_FILTE_MASK                    0x60u
#define LLWU_FILT1_FILTE_SHIFT                   5
#define LLWU_FILT1_FILTE(x)                      (((uint8_t)(((uint8_t)(x))<<LLWU_FILT1_FILTE_SHIFT))&LLWU_FILT1_FILTE_MASK)
#define LLWU_FILT1_FILTF_MASK                    0x80u
#define LLWU_FILT1_FILTF_SHIFT                   7
/* FILT2 Bit Fields */
#define LLWU_FILT2_FILTSEL_MASK                  0xFu
#define LLWU_FILT2_FILTSEL_SHIFT                 0
#define LLWU_FILT2_FILTSEL(x)                    (((uint8_t)(((uint8_t)(x))<<LLWU_FILT2_FILTSEL_SHIFT))&LLWU_FILT2_FILTSEL_MASK)
#define LLWU_FILT2_FILTE_MASK                    0x60u
#define LLWU_FILT2_FILTE_SHIFT                   5
#define LLWU_FILT2_FILTE(x)                      (((uint8_t)(((uint8_t)(x))<<LLWU_FILT2_FILTE_SHIFT))&LLWU_FILT2_FILTE_MASK)
#define LLWU_FILT2_FILTF_MASK                    0x80u
#define LLWU_FILT2_FILTF_SHIFT                   7

/*!
 * @}
 */ /* end of group LLWU_Register_Masks */


/* LLWU - Peripheral instance base addresses */
/** Peripheral LLWU base address */
#define LLWU_BASE                                (0x4007C000u)
/** Peripheral LLWU base pointer */
#define LLWU                                     ((LLWU_Type *)LLWU_BASE)
/** Array initializer of LLWU peripheral base pointers */
#define LLWU_BASES                               { LLWU }

/*!
 * @}
 */ /* end of group LLWU_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- LPTMR Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup LPTMR_Peripheral_Access_Layer LPTMR Peripheral Access Layer
 * @{
 */

/** LPTMR - Register Layout Typedef */
typedef struct {
        __IO uint32_t CSR;                               /**< Low Power Timer Control Status Register, offset: 0x0 */
        __IO uint32_t PSR;                               /**< Low Power Timer Prescale Register, offset: 0x4 */
        __IO uint32_t CMR;                               /**< Low Power Timer Compare Register, offset: 0x8 */
        __I  uint32_t CNR;                               /**< Low Power Timer Counter Register, offset: 0xC */
} LPTMR_Type;

/* ----------------------------------------------------------------------------
   -- LPTMR Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup LPTMR_Register_Masks LPTMR Register Masks
 * @{
 */

/* CSR Bit Fields */
#define LPTMR_CSR_TEN_MASK                       0x1u
#define LPTMR_CSR_TEN_SHIFT                      0
#define LPTMR_CSR_TMS_MASK                       0x2u
#define LPTMR_CSR_TMS_SHIFT                      1
#define LPTMR_CSR_TFC_MASK                       0x4u
#define LPTMR_CSR_TFC_SHIFT                      2
#define LPTMR_CSR_TPP_MASK                       0x8u
#define LPTMR_CSR_TPP_SHIFT                      3
#define LPTMR_CSR_TPS_MASK                       0x30u
#define LPTMR_CSR_TPS_SHIFT                      4
#define LPTMR_CSR_TPS(x)                         (((uint32_t)(((uint32_t)(x))<<LPTMR_CSR_TPS_SHIFT))&LPTMR_CSR_TPS_MASK)
#define LPTMR_CSR_TIE_MASK                       0x40u
#define LPTMR_CSR_TIE_SHIFT                      6
#define LPTMR_CSR_TCF_MASK                       0x80u
#define LPTMR_CSR_TCF_SHIFT                      7
/* PSR Bit Fields */
#define LPTMR_PSR_PCS_MASK                       0x3u
#define LPTMR_PSR_PCS_SHIFT                      0
#define LPTMR_PSR_PCS(x)                         (((uint32_t)(((uint32_t)(x))<<LPTMR_PSR_PCS_SHIFT))&LPTMR_PSR_PCS_MASK)
#define LPTMR_PSR_PBYP_MASK                      0x4u
#define LPTMR_PSR_PBYP_SHIFT                     2
#define LPTMR_PSR_PRESCALE_MASK                  0x78u
#define LPTMR_PSR_PRESCALE_SHIFT                 3
#define LPTMR_PSR_PRESCALE(x)                    (((uint32_t)(((uint32_t)(x))<<LPTMR_PSR_PRESCALE_SHIFT))&LPTMR_PSR_PRESCALE_MASK)
/* CMR Bit Fields */
#define LPTMR_CMR_COMPARE_MASK                   0xFFFFu
#define LPTMR_CMR_COMPARE_SHIFT                  0
#define LPTMR_CMR_COMPARE(x)                     (((uint32_t)(((uint32_t)(x))<<LPTMR_CMR_COMPARE_SHIFT))&LPTMR_CMR_COMPARE_MASK)
/* CNR Bit Fields */
#define LPTMR_CNR_COUNTER_MASK                   0xFFFFu
#define LPTMR_CNR_COUNTER_SHIFT                  0
#define LPTMR_CNR_COUNTER(x)                     (((uint32_t)(((uint32_t)(x))<<LPTMR_CNR_COUNTER_SHIFT))&LPTMR_CNR_COUNTER_MASK)

/*!
 * @}
 */ /* end of group LPTMR_Register_Masks */


/* LPTMR - Peripheral instance base addresses */
/** Peripheral LPTMR0 base address */
#define LPTMR0_BASE                              (0x40040000u)
/** Peripheral LPTMR0 base pointer */
#define LPTMR0                                   ((LPTMR_Type *)LPTMR0_BASE)
/** Array initializer of LPTMR peripheral base pointers */
#define LPTMR_BASES                              { LPTMR0 }

/*!
 * @}
 */ /* end of group LPTMR_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- MCG Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup MCG_Peripheral_Access_Layer MCG Peripheral Access Layer
 * @{
 */

/** MCG - Register Layout Typedef */
typedef struct {
        __IO uint8_t C1;                                 /**< MCG Control 1 Register, offset: 0x0 */
        __IO uint8_t C2;                                 /**< MCG Control 2 Register, offset: 0x1 */
        __IO uint8_t C3;                                 /**< MCG Control 3 Register, offset: 0x2 */
        __IO uint8_t C4;                                 /**< MCG Control 4 Register, offset: 0x3 */
        __IO uint8_t C5;                                 /**< MCG Control 5 Register, offset: 0x4 */
        __IO uint8_t C6;                                 /**< MCG Control 6 Register, offset: 0x5 */
        __I  uint8_t S;                                  /**< MCG Status Register, offset: 0x6 */
        uint8_t RESERVED_0[1];
        __IO uint8_t SC;                                 /**< MCG Status and Control Register, offset: 0x8 */
        uint8_t RESERVED_1[1];
        __IO uint8_t ATCVH;                              /**< MCG Auto Trim Compare Value High Register, offset: 0xA */
        __IO uint8_t ATCVL;                              /**< MCG Auto Trim Compare Value Low Register, offset: 0xB */
        __I  uint8_t C7;                                 /**< MCG Control 7 Register, offset: 0xC */
        __IO uint8_t C8;                                 /**< MCG Control 8 Register, offset: 0xD */
        __I  uint8_t C9;                                 /**< MCG Control 9 Register, offset: 0xE */
        __I  uint8_t C10;                                /**< MCG Control 10 Register, offset: 0xF */
} MCG_Type;

/* ----------------------------------------------------------------------------
   -- MCG Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup MCG_Register_Masks MCG Register Masks
 * @{
 */

/* C1 Bit Fields */
#define MCG_C1_IREFSTEN_MASK                     0x1u
#define MCG_C1_IREFSTEN_SHIFT                    0
#define MCG_C1_IRCLKEN_MASK                      0x2u
#define MCG_C1_IRCLKEN_SHIFT                     1
#define MCG_C1_IREFS_MASK                        0x4u
#define MCG_C1_IREFS_SHIFT                       2
#define MCG_C1_FRDIV_MASK                        0x38u
#define MCG_C1_FRDIV_SHIFT                       3
#define MCG_C1_FRDIV(x)                          (((uint8_t)(((uint8_t)(x))<<MCG_C1_FRDIV_SHIFT))&MCG_C1_FRDIV_MASK)
#define MCG_C1_CLKS_MASK                         0xC0u
#define MCG_C1_CLKS_SHIFT                        6
#define MCG_C1_CLKS(x)                           (((uint8_t)(((uint8_t)(x))<<MCG_C1_CLKS_SHIFT))&MCG_C1_CLKS_MASK)
/* C2 Bit Fields */
#define MCG_C2_IRCS_MASK                         0x1u
#define MCG_C2_IRCS_SHIFT                        0
#define MCG_C2_LP_MASK                           0x2u
#define MCG_C2_LP_SHIFT                          1
#define MCG_C2_EREFS0_MASK                       0x4u
#define MCG_C2_EREFS0_SHIFT                      2
#define MCG_C2_HGO0_MASK                         0x8u
#define MCG_C2_HGO0_SHIFT                        3
#define MCG_C2_RANGE0_MASK                       0x30u
#define MCG_C2_RANGE0_SHIFT                      4
#define MCG_C2_RANGE0(x)                         (((uint8_t)(((uint8_t)(x))<<MCG_C2_RANGE0_SHIFT))&MCG_C2_RANGE0_MASK)
#define MCG_C2_FCFTRIM_MASK                      0x40u
#define MCG_C2_FCFTRIM_SHIFT                     6
#define MCG_C2_LOCRE0_MASK                       0x80u
#define MCG_C2_LOCRE0_SHIFT                      7
/* C3 Bit Fields */
#define MCG_C3_SCTRIM_MASK                       0xFFu
#define MCG_C3_SCTRIM_SHIFT                      0
#define MCG_C3_SCTRIM(x)                         (((uint8_t)(((uint8_t)(x))<<MCG_C3_SCTRIM_SHIFT))&MCG_C3_SCTRIM_MASK)
/* C4 Bit Fields */
#define MCG_C4_SCFTRIM_MASK                      0x1u
#define MCG_C4_SCFTRIM_SHIFT                     0
#define MCG_C4_FCTRIM_MASK                       0x1Eu
#define MCG_C4_FCTRIM_SHIFT                      1
#define MCG_C4_FCTRIM(x)                         (((uint8_t)(((uint8_t)(x))<<MCG_C4_FCTRIM_SHIFT))&MCG_C4_FCTRIM_MASK)
#define MCG_C4_DRST_DRS_MASK                     0x60u
#define MCG_C4_DRST_DRS_SHIFT                    5
#define MCG_C4_DRST_DRS(x)                       (((uint8_t)(((uint8_t)(x))<<MCG_C4_DRST_DRS_SHIFT))&MCG_C4_DRST_DRS_MASK)
#define MCG_C4_DMX32_MASK                        0x80u
#define MCG_C4_DMX32_SHIFT                       7
/* C5 Bit Fields */
#define MCG_C5_PRDIV0_MASK                       0x1Fu
#define MCG_C5_PRDIV0_SHIFT                      0
#define MCG_C5_PRDIV0(x)                         (((uint8_t)(((uint8_t)(x))<<MCG_C5_PRDIV0_SHIFT))&MCG_C5_PRDIV0_MASK)
#define MCG_C5_PLLSTEN0_MASK                     0x20u
#define MCG_C5_PLLSTEN0_SHIFT                    5
#define MCG_C5_PLLCLKEN0_MASK                    0x40u
#define MCG_C5_PLLCLKEN0_SHIFT                   6
/* C6 Bit Fields */
#define MCG_C6_VDIV0_MASK                        0x1Fu
#define MCG_C6_VDIV0_SHIFT                       0
#define MCG_C6_VDIV0(x)                          (((uint8_t)(((uint8_t)(x))<<MCG_C6_VDIV0_SHIFT))&MCG_C6_VDIV0_MASK)
#define MCG_C6_CME0_MASK                         0x20u
#define MCG_C6_CME0_SHIFT                        5
#define MCG_C6_PLLS_MASK                         0x40u
#define MCG_C6_PLLS_SHIFT                        6
#define MCG_C6_LOLIE0_MASK                       0x80u
#define MCG_C6_LOLIE0_SHIFT                      7
/* S Bit Fields */
#define MCG_S_IRCST_MASK                         0x1u
#define MCG_S_IRCST_SHIFT                        0
#define MCG_S_OSCINIT0_MASK                      0x2u
#define MCG_S_OSCINIT0_SHIFT                     1
#define MCG_S_CLKST_MASK                         0xCu
#define MCG_S_CLKST_SHIFT                        2
#define MCG_S_CLKST(x)                           (((uint8_t)(((uint8_t)(x))<<MCG_S_CLKST_SHIFT))&MCG_S_CLKST_MASK)
#define MCG_S_IREFST_MASK                        0x10u
#define MCG_S_IREFST_SHIFT                       4
#define MCG_S_PLLST_MASK                         0x20u
#define MCG_S_PLLST_SHIFT                        5
#define MCG_S_LOCK0_MASK                         0x40u
#define MCG_S_LOCK0_SHIFT                        6
#define MCG_S_LOLS_MASK                          0x80u
#define MCG_S_LOLS_SHIFT                         7
/* SC Bit Fields */
#define MCG_SC_LOCS0_MASK                        0x1u
#define MCG_SC_LOCS0_SHIFT                       0
#define MCG_SC_FCRDIV_MASK                       0xEu
#define MCG_SC_FCRDIV_SHIFT                      1
#define MCG_SC_FCRDIV(x)                         (((uint8_t)(((uint8_t)(x))<<MCG_SC_FCRDIV_SHIFT))&MCG_SC_FCRDIV_MASK)
#define MCG_SC_FLTPRSRV_MASK                     0x10u
#define MCG_SC_FLTPRSRV_SHIFT                    4
#define MCG_SC_ATMF_MASK                         0x20u
#define MCG_SC_ATMF_SHIFT                        5
#define MCG_SC_ATMS_MASK                         0x40u
#define MCG_SC_ATMS_SHIFT                        6
#define MCG_SC_ATME_MASK                         0x80u
#define MCG_SC_ATME_SHIFT                        7
/* ATCVH Bit Fields */
#define MCG_ATCVH_ATCVH_MASK                     0xFFu
#define MCG_ATCVH_ATCVH_SHIFT                    0
#define MCG_ATCVH_ATCVH(x)                       (((uint8_t)(((uint8_t)(x))<<MCG_ATCVH_ATCVH_SHIFT))&MCG_ATCVH_ATCVH_MASK)
/* ATCVL Bit Fields */
#define MCG_ATCVL_ATCVL_MASK                     0xFFu
#define MCG_ATCVL_ATCVL_SHIFT                    0
#define MCG_ATCVL_ATCVL(x)                       (((uint8_t)(((uint8_t)(x))<<MCG_ATCVL_ATCVL_SHIFT))&MCG_ATCVL_ATCVL_MASK)
/* C8 Bit Fields */
#define MCG_C8_LOLRE_MASK                        0x40u
#define MCG_C8_LOLRE_SHIFT                       6

/*!
 * @}
 */ /* end of group MCG_Register_Masks */


/* MCG - Peripheral instance base addresses */
/** Peripheral MCG base address */
#define MCG_BASE                                 (0x40064000u)
/** Peripheral MCG base pointer */
#define MCG                                      ((MCG_Type *)MCG_BASE)
/** Array initializer of MCG peripheral base pointers */
#define MCG_BASES                                { MCG }

/*!
 * @}
 */ /* end of group MCG_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- MCM Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup MCM_Peripheral_Access_Layer MCM Peripheral Access Layer
 * @{
 */

/** MCM - Register Layout Typedef */
typedef struct {
        uint8_t RESERVED_0[8];
        __I  uint16_t PLASC;                             /**< Crossbar Switch (AXBS) Slave Configuration, offset: 0x8 */
        __I  uint16_t PLAMC;                             /**< Crossbar Switch (AXBS) Master Configuration, offset: 0xA */
        __IO uint32_t PLACR;                             /**< Platform Control Register, offset: 0xC */
        uint8_t RESERVED_1[48];
        __IO uint32_t CPO;                               /**< Compute Operation Control Register, offset: 0x40 */
} MCM_Type;

/* ----------------------------------------------------------------------------
   -- MCM Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup MCM_Register_Masks MCM Register Masks
 * @{
 */

/* PLASC Bit Fields */
#define MCM_PLASC_ASC_MASK                       0xFFu
#define MCM_PLASC_ASC_SHIFT                      0
#define MCM_PLASC_ASC(x)                         (((uint16_t)(((uint16_t)(x))<<MCM_PLASC_ASC_SHIFT))&MCM_PLASC_ASC_MASK)
/* PLAMC Bit Fields */
#define MCM_PLAMC_AMC_MASK                       0xFFu
#define MCM_PLAMC_AMC_SHIFT                      0
#define MCM_PLAMC_AMC(x)                         (((uint16_t)(((uint16_t)(x))<<MCM_PLAMC_AMC_SHIFT))&MCM_PLAMC_AMC_MASK)
/* PLACR Bit Fields */
#define MCM_PLACR_ARB_MASK                       0x200u
#define MCM_PLACR_ARB_SHIFT                      9
#define MCM_PLACR_CFCC_MASK                      0x400u
#define MCM_PLACR_CFCC_SHIFT                     10
#define MCM_PLACR_DFCDA_MASK                     0x800u
#define MCM_PLACR_DFCDA_SHIFT                    11
#define MCM_PLACR_DFCIC_MASK                     0x1000u
#define MCM_PLACR_DFCIC_SHIFT                    12
#define MCM_PLACR_DFCC_MASK                      0x2000u
#define MCM_PLACR_DFCC_SHIFT                     13
#define MCM_PLACR_EFDS_MASK                      0x4000u
#define MCM_PLACR_EFDS_SHIFT                     14
#define MCM_PLACR_DFCS_MASK                      0x8000u
#define MCM_PLACR_DFCS_SHIFT                     15
#define MCM_PLACR_ESFC_MASK                      0x10000u
#define MCM_PLACR_ESFC_SHIFT                     16
/* CPO Bit Fields */
#define MCM_CPO_CPOREQ_MASK                      0x1u
#define MCM_CPO_CPOREQ_SHIFT                     0
#define MCM_CPO_CPOACK_MASK                      0x2u
#define MCM_CPO_CPOACK_SHIFT                     1
#define MCM_CPO_CPOWOI_MASK                      0x4u
#define MCM_CPO_CPOWOI_SHIFT                     2

/*!
 * @}
 */ /* end of group MCM_Register_Masks */


/* MCM - Peripheral instance base addresses */
/** Peripheral MCM base address */
#define MCM_BASE                                 (0xF0003000u)
/** Peripheral MCM base pointer */
#define MCM                                      ((MCM_Type *)MCM_BASE)
/** Array initializer of MCM peripheral base pointers */
#define MCM_BASES                                { MCM }

/*!
 * @}
 */ /* end of group MCM_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- MTB Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup MTB_Peripheral_Access_Layer MTB Peripheral Access Layer
 * @{
 */

/** MTB - Register Layout Typedef */
typedef struct {
        __IO uint32_t POSITION;                          /**< MTB Position Register, offset: 0x0 */
        __IO uint32_t MASTER;                            /**< MTB Master Register, offset: 0x4 */
        __IO uint32_t FLOW;                              /**< MTB Flow Register, offset: 0x8 */
        __I  uint32_t BASE;                              /**< MTB Base Register, offset: 0xC */
        uint8_t RESERVED_0[3824];
        __I  uint32_t MODECTRL;                          /**< Integration Mode Control Register, offset: 0xF00 */
        uint8_t RESERVED_1[156];
        __I  uint32_t TAGSET;                            /**< Claim TAG Set Register, offset: 0xFA0 */
        __I  uint32_t TAGCLEAR;                          /**< Claim TAG Clear Register, offset: 0xFA4 */
        uint8_t RESERVED_2[8];
        __I  uint32_t LOCKACCESS;                        /**< Lock Access Register, offset: 0xFB0 */
        __I  uint32_t LOCKSTAT;                          /**< Lock Status Register, offset: 0xFB4 */
        __I  uint32_t AUTHSTAT;                          /**< Authentication Status Register, offset: 0xFB8 */
        __I  uint32_t DEVICEARCH;                        /**< Device Architecture Register, offset: 0xFBC */
        uint8_t RESERVED_3[8];
        __I  uint32_t DEVICECFG;                         /**< Device Configuration Register, offset: 0xFC8 */
        __I  uint32_t DEVICETYPID;                       /**< Device Type Identifier Register, offset: 0xFCC */
        __I  uint32_t PERIPHID[8];                       /**< Peripheral ID Register, array offset: 0xFD0, array step: 0x4 */
        __I  uint32_t COMPID[4];                         /**< Component ID Register, array offset: 0xFF0, array step: 0x4 */
} MTB_Type;

/* ----------------------------------------------------------------------------
   -- MTB Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup MTB_Register_Masks MTB Register Masks
 * @{
 */

/* POSITION Bit Fields */
#define MTB_POSITION_WRAP_MASK                   0x4u
#define MTB_POSITION_WRAP_SHIFT                  2
#define MTB_POSITION_POINTER_MASK                0xFFFFFFF8u
#define MTB_POSITION_POINTER_SHIFT               3
#define MTB_POSITION_POINTER(x)                  (((uint32_t)(((uint32_t)(x))<<MTB_POSITION_POINTER_SHIFT))&MTB_POSITION_POINTER_MASK)
/* MASTER Bit Fields */
#define MTB_MASTER_MASK_MASK                     0x1Fu
#define MTB_MASTER_MASK_SHIFT                    0
#define MTB_MASTER_MASK(x)                       (((uint32_t)(((uint32_t)(x))<<MTB_MASTER_MASK_SHIFT))&MTB_MASTER_MASK_MASK)
#define MTB_MASTER_TSTARTEN_MASK                 0x20u
#define MTB_MASTER_TSTARTEN_SHIFT                5
#define MTB_MASTER_TSTOPEN_MASK                  0x40u
#define MTB_MASTER_TSTOPEN_SHIFT                 6
#define MTB_MASTER_SFRWPRIV_MASK                 0x80u
#define MTB_MASTER_SFRWPRIV_SHIFT                7
#define MTB_MASTER_RAMPRIV_MASK                  0x100u
#define MTB_MASTER_RAMPRIV_SHIFT                 8
#define MTB_MASTER_HALTREQ_MASK                  0x200u
#define MTB_MASTER_HALTREQ_SHIFT                 9
#define MTB_MASTER_EN_MASK                       0x80000000u
#define MTB_MASTER_EN_SHIFT                      31
/* FLOW Bit Fields */
#define MTB_FLOW_AUTOSTOP_MASK                   0x1u
#define MTB_FLOW_AUTOSTOP_SHIFT                  0
#define MTB_FLOW_AUTOHALT_MASK                   0x2u
#define MTB_FLOW_AUTOHALT_SHIFT                  1
#define MTB_FLOW_WATERMARK_MASK                  0xFFFFFFF8u
#define MTB_FLOW_WATERMARK_SHIFT                 3
#define MTB_FLOW_WATERMARK(x)                    (((uint32_t)(((uint32_t)(x))<<MTB_FLOW_WATERMARK_SHIFT))&MTB_FLOW_WATERMARK_MASK)
/* BASE Bit Fields */
#define MTB_BASE_BASEADDR_MASK                   0xFFFFFFFFu
#define MTB_BASE_BASEADDR_SHIFT                  0
#define MTB_BASE_BASEADDR(x)                     (((uint32_t)(((uint32_t)(x))<<MTB_BASE_BASEADDR_SHIFT))&MTB_BASE_BASEADDR_MASK)
/* MODECTRL Bit Fields */
#define MTB_MODECTRL_MODECTRL_MASK               0xFFFFFFFFu
#define MTB_MODECTRL_MODECTRL_SHIFT              0
#define MTB_MODECTRL_MODECTRL(x)                 (((uint32_t)(((uint32_t)(x))<<MTB_MODECTRL_MODECTRL_SHIFT))&MTB_MODECTRL_MODECTRL_MASK)
/* TAGSET Bit Fields */
#define MTB_TAGSET_TAGSET_MASK                   0xFFFFFFFFu
#define MTB_TAGSET_TAGSET_SHIFT                  0
#define MTB_TAGSET_TAGSET(x)                     (((uint32_t)(((uint32_t)(x))<<MTB_TAGSET_TAGSET_SHIFT))&MTB_TAGSET_TAGSET_MASK)
/* TAGCLEAR Bit Fields */
#define MTB_TAGCLEAR_TAGCLEAR_MASK               0xFFFFFFFFu
#define MTB_TAGCLEAR_TAGCLEAR_SHIFT              0
#define MTB_TAGCLEAR_TAGCLEAR(x)                 (((uint32_t)(((uint32_t)(x))<<MTB_TAGCLEAR_TAGCLEAR_SHIFT))&MTB_TAGCLEAR_TAGCLEAR_MASK)
/* LOCKACCESS Bit Fields */
#define MTB_LOCKACCESS_LOCKACCESS_MASK           0xFFFFFFFFu
#define MTB_LOCKACCESS_LOCKACCESS_SHIFT          0
#define MTB_LOCKACCESS_LOCKACCESS(x)             (((uint32_t)(((uint32_t)(x))<<MTB_LOCKACCESS_LOCKACCESS_SHIFT))&MTB_LOCKACCESS_LOCKACCESS_MASK)
/* LOCKSTAT Bit Fields */
#define MTB_LOCKSTAT_LOCKSTAT_MASK               0xFFFFFFFFu
#define MTB_LOCKSTAT_LOCKSTAT_SHIFT              0
#define MTB_LOCKSTAT_LOCKSTAT(x)                 (((uint32_t)(((uint32_t)(x))<<MTB_LOCKSTAT_LOCKSTAT_SHIFT))&MTB_LOCKSTAT_LOCKSTAT_MASK)
/* AUTHSTAT Bit Fields */
#define MTB_AUTHSTAT_BIT0_MASK                   0x1u
#define MTB_AUTHSTAT_BIT0_SHIFT                  0
#define MTB_AUTHSTAT_BIT1_MASK                   0x2u
#define MTB_AUTHSTAT_BIT1_SHIFT                  1
#define MTB_AUTHSTAT_BIT2_MASK                   0x4u
#define MTB_AUTHSTAT_BIT2_SHIFT                  2
#define MTB_AUTHSTAT_BIT3_MASK                   0x8u
#define MTB_AUTHSTAT_BIT3_SHIFT                  3
/* DEVICEARCH Bit Fields */
#define MTB_DEVICEARCH_DEVICEARCH_MASK           0xFFFFFFFFu
#define MTB_DEVICEARCH_DEVICEARCH_SHIFT          0
#define MTB_DEVICEARCH_DEVICEARCH(x)             (((uint32_t)(((uint32_t)(x))<<MTB_DEVICEARCH_DEVICEARCH_SHIFT))&MTB_DEVICEARCH_DEVICEARCH_MASK)
/* DEVICECFG Bit Fields */
#define MTB_DEVICECFG_DEVICECFG_MASK             0xFFFFFFFFu
#define MTB_DEVICECFG_DEVICECFG_SHIFT            0
#define MTB_DEVICECFG_DEVICECFG(x)               (((uint32_t)(((uint32_t)(x))<<MTB_DEVICECFG_DEVICECFG_SHIFT))&MTB_DEVICECFG_DEVICECFG_MASK)
/* DEVICETYPID Bit Fields */
#define MTB_DEVICETYPID_DEVICETYPID_MASK         0xFFFFFFFFu
#define MTB_DEVICETYPID_DEVICETYPID_SHIFT        0
#define MTB_DEVICETYPID_DEVICETYPID(x)           (((uint32_t)(((uint32_t)(x))<<MTB_DEVICETYPID_DEVICETYPID_SHIFT))&MTB_DEVICETYPID_DEVICETYPID_MASK)
/* PERIPHID Bit Fields */
#define MTB_PERIPHID_PERIPHID_MASK               0xFFFFFFFFu
#define MTB_PERIPHID_PERIPHID_SHIFT              0
#define MTB_PERIPHID_PERIPHID(x)                 (((uint32_t)(((uint32_t)(x))<<MTB_PERIPHID_PERIPHID_SHIFT))&MTB_PERIPHID_PERIPHID_MASK)
/* COMPID Bit Fields */
#define MTB_COMPID_COMPID_MASK                   0xFFFFFFFFu
#define MTB_COMPID_COMPID_SHIFT                  0
#define MTB_COMPID_COMPID(x)                     (((uint32_t)(((uint32_t)(x))<<MTB_COMPID_COMPID_SHIFT))&MTB_COMPID_COMPID_MASK)

/*!
 * @}
 */ /* end of group MTB_Register_Masks */


/* MTB - Peripheral instance base addresses */
/** Peripheral MTB base address */
#define MTB_BASE                                 (0xF0000000u)
/** Peripheral MTB base pointer */
#define MTB                                      ((MTB_Type *)MTB_BASE)
/** Array initializer of MTB peripheral base pointers */
#define MTB_BASES                                { MTB }

/*!
 * @}
 */ /* end of group MTB_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- MTBDWT Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup MTBDWT_Peripheral_Access_Layer MTBDWT Peripheral Access Layer
 * @{
 */

/** MTBDWT - Register Layout Typedef */
typedef struct {
        __I  uint32_t CTRL;                              /**< MTB DWT Control Register, offset: 0x0 */
        uint8_t RESERVED_0[28];
        struct {                                         /* offset: 0x20, array step: 0x10 */
                __IO uint32_t COMP;                              /**< MTB_DWT Comparator Register, array offset: 0x20, array step: 0x10 */
                __IO uint32_t MASK;                              /**< MTB_DWT Comparator Mask Register, array offset: 0x24, array step: 0x10 */
                __IO uint32_t FCT;                               /**< MTB_DWT Comparator Function Register 0..MTB_DWT Comparator Function Register 1, array offset: 0x28, array step: 0x10 */
                uint8_t RESERVED_0[4];
        } COMPARATOR[2];
        uint8_t RESERVED_1[448];
        __IO uint32_t TBCTRL;                            /**< MTB_DWT Trace Buffer Control Register, offset: 0x200 */
        uint8_t RESERVED_2[3524];
        __I  uint32_t DEVICECFG;                         /**< Device Configuration Register, offset: 0xFC8 */
        __I  uint32_t DEVICETYPID;                       /**< Device Type Identifier Register, offset: 0xFCC */
        __I  uint32_t PERIPHID[8];                       /**< Peripheral ID Register, array offset: 0xFD0, array step: 0x4 */
        __I  uint32_t COMPID[4];                         /**< Component ID Register, array offset: 0xFF0, array step: 0x4 */
} MTBDWT_Type;

/* ----------------------------------------------------------------------------
   -- MTBDWT Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup MTBDWT_Register_Masks MTBDWT Register Masks
 * @{
 */

/* CTRL Bit Fields */
#define MTBDWT_CTRL_DWTCFGCTRL_MASK              0xFFFFFFFu
#define MTBDWT_CTRL_DWTCFGCTRL_SHIFT             0
#define MTBDWT_CTRL_DWTCFGCTRL(x)                (((uint32_t)(((uint32_t)(x))<<MTBDWT_CTRL_DWTCFGCTRL_SHIFT))&MTBDWT_CTRL_DWTCFGCTRL_MASK)
#define MTBDWT_CTRL_NUMCMP_MASK                  0xF0000000u
#define MTBDWT_CTRL_NUMCMP_SHIFT                 28
#define MTBDWT_CTRL_NUMCMP(x)                    (((uint32_t)(((uint32_t)(x))<<MTBDWT_CTRL_NUMCMP_SHIFT))&MTBDWT_CTRL_NUMCMP_MASK)
/* COMP Bit Fields */
#define MTBDWT_COMP_COMP_MASK                    0xFFFFFFFFu
#define MTBDWT_COMP_COMP_SHIFT                   0
#define MTBDWT_COMP_COMP(x)                      (((uint32_t)(((uint32_t)(x))<<MTBDWT_COMP_COMP_SHIFT))&MTBDWT_COMP_COMP_MASK)
/* MASK Bit Fields */
#define MTBDWT_MASK_MASK_MASK                    0x1Fu
#define MTBDWT_MASK_MASK_SHIFT                   0
#define MTBDWT_MASK_MASK(x)                      (((uint32_t)(((uint32_t)(x))<<MTBDWT_MASK_MASK_SHIFT))&MTBDWT_MASK_MASK_MASK)
/* FCT Bit Fields */
#define MTBDWT_FCT_FUNCTION_MASK                 0xFu
#define MTBDWT_FCT_FUNCTION_SHIFT                0
#define MTBDWT_FCT_FUNCTION(x)                   (((uint32_t)(((uint32_t)(x))<<MTBDWT_FCT_FUNCTION_SHIFT))&MTBDWT_FCT_FUNCTION_MASK)
#define MTBDWT_FCT_DATAVMATCH_MASK               0x100u
#define MTBDWT_FCT_DATAVMATCH_SHIFT              8
#define MTBDWT_FCT_DATAVSIZE_MASK                0xC00u
#define MTBDWT_FCT_DATAVSIZE_SHIFT               10
#define MTBDWT_FCT_DATAVSIZE(x)                  (((uint32_t)(((uint32_t)(x))<<MTBDWT_FCT_DATAVSIZE_SHIFT))&MTBDWT_FCT_DATAVSIZE_MASK)
#define MTBDWT_FCT_DATAVADDR0_MASK               0xF000u
#define MTBDWT_FCT_DATAVADDR0_SHIFT              12
#define MTBDWT_FCT_DATAVADDR0(x)                 (((uint32_t)(((uint32_t)(x))<<MTBDWT_FCT_DATAVADDR0_SHIFT))&MTBDWT_FCT_DATAVADDR0_MASK)
#define MTBDWT_FCT_MATCHED_MASK                  0x1000000u
#define MTBDWT_FCT_MATCHED_SHIFT                 24
/* TBCTRL Bit Fields */
#define MTBDWT_TBCTRL_ACOMP0_MASK                0x1u
#define MTBDWT_TBCTRL_ACOMP0_SHIFT               0
#define MTBDWT_TBCTRL_ACOMP1_MASK                0x2u
#define MTBDWT_TBCTRL_ACOMP1_SHIFT               1
#define MTBDWT_TBCTRL_NUMCOMP_MASK               0xF0000000u
#define MTBDWT_TBCTRL_NUMCOMP_SHIFT              28
#define MTBDWT_TBCTRL_NUMCOMP(x)                 (((uint32_t)(((uint32_t)(x))<<MTBDWT_TBCTRL_NUMCOMP_SHIFT))&MTBDWT_TBCTRL_NUMCOMP_MASK)
/* DEVICECFG Bit Fields */
#define MTBDWT_DEVICECFG_DEVICECFG_MASK          0xFFFFFFFFu
#define MTBDWT_DEVICECFG_DEVICECFG_SHIFT         0
#define MTBDWT_DEVICECFG_DEVICECFG(x)            (((uint32_t)(((uint32_t)(x))<<MTBDWT_DEVICECFG_DEVICECFG_SHIFT))&MTBDWT_DEVICECFG_DEVICECFG_MASK)
/* DEVICETYPID Bit Fields */
#define MTBDWT_DEVICETYPID_DEVICETYPID_MASK      0xFFFFFFFFu
#define MTBDWT_DEVICETYPID_DEVICETYPID_SHIFT     0
#define MTBDWT_DEVICETYPID_DEVICETYPID(x)        (((uint32_t)(((uint32_t)(x))<<MTBDWT_DEVICETYPID_DEVICETYPID_SHIFT))&MTBDWT_DEVICETYPID_DEVICETYPID_MASK)
/* PERIPHID Bit Fields */
#define MTBDWT_PERIPHID_PERIPHID_MASK            0xFFFFFFFFu
#define MTBDWT_PERIPHID_PERIPHID_SHIFT           0
#define MTBDWT_PERIPHID_PERIPHID(x)              (((uint32_t)(((uint32_t)(x))<<MTBDWT_PERIPHID_PERIPHID_SHIFT))&MTBDWT_PERIPHID_PERIPHID_MASK)
/* COMPID Bit Fields */
#define MTBDWT_COMPID_COMPID_MASK                0xFFFFFFFFu
#define MTBDWT_COMPID_COMPID_SHIFT               0
#define MTBDWT_COMPID_COMPID(x)                  (((uint32_t)(((uint32_t)(x))<<MTBDWT_COMPID_COMPID_SHIFT))&MTBDWT_COMPID_COMPID_MASK)

/*!
 * @}
 */ /* end of group MTBDWT_Register_Masks */


/* MTBDWT - Peripheral instance base addresses */
/** Peripheral MTBDWT base address */
#define MTBDWT_BASE                              (0xF0001000u)
/** Peripheral MTBDWT base pointer */
#define MTBDWT                                   ((MTBDWT_Type *)MTBDWT_BASE)
/** Array initializer of MTBDWT peripheral base pointers */
#define MTBDWT_BASES                             { MTBDWT }

/*!
 * @}
 */ /* end of group MTBDWT_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- NV Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup NV_Peripheral_Access_Layer NV Peripheral Access Layer
 * @{
 */

/** NV - Register Layout Typedef */
typedef struct {
        __I  uint8_t BACKKEY3;                           /**< Backdoor Comparison Key 3., offset: 0x0 */
        __I  uint8_t BACKKEY2;                           /**< Backdoor Comparison Key 2., offset: 0x1 */
        __I  uint8_t BACKKEY1;                           /**< Backdoor Comparison Key 1., offset: 0x2 */
        __I  uint8_t BACKKEY0;                           /**< Backdoor Comparison Key 0., offset: 0x3 */
        __I  uint8_t BACKKEY7;                           /**< Backdoor Comparison Key 7., offset: 0x4 */
        __I  uint8_t BACKKEY6;                           /**< Backdoor Comparison Key 6., offset: 0x5 */
        __I  uint8_t BACKKEY5;                           /**< Backdoor Comparison Key 5., offset: 0x6 */
        __I  uint8_t BACKKEY4;                           /**< Backdoor Comparison Key 4., offset: 0x7 */
        __I  uint8_t FPROT3;                             /**< Non-volatile P-Flash Protection 1 - Low Register, offset: 0x8 */
        __I  uint8_t FPROT2;                             /**< Non-volatile P-Flash Protection 1 - High Register, offset: 0x9 */
        __I  uint8_t FPROT1;                             /**< Non-volatile P-Flash Protection 0 - Low Register, offset: 0xA */
        __I  uint8_t FPROT0;                             /**< Non-volatile P-Flash Protection 0 - High Register, offset: 0xB */
        __I  uint8_t FSEC;                               /**< Non-volatile Flash Security Register, offset: 0xC */
        __I  uint8_t FOPT;                               /**< Non-volatile Flash Option Register, offset: 0xD */
} NV_Type;

/* ----------------------------------------------------------------------------
   -- NV Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup NV_Register_Masks NV Register Masks
 * @{
 */

/* BACKKEY3 Bit Fields */
#define NV_BACKKEY3_KEY_MASK                     0xFFu
#define NV_BACKKEY3_KEY_SHIFT                    0
#define NV_BACKKEY3_KEY(x)                       (((uint8_t)(((uint8_t)(x))<<NV_BACKKEY3_KEY_SHIFT))&NV_BACKKEY3_KEY_MASK)
/* BACKKEY2 Bit Fields */
#define NV_BACKKEY2_KEY_MASK                     0xFFu
#define NV_BACKKEY2_KEY_SHIFT                    0
#define NV_BACKKEY2_KEY(x)                       (((uint8_t)(((uint8_t)(x))<<NV_BACKKEY2_KEY_SHIFT))&NV_BACKKEY2_KEY_MASK)
/* BACKKEY1 Bit Fields */
#define NV_BACKKEY1_KEY_MASK                     0xFFu
#define NV_BACKKEY1_KEY_SHIFT                    0
#define NV_BACKKEY1_KEY(x)                       (((uint8_t)(((uint8_t)(x))<<NV_BACKKEY1_KEY_SHIFT))&NV_BACKKEY1_KEY_MASK)
/* BACKKEY0 Bit Fields */
#define NV_BACKKEY0_KEY_MASK                     0xFFu
#define NV_BACKKEY0_KEY_SHIFT                    0
#define NV_BACKKEY0_KEY(x)                       (((uint8_t)(((uint8_t)(x))<<NV_BACKKEY0_KEY_SHIFT))&NV_BACKKEY0_KEY_MASK)
/* BACKKEY7 Bit Fields */
#define NV_BACKKEY7_KEY_MASK                     0xFFu
#define NV_BACKKEY7_KEY_SHIFT                    0
#define NV_BACKKEY7_KEY(x)                       (((uint8_t)(((uint8_t)(x))<<NV_BACKKEY7_KEY_SHIFT))&NV_BACKKEY7_KEY_MASK)
/* BACKKEY6 Bit Fields */
#define NV_BACKKEY6_KEY_MASK                     0xFFu
#define NV_BACKKEY6_KEY_SHIFT                    0
#define NV_BACKKEY6_KEY(x)                       (((uint8_t)(((uint8_t)(x))<<NV_BACKKEY6_KEY_SHIFT))&NV_BACKKEY6_KEY_MASK)
/* BACKKEY5 Bit Fields */
#define NV_BACKKEY5_KEY_MASK                     0xFFu
#define NV_BACKKEY5_KEY_SHIFT                    0
#define NV_BACKKEY5_KEY(x)                       (((uint8_t)(((uint8_t)(x))<<NV_BACKKEY5_KEY_SHIFT))&NV_BACKKEY5_KEY_MASK)
/* BACKKEY4 Bit Fields */
#define NV_BACKKEY4_KEY_MASK                     0xFFu
#define NV_BACKKEY4_KEY_SHIFT                    0
#define NV_BACKKEY4_KEY(x)                       (((uint8_t)(((uint8_t)(x))<<NV_BACKKEY4_KEY_SHIFT))&NV_BACKKEY4_KEY_MASK)
/* FPROT3 Bit Fields */
#define NV_FPROT3_PROT_MASK                      0xFFu
#define NV_FPROT3_PROT_SHIFT                     0
#define NV_FPROT3_PROT(x)                        (((uint8_t)(((uint8_t)(x))<<NV_FPROT3_PROT_SHIFT))&NV_FPROT3_PROT_MASK)
/* FPROT2 Bit Fields */
#define NV_FPROT2_PROT_MASK                      0xFFu
#define NV_FPROT2_PROT_SHIFT                     0
#define NV_FPROT2_PROT(x)                        (((uint8_t)(((uint8_t)(x))<<NV_FPROT2_PROT_SHIFT))&NV_FPROT2_PROT_MASK)
/* FPROT1 Bit Fields */
#define NV_FPROT1_PROT_MASK                      0xFFu
#define NV_FPROT1_PROT_SHIFT                     0
#define NV_FPROT1_PROT(x)                        (((uint8_t)(((uint8_t)(x))<<NV_FPROT1_PROT_SHIFT))&NV_FPROT1_PROT_MASK)
/* FPROT0 Bit Fields */
#define NV_FPROT0_PROT_MASK                      0xFFu
#define NV_FPROT0_PROT_SHIFT                     0
#define NV_FPROT0_PROT(x)                        (((uint8_t)(((uint8_t)(x))<<NV_FPROT0_PROT_SHIFT))&NV_FPROT0_PROT_MASK)
/* FSEC Bit Fields */
#define NV_FSEC_SEC_MASK                         0x3u
#define NV_FSEC_SEC_SHIFT                        0
#define NV_FSEC_SEC(x)                           (((uint8_t)(((uint8_t)(x))<<NV_FSEC_SEC_SHIFT))&NV_FSEC_SEC_MASK)
#define NV_FSEC_FSLACC_MASK                      0xCu
#define NV_FSEC_FSLACC_SHIFT                     2
#define NV_FSEC_FSLACC(x)                        (((uint8_t)(((uint8_t)(x))<<NV_FSEC_FSLACC_SHIFT))&NV_FSEC_FSLACC_MASK)
#define NV_FSEC_MEEN_MASK                        0x30u
#define NV_FSEC_MEEN_SHIFT                       4
#define NV_FSEC_MEEN(x)                          (((uint8_t)(((uint8_t)(x))<<NV_FSEC_MEEN_SHIFT))&NV_FSEC_MEEN_MASK)
#define NV_FSEC_KEYEN_MASK                       0xC0u
#define NV_FSEC_KEYEN_SHIFT                      6
#define NV_FSEC_KEYEN(x)                         (((uint8_t)(((uint8_t)(x))<<NV_FSEC_KEYEN_SHIFT))&NV_FSEC_KEYEN_MASK)
/* FOPT Bit Fields */
#define NV_FOPT_LPBOOT0_MASK                     0x1u
#define NV_FOPT_LPBOOT0_SHIFT                    0
#define NV_FOPT_NMI_DIS_MASK                     0x4u
#define NV_FOPT_NMI_DIS_SHIFT                    2
#define NV_FOPT_RESET_PIN_CFG_MASK               0x8u
#define NV_FOPT_RESET_PIN_CFG_SHIFT              3
#define NV_FOPT_LPBOOT1_MASK                     0x10u
#define NV_FOPT_LPBOOT1_SHIFT                    4
#define NV_FOPT_FAST_INIT_MASK                   0x20u
#define NV_FOPT_FAST_INIT_SHIFT                  5

/*!
 * @}
 */ /* end of group NV_Register_Masks */


/* NV - Peripheral instance base addresses */
/** Peripheral FTFA_FlashConfig base address */
#define FTFA_FlashConfig_BASE                    (0x400u)
/** Peripheral FTFA_FlashConfig base pointer */
#define FTFA_FlashConfig                         ((NV_Type *)FTFA_FlashConfig_BASE)
/** Array initializer of NV peripheral base pointers */
#define NV_BASES                                 { FTFA_FlashConfig }

/*!
 * @}
 */ /* end of group NV_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- OSC Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup OSC_Peripheral_Access_Layer OSC Peripheral Access Layer
 * @{
 */

/** OSC - Register Layout Typedef */
typedef struct {
        __IO uint8_t CR;                                 /**< OSC Control Register, offset: 0x0 */
} OSC_Type;

/* ----------------------------------------------------------------------------
   -- OSC Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup OSC_Register_Masks OSC Register Masks
 * @{
 */

/* CR Bit Fields */
#define OSC_CR_SC16P_MASK                        0x1u
#define OSC_CR_SC16P_SHIFT                       0
#define OSC_CR_SC8P_MASK                         0x2u
#define OSC_CR_SC8P_SHIFT                        1
#define OSC_CR_SC4P_MASK                         0x4u
#define OSC_CR_SC4P_SHIFT                        2
#define OSC_CR_SC2P_MASK                         0x8u
#define OSC_CR_SC2P_SHIFT                        3
#define OSC_CR_EREFSTEN_MASK                     0x20u
#define OSC_CR_EREFSTEN_SHIFT                    5
#define OSC_CR_ERCLKEN_MASK                      0x80u
#define OSC_CR_ERCLKEN_SHIFT                     7

/*!
 * @}
 */ /* end of group OSC_Register_Masks */


/* OSC - Peripheral instance base addresses */
/** Peripheral OSC0 base address */
#define OSC0_BASE                                (0x40065000u)
/** Peripheral OSC0 base pointer */
#define OSC0                                     ((OSC_Type *)OSC0_BASE)
/** Array initializer of OSC peripheral base pointers */
#define OSC_BASES                                { OSC0 }

/*!
 * @}
 */ /* end of group OSC_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- PIT Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup PIT_Peripheral_Access_Layer PIT Peripheral Access Layer
 * @{
 */

/** PIT - Register Layout Typedef */
typedef struct {
        __IO uint32_t MCR;                               /**< PIT Module Control Register, offset: 0x0 */
        uint8_t RESERVED_0[220];
        __I  uint32_t LTMR64H;                           /**< PIT Upper Lifetime Timer Register, offset: 0xE0 */
        __I  uint32_t LTMR64L;                           /**< PIT Lower Lifetime Timer Register, offset: 0xE4 */
        uint8_t RESERVED_1[24];
        struct {                                         /* offset: 0x100, array step: 0x10 */
                __IO uint32_t LDVAL;                             /**< Timer Load Value Register, array offset: 0x100, array step: 0x10 */
                __I  uint32_t CVAL;                              /**< Current Timer Value Register, array offset: 0x104, array step: 0x10 */
                __IO uint32_t TCTRL;                             /**< Timer Control Register, array offset: 0x108, array step: 0x10 */
                __IO uint32_t TFLG;                              /**< Timer Flag Register, array offset: 0x10C, array step: 0x10 */
        } CHANNEL[2];
} PIT_Type;

/* ----------------------------------------------------------------------------
   -- PIT Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup PIT_Register_Masks PIT Register Masks
 * @{
 */

/* MCR Bit Fields */
#define PIT_MCR_FRZ_MASK                         0x1u
#define PIT_MCR_FRZ_SHIFT                        0
#define PIT_MCR_MDIS_MASK                        0x2u
#define PIT_MCR_MDIS_SHIFT                       1
/* LTMR64H Bit Fields */
#define PIT_LTMR64H_LTH_MASK                     0xFFFFFFFFu
#define PIT_LTMR64H_LTH_SHIFT                    0
#define PIT_LTMR64H_LTH(x)                       (((uint32_t)(((uint32_t)(x))<<PIT_LTMR64H_LTH_SHIFT))&PIT_LTMR64H_LTH_MASK)
/* LTMR64L Bit Fields */
#define PIT_LTMR64L_LTL_MASK                     0xFFFFFFFFu
#define PIT_LTMR64L_LTL_SHIFT                    0
#define PIT_LTMR64L_LTL(x)                       (((uint32_t)(((uint32_t)(x))<<PIT_LTMR64L_LTL_SHIFT))&PIT_LTMR64L_LTL_MASK)
/* LDVAL Bit Fields */
#define PIT_LDVAL_TSV_MASK                       0xFFFFFFFFu
#define PIT_LDVAL_TSV_SHIFT                      0
#define PIT_LDVAL_TSV(x)                         (((uint32_t)(((uint32_t)(x))<<PIT_LDVAL_TSV_SHIFT))&PIT_LDVAL_TSV_MASK)
/* CVAL Bit Fields */
#define PIT_CVAL_TVL_MASK                        0xFFFFFFFFu
#define PIT_CVAL_TVL_SHIFT                       0
#define PIT_CVAL_TVL(x)                          (((uint32_t)(((uint32_t)(x))<<PIT_CVAL_TVL_SHIFT))&PIT_CVAL_TVL_MASK)
/* TCTRL Bit Fields */
#define PIT_TCTRL_TEN_MASK                       0x1u
#define PIT_TCTRL_TEN_SHIFT                      0
#define PIT_TCTRL_TIE_MASK                       0x2u
#define PIT_TCTRL_TIE_SHIFT                      1
#define PIT_TCTRL_CHN_MASK                       0x4u
#define PIT_TCTRL_CHN_SHIFT                      2
/* TFLG Bit Fields */
#define PIT_TFLG_TIF_MASK                        0x1u
#define PIT_TFLG_TIF_SHIFT                       0

/*!
 * @}
 */ /* end of group PIT_Register_Masks */


/* PIT - Peripheral instance base addresses */
/** Peripheral PIT base address */
#define PIT_BASE                                 (0x40037000u)
/** Peripheral PIT base pointer */
#define PIT                                      ((PIT_Type *)PIT_BASE)
/** Array initializer of PIT peripheral base pointers */
#define PIT_BASES                                { PIT }

/*!
 * @}
 */ /* end of group PIT_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- PMC Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup PMC_Peripheral_Access_Layer PMC Peripheral Access Layer
 * @{
 */

/** PMC - Register Layout Typedef */
typedef struct {
        __IO uint8_t LVDSC1;                             /**< Low Voltage Detect Status And Control 1 register, offset: 0x0 */
        __IO uint8_t LVDSC2;                             /**< Low Voltage Detect Status And Control 2 register, offset: 0x1 */
        __IO uint8_t REGSC;                              /**< Regulator Status And Control register, offset: 0x2 */
} PMC_Type;

/* ----------------------------------------------------------------------------
   -- PMC Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup PMC_Register_Masks PMC Register Masks
 * @{
 */

/* LVDSC1 Bit Fields */
#define PMC_LVDSC1_LVDV_MASK                     0x3u
#define PMC_LVDSC1_LVDV_SHIFT                    0
#define PMC_LVDSC1_LVDV(x)                       (((uint8_t)(((uint8_t)(x))<<PMC_LVDSC1_LVDV_SHIFT))&PMC_LVDSC1_LVDV_MASK)
#define PMC_LVDSC1_LVDRE_MASK                    0x10u
#define PMC_LVDSC1_LVDRE_SHIFT                   4
#define PMC_LVDSC1_LVDIE_MASK                    0x20u
#define PMC_LVDSC1_LVDIE_SHIFT                   5
#define PMC_LVDSC1_LVDACK_MASK                   0x40u
#define PMC_LVDSC1_LVDACK_SHIFT                  6
#define PMC_LVDSC1_LVDF_MASK                     0x80u
#define PMC_LVDSC1_LVDF_SHIFT                    7
/* LVDSC2 Bit Fields */
#define PMC_LVDSC2_LVWV_MASK                     0x3u
#define PMC_LVDSC2_LVWV_SHIFT                    0
#define PMC_LVDSC2_LVWV(x)                       (((uint8_t)(((uint8_t)(x))<<PMC_LVDSC2_LVWV_SHIFT))&PMC_LVDSC2_LVWV_MASK)
#define PMC_LVDSC2_LVWIE_MASK                    0x20u
#define PMC_LVDSC2_LVWIE_SHIFT                   5
#define PMC_LVDSC2_LVWACK_MASK                   0x40u
#define PMC_LVDSC2_LVWACK_SHIFT                  6
#define PMC_LVDSC2_LVWF_MASK                     0x80u
#define PMC_LVDSC2_LVWF_SHIFT                    7
/* REGSC Bit Fields */
#define PMC_REGSC_BGBE_MASK                      0x1u
#define PMC_REGSC_BGBE_SHIFT                     0
#define PMC_REGSC_REGONS_MASK                    0x4u
#define PMC_REGSC_REGONS_SHIFT                   2
#define PMC_REGSC_ACKISO_MASK                    0x8u
#define PMC_REGSC_ACKISO_SHIFT                   3
#define PMC_REGSC_BGEN_MASK                      0x10u
#define PMC_REGSC_BGEN_SHIFT                     4

/*!
 * @}
 */ /* end of group PMC_Register_Masks */


/* PMC - Peripheral instance base addresses */
/** Peripheral PMC base address */
#define PMC_BASE                                 (0x4007D000u)
/** Peripheral PMC base pointer */
#define PMC                                      ((PMC_Type *)PMC_BASE)
/** Array initializer of PMC peripheral base pointers */
#define PMC_BASES                                { PMC }

/*!
 * @}
 */ /* end of group PMC_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- PORT Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup PORT_Peripheral_Access_Layer PORT Peripheral Access Layer
 * @{
 */

/** PORT - Register Layout Typedef */
typedef struct {
        __IO uint32_t PCR[32];                           /**< Pin Control Register n, array offset: 0x0, array step: 0x4 */
        __O  uint32_t GPCLR;                             /**< Global Pin Control Low Register, offset: 0x80 */
        __O  uint32_t GPCHR;                             /**< Global Pin Control High Register, offset: 0x84 */
        uint8_t RESERVED_0[24];
        __IO uint32_t ISFR;                              /**< Interrupt Status Flag Register, offset: 0xA0 */
} PORT_Type;

/* ----------------------------------------------------------------------------
   -- PORT Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup PORT_Register_Masks PORT Register Masks
 * @{
 */

/* PCR Bit Fields */
#define PORT_PCR_PS_MASK                         0x1u
#define PORT_PCR_PS_SHIFT                        0
#define PORT_PCR_PE_MASK                         0x2u
#define PORT_PCR_PE_SHIFT                        1
#define PORT_PCR_SRE_MASK                        0x4u
#define PORT_PCR_SRE_SHIFT                       2
#define PORT_PCR_PFE_MASK                        0x10u
#define PORT_PCR_PFE_SHIFT                       4
#define PORT_PCR_DSE_MASK                        0x40u
#define PORT_PCR_DSE_SHIFT                       6
#define PORT_PCR_MUX_MASK                        0x700u
#define PORT_PCR_MUX_SHIFT                       8
#define PORT_PCR_MUX(x)                          (((uint32_t)(((uint32_t)(x))<<PORT_PCR_MUX_SHIFT))&PORT_PCR_MUX_MASK)
#define PORT_PCR_IRQC_MASK                       0xF0000u
#define PORT_PCR_IRQC_SHIFT                      16
#define PORT_PCR_IRQC(x)                         (((uint32_t)(((uint32_t)(x))<<PORT_PCR_IRQC_SHIFT))&PORT_PCR_IRQC_MASK)
#define PORT_PCR_ISF_MASK                        0x1000000u
#define PORT_PCR_ISF_SHIFT                       24
/* GPCLR Bit Fields */
#define PORT_GPCLR_GPWD_MASK                     0xFFFFu
#define PORT_GPCLR_GPWD_SHIFT                    0
#define PORT_GPCLR_GPWD(x)                       (((uint32_t)(((uint32_t)(x))<<PORT_GPCLR_GPWD_SHIFT))&PORT_GPCLR_GPWD_MASK)
#define PORT_GPCLR_GPWE_MASK                     0xFFFF0000u
#define PORT_GPCLR_GPWE_SHIFT                    16
#define PORT_GPCLR_GPWE(x)                       (((uint32_t)(((uint32_t)(x))<<PORT_GPCLR_GPWE_SHIFT))&PORT_GPCLR_GPWE_MASK)
/* GPCHR Bit Fields */
#define PORT_GPCHR_GPWD_MASK                     0xFFFFu
#define PORT_GPCHR_GPWD_SHIFT                    0
#define PORT_GPCHR_GPWD(x)                       (((uint32_t)(((uint32_t)(x))<<PORT_GPCHR_GPWD_SHIFT))&PORT_GPCHR_GPWD_MASK)
#define PORT_GPCHR_GPWE_MASK                     0xFFFF0000u
#define PORT_GPCHR_GPWE_SHIFT                    16
#define PORT_GPCHR_GPWE(x)                       (((uint32_t)(((uint32_t)(x))<<PORT_GPCHR_GPWE_SHIFT))&PORT_GPCHR_GPWE_MASK)
/* ISFR Bit Fields */
#define PORT_ISFR_ISF_MASK                       0xFFFFFFFFu
#define PORT_ISFR_ISF_SHIFT                      0
#define PORT_ISFR_ISF(x)                         (((uint32_t)(((uint32_t)(x))<<PORT_ISFR_ISF_SHIFT))&PORT_ISFR_ISF_MASK)

/*!
 * @}
 */ /* end of group PORT_Register_Masks */


/* PORT - Peripheral instance base addresses */
/** Peripheral PORTA base address */
#define PORTA_BASE                               (0x40049000u)
/** Peripheral PORTA base pointer */
#define PORTA                                    ((PORT_Type *)PORTA_BASE)
/** Peripheral PORTB base address */
#define PORTB_BASE                               (0x4004A000u)
/** Peripheral PORTB base pointer */
#define PORTB                                    ((PORT_Type *)PORTB_BASE)
/** Peripheral PORTC base address */
#define PORTC_BASE                               (0x4004B000u)
/** Peripheral PORTC base pointer */
#define PORTC                                    ((PORT_Type *)PORTC_BASE)
/** Peripheral PORTD base address */
#define PORTD_BASE                               (0x4004C000u)
/** Peripheral PORTD base pointer */
#define PORTD                                    ((PORT_Type *)PORTD_BASE)
/** Peripheral PORTE base address */
#define PORTE_BASE                               (0x4004D000u)
/** Peripheral PORTE base pointer */
#define PORTE                                    ((PORT_Type *)PORTE_BASE)
/** Array initializer of PORT peripheral base pointers */
#define PORT_BASES                               { PORTA, PORTB, PORTC, PORTD, PORTE }

/*!
 * @}
 */ /* end of group PORT_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- RCM Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup RCM_Peripheral_Access_Layer RCM Peripheral Access Layer
 * @{
 */

/** RCM - Register Layout Typedef */
typedef struct {
        __I  uint8_t SRS0;                               /**< System Reset Status Register 0, offset: 0x0 */
        __I  uint8_t SRS1;                               /**< System Reset Status Register 1, offset: 0x1 */
        uint8_t RESERVED_0[2];
        __IO uint8_t RPFC;                               /**< Reset Pin Filter Control register, offset: 0x4 */
        __IO uint8_t RPFW;                               /**< Reset Pin Filter Width register, offset: 0x5 */
} RCM_Type;

/* ----------------------------------------------------------------------------
   -- RCM Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup RCM_Register_Masks RCM Register Masks
 * @{
 */

/* SRS0 Bit Fields */
#define RCM_SRS0_WAKEUP_MASK                     0x1u
#define RCM_SRS0_WAKEUP_SHIFT                    0
#define RCM_SRS0_LVD_MASK                        0x2u
#define RCM_SRS0_LVD_SHIFT                       1
#define RCM_SRS0_LOC_MASK                        0x4u
#define RCM_SRS0_LOC_SHIFT                       2
#define RCM_SRS0_LOL_MASK                        0x8u
#define RCM_SRS0_LOL_SHIFT                       3
#define RCM_SRS0_WDOG_MASK                       0x20u
#define RCM_SRS0_WDOG_SHIFT                      5
#define RCM_SRS0_PIN_MASK                        0x40u
#define RCM_SRS0_PIN_SHIFT                       6
#define RCM_SRS0_POR_MASK                        0x80u
#define RCM_SRS0_POR_SHIFT                       7
/* SRS1 Bit Fields */
#define RCM_SRS1_LOCKUP_MASK                     0x2u
#define RCM_SRS1_LOCKUP_SHIFT                    1
#define RCM_SRS1_SW_MASK                         0x4u
#define RCM_SRS1_SW_SHIFT                        2
#define RCM_SRS1_MDM_AP_MASK                     0x8u
#define RCM_SRS1_MDM_AP_SHIFT                    3
#define RCM_SRS1_SACKERR_MASK                    0x20u
#define RCM_SRS1_SACKERR_SHIFT                   5
/* RPFC Bit Fields */
#define RCM_RPFC_RSTFLTSRW_MASK                  0x3u
#define RCM_RPFC_RSTFLTSRW_SHIFT                 0
#define RCM_RPFC_RSTFLTSRW(x)                    (((uint8_t)(((uint8_t)(x))<<RCM_RPFC_RSTFLTSRW_SHIFT))&RCM_RPFC_RSTFLTSRW_MASK)
#define RCM_RPFC_RSTFLTSS_MASK                   0x4u
#define RCM_RPFC_RSTFLTSS_SHIFT                  2
/* RPFW Bit Fields */
#define RCM_RPFW_RSTFLTSEL_MASK                  0x1Fu
#define RCM_RPFW_RSTFLTSEL_SHIFT                 0
#define RCM_RPFW_RSTFLTSEL(x)                    (((uint8_t)(((uint8_t)(x))<<RCM_RPFW_RSTFLTSEL_SHIFT))&RCM_RPFW_RSTFLTSEL_MASK)

/*!
 * @}
 */ /* end of group RCM_Register_Masks */


/* RCM - Peripheral instance base addresses */
/** Peripheral RCM base address */
#define RCM_BASE                                 (0x4007F000u)
/** Peripheral RCM base pointer */
#define RCM                                      ((RCM_Type *)RCM_BASE)
/** Array initializer of RCM peripheral base pointers */
#define RCM_BASES                                { RCM }

/*!
 * @}
 */ /* end of group RCM_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- ROM Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup ROM_Peripheral_Access_Layer ROM Peripheral Access Layer
 * @{
 */

/** ROM - Register Layout Typedef */
typedef struct {
        __I  uint32_t ENTRY[3];                          /**< Entry, array offset: 0x0, array step: 0x4 */
        __I  uint32_t TABLEMARK;                         /**< End of Table Marker Register, offset: 0xC */
        uint8_t RESERVED_0[4028];
        __I  uint32_t SYSACCESS;                         /**< System Access Register, offset: 0xFCC */
        __I  uint32_t PERIPHID4;                         /**< Peripheral ID Register, offset: 0xFD0 */
        __I  uint32_t PERIPHID5;                         /**< Peripheral ID Register, offset: 0xFD4 */
        __I  uint32_t PERIPHID6;                         /**< Peripheral ID Register, offset: 0xFD8 */
        __I  uint32_t PERIPHID7;                         /**< Peripheral ID Register, offset: 0xFDC */
        __I  uint32_t PERIPHID0;                         /**< Peripheral ID Register, offset: 0xFE0 */
        __I  uint32_t PERIPHID1;                         /**< Peripheral ID Register, offset: 0xFE4 */
        __I  uint32_t PERIPHID2;                         /**< Peripheral ID Register, offset: 0xFE8 */
        __I  uint32_t PERIPHID3;                         /**< Peripheral ID Register, offset: 0xFEC */
        __I  uint32_t COMPID[4];                         /**< Component ID Register, array offset: 0xFF0, array step: 0x4 */
} ROM_Type;

/* ----------------------------------------------------------------------------
   -- ROM Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup ROM_Register_Masks ROM Register Masks
 * @{
 */

/* ENTRY Bit Fields */
#define ROM_ENTRY_ENTRY_MASK                     0xFFFFFFFFu
#define ROM_ENTRY_ENTRY_SHIFT                    0
#define ROM_ENTRY_ENTRY(x)                       (((uint32_t)(((uint32_t)(x))<<ROM_ENTRY_ENTRY_SHIFT))&ROM_ENTRY_ENTRY_MASK)
/* TABLEMARK Bit Fields */
#define ROM_TABLEMARK_MARK_MASK                  0xFFFFFFFFu
#define ROM_TABLEMARK_MARK_SHIFT                 0
#define ROM_TABLEMARK_MARK(x)                    (((uint32_t)(((uint32_t)(x))<<ROM_TABLEMARK_MARK_SHIFT))&ROM_TABLEMARK_MARK_MASK)
/* SYSACCESS Bit Fields */
#define ROM_SYSACCESS_SYSACCESS_MASK             0xFFFFFFFFu
#define ROM_SYSACCESS_SYSACCESS_SHIFT            0
#define ROM_SYSACCESS_SYSACCESS(x)               (((uint32_t)(((uint32_t)(x))<<ROM_SYSACCESS_SYSACCESS_SHIFT))&ROM_SYSACCESS_SYSACCESS_MASK)
/* PERIPHID4 Bit Fields */
#define ROM_PERIPHID4_PERIPHID_MASK              0xFFFFFFFFu
#define ROM_PERIPHID4_PERIPHID_SHIFT             0
#define ROM_PERIPHID4_PERIPHID(x)                (((uint32_t)(((uint32_t)(x))<<ROM_PERIPHID4_PERIPHID_SHIFT))&ROM_PERIPHID4_PERIPHID_MASK)
/* PERIPHID5 Bit Fields */
#define ROM_PERIPHID5_PERIPHID_MASK              0xFFFFFFFFu
#define ROM_PERIPHID5_PERIPHID_SHIFT             0
#define ROM_PERIPHID5_PERIPHID(x)                (((uint32_t)(((uint32_t)(x))<<ROM_PERIPHID5_PERIPHID_SHIFT))&ROM_PERIPHID5_PERIPHID_MASK)
/* PERIPHID6 Bit Fields */
#define ROM_PERIPHID6_PERIPHID_MASK              0xFFFFFFFFu
#define ROM_PERIPHID6_PERIPHID_SHIFT             0
#define ROM_PERIPHID6_PERIPHID(x)                (((uint32_t)(((uint32_t)(x))<<ROM_PERIPHID6_PERIPHID_SHIFT))&ROM_PERIPHID6_PERIPHID_MASK)
/* PERIPHID7 Bit Fields */
#define ROM_PERIPHID7_PERIPHID_MASK              0xFFFFFFFFu
#define ROM_PERIPHID7_PERIPHID_SHIFT             0
#define ROM_PERIPHID7_PERIPHID(x)                (((uint32_t)(((uint32_t)(x))<<ROM_PERIPHID7_PERIPHID_SHIFT))&ROM_PERIPHID7_PERIPHID_MASK)
/* PERIPHID0 Bit Fields */
#define ROM_PERIPHID0_PERIPHID_MASK              0xFFFFFFFFu
#define ROM_PERIPHID0_PERIPHID_SHIFT             0
#define ROM_PERIPHID0_PERIPHID(x)                (((uint32_t)(((uint32_t)(x))<<ROM_PERIPHID0_PERIPHID_SHIFT))&ROM_PERIPHID0_PERIPHID_MASK)
/* PERIPHID1 Bit Fields */
#define ROM_PERIPHID1_PERIPHID_MASK              0xFFFFFFFFu
#define ROM_PERIPHID1_PERIPHID_SHIFT             0
#define ROM_PERIPHID1_PERIPHID(x)                (((uint32_t)(((uint32_t)(x))<<ROM_PERIPHID1_PERIPHID_SHIFT))&ROM_PERIPHID1_PERIPHID_MASK)
/* PERIPHID2 Bit Fields */
#define ROM_PERIPHID2_PERIPHID_MASK              0xFFFFFFFFu
#define ROM_PERIPHID2_PERIPHID_SHIFT             0
#define ROM_PERIPHID2_PERIPHID(x)                (((uint32_t)(((uint32_t)(x))<<ROM_PERIPHID2_PERIPHID_SHIFT))&ROM_PERIPHID2_PERIPHID_MASK)
/* PERIPHID3 Bit Fields */
#define ROM_PERIPHID3_PERIPHID_MASK              0xFFFFFFFFu
#define ROM_PERIPHID3_PERIPHID_SHIFT             0
#define ROM_PERIPHID3_PERIPHID(x)                (((uint32_t)(((uint32_t)(x))<<ROM_PERIPHID3_PERIPHID_SHIFT))&ROM_PERIPHID3_PERIPHID_MASK)
/* COMPID Bit Fields */
#define ROM_COMPID_COMPID_MASK                   0xFFFFFFFFu
#define ROM_COMPID_COMPID_SHIFT                  0
#define ROM_COMPID_COMPID(x)                     (((uint32_t)(((uint32_t)(x))<<ROM_COMPID_COMPID_SHIFT))&ROM_COMPID_COMPID_MASK)

/*!
 * @}
 */ /* end of group ROM_Register_Masks */


/* ROM - Peripheral instance base addresses */
/** Peripheral ROM base address */
#define ROM_BASE                                 (0xF0002000u)
/** Peripheral ROM base pointer */
#define ROM                                      ((ROM_Type *)ROM_BASE)
/** Array initializer of ROM peripheral base pointers */
#define ROM_BASES                                { ROM }

/*!
 * @}
 */ /* end of group ROM_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- RTC Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup RTC_Peripheral_Access_Layer RTC Peripheral Access Layer
 * @{
 */

/** RTC - Register Layout Typedef */
typedef struct {
        __IO uint32_t TSR;                               /**< RTC Time Seconds Register, offset: 0x0 */
        __IO uint32_t TPR;                               /**< RTC Time Prescaler Register, offset: 0x4 */
        __IO uint32_t TAR;                               /**< RTC Time Alarm Register, offset: 0x8 */
        __IO uint32_t TCR;                               /**< RTC Time Compensation Register, offset: 0xC */
        __IO uint32_t CR;                                /**< RTC Control Register, offset: 0x10 */
        __IO uint32_t SR;                                /**< RTC Status Register, offset: 0x14 */
        __IO uint32_t LR;                                /**< RTC Lock Register, offset: 0x18 */
        __IO uint32_t IER;                               /**< RTC Interrupt Enable Register, offset: 0x1C */
} RTC_Type;

/* ----------------------------------------------------------------------------
   -- RTC Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup RTC_Register_Masks RTC Register Masks
 * @{
 */

/* TSR Bit Fields */
#define RTC_TSR_TSR_MASK                         0xFFFFFFFFu
#define RTC_TSR_TSR_SHIFT                        0
#define RTC_TSR_TSR(x)                           (((uint32_t)(((uint32_t)(x))<<RTC_TSR_TSR_SHIFT))&RTC_TSR_TSR_MASK)
/* TPR Bit Fields */
#define RTC_TPR_TPR_MASK                         0xFFFFu
#define RTC_TPR_TPR_SHIFT                        0
#define RTC_TPR_TPR(x)                           (((uint32_t)(((uint32_t)(x))<<RTC_TPR_TPR_SHIFT))&RTC_TPR_TPR_MASK)
/* TAR Bit Fields */
#define RTC_TAR_TAR_MASK                         0xFFFFFFFFu
#define RTC_TAR_TAR_SHIFT                        0
#define RTC_TAR_TAR(x)                           (((uint32_t)(((uint32_t)(x))<<RTC_TAR_TAR_SHIFT))&RTC_TAR_TAR_MASK)
/* TCR Bit Fields */
#define RTC_TCR_TCR_MASK                         0xFFu
#define RTC_TCR_TCR_SHIFT                        0
#define RTC_TCR_TCR(x)                           (((uint32_t)(((uint32_t)(x))<<RTC_TCR_TCR_SHIFT))&RTC_TCR_TCR_MASK)
#define RTC_TCR_CIR_MASK                         0xFF00u
#define RTC_TCR_CIR_SHIFT                        8
#define RTC_TCR_CIR(x)                           (((uint32_t)(((uint32_t)(x))<<RTC_TCR_CIR_SHIFT))&RTC_TCR_CIR_MASK)
#define RTC_TCR_TCV_MASK                         0xFF0000u
#define RTC_TCR_TCV_SHIFT                        16
#define RTC_TCR_TCV(x)                           (((uint32_t)(((uint32_t)(x))<<RTC_TCR_TCV_SHIFT))&RTC_TCR_TCV_MASK)
#define RTC_TCR_CIC_MASK                         0xFF000000u
#define RTC_TCR_CIC_SHIFT                        24
#define RTC_TCR_CIC(x)                           (((uint32_t)(((uint32_t)(x))<<RTC_TCR_CIC_SHIFT))&RTC_TCR_CIC_MASK)
/* CR Bit Fields */
#define RTC_CR_SWR_MASK                          0x1u
#define RTC_CR_SWR_SHIFT                         0
#define RTC_CR_WPE_MASK                          0x2u
#define RTC_CR_WPE_SHIFT                         1
#define RTC_CR_SUP_MASK                          0x4u
#define RTC_CR_SUP_SHIFT                         2
#define RTC_CR_UM_MASK                           0x8u
#define RTC_CR_UM_SHIFT                          3
#define RTC_CR_OSCE_MASK                         0x100u
#define RTC_CR_OSCE_SHIFT                        8
#define RTC_CR_CLKO_MASK                         0x200u
#define RTC_CR_CLKO_SHIFT                        9
#define RTC_CR_SC16P_MASK                        0x400u
#define RTC_CR_SC16P_SHIFT                       10
#define RTC_CR_SC8P_MASK                         0x800u
#define RTC_CR_SC8P_SHIFT                        11
#define RTC_CR_SC4P_MASK                         0x1000u
#define RTC_CR_SC4P_SHIFT                        12
#define RTC_CR_SC2P_MASK                         0x2000u
#define RTC_CR_SC2P_SHIFT                        13
/* SR Bit Fields */
#define RTC_SR_TIF_MASK                          0x1u
#define RTC_SR_TIF_SHIFT                         0
#define RTC_SR_TOF_MASK                          0x2u
#define RTC_SR_TOF_SHIFT                         1
#define RTC_SR_TAF_MASK                          0x4u
#define RTC_SR_TAF_SHIFT                         2
#define RTC_SR_TCE_MASK                          0x10u
#define RTC_SR_TCE_SHIFT                         4
/* LR Bit Fields */
#define RTC_LR_TCL_MASK                          0x8u
#define RTC_LR_TCL_SHIFT                         3
#define RTC_LR_CRL_MASK                          0x10u
#define RTC_LR_CRL_SHIFT                         4
#define RTC_LR_SRL_MASK                          0x20u
#define RTC_LR_SRL_SHIFT                         5
#define RTC_LR_LRL_MASK                          0x40u
#define RTC_LR_LRL_SHIFT                         6
/* IER Bit Fields */
#define RTC_IER_TIIE_MASK                        0x1u
#define RTC_IER_TIIE_SHIFT                       0
#define RTC_IER_TOIE_MASK                        0x2u
#define RTC_IER_TOIE_SHIFT                       1
#define RTC_IER_TAIE_MASK                        0x4u
#define RTC_IER_TAIE_SHIFT                       2
#define RTC_IER_TSIE_MASK                        0x10u
#define RTC_IER_TSIE_SHIFT                       4
#define RTC_IER_WPON_MASK                        0x80u
#define RTC_IER_WPON_SHIFT                       7

/*!
 * @}
 */ /* end of group RTC_Register_Masks */


/* RTC - Peripheral instance base addresses */
/** Peripheral RTC base address */
#define RTC_BASE                                 (0x4003D000u)
/** Peripheral RTC base pointer */
#define RTC                                      ((RTC_Type *)RTC_BASE)
/** Array initializer of RTC peripheral base pointers */
#define RTC_BASES                                { RTC }

/*!
 * @}
 */ /* end of group RTC_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- SIM Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup SIM_Peripheral_Access_Layer SIM Peripheral Access Layer
 * @{
 */

/** SIM - Register Layout Typedef */
typedef struct {
        __IO uint32_t SOPT1;                             /**< System Options Register 1, offset: 0x0 */
        __IO uint32_t SOPT1CFG;                          /**< SOPT1 Configuration Register, offset: 0x4 */
        uint8_t RESERVED_0[4092];
        __IO uint32_t SOPT2;                             /**< System Options Register 2, offset: 0x1004 */
        uint8_t RESERVED_1[4];
        __IO uint32_t SOPT4;                             /**< System Options Register 4, offset: 0x100C */
        __IO uint32_t SOPT5;                             /**< System Options Register 5, offset: 0x1010 */
        uint8_t RESERVED_2[4];
        __IO uint32_t SOPT7;                             /**< System Options Register 7, offset: 0x1018 */
        uint8_t RESERVED_3[8];
        __I  uint32_t SDID;                              /**< System Device Identification Register, offset: 0x1024 */
        uint8_t RESERVED_4[12];
        __IO uint32_t SCGC4;                             /**< System Clock Gating Control Register 4, offset: 0x1034 */
        __IO uint32_t SCGC5;                             /**< System Clock Gating Control Register 5, offset: 0x1038 */
        __IO uint32_t SCGC6;                             /**< System Clock Gating Control Register 6, offset: 0x103C */
        __IO uint32_t SCGC7;                             /**< System Clock Gating Control Register 7, offset: 0x1040 */
        __IO uint32_t CLKDIV1;                           /**< System Clock Divider Register 1, offset: 0x1044 */
        uint8_t RESERVED_5[4];
        __IO uint32_t FCFG1;                             /**< Flash Configuration Register 1, offset: 0x104C */
        __I  uint32_t FCFG2;                             /**< Flash Configuration Register 2, offset: 0x1050 */
        uint8_t RESERVED_6[4];
        __I  uint32_t UIDMH;                             /**< Unique Identification Register Mid-High, offset: 0x1058 */
        __I  uint32_t UIDML;                             /**< Unique Identification Register Mid Low, offset: 0x105C */
        __I  uint32_t UIDL;                              /**< Unique Identification Register Low, offset: 0x1060 */
        uint8_t RESERVED_7[156];
        __IO uint32_t COPC;                              /**< COP Control Register, offset: 0x1100 */
        __O  uint32_t SRVCOP;                            /**< Service COP Register, offset: 0x1104 */
} SIM_Type;

/* ----------------------------------------------------------------------------
   -- SIM Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup SIM_Register_Masks SIM Register Masks
 * @{
 */

/* SOPT1 Bit Fields */
#define SIM_SOPT1_OSC32KSEL_MASK                 0xC0000u
#define SIM_SOPT1_OSC32KSEL_SHIFT                18
#define SIM_SOPT1_OSC32KSEL(x)                   (((uint32_t)(((uint32_t)(x))<<SIM_SOPT1_OSC32KSEL_SHIFT))&SIM_SOPT1_OSC32KSEL_MASK)
#define SIM_SOPT1_USBVSTBY_MASK                  0x20000000u
#define SIM_SOPT1_USBVSTBY_SHIFT                 29
#define SIM_SOPT1_USBSSTBY_MASK                  0x40000000u
#define SIM_SOPT1_USBSSTBY_SHIFT                 30
#define SIM_SOPT1_USBREGEN_MASK                  0x80000000u
#define SIM_SOPT1_USBREGEN_SHIFT                 31
/* SOPT1CFG Bit Fields */
#define SIM_SOPT1CFG_URWE_MASK                   0x1000000u
#define SIM_SOPT1CFG_URWE_SHIFT                  24
#define SIM_SOPT1CFG_UVSWE_MASK                  0x2000000u
#define SIM_SOPT1CFG_UVSWE_SHIFT                 25
#define SIM_SOPT1CFG_USSWE_MASK                  0x4000000u
#define SIM_SOPT1CFG_USSWE_SHIFT                 26
/* SOPT2 Bit Fields */
#define SIM_SOPT2_RTCCLKOUTSEL_MASK              0x10u
#define SIM_SOPT2_RTCCLKOUTSEL_SHIFT             4
#define SIM_SOPT2_CLKOUTSEL_MASK                 0xE0u
#define SIM_SOPT2_CLKOUTSEL_SHIFT                5
#define SIM_SOPT2_CLKOUTSEL(x)                   (((uint32_t)(((uint32_t)(x))<<SIM_SOPT2_CLKOUTSEL_SHIFT))&SIM_SOPT2_CLKOUTSEL_MASK)
#define SIM_SOPT2_PLLFLLSEL_MASK                 0x10000u
#define SIM_SOPT2_PLLFLLSEL_SHIFT                16
#define SIM_SOPT2_USBSRC_MASK                    0x40000u
#define SIM_SOPT2_USBSRC_SHIFT                   18
#define SIM_SOPT2_TPMSRC_MASK                    0x3000000u
#define SIM_SOPT2_TPMSRC_SHIFT                   24
#define SIM_SOPT2_TPMSRC(x)                      (((uint32_t)(((uint32_t)(x))<<SIM_SOPT2_TPMSRC_SHIFT))&SIM_SOPT2_TPMSRC_MASK)
#define SIM_SOPT2_UART0SRC_MASK                  0xC000000u
#define SIM_SOPT2_UART0SRC_SHIFT                 26
#define SIM_SOPT2_UART0SRC(x)                    (((uint32_t)(((uint32_t)(x))<<SIM_SOPT2_UART0SRC_SHIFT))&SIM_SOPT2_UART0SRC_MASK)
/* SOPT4 Bit Fields */
#define SIM_SOPT4_TPM1CH0SRC_MASK                0xC0000u
#define SIM_SOPT4_TPM1CH0SRC_SHIFT               18
#define SIM_SOPT4_TPM1CH0SRC(x)                  (((uint32_t)(((uint32_t)(x))<<SIM_SOPT4_TPM1CH0SRC_SHIFT))&SIM_SOPT4_TPM1CH0SRC_MASK)
#define SIM_SOPT4_TPM2CH0SRC_MASK                0x100000u
#define SIM_SOPT4_TPM2CH0SRC_SHIFT               20
#define SIM_SOPT4_TPM0CLKSEL_MASK                0x1000000u
#define SIM_SOPT4_TPM0CLKSEL_SHIFT               24
#define SIM_SOPT4_TPM1CLKSEL_MASK                0x2000000u
#define SIM_SOPT4_TPM1CLKSEL_SHIFT               25
#define SIM_SOPT4_TPM2CLKSEL_MASK                0x4000000u
#define SIM_SOPT4_TPM2CLKSEL_SHIFT               26
/* SOPT5 Bit Fields */
#define SIM_SOPT5_UART0TXSRC_MASK                0x3u
#define SIM_SOPT5_UART0TXSRC_SHIFT               0
#define SIM_SOPT5_UART0TXSRC(x)                  (((uint32_t)(((uint32_t)(x))<<SIM_SOPT5_UART0TXSRC_SHIFT))&SIM_SOPT5_UART0TXSRC_MASK)
#define SIM_SOPT5_UART0RXSRC_MASK                0x4u
#define SIM_SOPT5_UART0RXSRC_SHIFT               2
#define SIM_SOPT5_UART1TXSRC_MASK                0x30u
#define SIM_SOPT5_UART1TXSRC_SHIFT               4
#define SIM_SOPT5_UART1TXSRC(x)                  (((uint32_t)(((uint32_t)(x))<<SIM_SOPT5_UART1TXSRC_SHIFT))&SIM_SOPT5_UART1TXSRC_MASK)
#define SIM_SOPT5_UART1RXSRC_MASK                0x40u
#define SIM_SOPT5_UART1RXSRC_SHIFT               6
#define SIM_SOPT5_UART0ODE_MASK                  0x10000u
#define SIM_SOPT5_UART0ODE_SHIFT                 16
#define SIM_SOPT5_UART1ODE_MASK                  0x20000u
#define SIM_SOPT5_UART1ODE_SHIFT                 17
#define SIM_SOPT5_UART2ODE_MASK                  0x40000u
#define SIM_SOPT5_UART2ODE_SHIFT                 18
/* SOPT7 Bit Fields */
#define SIM_SOPT7_ADC0TRGSEL_MASK                0xFu
#define SIM_SOPT7_ADC0TRGSEL_SHIFT               0
#define SIM_SOPT7_ADC0TRGSEL(x)                  (((uint32_t)(((uint32_t)(x))<<SIM_SOPT7_ADC0TRGSEL_SHIFT))&SIM_SOPT7_ADC0TRGSEL_MASK)
#define SIM_SOPT7_ADC0PRETRGSEL_MASK             0x10u
#define SIM_SOPT7_ADC0PRETRGSEL_SHIFT            4
#define SIM_SOPT7_ADC0ALTTRGEN_MASK              0x80u
#define SIM_SOPT7_ADC0ALTTRGEN_SHIFT             7
/* SDID Bit Fields */
#define SIM_SDID_PINID_MASK                      0xFu
#define SIM_SDID_PINID_SHIFT                     0
#define SIM_SDID_PINID(x)                        (((uint32_t)(((uint32_t)(x))<<SIM_SDID_PINID_SHIFT))&SIM_SDID_PINID_MASK)
#define SIM_SDID_DIEID_MASK                      0xF80u
#define SIM_SDID_DIEID_SHIFT                     7
#define SIM_SDID_DIEID(x)                        (((uint32_t)(((uint32_t)(x))<<SIM_SDID_DIEID_SHIFT))&SIM_SDID_DIEID_MASK)
#define SIM_SDID_REVID_MASK                      0xF000u
#define SIM_SDID_REVID_SHIFT                     12
#define SIM_SDID_REVID(x)                        (((uint32_t)(((uint32_t)(x))<<SIM_SDID_REVID_SHIFT))&SIM_SDID_REVID_MASK)
#define SIM_SDID_SRAMSIZE_MASK                   0xF0000u
#define SIM_SDID_SRAMSIZE_SHIFT                  16
#define SIM_SDID_SRAMSIZE(x)                     (((uint32_t)(((uint32_t)(x))<<SIM_SDID_SRAMSIZE_SHIFT))&SIM_SDID_SRAMSIZE_MASK)
#define SIM_SDID_SERIESID_MASK                   0xF00000u
#define SIM_SDID_SERIESID_SHIFT                  20
#define SIM_SDID_SERIESID(x)                     (((uint32_t)(((uint32_t)(x))<<SIM_SDID_SERIESID_SHIFT))&SIM_SDID_SERIESID_MASK)
#define SIM_SDID_SUBFAMID_MASK                   0xF000000u
#define SIM_SDID_SUBFAMID_SHIFT                  24
#define SIM_SDID_SUBFAMID(x)                     (((uint32_t)(((uint32_t)(x))<<SIM_SDID_SUBFAMID_SHIFT))&SIM_SDID_SUBFAMID_MASK)
#define SIM_SDID_FAMID_MASK                      0xF0000000u
#define SIM_SDID_FAMID_SHIFT                     28
#define SIM_SDID_FAMID(x)                        (((uint32_t)(((uint32_t)(x))<<SIM_SDID_FAMID_SHIFT))&SIM_SDID_FAMID_MASK)
/* SCGC4 Bit Fields */
#define SIM_SCGC4_I2C0_MASK                      0x40u
#define SIM_SCGC4_I2C0_SHIFT                     6
#define SIM_SCGC4_I2C1_MASK                      0x80u
#define SIM_SCGC4_I2C1_SHIFT                     7
#define SIM_SCGC4_UART0_MASK                     0x400u
#define SIM_SCGC4_UART0_SHIFT                    10
#define SIM_SCGC4_UART1_MASK                     0x800u
#define SIM_SCGC4_UART1_SHIFT                    11
#define SIM_SCGC4_UART2_MASK                     0x1000u
#define SIM_SCGC4_UART2_SHIFT                    12
#define SIM_SCGC4_USBOTG_MASK                    0x40000u
#define SIM_SCGC4_USBOTG_SHIFT                   18
#define SIM_SCGC4_CMP_MASK                       0x80000u
#define SIM_SCGC4_CMP_SHIFT                      19
#define SIM_SCGC4_SPI0_MASK                      0x400000u
#define SIM_SCGC4_SPI0_SHIFT                     22
#define SIM_SCGC4_SPI1_MASK                      0x800000u
#define SIM_SCGC4_SPI1_SHIFT                     23
/* SCGC5 Bit Fields */
#define SIM_SCGC5_LPTMR_MASK                     0x1u
#define SIM_SCGC5_LPTMR_SHIFT                    0
#define SIM_SCGC5_TSI_MASK                       0x20u
#define SIM_SCGC5_TSI_SHIFT                      5
#define SIM_SCGC5_PORTA_MASK                     0x200u
#define SIM_SCGC5_PORTA_SHIFT                    9
#define SIM_SCGC5_PORTB_MASK                     0x400u
#define SIM_SCGC5_PORTB_SHIFT                    10
#define SIM_SCGC5_PORTC_MASK                     0x800u
#define SIM_SCGC5_PORTC_SHIFT                    11
#define SIM_SCGC5_PORTD_MASK                     0x1000u
#define SIM_SCGC5_PORTD_SHIFT                    12
#define SIM_SCGC5_PORTE_MASK                     0x2000u
#define SIM_SCGC5_PORTE_SHIFT                    13
#define SIM_SCGC5_SLCD_MASK                      0x80000u
#define SIM_SCGC5_SLCD_SHIFT                     19
/* SCGC6 Bit Fields */
#define SIM_SCGC6_FTF_MASK                       0x1u
#define SIM_SCGC6_FTF_SHIFT                      0
#define SIM_SCGC6_DMAMUX_MASK                    0x2u
#define SIM_SCGC6_DMAMUX_SHIFT                   1
#define SIM_SCGC6_I2S_MASK                       0x8000u
#define SIM_SCGC6_I2S_SHIFT                      15
#define SIM_SCGC6_PIT_MASK                       0x800000u
#define SIM_SCGC6_PIT_SHIFT                      23
#define SIM_SCGC6_TPM0_MASK                      0x1000000u
#define SIM_SCGC6_TPM0_SHIFT                     24
#define SIM_SCGC6_TPM1_MASK                      0x2000000u
#define SIM_SCGC6_TPM1_SHIFT                     25
#define SIM_SCGC6_TPM2_MASK                      0x4000000u
#define SIM_SCGC6_TPM2_SHIFT                     26
#define SIM_SCGC6_ADC0_MASK                      0x8000000u
#define SIM_SCGC6_ADC0_SHIFT                     27
#define SIM_SCGC6_RTC_MASK                       0x20000000u
#define SIM_SCGC6_RTC_SHIFT                      29
#define SIM_SCGC6_DAC0_MASK                      0x80000000u
#define SIM_SCGC6_DAC0_SHIFT                     31
/* SCGC7 Bit Fields */
#define SIM_SCGC7_DMA_MASK                       0x100u
#define SIM_SCGC7_DMA_SHIFT                      8
/* CLKDIV1 Bit Fields */
#define SIM_CLKDIV1_OUTDIV4_MASK                 0x70000u
#define SIM_CLKDIV1_OUTDIV4_SHIFT                16
#define SIM_CLKDIV1_OUTDIV4(x)                   (((uint32_t)(((uint32_t)(x))<<SIM_CLKDIV1_OUTDIV4_SHIFT))&SIM_CLKDIV1_OUTDIV4_MASK)
#define SIM_CLKDIV1_OUTDIV1_MASK                 0xF0000000u
#define SIM_CLKDIV1_OUTDIV1_SHIFT                28
#define SIM_CLKDIV1_OUTDIV1(x)                   (((uint32_t)(((uint32_t)(x))<<SIM_CLKDIV1_OUTDIV1_SHIFT))&SIM_CLKDIV1_OUTDIV1_MASK)
/* FCFG1 Bit Fields */
#define SIM_FCFG1_FLASHDIS_MASK                  0x1u
#define SIM_FCFG1_FLASHDIS_SHIFT                 0
#define SIM_FCFG1_FLASHDOZE_MASK                 0x2u
#define SIM_FCFG1_FLASHDOZE_SHIFT                1
#define SIM_FCFG1_PFSIZE_MASK                    0xF000000u
#define SIM_FCFG1_PFSIZE_SHIFT                   24
#define SIM_FCFG1_PFSIZE(x)                      (((uint32_t)(((uint32_t)(x))<<SIM_FCFG1_PFSIZE_SHIFT))&SIM_FCFG1_PFSIZE_MASK)
/* FCFG2 Bit Fields */
#define SIM_FCFG2_MAXADDR1_MASK                  0x7F0000u
#define SIM_FCFG2_MAXADDR1_SHIFT                 16
#define SIM_FCFG2_MAXADDR1(x)                    (((uint32_t)(((uint32_t)(x))<<SIM_FCFG2_MAXADDR1_SHIFT))&SIM_FCFG2_MAXADDR1_MASK)
#define SIM_FCFG2_MAXADDR0_MASK                  0x7F000000u
#define SIM_FCFG2_MAXADDR0_SHIFT                 24
#define SIM_FCFG2_MAXADDR0(x)                    (((uint32_t)(((uint32_t)(x))<<SIM_FCFG2_MAXADDR0_SHIFT))&SIM_FCFG2_MAXADDR0_MASK)
/* UIDMH Bit Fields */
#define SIM_UIDMH_UID_MASK                       0xFFFFu
#define SIM_UIDMH_UID_SHIFT                      0
#define SIM_UIDMH_UID(x)                         (((uint32_t)(((uint32_t)(x))<<SIM_UIDMH_UID_SHIFT))&SIM_UIDMH_UID_MASK)
/* UIDML Bit Fields */
#define SIM_UIDML_UID_MASK                       0xFFFFFFFFu
#define SIM_UIDML_UID_SHIFT                      0
#define SIM_UIDML_UID(x)                         (((uint32_t)(((uint32_t)(x))<<SIM_UIDML_UID_SHIFT))&SIM_UIDML_UID_MASK)
/* UIDL Bit Fields */
#define SIM_UIDL_UID_MASK                        0xFFFFFFFFu
#define SIM_UIDL_UID_SHIFT                       0
#define SIM_UIDL_UID(x)                          (((uint32_t)(((uint32_t)(x))<<SIM_UIDL_UID_SHIFT))&SIM_UIDL_UID_MASK)
/* COPC Bit Fields */
#define SIM_COPC_COPW_MASK                       0x1u
#define SIM_COPC_COPW_SHIFT                      0
#define SIM_COPC_COPCLKS_MASK                    0x2u
#define SIM_COPC_COPCLKS_SHIFT                   1
#define SIM_COPC_COPT_MASK                       0xCu
#define SIM_COPC_COPT_SHIFT                      2
#define SIM_COPC_COPT(x)                         (((uint32_t)(((uint32_t)(x))<<SIM_COPC_COPT_SHIFT))&SIM_COPC_COPT_MASK)
/* SRVCOP Bit Fields */
#define SIM_SRVCOP_SRVCOP_MASK                   0xFFu
#define SIM_SRVCOP_SRVCOP_SHIFT                  0
#define SIM_SRVCOP_SRVCOP(x)                     (((uint32_t)(((uint32_t)(x))<<SIM_SRVCOP_SRVCOP_SHIFT))&SIM_SRVCOP_SRVCOP_MASK)

/*!
 * @}
 */ /* end of group SIM_Register_Masks */


/* SIM - Peripheral instance base addresses */
/** Peripheral SIM base address */
#define SIM_BASE                                 (0x40047000u)
/** Peripheral SIM base pointer */
#define SIM                                      ((SIM_Type *)SIM_BASE)
/** Array initializer of SIM peripheral base pointers */
#define SIM_BASES                                { SIM }

/*!
 * @}
 */ /* end of group SIM_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- SMC Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup SMC_Peripheral_Access_Layer SMC Peripheral Access Layer
 * @{
 */

/** SMC - Register Layout Typedef */
typedef struct {
        __IO uint8_t PMPROT;                             /**< Power Mode Protection register, offset: 0x0 */
        __IO uint8_t PMCTRL;                             /**< Power Mode Control register, offset: 0x1 */
        __IO uint8_t STOPCTRL;                           /**< Stop Control Register, offset: 0x2 */
        __I  uint8_t PMSTAT;                             /**< Power Mode Status register, offset: 0x3 */
} SMC_Type;

/* ----------------------------------------------------------------------------
   -- SMC Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup SMC_Register_Masks SMC Register Masks
 * @{
 */

/* PMPROT Bit Fields */
#define SMC_PMPROT_AVLLS_MASK                    0x2u
#define SMC_PMPROT_AVLLS_SHIFT                   1
#define SMC_PMPROT_ALLS_MASK                     0x8u
#define SMC_PMPROT_ALLS_SHIFT                    3
#define SMC_PMPROT_AVLP_MASK                     0x20u
#define SMC_PMPROT_AVLP_SHIFT                    5
/* PMCTRL Bit Fields */
#define SMC_PMCTRL_STOPM_MASK                    0x7u
#define SMC_PMCTRL_STOPM_SHIFT                   0
#define SMC_PMCTRL_STOPM(x)                      (((uint8_t)(((uint8_t)(x))<<SMC_PMCTRL_STOPM_SHIFT))&SMC_PMCTRL_STOPM_MASK)
#define SMC_PMCTRL_STOPA_MASK                    0x8u
#define SMC_PMCTRL_STOPA_SHIFT                   3
#define SMC_PMCTRL_RUNM_MASK                     0x60u
#define SMC_PMCTRL_RUNM_SHIFT                    5
#define SMC_PMCTRL_RUNM(x)                       (((uint8_t)(((uint8_t)(x))<<SMC_PMCTRL_RUNM_SHIFT))&SMC_PMCTRL_RUNM_MASK)
/* STOPCTRL Bit Fields */
#define SMC_STOPCTRL_VLLSM_MASK                  0x7u
#define SMC_STOPCTRL_VLLSM_SHIFT                 0
#define SMC_STOPCTRL_VLLSM(x)                    (((uint8_t)(((uint8_t)(x))<<SMC_STOPCTRL_VLLSM_SHIFT))&SMC_STOPCTRL_VLLSM_MASK)
#define SMC_STOPCTRL_PORPO_MASK                  0x20u
#define SMC_STOPCTRL_PORPO_SHIFT                 5
#define SMC_STOPCTRL_PSTOPO_MASK                 0xC0u
#define SMC_STOPCTRL_PSTOPO_SHIFT                6
#define SMC_STOPCTRL_PSTOPO(x)                   (((uint8_t)(((uint8_t)(x))<<SMC_STOPCTRL_PSTOPO_SHIFT))&SMC_STOPCTRL_PSTOPO_MASK)
/* PMSTAT Bit Fields */
#define SMC_PMSTAT_PMSTAT_MASK                   0x7Fu
#define SMC_PMSTAT_PMSTAT_SHIFT                  0
#define SMC_PMSTAT_PMSTAT(x)                     (((uint8_t)(((uint8_t)(x))<<SMC_PMSTAT_PMSTAT_SHIFT))&SMC_PMSTAT_PMSTAT_MASK)

/*!
 * @}
 */ /* end of group SMC_Register_Masks */


/* SMC - Peripheral instance base addresses */
/** Peripheral SMC base address */
#define SMC_BASE                                 (0x4007E000u)
/** Peripheral SMC base pointer */
#define SMC                                      ((SMC_Type *)SMC_BASE)
/** Array initializer of SMC peripheral base pointers */
#define SMC_BASES                                { SMC }

/*!
 * @}
 */ /* end of group SMC_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- SPI Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup SPI_Peripheral_Access_Layer SPI Peripheral Access Layer
 * @{
 */

/** SPI - Register Layout Typedef */
typedef struct {
        __I  uint8_t S;                                  /**< SPI status register, offset: 0x0 */
        __IO uint8_t BR;                                 /**< SPI baud rate register, offset: 0x1 */
        __IO uint8_t C2;                                 /**< SPI control register 2, offset: 0x2 */
        __IO uint8_t C1;                                 /**< SPI control register 1, offset: 0x3 */
        __IO uint8_t ML;                                 /**< SPI match register low, offset: 0x4 */
        __IO uint8_t MH;                                 /**< SPI match register high, offset: 0x5 */
        __IO uint8_t DL;                                 /**< SPI data register low, offset: 0x6 */
        __IO uint8_t DH;                                 /**< SPI data register high, offset: 0x7 */
        uint8_t RESERVED_0[2];
        __IO uint8_t CI;                                 /**< SPI clear interrupt register, offset: 0xA */
        __IO uint8_t C3;                                 /**< SPI control register 3, offset: 0xB */
} SPI_Type;

/* ----------------------------------------------------------------------------
   -- SPI Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup SPI_Register_Masks SPI Register Masks
 * @{
 */

/* S Bit Fields */
#define SPI_S_RFIFOEF_MASK                       0x1u
#define SPI_S_RFIFOEF_SHIFT                      0
#define SPI_S_TXFULLF_MASK                       0x2u
#define SPI_S_TXFULLF_SHIFT                      1
#define SPI_S_TNEAREF_MASK                       0x4u
#define SPI_S_TNEAREF_SHIFT                      2
#define SPI_S_RNFULLF_MASK                       0x8u
#define SPI_S_RNFULLF_SHIFT                      3
#define SPI_S_MODF_MASK                          0x10u
#define SPI_S_MODF_SHIFT                         4
#define SPI_S_SPTEF_MASK                         0x20u
#define SPI_S_SPTEF_SHIFT                        5
#define SPI_S_SPMF_MASK                          0x40u
#define SPI_S_SPMF_SHIFT                         6
#define SPI_S_SPRF_MASK                          0x80u
#define SPI_S_SPRF_SHIFT                         7
/* BR Bit Fields */
#define SPI_BR_SPR_MASK                          0xFu
#define SPI_BR_SPR_SHIFT                         0
#define SPI_BR_SPR(x)                            (((uint8_t)(((uint8_t)(x))<<SPI_BR_SPR_SHIFT))&SPI_BR_SPR_MASK)
#define SPI_BR_SPPR_MASK                         0x70u
#define SPI_BR_SPPR_SHIFT                        4
#define SPI_BR_SPPR(x)                           (((uint8_t)(((uint8_t)(x))<<SPI_BR_SPPR_SHIFT))&SPI_BR_SPPR_MASK)
/* C2 Bit Fields */
#define SPI_C2_SPC0_MASK                         0x1u
#define SPI_C2_SPC0_SHIFT                        0
#define SPI_C2_SPISWAI_MASK                      0x2u
#define SPI_C2_SPISWAI_SHIFT                     1
#define SPI_C2_RXDMAE_MASK                       0x4u
#define SPI_C2_RXDMAE_SHIFT                      2
#define SPI_C2_BIDIROE_MASK                      0x8u
#define SPI_C2_BIDIROE_SHIFT                     3
#define SPI_C2_MODFEN_MASK                       0x10u
#define SPI_C2_MODFEN_SHIFT                      4
#define SPI_C2_TXDMAE_MASK                       0x20u
#define SPI_C2_TXDMAE_SHIFT                      5
#define SPI_C2_SPIMODE_MASK                      0x40u
#define SPI_C2_SPIMODE_SHIFT                     6
#define SPI_C2_SPMIE_MASK                        0x80u
#define SPI_C2_SPMIE_SHIFT                       7
/* C1 Bit Fields */
#define SPI_C1_LSBFE_MASK                        0x1u
#define SPI_C1_LSBFE_SHIFT                       0
#define SPI_C1_SSOE_MASK                         0x2u
#define SPI_C1_SSOE_SHIFT                        1
#define SPI_C1_CPHA_MASK                         0x4u
#define SPI_C1_CPHA_SHIFT                        2
#define SPI_C1_CPOL_MASK                         0x8u
#define SPI_C1_CPOL_SHIFT                        3
#define SPI_C1_MSTR_MASK                         0x10u
#define SPI_C1_MSTR_SHIFT                        4
#define SPI_C1_SPTIE_MASK                        0x20u
#define SPI_C1_SPTIE_SHIFT                       5
#define SPI_C1_SPE_MASK                          0x40u
#define SPI_C1_SPE_SHIFT                         6
#define SPI_C1_SPIE_MASK                         0x80u
#define SPI_C1_SPIE_SHIFT                        7
/* ML Bit Fields */
#define SPI_ML_Bits_MASK                         0xFFu
#define SPI_ML_Bits_SHIFT                        0
#define SPI_ML_Bits(x)                           (((uint8_t)(((uint8_t)(x))<<SPI_ML_Bits_SHIFT))&SPI_ML_Bits_MASK)
/* MH Bit Fields */
#define SPI_MH_Bits_MASK                         0xFFu
#define SPI_MH_Bits_SHIFT                        0
#define SPI_MH_Bits(x)                           (((uint8_t)(((uint8_t)(x))<<SPI_MH_Bits_SHIFT))&SPI_MH_Bits_MASK)
/* DL Bit Fields */
#define SPI_DL_Bits_MASK                         0xFFu
#define SPI_DL_Bits_SHIFT                        0
#define SPI_DL_Bits(x)                           (((uint8_t)(((uint8_t)(x))<<SPI_DL_Bits_SHIFT))&SPI_DL_Bits_MASK)
/* DH Bit Fields */
#define SPI_DH_Bits_MASK                         0xFFu
#define SPI_DH_Bits_SHIFT                        0
#define SPI_DH_Bits(x)                           (((uint8_t)(((uint8_t)(x))<<SPI_DH_Bits_SHIFT))&SPI_DH_Bits_MASK)
/* CI Bit Fields */
#define SPI_CI_SPRFCI_MASK                       0x1u
#define SPI_CI_SPRFCI_SHIFT                      0
#define SPI_CI_SPTEFCI_MASK                      0x2u
#define SPI_CI_SPTEFCI_SHIFT                     1
#define SPI_CI_RNFULLFCI_MASK                    0x4u
#define SPI_CI_RNFULLFCI_SHIFT                   2
#define SPI_CI_TNEAREFCI_MASK                    0x8u
#define SPI_CI_TNEAREFCI_SHIFT                   3
#define SPI_CI_RXFOF_MASK                        0x10u
#define SPI_CI_RXFOF_SHIFT                       4
#define SPI_CI_TXFOF_MASK                        0x20u
#define SPI_CI_TXFOF_SHIFT                       5
#define SPI_CI_RXFERR_MASK                       0x40u
#define SPI_CI_RXFERR_SHIFT                      6
#define SPI_CI_TXFERR_MASK                       0x80u
#define SPI_CI_TXFERR_SHIFT                      7
/* C3 Bit Fields */
#define SPI_C3_FIFOMODE_MASK                     0x1u
#define SPI_C3_FIFOMODE_SHIFT                    0
#define SPI_C3_RNFULLIEN_MASK                    0x2u
#define SPI_C3_RNFULLIEN_SHIFT                   1
#define SPI_C3_TNEARIEN_MASK                     0x4u
#define SPI_C3_TNEARIEN_SHIFT                    2
#define SPI_C3_INTCLR_MASK                       0x8u
#define SPI_C3_INTCLR_SHIFT                      3
#define SPI_C3_RNFULLF_MARK_MASK                 0x10u
#define SPI_C3_RNFULLF_MARK_SHIFT                4
#define SPI_C3_TNEAREF_MARK_MASK                 0x20u
#define SPI_C3_TNEAREF_MARK_SHIFT                5

/*!
 * @}
 */ /* end of group SPI_Register_Masks */


/* SPI - Peripheral instance base addresses */
/** Peripheral SPI0 base address */
#define SPI0_BASE                                (0x40076000u)
/** Peripheral SPI0 base pointer */
#define SPI0                                     ((SPI_Type *)SPI0_BASE)
/** Peripheral SPI1 base address */
#define SPI1_BASE                                (0x40077000u)
/** Peripheral SPI1 base pointer */
#define SPI1                                     ((SPI_Type *)SPI1_BASE)
/** Array initializer of SPI peripheral base pointers */
#define SPI_BASES                                { SPI0, SPI1 }

/*!
 * @}
 */ /* end of group SPI_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- TPM Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup TPM_Peripheral_Access_Layer TPM Peripheral Access Layer
 * @{
 */

/** TPM - Register Layout Typedef */
typedef struct {
        __IO uint32_t SC;                                /**< Status and Control, offset: 0x0 */
        __IO uint32_t CNT;                               /**< Counter, offset: 0x4 */
        __IO uint32_t MOD;                               /**< Modulo, offset: 0x8 */
        struct {                                         /* offset: 0xC, array step: 0x8 */
                __IO uint32_t CnSC;                              /**< Channel (n) Status and Control, array offset: 0xC, array step: 0x8 */
                __IO uint32_t CnV;                               /**< Channel (n) Value, array offset: 0x10, array step: 0x8 */
        } CONTROLS[6];
        uint8_t RESERVED_0[20];
        __IO uint32_t STATUS;                            /**< Capture and Compare Status, offset: 0x50 */
        uint8_t RESERVED_1[48];
        __IO uint32_t CONF;                              /**< Configuration, offset: 0x84 */
} TPM_Type;

/* ----------------------------------------------------------------------------
   -- TPM Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup TPM_Register_Masks TPM Register Masks
 * @{
 */

/* SC Bit Fields */
#define TPM_SC_PS_MASK                           0x7u
#define TPM_SC_PS_SHIFT                          0
#define TPM_SC_PS(x)                             (((uint32_t)(((uint32_t)(x))<<TPM_SC_PS_SHIFT))&TPM_SC_PS_MASK)
#define TPM_SC_CMOD_MASK                         0x18u
#define TPM_SC_CMOD_SHIFT                        3
#define TPM_SC_CMOD(x)                           (((uint32_t)(((uint32_t)(x))<<TPM_SC_CMOD_SHIFT))&TPM_SC_CMOD_MASK)
#define TPM_SC_CPWMS_MASK                        0x20u
#define TPM_SC_CPWMS_SHIFT                       5
#define TPM_SC_TOIE_MASK                         0x40u
#define TPM_SC_TOIE_SHIFT                        6
#define TPM_SC_TOF_MASK                          0x80u
#define TPM_SC_TOF_SHIFT                         7
#define TPM_SC_DMA_MASK                          0x100u
#define TPM_SC_DMA_SHIFT                         8
/* CNT Bit Fields */
#define TPM_CNT_COUNT_MASK                       0xFFFFu
#define TPM_CNT_COUNT_SHIFT                      0
#define TPM_CNT_COUNT(x)                         (((uint32_t)(((uint32_t)(x))<<TPM_CNT_COUNT_SHIFT))&TPM_CNT_COUNT_MASK)
/* MOD Bit Fields */
#define TPM_MOD_MOD_MASK                         0xFFFFu
#define TPM_MOD_MOD_SHIFT                        0
#define TPM_MOD_MOD(x)                           (((uint32_t)(((uint32_t)(x))<<TPM_MOD_MOD_SHIFT))&TPM_MOD_MOD_MASK)
/* CnSC Bit Fields */
#define TPM_CnSC_DMA_MASK                        0x1u
#define TPM_CnSC_DMA_SHIFT                       0
#define TPM_CnSC_ELSA_MASK                       0x4u
#define TPM_CnSC_ELSA_SHIFT                      2
#define TPM_CnSC_ELSB_MASK                       0x8u
#define TPM_CnSC_ELSB_SHIFT                      3
#define TPM_CnSC_MSA_MASK                        0x10u
#define TPM_CnSC_MSA_SHIFT                       4
#define TPM_CnSC_MSB_MASK                        0x20u
#define TPM_CnSC_MSB_SHIFT                       5
#define TPM_CnSC_CHIE_MASK                       0x40u
#define TPM_CnSC_CHIE_SHIFT                      6
#define TPM_CnSC_CHF_MASK                        0x80u
#define TPM_CnSC_CHF_SHIFT                       7
/* CnV Bit Fields */
#define TPM_CnV_VAL_MASK                         0xFFFFu
#define TPM_CnV_VAL_SHIFT                        0
#define TPM_CnV_VAL(x)                           (((uint32_t)(((uint32_t)(x))<<TPM_CnV_VAL_SHIFT))&TPM_CnV_VAL_MASK)
/* STATUS Bit Fields */
#define TPM_STATUS_CH0F_MASK                     0x1u
#define TPM_STATUS_CH0F_SHIFT                    0
#define TPM_STATUS_CH1F_MASK                     0x2u
#define TPM_STATUS_CH1F_SHIFT                    1
#define TPM_STATUS_CH2F_MASK                     0x4u
#define TPM_STATUS_CH2F_SHIFT                    2
#define TPM_STATUS_CH3F_MASK                     0x8u
#define TPM_STATUS_CH3F_SHIFT                    3
#define TPM_STATUS_CH4F_MASK                     0x10u
#define TPM_STATUS_CH4F_SHIFT                    4
#define TPM_STATUS_CH5F_MASK                     0x20u
#define TPM_STATUS_CH5F_SHIFT                    5
#define TPM_STATUS_TOF_MASK                      0x100u
#define TPM_STATUS_TOF_SHIFT                     8
/* CONF Bit Fields */
#define TPM_CONF_DOZEEN_MASK                     0x20u
#define TPM_CONF_DOZEEN_SHIFT                    5
#define TPM_CONF_DBGMODE_MASK                    0xC0u
#define TPM_CONF_DBGMODE_SHIFT                   6
#define TPM_CONF_DBGMODE(x)                      (((uint32_t)(((uint32_t)(x))<<TPM_CONF_DBGMODE_SHIFT))&TPM_CONF_DBGMODE_MASK)
#define TPM_CONF_GTBEEN_MASK                     0x200u
#define TPM_CONF_GTBEEN_SHIFT                    9
#define TPM_CONF_CSOT_MASK                       0x10000u
#define TPM_CONF_CSOT_SHIFT                      16
#define TPM_CONF_CSOO_MASK                       0x20000u
#define TPM_CONF_CSOO_SHIFT                      17
#define TPM_CONF_CROT_MASK                       0x40000u
#define TPM_CONF_CROT_SHIFT                      18
#define TPM_CONF_TRGSEL_MASK                     0xF000000u
#define TPM_CONF_TRGSEL_SHIFT                    24
#define TPM_CONF_TRGSEL(x)                       (((uint32_t)(((uint32_t)(x))<<TPM_CONF_TRGSEL_SHIFT))&TPM_CONF_TRGSEL_MASK)

/*!
 * @}
 */ /* end of group TPM_Register_Masks */


/* TPM - Peripheral instance base addresses */
/** Peripheral TPM0 base address */
#define TPM0_BASE                                (0x40038000u)
/** Peripheral TPM0 base pointer */
#define TPM0                                     ((TPM_Type *)TPM0_BASE)
/** Peripheral TPM1 base address */
#define TPM1_BASE                                (0x40039000u)
/** Peripheral TPM1 base pointer */
#define TPM1                                     ((TPM_Type *)TPM1_BASE)
/** Peripheral TPM2 base address */
#define TPM2_BASE                                (0x4003A000u)
/** Peripheral TPM2 base pointer */
#define TPM2                                     ((TPM_Type *)TPM2_BASE)
/** Array initializer of TPM peripheral base pointers */
#define TPM_BASES                                { TPM0, TPM1, TPM2 }

/*!
 * @}
 */ /* end of group TPM_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- TSI Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup TSI_Peripheral_Access_Layer TSI Peripheral Access Layer
 * @{
 */

/** TSI - Register Layout Typedef */
typedef struct {
        __IO uint32_t GENCS;                             /**< TSI General Control and Status Register, offset: 0x0 */
        __IO uint32_t DATA;                              /**< TSI DATA Register, offset: 0x4 */
        __IO uint32_t TSHD;                              /**< TSI Threshold Register, offset: 0x8 */
} TSI_Type;

/* ----------------------------------------------------------------------------
   -- TSI Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup TSI_Register_Masks TSI Register Masks
 * @{
 */

/* GENCS Bit Fields */
#define TSI_GENCS_CURSW_MASK                     0x2u
#define TSI_GENCS_CURSW_SHIFT                    1
#define TSI_GENCS_EOSF_MASK                      0x4u
#define TSI_GENCS_EOSF_SHIFT                     2
#define TSI_GENCS_SCNIP_MASK                     0x8u
#define TSI_GENCS_SCNIP_SHIFT                    3
#define TSI_GENCS_STM_MASK                       0x10u
#define TSI_GENCS_STM_SHIFT                      4
#define TSI_GENCS_STPE_MASK                      0x20u
#define TSI_GENCS_STPE_SHIFT                     5
#define TSI_GENCS_TSIIEN_MASK                    0x40u
#define TSI_GENCS_TSIIEN_SHIFT                   6
#define TSI_GENCS_TSIEN_MASK                     0x80u
#define TSI_GENCS_TSIEN_SHIFT                    7
#define TSI_GENCS_NSCN_MASK                      0x1F00u
#define TSI_GENCS_NSCN_SHIFT                     8
#define TSI_GENCS_NSCN(x)                        (((uint32_t)(((uint32_t)(x))<<TSI_GENCS_NSCN_SHIFT))&TSI_GENCS_NSCN_MASK)
#define TSI_GENCS_PS_MASK                        0xE000u
#define TSI_GENCS_PS_SHIFT                       13
#define TSI_GENCS_PS(x)                          (((uint32_t)(((uint32_t)(x))<<TSI_GENCS_PS_SHIFT))&TSI_GENCS_PS_MASK)
#define TSI_GENCS_EXTCHRG_MASK                   0x70000u
#define TSI_GENCS_EXTCHRG_SHIFT                  16
#define TSI_GENCS_EXTCHRG(x)                     (((uint32_t)(((uint32_t)(x))<<TSI_GENCS_EXTCHRG_SHIFT))&TSI_GENCS_EXTCHRG_MASK)
#define TSI_GENCS_DVOLT_MASK                     0x180000u
#define TSI_GENCS_DVOLT_SHIFT                    19
#define TSI_GENCS_DVOLT(x)                       (((uint32_t)(((uint32_t)(x))<<TSI_GENCS_DVOLT_SHIFT))&TSI_GENCS_DVOLT_MASK)
#define TSI_GENCS_REFCHRG_MASK                   0xE00000u
#define TSI_GENCS_REFCHRG_SHIFT                  21
#define TSI_GENCS_REFCHRG(x)                     (((uint32_t)(((uint32_t)(x))<<TSI_GENCS_REFCHRG_SHIFT))&TSI_GENCS_REFCHRG_MASK)
#define TSI_GENCS_MODE_MASK                      0xF000000u
#define TSI_GENCS_MODE_SHIFT                     24
#define TSI_GENCS_MODE(x)                        (((uint32_t)(((uint32_t)(x))<<TSI_GENCS_MODE_SHIFT))&TSI_GENCS_MODE_MASK)
#define TSI_GENCS_ESOR_MASK                      0x10000000u
#define TSI_GENCS_ESOR_SHIFT                     28
#define TSI_GENCS_OUTRGF_MASK                    0x80000000u
#define TSI_GENCS_OUTRGF_SHIFT                   31
/* DATA Bit Fields */
#define TSI_DATA_TSICNT_MASK                     0xFFFFu
#define TSI_DATA_TSICNT_SHIFT                    0
#define TSI_DATA_TSICNT(x)                       (((uint32_t)(((uint32_t)(x))<<TSI_DATA_TSICNT_SHIFT))&TSI_DATA_TSICNT_MASK)
#define TSI_DATA_SWTS_MASK                       0x400000u
#define TSI_DATA_SWTS_SHIFT                      22
#define TSI_DATA_DMAEN_MASK                      0x800000u
#define TSI_DATA_DMAEN_SHIFT                     23
#define TSI_DATA_TSICH_MASK                      0xF0000000u
#define TSI_DATA_TSICH_SHIFT                     28
#define TSI_DATA_TSICH(x)                        (((uint32_t)(((uint32_t)(x))<<TSI_DATA_TSICH_SHIFT))&TSI_DATA_TSICH_MASK)
/* TSHD Bit Fields */
#define TSI_TSHD_THRESL_MASK                     0xFFFFu
#define TSI_TSHD_THRESL_SHIFT                    0
#define TSI_TSHD_THRESL(x)                       (((uint32_t)(((uint32_t)(x))<<TSI_TSHD_THRESL_SHIFT))&TSI_TSHD_THRESL_MASK)
#define TSI_TSHD_THRESH_MASK                     0xFFFF0000u
#define TSI_TSHD_THRESH_SHIFT                    16
#define TSI_TSHD_THRESH(x)                       (((uint32_t)(((uint32_t)(x))<<TSI_TSHD_THRESH_SHIFT))&TSI_TSHD_THRESH_MASK)

/*!
 * @}
 */ /* end of group TSI_Register_Masks */


/* TSI - Peripheral instance base addresses */
/** Peripheral TSI0 base address */
#define TSI0_BASE                                (0x40045000u)
/** Peripheral TSI0 base pointer */
#define TSI0                                     ((TSI_Type *)TSI0_BASE)
/** Array initializer of TSI peripheral base pointers */
#define TSI_BASES                                { TSI0 }

/*!
 * @}
 */ /* end of group TSI_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- UART Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup UART_Peripheral_Access_Layer UART Peripheral Access Layer
 * @{
 */

/** UART - Register Layout Typedef */
typedef struct {
        __IO uint8_t BDH;                                /**< UART Baud Rate Register: High, offset: 0x0 */
        __IO uint8_t BDL;                                /**< UART Baud Rate Register: Low, offset: 0x1 */
        __IO uint8_t C1;                                 /**< UART Control Register 1, offset: 0x2 */
        __IO uint8_t C2;                                 /**< UART Control Register 2, offset: 0x3 */
        __I  uint8_t S1;                                 /**< UART Status Register 1, offset: 0x4 */
        __IO uint8_t S2;                                 /**< UART Status Register 2, offset: 0x5 */
        __IO uint8_t C3;                                 /**< UART Control Register 3, offset: 0x6 */
        __IO uint8_t D;                                  /**< UART Data Register, offset: 0x7 */
        __IO uint8_t C4;                                 /**< UART Control Register 4, offset: 0x8 */
} UART_Type;

/* ----------------------------------------------------------------------------
   -- UART Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup UART_Register_Masks UART Register Masks
 * @{
 */

/* BDH Bit Fields */
#define UART_BDH_SBR_MASK                        0x1Fu
#define UART_BDH_SBR_SHIFT                       0
#define UART_BDH_SBR(x)                          (((uint8_t)(((uint8_t)(x))<<UART_BDH_SBR_SHIFT))&UART_BDH_SBR_MASK)
#define UART_BDH_SBNS_MASK                       0x20u
#define UART_BDH_SBNS_SHIFT                      5
#define UART_BDH_RXEDGIE_MASK                    0x40u
#define UART_BDH_RXEDGIE_SHIFT                   6
#define UART_BDH_LBKDIE_MASK                     0x80u
#define UART_BDH_LBKDIE_SHIFT                    7
/* BDL Bit Fields */
#define UART_BDL_SBR_MASK                        0xFFu
#define UART_BDL_SBR_SHIFT                       0
#define UART_BDL_SBR(x)                          (((uint8_t)(((uint8_t)(x))<<UART_BDL_SBR_SHIFT))&UART_BDL_SBR_MASK)
/* C1 Bit Fields */
#define UART_C1_PT_MASK                          0x1u
#define UART_C1_PT_SHIFT                         0
#define UART_C1_PE_MASK                          0x2u
#define UART_C1_PE_SHIFT                         1
#define UART_C1_ILT_MASK                         0x4u
#define UART_C1_ILT_SHIFT                        2
#define UART_C1_WAKE_MASK                        0x8u
#define UART_C1_WAKE_SHIFT                       3
#define UART_C1_M_MASK                           0x10u
#define UART_C1_M_SHIFT                          4
#define UART_C1_RSRC_MASK                        0x20u
#define UART_C1_RSRC_SHIFT                       5
#define UART_C1_UARTSWAI_MASK                    0x40u
#define UART_C1_UARTSWAI_SHIFT                   6
#define UART_C1_LOOPS_MASK                       0x80u
#define UART_C1_LOOPS_SHIFT                      7
/* C2 Bit Fields */
#define UART_C2_SBK_MASK                         0x1u
#define UART_C2_SBK_SHIFT                        0
#define UART_C2_RWU_MASK                         0x2u
#define UART_C2_RWU_SHIFT                        1
#define UART_C2_RE_MASK                          0x4u
#define UART_C2_RE_SHIFT                         2
#define UART_C2_TE_MASK                          0x8u
#define UART_C2_TE_SHIFT                         3
#define UART_C2_ILIE_MASK                        0x10u
#define UART_C2_ILIE_SHIFT                       4
#define UART_C2_RIE_MASK                         0x20u
#define UART_C2_RIE_SHIFT                        5
#define UART_C2_TCIE_MASK                        0x40u
#define UART_C2_TCIE_SHIFT                       6
#define UART_C2_TIE_MASK                         0x80u
#define UART_C2_TIE_SHIFT                        7
/* S1 Bit Fields */
#define UART_S1_PF_MASK                          0x1u
#define UART_S1_PF_SHIFT                         0
#define UART_S1_FE_MASK                          0x2u
#define UART_S1_FE_SHIFT                         1
#define UART_S1_NF_MASK                          0x4u
#define UART_S1_NF_SHIFT                         2
#define UART_S1_OR_MASK                          0x8u
#define UART_S1_OR_SHIFT                         3
#define UART_S1_IDLE_MASK                        0x10u
#define UART_S1_IDLE_SHIFT                       4
#define UART_S1_RDRF_MASK                        0x20u
#define UART_S1_RDRF_SHIFT                       5
#define UART_S1_TC_MASK                          0x40u
#define UART_S1_TC_SHIFT                         6
#define UART_S1_TDRE_MASK                        0x80u
#define UART_S1_TDRE_SHIFT                       7
/* S2 Bit Fields */
#define UART_S2_RAF_MASK                         0x1u
#define UART_S2_RAF_SHIFT                        0
#define UART_S2_LBKDE_MASK                       0x2u
#define UART_S2_LBKDE_SHIFT                      1
#define UART_S2_BRK13_MASK                       0x4u
#define UART_S2_BRK13_SHIFT                      2
#define UART_S2_RWUID_MASK                       0x8u
#define UART_S2_RWUID_SHIFT                      3
#define UART_S2_RXINV_MASK                       0x10u
#define UART_S2_RXINV_SHIFT                      4
#define UART_S2_RXEDGIF_MASK                     0x40u
#define UART_S2_RXEDGIF_SHIFT                    6
#define UART_S2_LBKDIF_MASK                      0x80u
#define UART_S2_LBKDIF_SHIFT                     7
/* C3 Bit Fields */
#define UART_C3_PEIE_MASK                        0x1u
#define UART_C3_PEIE_SHIFT                       0
#define UART_C3_FEIE_MASK                        0x2u
#define UART_C3_FEIE_SHIFT                       1
#define UART_C3_NEIE_MASK                        0x4u
#define UART_C3_NEIE_SHIFT                       2
#define UART_C3_ORIE_MASK                        0x8u
#define UART_C3_ORIE_SHIFT                       3
#define UART_C3_TXINV_MASK                       0x10u
#define UART_C3_TXINV_SHIFT                      4
#define UART_C3_TXDIR_MASK                       0x20u
#define UART_C3_TXDIR_SHIFT                      5
#define UART_C3_T8_MASK                          0x40u
#define UART_C3_T8_SHIFT                         6
#define UART_C3_R8_MASK                          0x80u
#define UART_C3_R8_SHIFT                         7
/* D Bit Fields */
#define UART_D_R0T0_MASK                         0x1u
#define UART_D_R0T0_SHIFT                        0
#define UART_D_R1T1_MASK                         0x2u
#define UART_D_R1T1_SHIFT                        1
#define UART_D_R2T2_MASK                         0x4u
#define UART_D_R2T2_SHIFT                        2
#define UART_D_R3T3_MASK                         0x8u
#define UART_D_R3T3_SHIFT                        3
#define UART_D_R4T4_MASK                         0x10u
#define UART_D_R4T4_SHIFT                        4
#define UART_D_R5T5_MASK                         0x20u
#define UART_D_R5T5_SHIFT                        5
#define UART_D_R6T6_MASK                         0x40u
#define UART_D_R6T6_SHIFT                        6
#define UART_D_R7T7_MASK                         0x80u
#define UART_D_R7T7_SHIFT                        7
/* C4 Bit Fields */
#define UART_C4_RDMAS_MASK                       0x20u
#define UART_C4_RDMAS_SHIFT                      5
#define UART_C4_TDMAS_MASK                       0x80u
#define UART_C4_TDMAS_SHIFT                      7

/*!
 * @}
 */ /* end of group UART_Register_Masks */


/* UART - Peripheral instance base addresses */
/** Peripheral UART1 base address */
#define UART1_BASE                               (0x4006B000u)
/** Peripheral UART1 base pointer */
#define UART1                                    ((UART_Type *)UART1_BASE)
/** Peripheral UART2 base address */
#define UART2_BASE                               (0x4006C000u)
/** Peripheral UART2 base pointer */
#define UART2                                    ((UART_Type *)UART2_BASE)
/** Array initializer of UART peripheral base pointers */
#define UART_BASES                               { UART1, UART2 }

/*!
 * @}
 */ /* end of group UART_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- UART0 Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup UART0_Peripheral_Access_Layer UART0 Peripheral Access Layer
 * @{
 */

/** UART0 - Register Layout Typedef */
typedef struct {
        __IO uint8_t BDH;                                /**< UART Baud Rate Register High, offset: 0x0 */
        __IO uint8_t BDL;                                /**< UART Baud Rate Register Low, offset: 0x1 */
        __IO uint8_t C1;                                 /**< UART Control Register 1, offset: 0x2 */
        __IO uint8_t C2;                                 /**< UART Control Register 2, offset: 0x3 */
        __IO uint8_t S1;                                 /**< UART Status Register 1, offset: 0x4 */
        __IO uint8_t S2;                                 /**< UART Status Register 2, offset: 0x5 */
        __IO uint8_t C3;                                 /**< UART Control Register 3, offset: 0x6 */
        __IO uint8_t D;                                  /**< UART Data Register, offset: 0x7 */
        __IO uint8_t MA1;                                /**< UART Match Address Registers 1, offset: 0x8 */
        __IO uint8_t MA2;                                /**< UART Match Address Registers 2, offset: 0x9 */
        __IO uint8_t C4;                                 /**< UART Control Register 4, offset: 0xA */
        __IO uint8_t C5;                                 /**< UART Control Register 5, offset: 0xB */
} UART0_Type;

/* ----------------------------------------------------------------------------
   -- UART0 Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup UART0_Register_Masks UART0 Register Masks
 * @{
 */

/* BDH Bit Fields */
#define UART0_BDH_SBR_MASK                       0x1Fu
#define UART0_BDH_SBR_SHIFT                      0
#define UART0_BDH_SBR(x)                         (((uint8_t)(((uint8_t)(x))<<UART0_BDH_SBR_SHIFT))&UART0_BDH_SBR_MASK)
#define UART0_BDH_SBNS_MASK                      0x20u
#define UART0_BDH_SBNS_SHIFT                     5
#define UART0_BDH_RXEDGIE_MASK                   0x40u
#define UART0_BDH_RXEDGIE_SHIFT                  6
#define UART0_BDH_LBKDIE_MASK                    0x80u
#define UART0_BDH_LBKDIE_SHIFT                   7
/* BDL Bit Fields */
#define UART0_BDL_SBR_MASK                       0xFFu
#define UART0_BDL_SBR_SHIFT                      0
#define UART0_BDL_SBR(x)                         (((uint8_t)(((uint8_t)(x))<<UART0_BDL_SBR_SHIFT))&UART0_BDL_SBR_MASK)
/* C1 Bit Fields */
#define UART0_C1_PT_MASK                         0x1u
#define UART0_C1_PT_SHIFT                        0
#define UART0_C1_PE_MASK                         0x2u
#define UART0_C1_PE_SHIFT                        1
#define UART0_C1_ILT_MASK                        0x4u
#define UART0_C1_ILT_SHIFT                       2
#define UART0_C1_WAKE_MASK                       0x8u
#define UART0_C1_WAKE_SHIFT                      3
#define UART0_C1_M_MASK                          0x10u
#define UART0_C1_M_SHIFT                         4
#define UART0_C1_RSRC_MASK                       0x20u
#define UART0_C1_RSRC_SHIFT                      5
#define UART0_C1_DOZEEN_MASK                     0x40u
#define UART0_C1_DOZEEN_SHIFT                    6
#define UART0_C1_LOOPS_MASK                      0x80u
#define UART0_C1_LOOPS_SHIFT                     7
/* C2 Bit Fields */
#define UART0_C2_SBK_MASK                        0x1u
#define UART0_C2_SBK_SHIFT                       0
#define UART0_C2_RWU_MASK                        0x2u
#define UART0_C2_RWU_SHIFT                       1
#define UART0_C2_RE_MASK                         0x4u
#define UART0_C2_RE_SHIFT                        2
#define UART0_C2_TE_MASK                         0x8u
#define UART0_C2_TE_SHIFT                        3
#define UART0_C2_ILIE_MASK                       0x10u
#define UART0_C2_ILIE_SHIFT                      4
#define UART0_C2_RIE_MASK                        0x20u
#define UART0_C2_RIE_SHIFT                       5
#define UART0_C2_TCIE_MASK                       0x40u
#define UART0_C2_TCIE_SHIFT                      6
#define UART0_C2_TIE_MASK                        0x80u
#define UART0_C2_TIE_SHIFT                       7
/* S1 Bit Fields */
#define UART0_S1_PF_MASK                         0x1u
#define UART0_S1_PF_SHIFT                        0
#define UART0_S1_FE_MASK                         0x2u
#define UART0_S1_FE_SHIFT                        1
#define UART0_S1_NF_MASK                         0x4u
#define UART0_S1_NF_SHIFT                        2
#define UART0_S1_OR_MASK                         0x8u
#define UART0_S1_OR_SHIFT                        3
#define UART0_S1_IDLE_MASK                       0x10u
#define UART0_S1_IDLE_SHIFT                      4
#define UART0_S1_RDRF_MASK                       0x20u
#define UART0_S1_RDRF_SHIFT                      5
#define UART0_S1_TC_MASK                         0x40u
#define UART0_S1_TC_SHIFT                        6
#define UART0_S1_TDRE_MASK                       0x80u
#define UART0_S1_TDRE_SHIFT                      7
/* S2 Bit Fields */
#define UART0_S2_RAF_MASK                        0x1u
#define UART0_S2_RAF_SHIFT                       0
#define UART0_S2_LBKDE_MASK                      0x2u
#define UART0_S2_LBKDE_SHIFT                     1
#define UART0_S2_BRK13_MASK                      0x4u
#define UART0_S2_BRK13_SHIFT                     2
#define UART0_S2_RWUID_MASK                      0x8u
#define UART0_S2_RWUID_SHIFT                     3
#define UART0_S2_RXINV_MASK                      0x10u
#define UART0_S2_RXINV_SHIFT                     4
#define UART0_S2_MSBF_MASK                       0x20u
#define UART0_S2_MSBF_SHIFT                      5
#define UART0_S2_RXEDGIF_MASK                    0x40u
#define UART0_S2_RXEDGIF_SHIFT                   6
#define UART0_S2_LBKDIF_MASK                     0x80u
#define UART0_S2_LBKDIF_SHIFT                    7
/* C3 Bit Fields */
#define UART0_C3_PEIE_MASK                       0x1u
#define UART0_C3_PEIE_SHIFT                      0
#define UART0_C3_FEIE_MASK                       0x2u
#define UART0_C3_FEIE_SHIFT                      1
#define UART0_C3_NEIE_MASK                       0x4u
#define UART0_C3_NEIE_SHIFT                      2
#define UART0_C3_ORIE_MASK                       0x8u
#define UART0_C3_ORIE_SHIFT                      3
#define UART0_C3_TXINV_MASK                      0x10u
#define UART0_C3_TXINV_SHIFT                     4
#define UART0_C3_TXDIR_MASK                      0x20u
#define UART0_C3_TXDIR_SHIFT                     5
#define UART0_C3_R9T8_MASK                       0x40u
#define UART0_C3_R9T8_SHIFT                      6
#define UART0_C3_R8T9_MASK                       0x80u
#define UART0_C3_R8T9_SHIFT                      7
/* D Bit Fields */
#define UART0_D_R0T0_MASK                        0x1u
#define UART0_D_R0T0_SHIFT                       0
#define UART0_D_R1T1_MASK                        0x2u
#define UART0_D_R1T1_SHIFT                       1
#define UART0_D_R2T2_MASK                        0x4u
#define UART0_D_R2T2_SHIFT                       2
#define UART0_D_R3T3_MASK                        0x8u
#define UART0_D_R3T3_SHIFT                       3
#define UART0_D_R4T4_MASK                        0x10u
#define UART0_D_R4T4_SHIFT                       4
#define UART0_D_R5T5_MASK                        0x20u
#define UART0_D_R5T5_SHIFT                       5
#define UART0_D_R6T6_MASK                        0x40u
#define UART0_D_R6T6_SHIFT                       6
#define UART0_D_R7T7_MASK                        0x80u
#define UART0_D_R7T7_SHIFT                       7
/* MA1 Bit Fields */
#define UART0_MA1_MA_MASK                        0xFFu
#define UART0_MA1_MA_SHIFT                       0
#define UART0_MA1_MA(x)                          (((uint8_t)(((uint8_t)(x))<<UART0_MA1_MA_SHIFT))&UART0_MA1_MA_MASK)
/* MA2 Bit Fields */
#define UART0_MA2_MA_MASK                        0xFFu
#define UART0_MA2_MA_SHIFT                       0
#define UART0_MA2_MA(x)                          (((uint8_t)(((uint8_t)(x))<<UART0_MA2_MA_SHIFT))&UART0_MA2_MA_MASK)
/* C4 Bit Fields */
#define UART0_C4_OSR_MASK                        0x1Fu
#define UART0_C4_OSR_SHIFT                       0
#define UART0_C4_OSR(x)                          (((uint8_t)(((uint8_t)(x))<<UART0_C4_OSR_SHIFT))&UART0_C4_OSR_MASK)
#define UART0_C4_M10_MASK                        0x20u
#define UART0_C4_M10_SHIFT                       5
#define UART0_C4_MAEN2_MASK                      0x40u
#define UART0_C4_MAEN2_SHIFT                     6
#define UART0_C4_MAEN1_MASK                      0x80u
#define UART0_C4_MAEN1_SHIFT                     7
/* C5 Bit Fields */
#define UART0_C5_RESYNCDIS_MASK                  0x1u
#define UART0_C5_RESYNCDIS_SHIFT                 0
#define UART0_C5_BOTHEDGE_MASK                   0x2u
#define UART0_C5_BOTHEDGE_SHIFT                  1
#define UART0_C5_RDMAE_MASK                      0x20u
#define UART0_C5_RDMAE_SHIFT                     5
#define UART0_C5_TDMAE_MASK                      0x80u
#define UART0_C5_TDMAE_SHIFT                     7

/*!
 * @}
 */ /* end of group UART0_Register_Masks */


/* UART0 - Peripheral instance base addresses */
/** Peripheral UART0 base address */
#define UART0_BASE                               (0x4006A000u)
/** Peripheral UART0 base pointer */
#define UART0                                    ((UART0_Type *)UART0_BASE)
/** Array initializer of UART0 peripheral base pointers */
#define UART0_BASES                              { UART0 }

/*!
 * @}
 */ /* end of group UART0_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- USB Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup USB_Peripheral_Access_Layer USB Peripheral Access Layer
 * @{
 */

/** USB - Register Layout Typedef */
typedef struct {
        __I  uint8_t PERID;                              /**< Peripheral ID register, offset: 0x0 */
        uint8_t RESERVED_0[3];
        __I  uint8_t IDCOMP;                             /**< Peripheral ID Complement register, offset: 0x4 */
        uint8_t RESERVED_1[3];
        __I  uint8_t REV;                                /**< Peripheral Revision register, offset: 0x8 */
        uint8_t RESERVED_2[3];
        __I  uint8_t ADDINFO;                            /**< Peripheral Additional Info register, offset: 0xC */
        uint8_t RESERVED_3[3];
        __IO uint8_t OTGISTAT;                           /**< OTG Interrupt Status register, offset: 0x10 */
        uint8_t RESERVED_4[3];
        __IO uint8_t OTGICR;                             /**< OTG Interrupt Control Register, offset: 0x14 */
        uint8_t RESERVED_5[3];
        __IO uint8_t OTGSTAT;                            /**< OTG Status register, offset: 0x18 */
        uint8_t RESERVED_6[3];
        __IO uint8_t OTGCTL;                             /**< OTG Control register, offset: 0x1C */
        uint8_t RESERVED_7[99];
        __IO uint8_t ISTAT;                              /**< Interrupt Status register, offset: 0x80 */
        uint8_t RESERVED_8[3];
        __IO uint8_t INTEN;                              /**< Interrupt Enable register, offset: 0x84 */
        uint8_t RESERVED_9[3];
        __IO uint8_t ERRSTAT;                            /**< Error Interrupt Status register, offset: 0x88 */
        uint8_t RESERVED_10[3];
        __IO uint8_t ERREN;                              /**< Error Interrupt Enable register, offset: 0x8C */
        uint8_t RESERVED_11[3];
        __I  uint8_t STAT;                               /**< Status register, offset: 0x90 */
        uint8_t RESERVED_12[3];
        __IO uint8_t CTL;                                /**< Control register, offset: 0x94 */
        uint8_t RESERVED_13[3];
        __IO uint8_t ADDR;                               /**< Address register, offset: 0x98 */
        uint8_t RESERVED_14[3];
        __IO uint8_t BDTPAGE1;                           /**< BDT Page Register 1, offset: 0x9C */
        uint8_t RESERVED_15[3];
        __IO uint8_t FRMNUML;                            /**< Frame Number Register Low, offset: 0xA0 */
        uint8_t RESERVED_16[3];
        __IO uint8_t FRMNUMH;                            /**< Frame Number Register High, offset: 0xA4 */
        uint8_t RESERVED_17[3];
        __IO uint8_t TOKEN;                              /**< Token register, offset: 0xA8 */
        uint8_t RESERVED_18[3];
        __IO uint8_t SOFTHLD;                            /**< SOF Threshold Register, offset: 0xAC */
        uint8_t RESERVED_19[3];
        __IO uint8_t BDTPAGE2;                           /**< BDT Page Register 2, offset: 0xB0 */
        uint8_t RESERVED_20[3];
        __IO uint8_t BDTPAGE3;                           /**< BDT Page Register 3, offset: 0xB4 */
        uint8_t RESERVED_21[11];
        struct {                                         /* offset: 0xC0, array step: 0x4 */
                __IO uint8_t ENDPT;                              /**< Endpoint Control register, array offset: 0xC0, array step: 0x4 */
                uint8_t RESERVED_0[3];
        } ENDPOINT[16];
        __IO uint8_t USBCTRL;                            /**< USB Control register, offset: 0x100 */
        uint8_t RESERVED_22[3];
        __I  uint8_t OBSERVE;                            /**< USB OTG Observe register, offset: 0x104 */
        uint8_t RESERVED_23[3];
        __IO uint8_t CONTROL;                            /**< USB OTG Control register, offset: 0x108 */
        uint8_t RESERVED_24[3];
        __IO uint8_t USBTRC0;                            /**< USB Transceiver Control Register 0, offset: 0x10C */
        uint8_t RESERVED_25[7];
        __IO uint8_t USBFRMADJUST;                       /**< Frame Adjust Register, offset: 0x114 */
} USB_Type;

/* ----------------------------------------------------------------------------
   -- USB Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup USB_Register_Masks USB Register Masks
 * @{
 */

/* PERID Bit Fields */
#define USB_PERID_ID_MASK                        0x3Fu
#define USB_PERID_ID_SHIFT                       0
#define USB_PERID_ID(x)                          (((uint8_t)(((uint8_t)(x))<<USB_PERID_ID_SHIFT))&USB_PERID_ID_MASK)
/* IDCOMP Bit Fields */
#define USB_IDCOMP_NID_MASK                      0x3Fu
#define USB_IDCOMP_NID_SHIFT                     0
#define USB_IDCOMP_NID(x)                        (((uint8_t)(((uint8_t)(x))<<USB_IDCOMP_NID_SHIFT))&USB_IDCOMP_NID_MASK)
/* REV Bit Fields */
#define USB_REV_REV_MASK                         0xFFu
#define USB_REV_REV_SHIFT                        0
#define USB_REV_REV(x)                           (((uint8_t)(((uint8_t)(x))<<USB_REV_REV_SHIFT))&USB_REV_REV_MASK)
/* ADDINFO Bit Fields */
#define USB_ADDINFO_IEHOST_MASK                  0x1u
#define USB_ADDINFO_IEHOST_SHIFT                 0
#define USB_ADDINFO_IRQNUM_MASK                  0xF8u
#define USB_ADDINFO_IRQNUM_SHIFT                 3
#define USB_ADDINFO_IRQNUM(x)                    (((uint8_t)(((uint8_t)(x))<<USB_ADDINFO_IRQNUM_SHIFT))&USB_ADDINFO_IRQNUM_MASK)
/* OTGISTAT Bit Fields */
#define USB_OTGISTAT_AVBUSCHG_MASK               0x1u
#define USB_OTGISTAT_AVBUSCHG_SHIFT              0
#define USB_OTGISTAT_B_SESS_CHG_MASK             0x4u
#define USB_OTGISTAT_B_SESS_CHG_SHIFT            2
#define USB_OTGISTAT_SESSVLDCHG_MASK             0x8u
#define USB_OTGISTAT_SESSVLDCHG_SHIFT            3
#define USB_OTGISTAT_LINE_STATE_CHG_MASK         0x20u
#define USB_OTGISTAT_LINE_STATE_CHG_SHIFT        5
#define USB_OTGISTAT_ONEMSEC_MASK                0x40u
#define USB_OTGISTAT_ONEMSEC_SHIFT               6
#define USB_OTGISTAT_IDCHG_MASK                  0x80u
#define USB_OTGISTAT_IDCHG_SHIFT                 7
/* OTGICR Bit Fields */
#define USB_OTGICR_AVBUSEN_MASK                  0x1u
#define USB_OTGICR_AVBUSEN_SHIFT                 0
#define USB_OTGICR_BSESSEN_MASK                  0x4u
#define USB_OTGICR_BSESSEN_SHIFT                 2
#define USB_OTGICR_SESSVLDEN_MASK                0x8u
#define USB_OTGICR_SESSVLDEN_SHIFT               3
#define USB_OTGICR_LINESTATEEN_MASK              0x20u
#define USB_OTGICR_LINESTATEEN_SHIFT             5
#define USB_OTGICR_ONEMSECEN_MASK                0x40u
#define USB_OTGICR_ONEMSECEN_SHIFT               6
#define USB_OTGICR_IDEN_MASK                     0x80u
#define USB_OTGICR_IDEN_SHIFT                    7
/* OTGSTAT Bit Fields */
#define USB_OTGSTAT_AVBUSVLD_MASK                0x1u
#define USB_OTGSTAT_AVBUSVLD_SHIFT               0
#define USB_OTGSTAT_BSESSEND_MASK                0x4u
#define USB_OTGSTAT_BSESSEND_SHIFT               2
#define USB_OTGSTAT_SESS_VLD_MASK                0x8u
#define USB_OTGSTAT_SESS_VLD_SHIFT               3
#define USB_OTGSTAT_LINESTATESTABLE_MASK         0x20u
#define USB_OTGSTAT_LINESTATESTABLE_SHIFT        5
#define USB_OTGSTAT_ONEMSECEN_MASK               0x40u
#define USB_OTGSTAT_ONEMSECEN_SHIFT              6
#define USB_OTGSTAT_ID_MASK                      0x80u
#define USB_OTGSTAT_ID_SHIFT                     7
/* OTGCTL Bit Fields */
#define USB_OTGCTL_OTGEN_MASK                    0x4u
#define USB_OTGCTL_OTGEN_SHIFT                   2
#define USB_OTGCTL_DMLOW_MASK                    0x10u
#define USB_OTGCTL_DMLOW_SHIFT                   4
#define USB_OTGCTL_DPLOW_MASK                    0x20u
#define USB_OTGCTL_DPLOW_SHIFT                   5
#define USB_OTGCTL_DPHIGH_MASK                   0x80u
#define USB_OTGCTL_DPHIGH_SHIFT                  7
/* ISTAT Bit Fields */
#define USB_ISTAT_USBRST_MASK                    0x1u
#define USB_ISTAT_USBRST_SHIFT                   0
#define USB_ISTAT_ERROR_MASK                     0x2u
#define USB_ISTAT_ERROR_SHIFT                    1
#define USB_ISTAT_SOFTOK_MASK                    0x4u
#define USB_ISTAT_SOFTOK_SHIFT                   2
#define USB_ISTAT_TOKDNE_MASK                    0x8u
#define USB_ISTAT_TOKDNE_SHIFT                   3
#define USB_ISTAT_SLEEP_MASK                     0x10u
#define USB_ISTAT_SLEEP_SHIFT                    4
#define USB_ISTAT_RESUME_MASK                    0x20u
#define USB_ISTAT_RESUME_SHIFT                   5
#define USB_ISTAT_ATTACH_MASK                    0x40u
#define USB_ISTAT_ATTACH_SHIFT                   6
#define USB_ISTAT_STALL_MASK                     0x80u
#define USB_ISTAT_STALL_SHIFT                    7
/* INTEN Bit Fields */
#define USB_INTEN_USBRSTEN_MASK                  0x1u
#define USB_INTEN_USBRSTEN_SHIFT                 0
#define USB_INTEN_ERROREN_MASK                   0x2u
#define USB_INTEN_ERROREN_SHIFT                  1
#define USB_INTEN_SOFTOKEN_MASK                  0x4u
#define USB_INTEN_SOFTOKEN_SHIFT                 2
#define USB_INTEN_TOKDNEEN_MASK                  0x8u
#define USB_INTEN_TOKDNEEN_SHIFT                 3
#define USB_INTEN_SLEEPEN_MASK                   0x10u
#define USB_INTEN_SLEEPEN_SHIFT                  4
#define USB_INTEN_RESUMEEN_MASK                  0x20u
#define USB_INTEN_RESUMEEN_SHIFT                 5
#define USB_INTEN_ATTACHEN_MASK                  0x40u
#define USB_INTEN_ATTACHEN_SHIFT                 6
#define USB_INTEN_STALLEN_MASK                   0x80u
#define USB_INTEN_STALLEN_SHIFT                  7
/* ERRSTAT Bit Fields */
#define USB_ERRSTAT_PIDERR_MASK                  0x1u
#define USB_ERRSTAT_PIDERR_SHIFT                 0
#define USB_ERRSTAT_CRC5EOF_MASK                 0x2u
#define USB_ERRSTAT_CRC5EOF_SHIFT                1
#define USB_ERRSTAT_CRC16_MASK                   0x4u
#define USB_ERRSTAT_CRC16_SHIFT                  2
#define USB_ERRSTAT_DFN8_MASK                    0x8u
#define USB_ERRSTAT_DFN8_SHIFT                   3
#define USB_ERRSTAT_BTOERR_MASK                  0x10u
#define USB_ERRSTAT_BTOERR_SHIFT                 4
#define USB_ERRSTAT_DMAERR_MASK                  0x20u
#define USB_ERRSTAT_DMAERR_SHIFT                 5
#define USB_ERRSTAT_BTSERR_MASK                  0x80u
#define USB_ERRSTAT_BTSERR_SHIFT                 7
/* ERREN Bit Fields */
#define USB_ERREN_PIDERREN_MASK                  0x1u
#define USB_ERREN_PIDERREN_SHIFT                 0
#define USB_ERREN_CRC5EOFEN_MASK                 0x2u
#define USB_ERREN_CRC5EOFEN_SHIFT                1
#define USB_ERREN_CRC16EN_MASK                   0x4u
#define USB_ERREN_CRC16EN_SHIFT                  2
#define USB_ERREN_DFN8EN_MASK                    0x8u
#define USB_ERREN_DFN8EN_SHIFT                   3
#define USB_ERREN_BTOERREN_MASK                  0x10u
#define USB_ERREN_BTOERREN_SHIFT                 4
#define USB_ERREN_DMAERREN_MASK                  0x20u
#define USB_ERREN_DMAERREN_SHIFT                 5
#define USB_ERREN_BTSERREN_MASK                  0x80u
#define USB_ERREN_BTSERREN_SHIFT                 7
/* STAT Bit Fields */
#define USB_STAT_ODD_MASK                        0x4u
#define USB_STAT_ODD_SHIFT                       2
#define USB_STAT_TX_MASK                         0x8u
#define USB_STAT_TX_SHIFT                        3
#define USB_STAT_ENDP_MASK                       0xF0u
#define USB_STAT_ENDP_SHIFT                      4
#define USB_STAT_ENDP(x)                         (((uint8_t)(((uint8_t)(x))<<USB_STAT_ENDP_SHIFT))&USB_STAT_ENDP_MASK)
/* CTL Bit Fields */
#define USB_CTL_USBENSOFEN_MASK                  0x1u
#define USB_CTL_USBENSOFEN_SHIFT                 0
#define USB_CTL_ODDRST_MASK                      0x2u
#define USB_CTL_ODDRST_SHIFT                     1
#define USB_CTL_RESUME_MASK                      0x4u
#define USB_CTL_RESUME_SHIFT                     2
#define USB_CTL_HOSTMODEEN_MASK                  0x8u
#define USB_CTL_HOSTMODEEN_SHIFT                 3
#define USB_CTL_RESET_MASK                       0x10u
#define USB_CTL_RESET_SHIFT                      4
#define USB_CTL_TXSUSPENDTOKENBUSY_MASK          0x20u
#define USB_CTL_TXSUSPENDTOKENBUSY_SHIFT         5
#define USB_CTL_SE0_MASK                         0x40u
#define USB_CTL_SE0_SHIFT                        6
#define USB_CTL_JSTATE_MASK                      0x80u
#define USB_CTL_JSTATE_SHIFT                     7
/* ADDR Bit Fields */
#define USB_ADDR_ADDR_MASK                       0x7Fu
#define USB_ADDR_ADDR_SHIFT                      0
#define USB_ADDR_ADDR(x)                         (((uint8_t)(((uint8_t)(x))<<USB_ADDR_ADDR_SHIFT))&USB_ADDR_ADDR_MASK)
#define USB_ADDR_LSEN_MASK                       0x80u
#define USB_ADDR_LSEN_SHIFT                      7
/* BDTPAGE1 Bit Fields */
#define USB_BDTPAGE1_BDTBA_MASK                  0xFEu
#define USB_BDTPAGE1_BDTBA_SHIFT                 1
#define USB_BDTPAGE1_BDTBA(x)                    (((uint8_t)(((uint8_t)(x))<<USB_BDTPAGE1_BDTBA_SHIFT))&USB_BDTPAGE1_BDTBA_MASK)
/* FRMNUML Bit Fields */
#define USB_FRMNUML_FRM_MASK                     0xFFu
#define USB_FRMNUML_FRM_SHIFT                    0
#define USB_FRMNUML_FRM(x)                       (((uint8_t)(((uint8_t)(x))<<USB_FRMNUML_FRM_SHIFT))&USB_FRMNUML_FRM_MASK)
/* FRMNUMH Bit Fields */
#define USB_FRMNUMH_FRM_MASK                     0x7u
#define USB_FRMNUMH_FRM_SHIFT                    0
#define USB_FRMNUMH_FRM(x)                       (((uint8_t)(((uint8_t)(x))<<USB_FRMNUMH_FRM_SHIFT))&USB_FRMNUMH_FRM_MASK)
/* TOKEN Bit Fields */
#define USB_TOKEN_TOKENENDPT_MASK                0xFu
#define USB_TOKEN_TOKENENDPT_SHIFT               0
#define USB_TOKEN_TOKENENDPT(x)                  (((uint8_t)(((uint8_t)(x))<<USB_TOKEN_TOKENENDPT_SHIFT))&USB_TOKEN_TOKENENDPT_MASK)
#define USB_TOKEN_TOKENPID_MASK                  0xF0u
#define USB_TOKEN_TOKENPID_SHIFT                 4
#define USB_TOKEN_TOKENPID(x)                    (((uint8_t)(((uint8_t)(x))<<USB_TOKEN_TOKENPID_SHIFT))&USB_TOKEN_TOKENPID_MASK)
/* SOFTHLD Bit Fields */
#define USB_SOFTHLD_CNT_MASK                     0xFFu
#define USB_SOFTHLD_CNT_SHIFT                    0
#define USB_SOFTHLD_CNT(x)                       (((uint8_t)(((uint8_t)(x))<<USB_SOFTHLD_CNT_SHIFT))&USB_SOFTHLD_CNT_MASK)
/* BDTPAGE2 Bit Fields */
#define USB_BDTPAGE2_BDTBA_MASK                  0xFFu
#define USB_BDTPAGE2_BDTBA_SHIFT                 0
#define USB_BDTPAGE2_BDTBA(x)                    (((uint8_t)(((uint8_t)(x))<<USB_BDTPAGE2_BDTBA_SHIFT))&USB_BDTPAGE2_BDTBA_MASK)
/* BDTPAGE3 Bit Fields */
#define USB_BDTPAGE3_BDTBA_MASK                  0xFFu
#define USB_BDTPAGE3_BDTBA_SHIFT                 0
#define USB_BDTPAGE3_BDTBA(x)                    (((uint8_t)(((uint8_t)(x))<<USB_BDTPAGE3_BDTBA_SHIFT))&USB_BDTPAGE3_BDTBA_MASK)
/* ENDPT Bit Fields */
#define USB_ENDPT_EPHSHK_MASK                    0x1u
#define USB_ENDPT_EPHSHK_SHIFT                   0
#define USB_ENDPT_EPSTALL_MASK                   0x2u
#define USB_ENDPT_EPSTALL_SHIFT                  1
#define USB_ENDPT_EPTXEN_MASK                    0x4u
#define USB_ENDPT_EPTXEN_SHIFT                   2
#define USB_ENDPT_EPRXEN_MASK                    0x8u
#define USB_ENDPT_EPRXEN_SHIFT                   3
#define USB_ENDPT_EPCTLDIS_MASK                  0x10u
#define USB_ENDPT_EPCTLDIS_SHIFT                 4
#define USB_ENDPT_RETRYDIS_MASK                  0x40u
#define USB_ENDPT_RETRYDIS_SHIFT                 6
#define USB_ENDPT_HOSTWOHUB_MASK                 0x80u
#define USB_ENDPT_HOSTWOHUB_SHIFT                7
/* USBCTRL Bit Fields */
#define USB_USBCTRL_PDE_MASK                     0x40u
#define USB_USBCTRL_PDE_SHIFT                    6
#define USB_USBCTRL_SUSP_MASK                    0x80u
#define USB_USBCTRL_SUSP_SHIFT                   7
/* OBSERVE Bit Fields */
#define USB_OBSERVE_DMPD_MASK                    0x10u
#define USB_OBSERVE_DMPD_SHIFT                   4
#define USB_OBSERVE_DPPD_MASK                    0x40u
#define USB_OBSERVE_DPPD_SHIFT                   6
#define USB_OBSERVE_DPPU_MASK                    0x80u
#define USB_OBSERVE_DPPU_SHIFT                   7
/* CONTROL Bit Fields */
#define USB_CONTROL_DPPULLUPNONOTG_MASK          0x10u
#define USB_CONTROL_DPPULLUPNONOTG_SHIFT         4
/* USBTRC0 Bit Fields */
#define USB_USBTRC0_USB_RESUME_INT_MASK          0x1u
#define USB_USBTRC0_USB_RESUME_INT_SHIFT         0
#define USB_USBTRC0_SYNC_DET_MASK                0x2u
#define USB_USBTRC0_SYNC_DET_SHIFT               1
#define USB_USBTRC0_USBRESMEN_MASK               0x20u
#define USB_USBTRC0_USBRESMEN_SHIFT              5
#define USB_USBTRC0_USBRESET_MASK                0x80u
#define USB_USBTRC0_USBRESET_SHIFT               7
/* USBFRMADJUST Bit Fields */
#define USB_USBFRMADJUST_ADJ_MASK                0xFFu
#define USB_USBFRMADJUST_ADJ_SHIFT               0
#define USB_USBFRMADJUST_ADJ(x)                  (((uint8_t)(((uint8_t)(x))<<USB_USBFRMADJUST_ADJ_SHIFT))&USB_USBFRMADJUST_ADJ_MASK)

/*!
 * @}
 */ /* end of group USB_Register_Masks */


/* USB - Peripheral instance base addresses */
/** Peripheral USB0 base address */
#define USB0_BASE                                (0x40072000u)
/** Peripheral USB0 base pointer */
#define USB0                                     ((USB_Type *)USB0_BASE)
/** Array initializer of USB peripheral base pointers */
#define USB_BASES                                { USB0 }

/*!
 * @}
 */ /* end of group USB_Peripheral_Access_Layer */


/*
** End of section using anonymous unions
*/

#if defined(__ARMCC_VERSION)
#pragma pop
#elif defined(__CWCC__)
#pragma pop
#elif defined(__GNUC__)
/* leave anonymous unions enabled */
#elif defined(__IAR_SYSTEMS_ICC__)
#pragma language=default
#else
#error Not supported compiler type
#endif

/*!
 * @}
 */ /* end of group Peripheral_access_layer */


/* ----------------------------------------------------------------------------
   -- Backward Compatibility
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup Backward_Compatibility_Symbols Backward Compatibility
 * @{
 */

/* No backward compatibility issues. */

/*!
 * @}
 */ /* end of group Backward_Compatibility_Symbols */


#endif  /* #if !defined(MKL46Z4_H_) */

/* MKL46Z4.h, eof. */

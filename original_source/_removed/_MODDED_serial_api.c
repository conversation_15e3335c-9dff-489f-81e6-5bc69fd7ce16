/* mbed Microcontroller Library
 * Copyright (c) 2006-2013 ARM Limited
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 *
 */

/**
   @note   Random notes on the serial ports
           (the serial driver is agnostic -- the upper level drivers
           actually set the serial's parameters. The following is just for a brain dump.)
           * Console UART
               - Initialized to 115,200 baud
               - a mutex used to prevent garbled text when two or more threads are
                 trying to send stuff at the same time.
           * GSM UART
               - Initialized to 9600 baud
               - DMA TX is used, no DMA RX (see below)
               - serial_hold & serial_release are used to buffer as much as possible
                 to transmit so DMA can be use efficiently.
           * GPS UART
               - Initialized to 9600 baud
               - No DMA is used. (TX operations are extremely infrequent and not worth the extra RAM needed)
               - Operates in a line mode - the gps thread is only notified when the buffer is filled OR
                 a newline has been received.

           The source has been heavily modified from its original MBED source.

           While there is DMA RX support here, DMA RX cannot be used on
           the KL* series - the DMA & UART engines are not adequately sophisticated
           enough to handle non deterministic receives.
           See this thread for reasons why:
           https://community.freescale.com/thread/301651
           (TL;DR no true scatter/gather, no idle line detection, dropped characters when switching DMA buffers, etc.)
*/

#include "mbed_assert.h"
#include "pelagic-types.h"
#include "serial_api.h"

#include "cmsis.h"
#include "pinmap.h"
#include "clk_freqs.h"
#include "PeripheralPins.h"

#include "dma.h"

#include "stats.h"
#include "signals.h"
#include "hardware.h"

#include "semihost_api.h"

// There some commonality between UART0 & UART{1,2}

#define UARTLP_C2_RE_MASK       UART0_C2_RE_MASK
#define UARTLP_C2_TE_MASK       UART0_C2_TE_MASK
#define UARTLP_BDH_SBNS_MASK    UART0_BDH_SBNS_MASK
#define UARTLP_BDH_SBNS_SHIFT   UART0_BDH_SBNS_SHIFT
#define UARTLP_S1_TDRE_MASK     UART0_S1_TDRE_MASK
#define UARTLP_S1_OR_MASK       UART0_S1_OR_MASK
#define UARTLP_C2_RIE_MASK      UART0_C2_RIE_MASK
#define UARTLP_C2_TIE_MASK      UART0_C2_TIE_MASK
#define UARTLP_C2_SBK_MASK      UART0_C2_SBK_MASK
#define UARTLP_S1_RDRF_MASK     UART0_S1_RDRF_MASK

#define UART_NUM        3

// poll the dma rx ~ 1/3 of second
#define SERIAL_POLL_RX_TIME_US  (330 * 1000)

/******************************************************************************
 * INITIALIZATION
 ******************************************************************************/

int8_t serial_semihost = 0;
serial_t console_uart;

serial_t *uart_ports[UART_NUM];
struct serial_stat_s serial_stats[3];

void serial_dma_transmit(serial_t *obj);
void serial_dma_tx_irq(uint32_t param);
void serial_dma_rx_irq(uint32_t param);

typedef struct uart_dma_channel_s {
        uint8_t    rx_channel;
        uint8_t    rx_slot;
        uint8_t    tx_channel;
        uint8_t    tx_slot;
} uart_dma_channel_t;

uart_dma_channel_t uart_dma_channels[UART_NUM] = {
        { 0, DMA_MUX_UART0_RX_SLOT, 1, DMA_MUX_UART0_TX_SLOT },
        { 0, DMA_MUX_UART1_RX_SLOT, 1, DMA_MUX_UART1_TX_SLOT },
        { 0, DMA_MUX_UART2_RX_SLOT, 1, DMA_MUX_UART2_TX_SLOT },
};

enum {
        CONSOLE_RX_BUFFER_SIZE = 32,
        CONSOLE_TX_BUFFER_SIZE = 128,
};

char    console_rx_buffer[CONSOLE_RX_BUFFER_SIZE], console_tx_buffer[CONSOLE_TX_BUFFER_SIZE];
osMutexDef(serial_console_mutex);


void error(char *fmt, ...);

void
serial_enable_rx_irq(serial_t *obj)
{
        if (obj->dma_enabled)
                NVIC_EnableIRQ(obj->dma_irq_n);
        else
                NVIC_EnableIRQ(obj->irq_n);
}

void
serial_disable_rx_irq(serial_t *obj)
{
        if (obj->dma_enabled)
                NVIC_DisableIRQ(obj->dma_irq_n);
        else
                NVIC_DisableIRQ(obj->irq_n);
}

void
serial_enable_tx_irq(serial_t *obj)
{
        if (obj->dma_enabled)
                NVIC_EnableIRQ(obj->dma_irq_n);
        else
                NVIC_EnableIRQ(obj->irq_n);
}

void
serial_disable_tx_irq(serial_t *obj)
{
        if (obj->dma_enabled)
                NVIC_DisableIRQ(obj->dma_irq_n);
        else
                NVIC_DisableIRQ(obj->irq_n);
}

void
serial_init(serial_t *obj, PinName tx, PinName rx)
{
        // determine the UART to use
        UARTName uart_tx = (UARTName)pinmap_peripheral(tx, PinMap_UART_TX);
        UARTName uart_rx = (UARTName)pinmap_peripheral(rx, PinMap_UART_RX);
        UARTName uart = (UARTName)pinmap_merge(uart_tx, uart_rx);
        MBED_ASSERT((int)uart != NC);

        obj->uart = (UARTLP_Type *) uart;

        obj->tx_pin = tx;
        obj->rx_pin = rx;

        // enable clk
        switch (uart) {
        case UART_0:
                obj->index = 0;
                obj->irq_n = UART0_IRQn;
                if (mcgpllfll_frequency() != 0)                    //PLL/FLL is selected
                        SIM->SOPT2 |= (1<<SIM_SOPT2_UART0SRC_SHIFT);
                else
                        SIM->SOPT2 |= (2<<SIM_SOPT2_UART0SRC_SHIFT);
                SIM->SCGC4 |= SIM_SCGC4_UART0_MASK;
                break;

        case UART_1:
                obj->index = 1;
                obj->irq_n = UART1_IRQn;
                SIM->SCGC4 |= SIM_SCGC4_UART1_MASK;
                break;

        case UART_2:
                obj->index = 2;
                obj->irq_n = UART2_IRQn;
                SIM->SCGC4 |= SIM_SCGC4_UART2_MASK;
                break;
        }

        NVIC_SetPriority(obj->irq_n, 1);

        uart_ports[obj->index] = obj;

        // Disable UART before changing registers
        obj->uart->C2 &= ~(UARTLP_C2_RE_MASK | UARTLP_C2_TE_MASK);

        // set default baud rate and format
        serial_baud  (obj, 9600);
        serial_format(obj, 8, ParityNone, 1);

        // pinout the chosen uart
        pinmap_pinout(tx, PinMap_UART_TX);
        pinmap_pinout(rx, PinMap_UART_RX);

        // uart is left disabled / powered off
        // higher level driver will power on as needed
        pin_mode(obj->tx_pin, PullNone);
        pin_mode(obj->rx_pin, PullNone);
}

void
serial_power_off(serial_t *obj)
{
        if (obj->uart == NULL || obj->is_on == false)
                return;

        obj->uart->C2 &= ~(UARTLP_C2_RE_MASK | UARTLP_C2_TE_MASK);
        pin_mode(obj->tx_pin, PullNone);
        pin_mode(obj->rx_pin, PullNone);
        obj->is_on = false;
}

void
serial_power_on(serial_t *obj)
{
        if (obj->uart == NULL)
                return;

        pin_mode(obj->tx_pin, PullUp);
        pin_mode(obj->rx_pin, PullUp);

        obj->uart->C2 |= (UARTLP_C2_RE_MASK | UARTLP_C2_TE_MASK);
        obj->is_on = true;
}


// serial_baud
//
// set the baud rate, taking in to account the current SystemFrequency
void
serial_baud(serial_t *obj, int baudrate)
{

        // save C2 state
        uint8_t c2_state = (obj->uart->C2 & (UARTLP_C2_RE_MASK | UARTLP_C2_TE_MASK));

        // Disable UART before changing registers
        obj->uart->C2 &= ~(UARTLP_C2_RE_MASK | UARTLP_C2_TE_MASK);

        uint32_t PCLK;
        if (obj->uart == UART0) {
                if (mcgpllfll_frequency() != 0)
                        PCLK = mcgpllfll_frequency();
                else
                        PCLK = extosc_frequency();
        } else
                PCLK = bus_frequency();

        // First we check to see if the basic divide with no DivAddVal/MulVal
        // ratio gives us an integer result. If it does, we set DivAddVal = 0,
        // MulVal = 1. Otherwise, we search the valid ratio value range to find
        // the closest match. This could be more elegant, using search methods
        // and/or lookup tables, but the brute force method is not that much
        // slower, and is more maintainable.
        uint16_t DL = PCLK / (16 * baudrate);

        // set BDH and BDL
        obj->uart->BDH = (obj->uart->BDH & ~(0x1f)) | ((DL >> 8) & 0x1f);
        obj->uart->BDL = (obj->uart->BDL & ~(0xff)) | ((DL >> 0) & 0xff);

        // restore C2 state
        obj->uart->C2 |= c2_state;
}

void
serial_format(serial_t *obj, int data_bits, SerialParity parity, int stop_bits)
{
        MBED_ASSERT((stop_bits == 1) || (stop_bits == 2));
        MBED_ASSERT((parity == ParityNone) || (parity == ParityOdd) || (parity == ParityEven));
        MBED_ASSERT(data_bits == 8); // TODO: Support other number of data bits (also in the write method!)

        // save C2 state
        uint8_t c2_state = (obj->uart->C2 & (UARTLP_C2_RE_MASK | UARTLP_C2_TE_MASK));

        // Disable UART before changing registers
        obj->uart->C2 &= ~(UARTLP_C2_RE_MASK | UARTLP_C2_TE_MASK);


        uint8_t parity_enable, parity_select;
        switch (parity) {
        case ParityNone:
                parity_enable = 0;
                parity_select = 0;
                break;
        case ParityOdd :
                parity_enable = 1;
                parity_select = 1;
                data_bits++;
                break;
        case ParityEven:
                parity_enable = 1;
                parity_select = 0;
                data_bits++;
                break;
        default:
                error("uart%d: unknown format %d", obj->index, parity);
                return;
        }

        stop_bits -= 1;

        // data bits, parity and parity mode
        obj->uart->C1 = ((parity_enable << 1)
                         |  (parity_select << 0));

        // stop bits
        obj->uart->BDH &= ~UARTLP_BDH_SBNS_MASK;
        obj->uart->BDH |= (stop_bits << UARTLP_BDH_SBNS_SHIFT);

        // restore C2 state
        obj->uart->C2 |= c2_state;
}


/******************************************************************************
 * READ/WRITE
 ******************************************************************************/
int
serial_getc(serial_t *obj)
{
        while (!serial_readable(obj));
        return obj->uart->D;
}

void
serial_putc(serial_t *obj, int c)
{
        while (!serial_writable(obj));
        obj->uart->D = c;
}

int
serial_readable(serial_t *obj)
{
        // check overrun
        if (obj->uart->S1 &  UARTLP_S1_OR_MASK) {
                obj->uart->S1 |= UARTLP_S1_OR_MASK;
        }
        return (obj->uart->S1 & UARTLP_S1_RDRF_MASK);
}

int
serial_writable(serial_t *obj)
{
        // check overrun
        if (obj->uart->S1 &  UARTLP_S1_OR_MASK) {
                obj->uart->S1 |= UARTLP_S1_OR_MASK;
        }
        return (obj->uart->S1 & UARTLP_S1_TDRE_MASK);
}

void
serial_break_set(serial_t *obj)
{
        obj->uart->C2 |= UARTLP_C2_SBK_MASK;
}

void
serial_break_clear(serial_t *obj)
{
        obj->uart->C2 &= ~UARTLP_C2_SBK_MASK;
}

int
serial_input_char(serial_t *obj, char ch)
{
        obj->rx_buffer[obj->rx_in] = ch;
        obj->rx_in = (obj->rx_in + 1) & obj->rx_size_mask;

        // Advance the outbound pointer if the buffer has filled up
        // (i.e. start dropping characters)
        if (obj->rx_count < obj->rx_size) {
                obj->rx_count++;
        } else {
                obj->rx_out = (obj->rx_out + 1) & obj->rx_size_mask;
        }


        // Only signal if the port is not in line mode or
        // if it is in line mode, and a newline has been received.
        return (!obj->line_mode  || ch == '\n' || obj->rx_count == obj->rx_size);
}

void
uart_common_irq(int port)
{
        serial_t *obj = uart_ports[port];
        UARTLP_Type *uart = obj->uart;
        uint8_t     notify = 0;
        serial_stat_t *stats = &serial_stats[obj->index];

        stats->interrupts++;

        if (uart->S1 & UART_S1_OR_MASK) {
                uart->S1 |= UART_S1_OR_MASK;
        }

        while (uart->S1 & UARTLP_S1_RDRF_MASK) {
                notify |= serial_input_char(obj, uart->D);
                stats->rx_bytes++;
        }

        if (obj->rx_waiting && notify) {
                osSignalSet(obj->rx_waiting, IO_SIGNAL_SERIAL_RX);
                obj->rx_waiting = 0;
        }

        if (obj->tx_size == 0)  // Not dealing with a transmit buffer
                return;

        if ((uart->S1 & UARTLP_S1_TDRE_MASK) == 0)
                return;

        notify = 0;

        while (obj->tx_out != obj->tx_in && (uart->S1 & UARTLP_S1_TDRE_MASK)) {
                uart->D = obj->tx_buffer[obj->tx_out];
                obj->tx_out = (obj->tx_out + 1) & obj->tx_size_mask;
                notify = 1;
                stats->tx_bytes++;
        }

        // Kill the TX irq if the tx buffer is empty
        if (obj->tx_out == obj->tx_in) {
                uart->C2 &= ~(UARTLP_C2_TIE_MASK);
                obj->tx_out = obj->tx_in = 0;
                obj->transmitting = 0;
        }

        if (obj->tx_waiting && notify) {
                osSignalSet(obj->tx_waiting, IO_SIGNAL_SERIAL_TX);
                obj->tx_waiting = 0;
        }
}

void
uart0_irq()
{
        uart_common_irq(0);
}
void
uart1_irq()
{
        uart_common_irq(1);
}
void
uart2_irq()
{
        uart_common_irq(2);
}

void
serial_buffer(serial_t *obj, char *tx_buffer, int tx_size, char *rx_buffer, int rx_size)
{
        uint32_t vector = 0;

        obj->tx_buffer = tx_buffer;
        obj->tx_size = tx_size;
        obj->tx_size_mask = tx_size - 1;
        obj->tx_in = 0;
        obj->tx_out = 0;

        obj->rx_buffer = rx_buffer;
        obj->rx_size = rx_size;
        obj->rx_size_mask = rx_size - 1;
        obj->rx_in = 0;
        obj->rx_out = 0;
        obj->rx_count = 0;
        obj->line_mode = 0;

        obj->holding = 0;
        obj->transmitting = 0;
        obj->rx_waiting = 0;
        obj->tx_waiting = 0;

        obj->dma_rx_size = 0;

        switch ((int)obj->uart) {
        case UART_0:
                vector = (uint32_t)&uart0_irq;
                break;

        case UART_1:
                vector = (uint32_t)&uart1_irq;
                break;

        case UART_2:
                vector = (uint32_t)&uart2_irq;
                break;
        }

        // Setup the RX interrupt -- TX will fire continuously
        obj->uart->C2 |= (UARTLP_C2_RIE_MASK);

        NVIC_SetVector(obj->irq_n, vector);
        NVIC_EnableIRQ(obj->irq_n);
}

void
serial_line_mode(serial_t *obj, int mode)
{
        obj->line_mode = mode;
}

void
serial_transmit(serial_t *obj)
{

        if (obj->dma_tx_regs) {
                serial_dma_transmit(obj);
                return;
        }

        UARTLP_Type *uart = obj->uart;
        serial_stat_t *stats = &serial_stats[obj->index];

        while (obj->tx_out != obj->tx_in && (uart->S1 & UARTLP_S1_TDRE_MASK)) {
                uart->D = obj->tx_buffer[obj->tx_out];
                obj->tx_out = (obj->tx_out + 1) & obj->tx_size_mask;
                stats->tx_bytes++;
        }

        obj->transmitting = 1;

        uart->C2 |= (UARTLP_C2_TIE_MASK);
}

extern int8_t board_fault;

void
serial_write_direct(serial_t *obj, char *buffer, int bytes)
{
        UARTLP_Type *uart = obj->uart;

        if (obj->is_on == false)
                return;

        serial_stats[obj->index].tx_bytes += bytes;

        while (bytes-- > 0) {
                while ((uart->S1 & UARTLP_S1_TDRE_MASK) == 0)
                        ;

                uart->D = *buffer++;
        }
}

void
serial_write(serial_t *obj, char *buffer, int bytes)
{
        int tx_next;

        if (serial_semihost && obj == &console_uart) {
                //while (bytes-- > 0)
                //    semihost_writec(buffer++);
                semihost_write(1, buffer, bytes);
                return;
        }

        if (obj->is_on == false)
                return;


        // If no transmit buffer (i.e. the debug console) was setup, just splat it out.
        if (board_fault || obj->tx_size == 0) {
                serial_write_direct(obj, buffer, bytes);
                return;
        }

        if (obj->lock)
                osMutexWait(obj->lock, osWaitForever);

        for (;;) {
                serial_disable_tx_irq(obj);

                while (bytes > 0) {
                        tx_next = (obj->tx_in + 1) & obj->tx_size_mask;

                        // stop if the xmit buffer is full
                        if (tx_next == obj->tx_out)
                                break;

                        obj->tx_buffer[obj->tx_in] = *buffer++;
                        obj->tx_in = tx_next;
                        bytes--;
                }

                //
                // Start transmitting if not already and the buffer is not being
                // held or the buffer is full
                // a chunk of data can be built up, splatted out in one go
                // (avoids activating the xmit irq which may jump in while the entire buffer
                // is being written)

                if (!obj->transmitting && (!obj->holding || bytes > 0)) {
                        serial_transmit(obj);
                }

                if (bytes == 0) {
                        serial_enable_tx_irq(obj);

                        if (obj->lock)
                                osMutexRelease(obj->lock);
                        return;
                }

                obj->tx_waiting = osThreadGetId();

                serial_enable_tx_irq(obj);

                osSignalWait(IO_SIGNAL_SERIAL_TX, osWaitForever);
        }

}

int
serial_available(serial_t *obj)
{
        return obj->rx_count;
}

int
serial_read(serial_t *obj, char *buffer, int bytes)
{
        return serial_read_timeout_signal(obj, buffer, bytes, osWaitForever, NULL);
}

int
serial_read_signal(serial_t *obj, char *buffer, int bytes, uint16_t *signals)
{
        return serial_read_timeout_signal(obj, buffer, bytes, osWaitForever, signals);
}

int
serial_read_timeout(serial_t *obj, char *buffer, int bytes, uint32_t millisecs)
{
        return serial_read_timeout_signal(obj, buffer, bytes, millisecs, NULL);
}

int
serial_read_timeout_signal(serial_t *obj, char *buffer, int bytes, uint32_t millisecs, uint16_t *signals)
{
        int count = 0;
        osEvent result;
        uint16_t    fired;

        if (signals)
                *signals = 0;

        for (;;) {
                serial_disable_rx_irq(obj);
                while (obj->rx_count > 0 && bytes > 0) {
                        *buffer++ = obj->rx_buffer[obj->rx_out];
                        obj->rx_out = (obj->rx_out + 1) & obj->rx_size_mask;
                        bytes--;
                        obj->rx_count--;
                        count++;
                }

                if (count) {
                        serial_enable_rx_irq(obj);
                        return count;
                }

                obj->rx_waiting = osThreadGetId();

                serial_enable_rx_irq(obj);

                result = osSignalWait(signals ? 0 : IO_SIGNAL_SERIAL_RX, millisecs);

                if (result.status == osEventTimeout)
                        return 0;

                fired = result.value.signals & ~IO_SIGNAL_SERIAL_RX;

                if (fired && signals) {
                        *signals = fired;
                        return 0;
                }
        }

}


void
serial_hold(serial_t *obj)
{
        obj->holding = 1;
}

void
serial_release(serial_t *obj)
{
        obj->holding = 0;

        if (obj->transmitting)
                return;

        serial_disable_tx_irq(obj);
        serial_transmit(obj);
        serial_enable_tx_irq(obj);
}

void
serial_flush(serial_t *obj)
{
        serial_disable_rx_irq(obj);
        obj->rx_in = 0;
        obj->rx_out = 0;
        obj->rx_count = 0;
        serial_enable_rx_irq(obj);
}

void
serial_enable_dma_rx(serial_t *obj, char *buffer, int size)
{
        uart_dma_channel_t *channel = &uart_dma_channels[obj->index];

        // Split the buffer up into chunks

        obj->dma_rx_size = size / SERIAL_DMA_CHUNKS;

        for (int i = 0; i < SERIAL_DMA_CHUNKS; i++) {
                obj->dma_rx_buffers[i] = buffer;
                buffer += obj->dma_rx_size;
        }

        obj->dma_rx_index = 0;

        obj->dmamux_rx_slot = channel->rx_slot;
        obj->dmamux_rx_reg = &DMAMUX0->CHCFG[channel->rx_channel];
        obj->dma_rx_regs = &DMA0->DMA[channel->rx_channel];
        obj->dma_irq_n = DMA0_IRQn + channel->rx_channel;

        dma_init();
        dma_register_handler(obj->dma_irq_n, serial_dma_rx_irq, (uint32_t) obj);

        serial_resume_dma_rx(obj);
}

void
serial_suspend_dma_rx(serial_t *obj)
{
        UARTLP_Type *uart = obj->uart;
        __IO uint8_t *dmamux = obj->dmamux_rx_reg;

        if (!obj->dma_enabled)
                return;

        NVIC_DisableIRQ(obj->dma_irq_n);

        uart->C2 &= ~(UART0_C2_TE_MASK | UART0_C2_RE_MASK);

        *dmamux = 0; // Disable slot

        if (obj->index == 0) {
                ((UART0_Type *) uart)->C5 &= ~UART0_C5_RDMAE_MASK;
        } else {
                ((UART_Type *) uart)->C4 &= ~UART_C4_RDMAS_MASK;
        }

        serial_flush(obj);
        obj->dma_enabled = 0;
        uart->C2 |= (UART0_C2_TE_MASK | UART0_C2_RE_MASK | UARTLP_C2_RIE_MASK);
}

void
serial_resume_dma_rx(serial_t *obj)
{
        UARTLP_Type *uart = obj->uart;
        struct DMA_Channel *dma = obj->dma_rx_regs;
        __IO uint8_t *dmamux = obj->dmamux_rx_reg;

        if (obj->dma_enabled)
                return;

        NVIC_DisableIRQ(obj->irq_n);
        uart->C2 &= ~(UART0_C2_TE_MASK | UART0_C2_RE_MASK);

        *dmamux = 0x00;   //Disable DMA MUX channel first

        dma->DSR_BCR = DMA_DSR_BCR_DONE_MASK;
        dma->SAR = (uint32_t) &obj->uart->D;
        dma->DAR = (uint32_t)obj->dma_rx_buffers[0];
        dma->DSR_BCR = obj->dma_rx_size;

        dma->DCR = (DMA_DCR_EINT_MASK | DMA_DCR_SSIZE(1) | DMA_DCR_DSIZE(1)
                    | DMA_DCR_DINC_MASK | DMA_DCR_CS_MASK | DMA_DCR_ERQ_MASK | DMA_DCR_D_REQ_MASK);

        // setup which slot source to use and enable mux channel
        *dmamux = (obj->dmamux_rx_slot | DMAMUX_CHCFG_ENBL_MASK);

        if (obj->index == 0) {
                ((UART0_Type *) uart)->C5 |= UART0_C5_RDMAE_MASK;
        } else {
                ((UART_Type *) uart)->C4 |= UART_C4_RDMAS_MASK;
        }

        obj->dma_enabled = 1;
        uart->C2 |= (UARTLP_C2_TE_MASK | UARTLP_C2_RE_MASK | UARTLP_C2_RIE_MASK);
        NVIC_EnableIRQ(obj->dma_irq_n);
        NVIC_EnableIRQ(obj->irq_n);
}

void
serial_dma_rx_irq(uint32_t param)
{
        serial_t *obj = (serial_t *) param;
        serial_stat_t *stats = &serial_stats[obj->index];

        struct DMA_Channel *dma = obj->dma_rx_regs;
        UARTLP_Type *uart = obj->uart;
        char *buf = obj->dma_rx_buffers[obj->dma_rx_index];
        int notify = 0;

        uint32_t status = dma->DSR_BCR;

        stats->dma_rx_interrupts++;

        // check for errors
        if (status & (DMA_DSR_BCR_CE_MASK|DMA_DSR_BCR_BES_MASK|DMA_DSR_BCR_BED_MASK)) {
                // TODO - clear the error, and fall back to non-dma receives
                error("uart%d: rx dma err 0x%x", obj->index, status);
        }

        if (status & DMA_DSR_BCR_DONE_MASK) {
                dma->DSR_BCR |= DMA_DSR_BCR_DONE_MASK;

                // TODO: Find out why this works. this was an accidental discovery, the second dma programming will
                // result in the last character received duplicated - no new bytes will appear.
                // that is unless S1 is read.

                if (uart->S1 & UART_S1_OR_MASK) {
                        stats->rx_overflow++;
                }

                // Bounce over to the next buffer
                obj->dma_rx_index = (obj->dma_rx_index + 1 ) & (SERIAL_DMA_CHUNKS-1);

                dma->DAR = (uint32_t) obj->dma_rx_buffers[obj->dma_rx_index];
                dma->DSR_BCR = DMA_DSR_BCR_BCR(obj->dma_rx_size);
                dma->DCR |= DMA_DCR_ERQ_MASK;

                // process the one that was just filled
                for (int i = 0; i < obj->dma_rx_size; i++) {
                        notify |= serial_input_char(obj, buf[i]);
                }

                stats->rx_bytes += obj->dma_rx_size;

                if (obj->rx_waiting && notify) {
                        osSignalSet(obj->rx_waiting, IO_SIGNAL_SERIAL_RX);
                        obj->rx_waiting = 0;
                }
        }
}

void
serial_enable_dma_tx(serial_t *obj)
{
        UARTLP_Type *uart = obj->uart;
        uart_dma_channel_t *channel = &uart_dma_channels[obj->index];
        struct DMA_Channel *dma;
        __IO uint8_t *dmamux;
        uint32_t c2_state;

        obj->dmamux_tx_slot = channel->tx_slot;
        obj->dmamux_tx_reg = &DMAMUX0->CHCFG[channel->tx_channel];
        obj->dma_tx_regs = &DMA0->DMA[channel->tx_channel];
        obj->dma_irq_n = DMA0_IRQn + channel->tx_channel;

        dma = obj->dma_tx_regs;
        dmamux = obj->dmamux_tx_reg;

        dma_init();
        dma_register_handler(obj->dma_irq_n, serial_dma_tx_irq, (uint32_t) obj);

        c2_state = uart->C2 & (UART0_C2_TE_MASK | UART0_C2_RE_MASK);
        uart->C2 &= ~(UART0_C2_TE_MASK | UART0_C2_RE_MASK);

        *dmamux = 0x00;   //Disable DMA MUX channel first

        dma->DSR_BCR = DMA_DSR_BCR_DONE_MASK;     // clear any pending errors
        dma->DAR = (uint32_t) &obj->uart->D;                //Set source address UART0_D

        // setup which slot source to use and enable mux channel
        *dmamux = (DMAMUX_CHCFG_ENBL_MASK | obj->dmamux_tx_slot);

        if (obj->index == 0) {
                ((UART0_Type *) uart)->C5 |= UART0_C5_TDMAE_MASK;   // Turn on DMA request
        } else {
                ((UART_Type *) uart)->C4 |= UART_C4_TDMAS_MASK;
        }

        uart->C2 |= c2_state;

        // serial_dma_transmit will finish the dma setup when a request is received
}

void
serial_dma_tx_irq(uint32_t param)
{
        serial_t *obj = (serial_t *)param;
        struct DMA_Channel *dma = obj->dma_tx_regs;
        UARTLP_Type *uart = obj->uart;
        serial_stat_t *stats = &serial_stats[obj->index];

        uint32_t status = dma->DSR_BCR;

        stats->dma_tx_interrupts++;

        uart->C2 &= ~(UARTLP_C2_TIE_MASK);

        dma->DSR_BCR = DMA_DSR_BCR_DONE_MASK;

        //uart_printf("[tx_dma0_irq]");
        // check for errors
        if (status & (DMA_DSR_BCR_CE_MASK|DMA_DSR_BCR_BES_MASK|DMA_DSR_BCR_BED_MASK)) {
                // TODO - clear the error, and fall back to non-dma tx
                error("uart%d: dma err 0x%x", obj->index, status);
        }

        if (status & DMA_DSR_BCR_DONE_MASK) {
                obj->tx_out = (obj->tx_out + obj->dma_tx_xfer) & obj->tx_size_mask;

                if (uart->S1 & UART_S1_OR_MASK) {
                        stats->rx_overflow++;
                }

                serial_dma_transmit(obj);

                if (obj->tx_waiting) {
                        osSignalSet(obj->tx_waiting, IO_SIGNAL_SERIAL_TX);
                        obj->tx_waiting = 0;
                }
        }
}

void
serial_dma_transmit(serial_t *obj)
{
        struct DMA_Channel *dma = obj->dma_tx_regs;
        UARTLP_Type *uart = obj->uart;

        int bytes;

        if (obj->tx_out == obj->tx_in) {
                obj->tx_out = obj->tx_in = 0;
                obj->transmitting = 0;
                return;
        }

        if (obj->tx_out > obj->tx_in)
                bytes = obj->tx_size - obj->tx_out;
        else
                bytes = obj->tx_in - obj->tx_out;

        obj->dma_tx_xfer = bytes;

        dma->SAR = (uint32_t) &obj->tx_buffer[obj->tx_out];
        dma->DSR_BCR = DMA_DSR_BCR_BCR(bytes);
        dma->DCR = (DMA_DCR_EINT_MASK | DMA_DCR_ERQ_MASK | DMA_DCR_SSIZE(1) | DMA_DCR_DSIZE(1)
                    | DMA_DCR_SINC_MASK | DMA_DCR_CS_MASK | DMA_DCR_D_REQ_MASK);

        uart->C2 |= (UARTLP_C2_TIE_MASK);
        obj->transmitting = 1;

        serial_stats[obj->index].tx_bytes += bytes;
}

void
serial_console_init(bool power_on)
{
        serial_init(&console_uart, CONSOLE_TX, CONSOLE_RX);
        serial_baud(&console_uart, 57600);
        serial_buffer(&console_uart, console_tx_buffer, CONSOLE_TX_BUFFER_SIZE, console_rx_buffer, CONSOLE_RX_BUFFER_SIZE);

        console_uart.lock = osMutexCreate(osMutex(serial_console_mutex));

        if (power_on)
                serial_power_on(&console_uart);
}

/*
** ###################################################################
**     Processors:          MKL46Z256VLH4
**                          MKL46Z128VLH4
**                          MKL46Z256VLL4
**                          MKL46Z128VLL4
**                          MKL46Z256VMC4
**                          MKL46Z128VMC4
**
**     Compilers:           ARM Compiler
**                          Freescale C/C++ for Embedded ARM
**                          GNU C Compiler
**                          IAR ANSI C/C++ Compiler for ARM
**
**     Reference manual:    KL46P121M48SF4RM, Rev.1 Draft A, Aug 2012
**     Version:             rev. 2.0, 2012-12-12
**
**     Abstract:
**         Provides a system configuration function and a global variable that
**         contains the system frequency. It configures the device and initializes
**         the oscillator (PLL) that is part of the microcontroller device.
**
**     Copyright: 2012 Freescale, Inc. All Rights Reserved.
**
**     http:                 www.freescale.com
**     mail:                 <EMAIL>
**
**     Revisions:
**     - rev. 1.0 (2012-10-16)
**         Initial version.
**     - rev. 2.0 (2012-12-12)
**         Update to reference manual rev. 1.
**
** ###################################################################
*/

/**
 * @file MKL46Z4
 * @version 2.0
 * @date 2012-12-12
 * @brief Device specific configuration file for MKL46Z4 (header file)
 *
 * Provides a system configuration function and a global variable that contains
 * the system frequency. It configures the device and initializes the oscillator
 * (PLL) that is part of the microcontroller device.
 */

#ifndef SYSTEM_MKL46Z4_H_
#define SYSTEM_MKL46Z4_H_                        /**< Symbol preventing repeated inclusion */

#include <stdint.h>

/**
 * @brief Setup the microcontroller system.
 *
 * Typically this function configures the oscillator (PLL) that is part of the
 * microcontroller device. For systems with variable clock speed it also updates
 * the variable SystemCoreClock. SystemInit is called from startup_device file.
 */
void SystemInit (void);


#endif  /* #if !defined(SYSTEM_MKL46Z4_H_) */

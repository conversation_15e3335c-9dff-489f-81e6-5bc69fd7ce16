/** @{

    @ingroup    console
    @file
*/

#include "pelagic.h"
#include "bnet.h"
#include "bnet_console.h"
#include "radio.h"

volatile bool bnet_console_capture_pong, bnet_console_have_pong;
board_uid_t bnet_console_pong_uid;

/**
    @brief  discover a VMS on the network with a partial IMEI
    @param[in]  imei    Partial IMEI number to ping the network with
    @param[out] uid     Discovered board's UID
    @return true if a board responded

    @note   An IMEI partial PING is sent on the network once a second until
            a system responds. The receiving thread will set the responding
            UID.
*/


bool
find_server(imei_t imei, board_uid_t uid)
{
        uint8_t ping_data[sizeof(bnet_ping_t) + sizeof(imei_t)];
        bnet_ping_t *ping = (bnet_ping_t *) ping_data;

        ping->type = BNET_TYPE_PING;
        ping->ping_type = BNET_TYPE_IMEI;
        ping->sequence = 0;
        uid_set_me(ping->from_uid);

        imei_copy(ping->destination, imei);

        bnet_console_capture_pong = true;
        bnet_console_have_pong = false;

        for (int retries = 0; retries < 20 && bnet_console_have_pong == false; retries++) {
                uart_printf(".");
                if (have_esc()) {
                        bnet_console_capture_pong = false;
                        uart_printf("cancelled\n");
                        return false;
                }

                ping->sequence = retries;
                radio_send((uint8_t *)ping, sizeof(bnet_ping_t) + sizeof(imei_t));
                osDelay(1000);
        }

        bnet_console_capture_pong = false;
        if (bnet_console_have_pong == false) {
                uart_printf("did not find any devices\n");
                return false;
        }

        uid_copy(uid, bnet_console_pong_uid);

        return true;
}

/**
    @brief  connect to a board
    @param[in]  argc    argv array size
    @param[in]  argv    connect arguments
*/

void
connect_command(int argc, char **argv)
{
        board_uid_t uid;
        imei_t want_imei;

        if (argc == 1) {
                uart_printf("usage: c IMEI\n");
                return;
        }

        imei_to_i(argv[1], want_imei);

        if (find_server(want_imei, uid))
                bnet_console_client_connect(uid);
}

/**
    @brief  display radio statistics
*/

void
status_command(int argc, char **argv)
{
        radio_stats();
}

/**
    @brief  display radio register status
*/

void
chip_command(int argc, char **argv)
{
        radio_chip_status();
}

extern void bnet_console_client_test(board_uid_t uid);

/**
    @brief  run a repetitive test against target system
*/

void
test_command(int argc, char **argv)
{
        board_uid_t uid;
        imei_t want_imei;

        if (argc == 1) {
                uart_printf("usage: test IMEI\n");
                return;
        }

        imei_to_i(argv[1], want_imei);

        if (find_server(want_imei, uid))
                bnet_console_client_test(uid);
}

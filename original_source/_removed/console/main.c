/** @{

    @ingroup    console
    @file

    @brief      Console Main Thread
*/

#include "pelagic.h"
#include "led.h"
#include "bnet.h"
#include "board_fault.h"
#include "system_file.h"
#include "radio.h"
#include "alarm.h"
#include "shell.h"

// Used by multiple locations
shared_buffer_t the_buffer __attribute__ (( aligned (4) ));

extern void random_setup_seed();

void bnet_console_keepalive_thread(void const *arg);
osThreadDef(bnet_console_keepalive_thread, osPriorityNormal, 1, 0);
osThreadId bnet_console_keepalive_tid;

void bnet_thread(void const *arg);
osThreadDef(bnet_thread, osPriorityNormal, 1, 0);
osThreadId bnet_tid;

osThreadId main_tid;

void printf_init();

shell_t shell;

extern void bnet_console_init();

/**
    @brief  setup console box and run the shell
*/

int
main()
{
        // serial_console_init(true);

        rtc_init();
        printf_init();

#ifdef HAVE_LED
        led_init();
#endif

        uid_init();

        announce("Console Box");

        main_tid = osThreadGetId();

        random_setup_seed();

        radio_init(true);
        radio_set_rx_mode(RADIO_RX_MODE_HIGH);

        bnet_console_init();
        bnet_console_keepalive_tid = osThreadCreate(osThread(bnet_console_keepalive_thread), NULL);
        bnet_tid = osThreadCreate(osThread(bnet_thread), NULL);

        if (bnet_console_keepalive_tid == NULL) {
                printf("could not create keepalive thread\n");
        }

        osDelay(2000);

        for (;;) {
                shell_init(&shell, shell_commands);
                shell_run(&shell);
        }
}

/** @} */

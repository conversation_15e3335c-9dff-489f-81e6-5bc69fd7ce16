/** @{

    @ingroup    console
    @file
*/

#include "pelagic.h"
#include "bnet.h"
#include "bnet_console.h"
#include "radio.h"
#include "test_server.h"

void
ft_command(int argc, char **argv)
{
        imei_t want_imei;

        if (argc < 2 || !imei_valid_str(argv[2])) {
                uart_printf("usage: ft <IMEI>\n");
                return;
        }

        imei_to_i(argv[2], want_imei);

        test_server_loop();
}


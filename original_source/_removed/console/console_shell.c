#include "pelagic.h"
#include "shell.h"

void connect_command(int argc, char **argv);
void ping_command(int argc, char **argv);
void wakeup_command(int argc, char **argv);
void test_command(int argc, char **argv);
void status_command(int argc, char **argv);
void chip_command(int argc, char **argv);
void net_command(int argc, char **argv);
void deploy_all_command(int argc, char **argv);
bool send_provision(int provision_type, int provision_mode, imei_t imei);
void provision_bnet_command(int argc, char **argv);
void radio_syncword_command(int argc, char **argv);

const shell_command_t shell_commands[] = {
        { "c", "connect to device", connect_command },
        { "test", "test to device", test_command },
        { "ping", "ping network", ping_command },
        { "wakeup", "wakeup sensors", wakeup_command },
        { "net", "net commands", net_command },
        { "provision", "force a device into [factory | shipping | deployed] Mode", provision_bnet_command },
        { "syncword", "set the radio syncword to [ old | new ]", radio_syncword_command },
        { NULL, NULL, NULL }
};

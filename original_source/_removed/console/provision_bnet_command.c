/** @{

	@ingroup    console
	@file
*/

#include "pelagic.h"
#include "bnet.h"
#include "bnet_console.h"
#include "radio.h"
#include "bnet_factory.h"
#include "provision.h"

int FACTORY_SLEEP_INTERVAL = 15; // Must match factory_test_radio.c

bool
send_provision(int provision_type, int provision_mode, imei_t imei)
{
        bnet_provision_t *provision_pkt = (bnet_provision_t *)bnet_send_buffer;
        bnet_provision_ack_t *provision_ack_pkt = (bnet_provision_ack_t *)bnet_receive_buffer;
        int bytes;
        bool got_ack;
        bool got_correct_ack;

        provision_pkt->type = BNET_TYPE_PROVISION;

        if (provision_type == PROVISION_TYPE_IMEI)
                imei_copy(provision_pkt->to_imei, imei);

        provision_pkt->provision_type = provision_type;
        provision_pkt->provision_mode = provision_mode;
        uid_set_me(provision_pkt->from_uid);

        // TODO: Handle ESC
        for(int i=0; i<FACTORY_SLEEP_INTERVAL+10; i++) {
                if (have_esc()) {
                        printf("Cancelled.\n");
                        return false;
                }

#ifndef BNET_VERBOSE
                uart_printf(".");
#endif
                radio_send((uint8_t *)provision_pkt, sizeof(bnet_ft_target_t));
                got_ack = false;
                got_correct_ack = false;

                while (true) {
                        bytes = radio_read(provision_ack_pkt, 1000, NULL);
                        got_ack = (
                                          bytes == sizeof(bnet_provision_ack_t) &&
                                          provision_ack_pkt->type == BNET_TYPE_PROVISION_ACK &&
                                          uid_match_me(provision_ack_pkt->to_uid)
                                  );

                        got_correct_ack = got_ack && imei_match(provision_ack_pkt->from_imei, imei);

                        if (got_correct_ack) {
                                return true;
                        } else if (got_ack) {
                                // If we're provisioning many devices we may have lots of ACKs, so quickly keep looking for the right packet
                                continue;
                        } else {
                                osDelay(1000);
                                break;
                        }
                }
        }

        return false;
}

void
provision_bnet_command(int argc, char **argv)
{
        imei_t want_imei;
        int mode = 0;

        if (argc < 3) {
                uart_printf("usage: provision <factory | shipping | deployed> <IMEI>\n");
                return;
        }

        if (strcmp(argv[1], "factory") == 0) {
                mode = PROVISION_STAGE_FACTORY;
        } else if (strcmp(argv[1], "shipping") == 0) {
                mode = PROVISION_STAGE_SHIPPING;
        } else if (strcmp(argv[1], "deployed") == 0) {
                mode = PROVISION_STAGE_DEPLOYED;
        } else {
                uart_printf("usage: provision <factory | shipping | deployed> <IMEI>\n");
                return;
        }

        if (strcmp(argv[2], "broadcast") == 0) {
                // if (send_provision(PROVISION_TYPE_BROADCAST, mode, want_imei)) {
                //     uart_printf("\nSuccessfully switched to %s mode.\n\n", argv[1]);
                // } else {
                //     uart_printf("\nFailed switching to %s mode.\n\n", argv[1]);
                // }
        } else {
                if (!imei_valid_str(argv[2])) {
                        uart_printf("usage: provision <factory | shipping | deployed> <IMEI>\n");
                        return;
                }

                imei_to_i(argv[2], want_imei);

                if (send_provision(PROVISION_TYPE_IMEI, mode, want_imei)) {
                        uart_printf("\nSuccessfully switched to %s mode.\n\n", argv[1]);
                } else {
                        uart_printf("\nFailed switching to %s mode.\n\n", argv[1]);
                }
        }
}


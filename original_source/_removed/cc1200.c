#include "pelagic.h"
#include "wait_api.h"
#include "gpio_api.h"
#include "gpio_irq_api.h"
#include "spi_api.h"
#include "signals.h"
#include "cc1200.h"
#include "radio.h"
#include "bnet.h"
#include "pinmap.h"

// Registers
enum {
        CC1200_MAX_PACKET_LEN  = 124,

        // old sync
        CC1200_SYNC_WORD0      = 0xCA,
        CC1200_SYNC_WORD1      = 0xFE,

        // new sync
        CC1200_SYNC_BYTE3      = 0xD3,
        CC1200_SYNC_BYTE2      = 0x91,
        CC1200_SYNC_BYTE1      = 0xD3,
        CC1200_SYNC_BYTE0      = 0x91,

        CC1200_CHIP_ID             = 0x20,

        CC1200_RETRY_ATTEMPTS  = 4,
        CC1200_RETRY_RESET     = 2,

};

enum {
// command strobes
        CC1200_SRES    = 0x30,          // Reset CC1200 chip
        CC1200_SFSTXON = 0x31,         // Enable and calibrate frequency synthesizer (if MCSM0.FS_AUTOCAL=1). If in RX (with CCA):
        // Go to a wait state where only the synthesizer is running (for quick RX / TX turnaround).
        CC1200_SXOFF   = 0x32,         // Turn off crystal oscillator
        CC1200_SCAL    = 0x33,         // Calibrate frequency synthesizer and turn it off. SCAL can be strobed from IDLE mode without
        // setting manual calibration mode (MCSM0.FS_AUTOCAL 0)
        CC1200_SRX     = 0x34,         // Enable RX. Perform calibration first if coming from IDLE and MCSM0.FS_AUTOCAL=1
        CC1200_STX     = 0x35,         // In IDLE state: Enable TX. Perform calibration first if MCSM0.FS_AUTOCAL=1.
        // If in RX state and CCA is enabled: Only go to TX if channel is clear
        CC1200_SIDLE   = 0x36,         // Exit RX / TX, turn off frequency synthesizer and exit Wake-On-Radio mode if applicable
        CC1200_SAFC    = 0x37,
        CC1200_SWOR    = 0x38,         // Start automatic RX polling sequence (Wake-on-Radio) as described in Section 19.5 if WORCTRL.RC_PD =0
        CC1200_SPWD    = 0x39,         // Enter power down mode when CSn goes high
        CC1200_SFRX    = 0x3A,         // Flush the RX FIFO buffer. Only issue SFRX in IDLE or RXFIFO_OVERFLOW states
        CC1200_SFTX    = 0x3B,         // Flush the TX FIFO buffer. Only issue SFTX in IDLE or TXFIFO_UNDERFLOW states
        CC1200_SWORRST = 0x3C,         // Reset real time clock to Event1 value
        CC1200_SNOP    = 0x3D,         // No operation. May be used to get access to the chip status byte
};

void cc1200_shutdown();
uint8_t cc1200_read_reg(uint16_t addr);
void cc1200_write_reg(uint16_t addr, uint8_t value);

typedef struct {
        uint16_t reg;
        uint8_t value;
} cc_config_t;

const cc_config_t cc1200_config_433[] =  {
// Bit rate = 100kbps (~12.5kbytes/sec)
// RX filter BW = 208.333333
// Address config = No address check
// Modulation format = 2-GFSK
// Packet length mode = Variable
// Packet bit length = 0
// Whitening = false
// Packet length = 255
// Symbol rate = 100
// Carrier frequency = 433.999939
// Manchester enable = false
// Deviation = 49.896240

        {CC1200_SYNC_CFG1,         0xa8},       // 32-bit sync word,
        {CC1200_SYNC_CFG0,         0x23},       // auto clear, strict sync disabled
        {CC1200_DEVIATION_M,       0x47},
        {CC1200_MODCFG_DEV_E,      0x0c},
        {CC1200_DCFILT_CFG,        0x4b},
        {CC1200_PREAMBLE_CFG0,     0x8a},
        {CC1200_IQIC,              0xD8},
        {CC1200_CHAN_BW,           0x08},
        {CC1200_MDMCFG1,           0x4A}, // TODO - should CS_GATE enabled? (0x62?)
        {CC1200_MDMCFG0,           0x05},
        {CC1200_SYMBOL_RATE2,      0xA4},
        {CC1200_SYMBOL_RATE1,      0x7A},
        {CC1200_SYMBOL_RATE0,      0xE1},
        {CC1200_AGC_REF,           0x2A},
        {CC1200_AGC_CS_THR,        0xF7},
        {CC1200_AGC_CFG1,          0x00},
        {CC1200_AGC_CFG0,          0x80},
        {CC1200_FS_CFG,            0x14},
        {CC1200_PKT_CFG0,          0x20},
        {CC1200_IF_MIX_CFG,        0x1c},
        {CC1200_TOC_CFG,           0x03},
        {CC1200_MDMCFG2,           0x02},
        {CC1200_FREQ2,             0x56},
        {CC1200_FREQ1,             0xCC},
        {CC1200_FREQ0,             0xCC},
        {CC1200_IF_ADC1,           0xEE},
        {CC1200_IF_ADC0,           0x10},
        {CC1200_FS_DIG1,           0x04},
        {CC1200_FS_DIG0,           0x50},
        {CC1200_FS_CAL1,           0x40},
        {CC1200_FS_CAL0,           0x0E},
        {CC1200_FS_DIVTWO,         0x03},
        {CC1200_FS_DSM0,           0x33},
        {CC1200_FS_DVC1,           0xF7},
        {CC1200_FS_DVC0,           0x0F},
        {CC1200_FS_PFD,            0x00},
        {CC1200_FS_PRE,            0x6E},
        {CC1200_FS_REG_DIV_CML,    0x1C},
        {CC1200_FS_SPARE,          0xAC},
        {CC1200_FS_VCO0,           0xB5},
        {CC1200_IFAMP,             0x09},
        {CC1200_XOSC5,             0x0E},
        {CC1200_XOSC1,             0x03},

        // the following two configs are use for SWOR mode - comment out if
        // switching back to normal SRX.
        { CC1200_PKT_LEN,           125 },
        { CC1200_PREAMBLE_CFG1,     0xc << 2 },           // 24 byte preamble
        { CC1200_IOCFG0,            20 },        // MCU Status
        { CC1200_IOCFG2,            6 },         // Packet receiving in progress
        { CC1200_PKT_CFG1,          0x03 },      // CRC check, and append status.
#ifdef USE_OLD_SYNCWORD
        { CC1200_SYNC1,             CC1200_SYNC_WORD1 },  // Sync word
        { CC1200_SYNC0,             CC1200_SYNC_WORD0 },
#else
        { CC1200_SYNC3,             CC1200_SYNC_BYTE3 },  // Sync bytes
        { CC1200_SYNC2,             CC1200_SYNC_BYTE2 },
        { CC1200_SYNC1,             CC1200_SYNC_BYTE1 },
        { CC1200_SYNC0,             CC1200_SYNC_BYTE0 },
#endif
        { CC1200_FIFO_CFG,          0x80 },      // Auto flush when CRC mismatch happens
        { CC1200_PKT_CFG2,          0x0 },       // Always give clear channel - try to over come interference
};

cc_config_t cc1200_config_rx_full[] = {
        { CC1200_RFEND_CFG0,        0x36 },    // head to RX after TX, cont. antenna diversity based on PQT.
        { CC1200_SETTLING_CFG,      0x1b },     // calibrate every 4th time when rx/tx to idle
        { CC1200_WOR_CFG0,          0x21 },
        { CC1200_WOR_EVENT0_LSB,    0 },
};

const cc_config_t cc1200_config_rx_sniff[] = {
        { CC1200_PREAMBLE_CFG0,    0xda },    // enable preamble dectection
        { CC1200_RFEND_CFG0,       0x32 },    // head to RX after TX, RX termination based on CS
        { CC1200_SETTLING_CFG,     0x03 },    // No calibration, recommended for RX sniff.
        { CC1200_WOR_CFG0,         0x00 },    // disable clock division.
        { CC1200_WOR_EVENT0_LSB,   0x46 },
};

enum {
#if defined(TARGET_CONSOLE) || defined(TARGET_TESTBOX) || defined(TARGET_OLDTESTBOX)
        CC1200_RX_PACKETS        = 32,
#else
        CC1200_RX_PACKETS        = 6,
#endif
};

enum {
        CC1200_MODE_IDLE = 0,
        CC1200_MODE_RX,
        CC1200_MODE_TX,
        CC1200_MODE_OFF
};

volatile uint8_t cc1200_mode;

volatile osThreadId cc1200_xmit_tid, cc1200_rx_tid;

typedef struct radio_packet {
        uint8_t size;
        int8_t rssi;
        uint8_t status;
        uint8_t data[CC1200_MAX_PACKET_LEN];
} radio_packet_t;

radio_packet_t cc1200_rx_packets[CC1200_RX_PACKETS];

volatile uint8_t cc1200_rx_in = 0, cc1200_rx_out = 0, cc1200_rx_count = 0;
volatile bool cc1200_xmit_retry = false;
bool cc1200_is_on = false;

osMutexId cc1200_xmit_mutex;
osMutexDef(cc1200_xmit_mutex);

spi_t cc1200_spi;
gpio_t cc1200_cs_pin, cc1200_dio0_pin, cc1200_dio2_pin, cc1200_reset_pin, cc1200_miso_pin;
gpio_irq_t cc1200_int_irq, cc1200_dio2_irq;

#ifdef HAVE_V1
gpio_t cc1200_power_switch;
volatile bool cc1200_have_power = false;
#endif

bool cc1200_initted = false, cc1200_pin_initted = false;

void cc1200_interrupt(gpio_irq_event event);

struct {
        uint32_t rexmit;
        uint32_t tx_timeout;
        uint32_t cca_busy;
        uint32_t rx_fifo_err;
        uint32_t tx_fifo_err;
        uint32_t zero_bytes;
        uint32_t settle_timeout;
        uint32_t zero_status;
} cc1200_stats;

radio_rx_mode_t cc1200_rx_mode;

void
cc1200_lock_xmit()
{
        osMutexWait(cc1200_xmit_mutex, osWaitForever);
}

void
cc1200_unlock_xmit()
{
        osMutexRelease(cc1200_xmit_mutex);
}

void
cc1200_disable_int()
{
        gpio_irq_disable(&cc1200_int_irq);
}

void
cc1200_enable_int()
{
        gpio_irq_enable(&cc1200_int_irq);
}

void
cc1200_select()
{
        gpio_write(&cc1200_cs_pin, 0);

        // The cc1200 indicates readiness by lowering MISO after CS goes low
        // Typically this takes around 1,200 - 1,700 loops at 20mhz.

        for (int i = 0; i < 1000000 && gpio_read(&cc1200_miso_pin); i++)
                ;
}

void
cc1200_unselect()
{
        gpio_write(&cc1200_cs_pin, 1);
}

void
cc1200_command_strobe(uint8_t reg)
{
        cc1200_select();
        spi_master_write(&cc1200_spi, reg);
        cc1200_unselect();
}

void
cc1200_write_reg(uint16_t addr, uint8_t data)
{
        uint8_t space = (addr >> 8) & 0xff, reg = addr & 0xff;

        cc1200_select();
        if (space == 0x2f) {
                spi_master_write(&cc1200_spi, space | CC1200_BURST);
                spi_master_write(&cc1200_spi, reg);
                spi_master_write(&cc1200_spi, data);
        } else {
                spi_master_write(&cc1200_spi, reg);
                spi_master_write(&cc1200_spi, data);
        }
        cc1200_unselect();
}

uint8_t
cc1200_read_reg(uint16_t addr)
{
        uint8_t space = (addr >> 8) & 0xff, reg = addr & 0xff;
        uint8_t result;

        cc1200_select();
        if (space == 0x2f) {
                spi_master_write(&cc1200_spi, space | CC1200_BURST | CC1200_READ_REG);
                spi_master_write(&cc1200_spi, reg);
                result = spi_master_write(&cc1200_spi, 0);
        } else {
                spi_master_write(&cc1200_spi, reg | CC1200_READ_REG);
                result = spi_master_write(&cc1200_spi, 0);
        }
        cc1200_unselect();

        return result;
}

uint8_t
cc1200_read_status()
{
        uint8_t data;

        cc1200_select();
        data = spi_master_write(&cc1200_spi, (CC1200_SNOP | CC1200_READ_REG));
        cc1200_unselect();

        return data;
}

//
// Similar to cs1200_select in waiting for MISO to lower
// except allows for a longer delay for when coming out
// of a hard reset
//
void
cc1200_reset_settle()
{
        gpio_write(&cc1200_cs_pin, 0);

        for (int i = 0; i < 1000 && gpio_read(&cc1200_miso_pin); i++)
                osDelay(1);

        gpio_write(&cc1200_cs_pin, 1);
}

void
cc1200_wait_for_settle()
{
        uint8_t status;

        for (int i = 0; i < 1000; i++) {
                status = cc1200_read_status();
                if ((status & 0x80) == 0)
                        return;

                wait_us(50);
        }
}

void
cc1200_mode_rx()
{
        cc1200_wait_for_settle();

        if (cc1200_rx_mode == RADIO_RX_MODE_HIGH)
                cc1200_command_strobe(CC1200_SRX);
        else
                cc1200_command_strobe(CC1200_SWOR);

        cc1200_mode = CC1200_MODE_RX;
}

void
cc1200_mode_tx()
{
        cc1200_wait_for_settle();
        cc1200_command_strobe(CC1200_STX);
        cc1200_mode = CC1200_MODE_TX;
}

void
cc1200_mode_off()
{
        cc1200_wait_for_settle();
        cc1200_command_strobe(CC1200_SPWD);
        cc1200_mode = CC1200_MODE_OFF;
}

void
cc1200_mode_idle()
{
        uint8_t status;
        int i;

        cc1200_wait_for_settle();
        cc1200_command_strobe(CC1200_SIDLE);

        // wait until chip settles into idle mode
        // NOTE: Do not call this from interrupt - osDelay() will fail
        //

        for (i = 0; i < 1000; i++) {
                status = cc1200_read_status();

                if (status & 0x80) {
                        osDelay(1);
                        continue;
                }

                if ((status & 0x70) == 0)
                        break;

                switch (status & 0x70) {
                case 0x60:
                        cc1200_command_strobe(CC1200_SFRX);
                        cc1200_stats.rx_fifo_err++;
                        break;

                case 0x70:
                        cc1200_command_strobe(CC1200_SFTX);
                        cc1200_stats.tx_fifo_err++;
                        break;
                }

                osDelay(1);
        }

        if (status) {
                uart_printf("cc1200: idle settle timeout [%x]\n", status);
                //radio_chip_status();
                cc1200_stats.settle_timeout++;
        }

        cc1200_mode = CC1200_MODE_IDLE;
}

void
cc1200_calibrate_rcosc()
{
        uint8_t temp;

        // Read current register value
        temp = cc1200_read_reg(CC1200_WOR_CFG0);

        // Mask register bit fields and write new values
        temp = (temp & 0xF9) | (0x02 << 1);

        // Write new register value
        cc1200_write_reg(CC1200_WOR_CFG0, temp);

        // Strobe IDLE to calibrate the RCOSC
        cc1200_mode_idle();

        // Disable RC calibration
        temp = (temp & 0xF9) | (0x00 << 1);
        cc1200_write_reg(CC1200_WOR_CFG0, temp);
}

void
cc1200_load_regs(const cc_config_t *regs, int size)
{
        for (int i = 0; i < size; i++, regs++) {
                cc1200_write_reg(regs->reg, regs->value);
        }
}

void
cc1200_init_rx_sniff()
{
        cc1200_load_regs(cc1200_config_rx_sniff, ARRAY_SIZE(cc1200_config_rx_sniff));
}

void
cc1200_init_rx_full()
{
        cc1200_load_regs(cc1200_config_rx_full, ARRAY_SIZE(cc1200_config_rx_full));
}


void
cc1200_reset()
{
        // Reset the chip
        gpio_write(&cc1200_cs_pin, 1);
        gpio_write(&cc1200_reset_pin, 1);
        osDelay(100);
        gpio_write(&cc1200_reset_pin, 0);
        osDelay(10);
        gpio_write(&cc1200_reset_pin, 1);

        cc1200_reset_settle();

        cc1200_mode_idle();

        cc1200_load_regs(cc1200_config_433, ARRAY_SIZE(cc1200_config_433));

        if (cc1200_rx_mode == RADIO_RX_MODE_LOW) {
                cc1200_init_rx_sniff();
        } else {
                cc1200_init_rx_full();
        }
}

// only setup those pins needed to maintain state
//  across sleep periods.

void
radio_pin_setup()
{
        if (cc1200_pin_initted)
                return;

        // SPI - chip select pin
        gpio_init_out(&cc1200_cs_pin, RADIO_CS);
        gpio_init_out(&cc1200_reset_pin, RADIO_RST);

#ifdef HAVE_V1
        gpio_init_out_ex(&cc1200_power_switch, RADIO_PWR_SWITCH, 0);
        gpio_write(&cc1200_cs_pin, 0);
        gpio_write(&cc1200_reset_pin, 0);
#else
        gpio_write(&cc1200_cs_pin, 1);
        gpio_write(&cc1200_reset_pin, 1);
#endif
        pin_mode(RADIO_DIO0, PullDown);
        pin_mode(RADIO_DIO2, PullDown);

        cc1200_pin_initted = true;
}

void
cc1200_calibrate()
{
        uint8_t marc;

        cc1200_command_strobe(CC1200_SCAL);

        do {
                marc = cc1200_read_reg(CC1200_MARCSTATE);
        } while (marc != 0x41);

        cc1200_calibrate_rcosc();
}

bool
radio_init(bool power_on)
{
        if (cc1200_initted)
                return true;

        radio_pin_setup();

#ifdef HAVE_V1
        gpio_write(&cc1200_power_switch, 1);
        osDelay(500);
        cc1200_have_power = true;
#else
        gpio_write(&cc1200_cs_pin, 1);
        gpio_write(&cc1200_reset_pin, 1);
#endif

        pin_mode(RADIO_DIO0, PullNone);
        pin_mode(RADIO_DIO2, PullNone);

        cc1200_xmit_mutex = osMutexCreate(osMutex(cc1200_xmit_mutex));

        gpio_init_in(&cc1200_miso_pin, RADIO_MISO);

        // setup SPI
        spi_init(&cc1200_spi,  RADIO_MOSI, RADIO_MISO, RADIO_SCLK, NC);
        spi_format(&cc1200_spi, 8, 0, 0);
        spi_frequency(&cc1200_spi, 4000000);

        cc1200_reset();

        uint8_t part = cc1200_read_reg(CC1200_PARTNUMBER);

        if (part != CC1200_CHIP_ID) {
                EVENT_LOG1(EVT_RADIO_NOT_PRESENT, "not present", "chip", EVT_STRCONST, "cc1200");
                return false;
        }

        EVENT_LOG2(EVT_RADIO_INIT, "init", "chip", EVT_STRCONST, "cc1200", "vers", EVT_8BIT|EVT_HEX, cc1200_read_reg(CC1200_PARTVERSION));

        gpio_init_in(&cc1200_dio0_pin, RADIO_DIO0);
        gpio_irq_init(&cc1200_int_irq, RADIO_DIO0, cc1200_interrupt);
        gpio_irq_set(&cc1200_int_irq, IRQ_FALL, 1);
        gpio_irq_disable(&cc1200_int_irq);

#if 0
        gpio_init_in(&cc1200_dio2_pin, RADIO_DIO2);
        gpio_irq_init(&cc1200_int_irq, RADIO_DIO2, cc1200_interrupt);
#endif
        gpio_irq_set(&cc1200_int_irq, IRQ_FALL, 1);
        gpio_irq_disable(&cc1200_int_irq);

        cc1200_initted = true;

        if (power_on)
                radio_power_on();
        else
                cc1200_shutdown();

        return true;
}

void
radio_factory_provision()
{
        radio_init(false);
}

void
radio_power_off()
{
        if (!cc1200_initted || cc1200_is_on == false)
                return;

        cc1200_shutdown();
}

void
cc1200_shutdown()
{
        gpio_irq_disable(&cc1200_int_irq);

#ifdef HAVE_V1
        gpio_write(&cc1200_power_switch, 0);    // BLAM!
        gpio_write(&cc1200_cs_pin, 0);
        gpio_write(&cc1200_reset_pin, 0);
        cc1200_have_power = false;
#else
        cc1200_mode_idle();
        cc1200_mode_off();
#endif

        spi_power_off(&cc1200_spi);

        cc1200_is_on = false;

        pin_mode(RADIO_DIO0, PullDown);
        pin_mode(RADIO_DIO2, PullDown);
        EVENT_LOG(EVT_RADIO_POWER_OFF, "power off");
}

void
radio_power_on()
{
        if (!cc1200_initted || cc1200_is_on)
                return;

        spi_power_on(&cc1200_spi);

#ifdef HAVE_V1
        if (cc1200_have_power == false) {
                gpio_write(&cc1200_power_switch, 1);
                gpio_write(&cc1200_cs_pin, 1);
                gpio_write(&cc1200_reset_pin, 1);
                osDelay(500);
                cc1200_reset();
                cc1200_have_power = true;
        }
#endif

        pin_mode(RADIO_DIO0, PullNone);
        pin_mode(RADIO_DIO2, PullNone);

        cc1200_reset_settle();                    // cause a power up
        cc1200_calibrate();
        cc1200_mode_rx();
        gpio_irq_enable(&cc1200_int_irq);

        cc1200_is_on = true;

        EVENT_LOG(EVT_RADIO_POWER_ON, "power on");
}

bool
radio_is_off()
{
        return !cc1200_is_on;
}

void
cc1200_receive()
{
        radio_packet_t *pkt = &cc1200_rx_packets[cc1200_rx_in];
        uint8_t bytes = cc1200_read_reg(CC1200_NUM_RXBYTES);

        if (bytes == 0) {
                cc1200_stats.zero_bytes++;
                return;
        }

        cc1200_select();
        spi_master_write(&cc1200_spi, CC1200_BURST_RXFIFO);
        pkt->size = spi_master_write(&cc1200_spi, 0);
        spi_master_block_read(&cc1200_spi, pkt->data, pkt->size);
        pkt->rssi = spi_master_write(&cc1200_spi, 0);
        pkt->status = spi_master_write(&cc1200_spi, 0);
        cc1200_unselect();
        //uart_printf("{s%x}", pkt->status);
        // Advance the outbound pointer if the packets have filled up
        // (i.e. start dropping packets)

        cc1200_rx_in = (cc1200_rx_in + 1) % CC1200_RX_PACKETS;
        if (cc1200_rx_count < CC1200_RX_PACKETS) {
                cc1200_rx_count++;
        } else {
                cc1200_rx_out = (cc1200_rx_out + 1) % CC1200_RX_PACKETS;
        }

        if (cc1200_rx_tid) {
                osSignalSet(cc1200_rx_tid, IO_SIGNAL_RADIO_RX);
                cc1200_rx_tid = NULL;
        }

        cc1200_mode_rx();
}

void
cc1200_interrupt(gpio_irq_event event)
{
        uint8_t status1 = cc1200_read_reg(CC1200_MARC_STATUS1);

        if (status1 == 0) {
                cc1200_stats.zero_status++;
                return;
        }

        //uart_printf("[%x]", status1);
        switch (status1) {
        case 0x2:               // rx termination - restart
                break;

        case 0x7:                   // TX overflow
        case 0x8:                   // TX underflow
                cc1200_command_strobe(CC1200_SFTX);
                cc1200_stats.tx_fifo_err++;
                break;

        case 0x9:                   // rx fifo overflow
        case 0xa:                   // rx fifo underflow
                cc1200_command_strobe(CC1200_SFRX);                                 // clear out the fifo
                cc1200_stats.rx_fifo_err++;
                break;

        case 0xb:           // TX ON CCA Failed - aka transmit failed due to a noisy line.
                cc1200_stats.cca_busy++;
                break;

        case 0x40:
                if (cc1200_xmit_tid) {
                        osSignalSet(cc1200_xmit_tid, IO_SIGNAL_RADIO_TX);
                        cc1200_xmit_tid = NULL;
                }
                // chip automatically heads back to RX
                cc1200_mode = CC1200_MODE_RX;
                return;

        case 0x80:          // packet received
                cc1200_receive();
                break;

        default:
                return;         // safe to ignore.
        }

        if (cc1200_xmit_tid) {
                cc1200_xmit_retry = true;
                osSignalSet(cc1200_xmit_tid, IO_SIGNAL_RADIO_TX);
                cc1200_xmit_tid = NULL;
        } else {
                cc1200_mode_rx();
        }
}

int
radio_read_signal(void *buffer, uint32_t millisecs, int8_t *rssi, uint16_t *signals)
{
        radio_packet_t *pkt;
        int size;
        osEvent result;
        uint16_t fired;

        osThreadId tid = osThreadGetId();

        if (signals)
                *signals = 0;

        if (!cc1200_initted)
                return 0;

        for (;; ) {
                int_disable();
                //cc1200_disable_int();

                if (cc1200_rx_count > 0) {
                        pkt = &cc1200_rx_packets[cc1200_rx_out];
                        cc1200_rx_count--;
                        cc1200_rx_out = (cc1200_rx_out + 1) % CC1200_RX_PACKETS;

                        memcpy(buffer, pkt->data, pkt->size);
                        size = pkt->size;
                        if (rssi)
                                *rssi = pkt->rssi;
                        //cc1200_enable_int();
                        int_enable();

#ifdef BNET_VERBOSE
                        uart_printf("Rx ");
                        bnet_decode_packet(buffer, size, *rssi);
#endif

                        return size;
                }

                cc1200_rx_tid = tid;
                //cc1200_enable_int();
                int_enable();

                result = osSignalWait(signals ? 0 : IO_SIGNAL_RADIO_RX, millisecs);
                cc1200_rx_tid = NULL;

                if (result.status == osEventTimeout)
                        return 0;

                fired = result.value.signals & ~IO_SIGNAL_RADIO_RX;

                if (fired && signals) {
                        *signals = fired;
                        return 0;
                }
        }
}

int
radio_read(void *buffer, uint32_t millisecs, int8_t *rssi)
{
        return radio_read_signal(buffer, millisecs, rssi, NULL);
}

void
radio_flush()
{
        int_disable();
        cc1200_rx_count = 0;
        cc1200_rx_out = cc1200_rx_in = 0;
        int_enable();
}

void
radio_stats()
{
        printf("rexmits %d\n", cc1200_stats.rexmit);
        printf("tx timeout %d\n", cc1200_stats.tx_timeout);
        printf("cca busy %d\n", cc1200_stats.cca_busy);
        printf("rx fifo err %d\n", cc1200_stats.rx_fifo_err);
        printf("tx fifo err %d\n", cc1200_stats.tx_fifo_err);
        printf("zero bytes %d\n", cc1200_stats.zero_bytes);
        printf("settle timeout %d\n", cc1200_stats.settle_timeout);
        printf("zero status %d\n", cc1200_stats.zero_status);
}

void
radio_chip_status()
{
        uint8_t status, marc_state, marc_status0, marc_status1;

        if (cc1200_is_on == false) {
                uart_printf("chip is off\n");
                return;
        }

        cc1200_disable_int();
        status = cc1200_read_status();
        marc_state = cc1200_read_reg(CC1200_MARCSTATE);
        marc_status0 = cc1200_read_reg(CC1200_MARC_STATUS0);
        marc_status1 = cc1200_read_reg(CC1200_MARC_STATUS1);
        cc1200_enable_int();

        uart_printf("status %2.2x marc_state %x/%2.2x marc_status0 %2.2x marc_status1 %2.2x\n",
                    status >> 4,
                    marc_state >> 5,
                    marc_state & 0x1f,
                    marc_status0, marc_status1);

        uart_printf("mode %d\n", cc1200_mode);
}

void
radio_send(void *buffer, int size)
{
        osEvent result;
        osThreadId tid = osThreadGetId();
        int retries, timeouts = 0;

        if (!cc1200_is_on)
                return;

        cc1200_lock_xmit();
        for (retries = 0; retries < 4; retries++) {
                cc1200_disable_int();
                cc1200_xmit_retry = false;

                //
                // Check to see if the chip is receiving a packet or
                // if the carrier signal is present, wait up to 200 ms
                //

                for (int wait = 0; wait < 200; wait++) {
                        uint8_t cca;
                        if (gpio_read(&cc1200_dio2_pin) == 0) { // Not receiving a packet
                                cca = cc1200_read_reg(CC1200_RSSI0);

                                // Check to see if there's a carrier signal present
                                if ((cca & CC1200_CARRIER_SENSE_VALID) == 0)
                                        break;

                                if ((cca & CC1200_CARRIER_SENSE) == 0)
                                        break;
                        }

                        cc1200_enable_int();
                        osDelay(1);
                        cc1200_disable_int();
                }

                if (retries) {
                        cc1200_stats.rexmit++;
                }

                if (retries|| cc1200_rx_mode == RADIO_RX_MODE_LOW) {
                        if (timeouts >= CC1200_RETRY_RESET) {
                                cc1200_reset();
                        }

                        cc1200_mode_idle();
                        cc1200_command_strobe(CC1200_SFTX);
                        cc1200_command_strobe(CC1200_SFRX);
                        osDelay(10);
                }

                cc1200_command_strobe(CC1200_SFSTXON);
                cc1200_select();
                spi_master_write(&cc1200_spi, CC1200_BURST_TXFIFO);
                spi_master_write(&cc1200_spi, size);
                spi_master_block_write(&cc1200_spi, buffer, size);
                cc1200_unselect();

                cc1200_xmit_tid = tid;
                cc1200_mode_tx();
                cc1200_enable_int();

                result = osSignalWait(IO_SIGNAL_RADIO_TX, 100);
                cc1200_xmit_tid = NULL;

                if (result.status == osEventSignal) {
                        if (cc1200_xmit_retry)
                                continue;
                        break;
                }

                cc1200_disable_int();
                cc1200_reset();
                cc1200_mode_rx();
                cc1200_enable_int();

                cc1200_stats.tx_timeout++;
                timeouts++;
        }

        cc1200_disable_int();
        if (retries == CC1200_RETRY_ATTEMPTS) {
                cc1200_reset();
                cc1200_mode_rx();
                EVENT_LOG(EVT_RADIO_XMIT_TIMEOUT, "xmit timeout");
        } else if (cc1200_mode != CC1200_MODE_RX) {
                cc1200_mode_idle();
                cc1200_mode_rx();
        }
        cc1200_enable_int();
        cc1200_unlock_xmit();

#ifdef BNET_VERBOSE
        uart_printf("Tx ");
        bnet_decode_packet(buffer, size, 0);
#endif
}

void
cc1200_lock_and_idle()
{
        cc1200_lock_xmit();
        cc1200_disable_int();
        cc1200_mode_idle();  // idle the chip
        cc1200_command_strobe(CC1200_SFTX);
        cc1200_command_strobe(CC1200_SFRX);
        osDelay(10);
}

void
cc1200_unlock_and_resume()
{
        cc1200_calibrate();
        cc1200_mode_rx();
        cc1200_enable_int();
        cc1200_unlock_xmit();
}

void
radio_set_power_level(int8_t level)
{
        cc1200_lock_and_idle();
        cc1200_write_reg(CC1200_PA_CFG1, 0x40 | ((level * 2 + 36) - 1));
        cc1200_unlock_and_resume();
}

void
radio_set_rx_mode(radio_rx_mode_t mode)
{
        cc1200_lock_and_idle();
        cc1200_rx_mode = mode;

        if (mode == RADIO_RX_MODE_HIGH)
                cc1200_init_rx_full();
        else
                cc1200_init_rx_sniff();

        cc1200_unlock_and_resume();
}

void
radio_calibrate()
{
        if (cc1200_is_on == false)
                return;

        cc1200_lock_and_idle();
        cc1200_unlock_and_resume(); // calibrate will be run right before restarting rx
}

#if defined(TARGET_CONSOLE)
/*
   Support the ability to change the radio syncword in console image only.
*/
static void
radio_old_syncword(void)
{
        cc1200_write_reg(CC1200_SYNC3, 0x93);   // defaults from data sheet
        cc1200_write_reg(CC1200_SYNC2, 0x0b);   // defaults from data sheet
        cc1200_write_reg(CC1200_SYNC1, CC1200_SYNC_WORD1);
        cc1200_write_reg(CC1200_SYNC0, CC1200_SYNC_WORD0);
}

static void
radio_new_syncword(void)
{
        cc1200_write_reg(CC1200_SYNC3, CC1200_SYNC_BYTE3);
        cc1200_write_reg(CC1200_SYNC2, CC1200_SYNC_BYTE2);
        cc1200_write_reg(CC1200_SYNC1, CC1200_SYNC_BYTE1);
        cc1200_write_reg(CC1200_SYNC0, CC1200_SYNC_BYTE0);
}

void
radio_syncword_command(int argc, char **argv)
{
        if (argc == 1) {
                printf("Sync:\n");
                for (int i = 0; i < 4; i++) {
                        printf(" [%d] = 0x%02x\n", 3 - i, cc1200_read_reg(CC1200_SYNC3 + i));
                }
                return;
        }

        if (strcmp(argv[1], "old") == 0) {
                radio_old_syncword();
        } else if (strcmp(argv[1], "new") == 0) {
                radio_new_syncword();
        } else {
                printf("unknown syncword argument: %s\n", argv[1]);
        }
}
#endif

#ifndef __CC1200_H__
#define __CC1200_H__

enum {
        CC1200_IOCFG3           = 0x0000,
        CC1200_IOCFG2           = 0x0001,
        CC1200_IOCFG1           = 0x0002,
        CC1200_IOCFG0           = 0x0003,
        CC1200_SYNC3            = 0x0004,
        CC1200_SYNC2            = 0x0005,
        CC1200_SYNC1            = 0x0006,
        CC1200_SYNC0            = 0x0007,
        CC1200_SYNC_CFG1        = 0x0008,
        CC1200_SYNC_CFG0        = 0x0009,
        CC1200_DEVIATION_M      = 0x000A,
        CC1200_MODCFG_DEV_E     = 0x000B,
        CC1200_DCFILT_CFG       = 0x000C,
        CC1200_PREAMBLE_CFG1    = 0x000D,
        CC1200_PREAMBLE_CFG0    = 0x000E,
        CC1200_IQIC             = 0x000F,
        CC1200_CHAN_BW          = 0x0010,
        CC1200_MDMCFG1          = 0x0011,
        CC1200_MDMCFG0          = 0x0012,
        CC1200_SYMBOL_RATE2     = 0x0013,
        CC1200_SYMBOL_RATE1     = 0x0014,
        CC1200_SYMBOL_RATE0     = 0x0015,
        CC1200_AGC_REF          = 0x0016,
        CC1200_AGC_CS_THR       = 0x0017,
        CC1200_AGC_GAIN_ADJUST  = 0x0018,
        CC1200_AGC_CFG3         = 0x0019,
        CC1200_AGC_CFG2         = 0x001A,
        CC1200_AGC_CFG1         = 0x001B,
        CC1200_AGC_CFG0         = 0x001C,
        CC1200_FIFO_CFG         = 0x001D,
        CC1200_DEV_ADDR         = 0x001E,
        CC1200_SETTLING_CFG     = 0x001F,
        CC1200_FS_CFG           = 0x0020,
        CC1200_WOR_CFG1         = 0x0021,
        CC1200_WOR_CFG0         = 0x0022,
        CC1200_WOR_EVENT0_MSB   = 0x0023,
        CC1200_WOR_EVENT0_LSB   = 0x0024,
        CC1200_RXDCM_TIME       = 0x0025,
        CC1200_PKT_CFG2         = 0x0026,
        CC1200_PKT_CFG1         = 0x0027,
        CC1200_PKT_CFG0         = 0x0028,
        CC1200_RFEND_CFG1       = 0x0029,
        CC1200_RFEND_CFG0       = 0x002A,
        CC1200_PA_CFG1          = 0x002B,
        CC1200_PA_CFG0          = 0x002C,
        CC1200_ASK_CFG          = 0x002D,
        CC1200_PKT_LEN          = 0x002E,
};

enum {
        /* Extended Configuration Registers */
        CC1200_IF_MIX_CFG       = 0x2F00,
        CC1200_FREQOFF_CFG      = 0x2F01,
        CC1200_TOC_CFG          = 0x2F02,
        CC1200_MARC_SPARE       = 0x2F03,
        CC1200_ECG_CFG          = 0x2F04,
        CC1200_MDMCFG2          = 0x2F05,
        CC1200_EXT_CTRL         = 0x2F06,
        CC1200_RCCAL_FINE       = 0x2F07,
        CC1200_RCCAL_COARSE     = 0x2F08,
        CC1200_RCCAL_OFFSET     = 0x2F09,
        CC1200_FREQOFF1         = 0x2F0A,
        CC1200_FREQOFF0         = 0x2F0B,
        CC1200_FREQ2            = 0x2F0C,
        CC1200_FREQ1            = 0x2F0D,
        CC1200_FREQ0            = 0x2F0E,
        CC1200_IF_ADC2          = 0x2F0F,
        CC1200_IF_ADC1          = 0x2F10,
        CC1200_IF_ADC0          = 0x2F11,
        CC1200_FS_DIG1          = 0x2F12,
        CC1200_FS_DIG0          = 0x2F13,
        CC1200_FS_CAL3          = 0x2F14,
        CC1200_FS_CAL2          = 0x2F15,
        CC1200_FS_CAL1          = 0x2F16,
        CC1200_FS_CAL0          = 0x2F17,
        CC1200_FS_CHP           = 0x2F18,
        CC1200_FS_DIVTWO        = 0x2F19,
        CC1200_FS_DSM1          = 0x2F1A,
        CC1200_FS_DSM0          = 0x2F1B,
        CC1200_FS_DVC1          = 0x2F1C,
        CC1200_FS_DVC0          = 0x2F1D,
        CC1200_FS_LBI           = 0x2F1E,
        CC1200_FS_PFD           = 0x2F1F,
        CC1200_FS_PRE           = 0x2F20,
        CC1200_FS_REG_DIV_CML   = 0x2F21,
        CC1200_FS_SPARE         = 0x2F22,
        CC1200_FS_VCO4          = 0x2F23,
        CC1200_FS_VCO3          = 0x2F24,
        CC1200_FS_VCO2          = 0x2F25,
        CC1200_FS_VCO1          = 0x2F26,
        CC1200_FS_VCO0          = 0x2F27,
        CC1200_GBIAS6           = 0x2F28,
        CC1200_GBIAS5           = 0x2F29,
        CC1200_GBIAS4           = 0x2F2A,
        CC1200_GBIAS3           = 0x2F2B,
        CC1200_GBIAS2           = 0x2F2C,
        CC1200_GBIAS1           = 0x2F2D,
        CC1200_GBIAS0           = 0x2F2E,
        CC1200_IFAMP            = 0x2F2F,
        CC1200_LNA              = 0x2F30,
        CC1200_RXMIX            = 0x2F31,
        CC1200_XOSC5            = 0x2F32,
        CC1200_XOSC4            = 0x2F33,
        CC1200_XOSC3            = 0x2F34,
        CC1200_XOSC2            = 0x2F35,
        CC1200_XOSC1            = 0x2F36,
        CC1200_XOSC0            = 0x2F37,
        CC1200_ANALOG_SPARE     = 0x2F38,
        CC1200_PA_CFG3          = 0x2F39,
        CC1200_IRQ0M            = 0x2F3F,
        CC1200_IRQ0F            = 0x2F40,

        /* Status Registers */
        CC1200_WOR_TIME1        = 0x2F64,
        CC1200_WOR_TIME0        = 0x2F65,
        CC1200_WOR_CAPTURE1     = 0x2F66,
        CC1200_WOR_CAPTURE0     = 0x2F67,
        CC1200_BIST             = 0x2F68,
        CC1200_DCFILTOFFSET_I1  = 0x2F69,
        CC1200_DCFILTOFFSET_I0  = 0x2F6A,
        CC1200_DCFILTOFFSET_Q1  = 0x2F6B,
        CC1200_DCFILTOFFSET_Q0  = 0x2F6C,
        CC1200_IQIE_I1          = 0x2F6D,
        CC1200_IQIE_I0          = 0x2F6E,
        CC1200_IQIE_Q1          = 0x2F6F,
        CC1200_IQIE_Q0          = 0x2F70,
        CC1200_RSSI1            = 0x2F71,
        CC1200_RSSI0            = 0x2F72,
        CC1200_MARCSTATE        = 0x2F73,
        CC1200_LQI_VAL          = 0x2F74,
        CC1200_PQT_SYNC_ERR     = 0x2F75,
        CC1200_DEM_STATUS       = 0x2F76,
        CC1200_FREQOFF_EST1     = 0x2F77,
        CC1200_FREQOFF_EST0     = 0x2F78,
        CC1200_AGC_GAIN3        = 0x2F79,
        CC1200_AGC_GAIN2        = 0x2F7A,
        CC1200_AGC_GAIN1        = 0x2F7B,
        CC1200_AGC_GAIN0        = 0x2F7C,
        CC1200_CFM_RX_DATA_OUT  = 0x2F7D,
        CC1200_CFM_TX_DATA_IN   = 0x2F7E,
        CC1200_ASK_SOFT_RX_DATA = 0x2F7F,
        CC1200_RNDGEN           = 0x2F80,
        CC1200_MAGN2            = 0x2F81,
        CC1200_MAGN1            = 0x2F82,
        CC1200_MAGN0            = 0x2F83,
        CC1200_ANG1             = 0x2F84,
        CC1200_ANG0             = 0x2F85,
        CC1200_CHFILT_I2        = 0x2F86,
        CC1200_CHFILT_I1        = 0x2F87,
        CC1200_CHFILT_I0        = 0x2F88,
        CC1200_CHFILT_Q2        = 0x2F89,
        CC1200_CHFILT_Q1        = 0x2F8A,
        CC1200_CHFILT_Q0        = 0x2F8B,
        CC1200_GPIO_STATUS      = 0x2F8C,
        CC1200_FSCAL_CTRL       = 0x2F8D,
        CC1200_PHASE_ADJUST     = 0x2F8E,
        CC1200_PARTNUMBER       = 0x2F8F,
        CC1200_PARTVERSION      = 0x2F90,
        CC1200_SERIAL_STATUS    = 0x2F91,
        CC1200_MODEM_STATUS1    = 0x2F92,
        CC1200_MODEM_STATUS0    = 0x2F93,
        CC1200_MARC_STATUS1     = 0x2F94,
        CC1200_MARC_STATUS0     = 0x2F95,
        CC1200_PA_IFAMP_TEST    = 0x2F96,
        CC1200_FSRF_TEST        = 0x2F97,
        CC1200_PRE_TEST         = 0x2F98,
        CC1200_PRE_OVR          = 0x2F99,
        CC1200_ADC_TEST         = 0x2F9A,
        CC1200_DVC_TEST         = 0x2F9B,
        CC1200_ATEST            = 0x2F9C,
        CC1200_ATEST_LVDS       = 0x2F9D,
        CC1200_ATEST_MODE       = 0x2F9E,
        CC1200_XOSC_TEST1       = 0x2F9F,
        CC1200_XOSC_TEST0       = 0x2FA0,
        CC1200_AES              = 0x2FA1,
        CC1200_MDM_TEST         = 0x2FA2,

        CC1200_RXFIRST          = 0x2FD2,
        CC1200_TXFIRST          = 0x2FD3,
        CC1200_RXLAST           = 0x2FD4,
        CC1200_TXLAST           = 0x2FD5,
        CC1200_NUM_TXBYTES      = 0x2FD6, /* Number of bytes in TXFIFO */
        CC1200_NUM_RXBYTES      = 0x2FD7, /* Number of bytes in RXFIFO */
        CC1200_FIFO_NUM_TXBYTES = 0x2FD8,
        CC1200_FIFO_NUM_RXBYTES = 0x2FD9,
        CC1200_RXFIFO_PRE_BUF   = 0x2FDA,
        /* AES Workspace */
        /* AES Key */
        CC1200_AES_KEY          = 0x2FE0, /* AES_KEY - Address for AES key input */
        CC1200_AES_KEY15        = 0x2FE0,
        CC1200_AES_KEY14        = 0x2FE1,
        CC1200_AES_KEY13        = 0x2FE2,
        CC1200_AES_KEY12        = 0x2FE3,
        CC1200_AES_KEY11        = 0x2FE4,
        CC1200_AES_KEY10        = 0x2FE5,
        CC1200_AES_KEY9         = 0x2FE6,
        CC1200_AES_KEY8         = 0x2FE7,
        CC1200_AES_KEY7         = 0x2FE8,
        CC1200_AES_KEY6         = 0x2FE9,
        CC1200_AES_KEY5         = 0x2FE10,
        CC1200_AES_KEY4         = 0x2FE11,
        CC1200_AES_KEY3         = 0x2FE12,
        CC1200_AES_KEY2         = 0x2FE13,
        CC1200_AES_KEY1         = 0x2FE14,
        CC1200_AES_KEY0         = 0x2FE15,

        /* AES Buffer */
        CC1200_AES_BUFFER       = 0x2FF0, /* AES_BUFFER - Address for AES Buffer */
        CC1200_AES_BUFFER15     = 0x2FF0,
        CC1200_AES_BUFFER14     = 0x2FF1,
        CC1200_AES_BUFFER13     = 0x2FF2,
        CC1200_AES_BUFFER12     = 0x2FF3,
        CC1200_AES_BUFFER11     = 0x2FF4,
        CC1200_AES_BUFFER10     = 0x2FF5,
        CC1200_AES_BUFFER9      = 0x2FF6,
        CC1200_AES_BUFFER8      = 0x2FF7,
        CC1200_AES_BUFFER7      = 0x2FF8,
        CC1200_AES_BUFFER6      = 0x2FF9,
        CC1200_AES_BUFFER5      = 0x2FF10,
        CC1200_AES_BUFFER4      = 0x2FF11,
        CC1200_AES_BUFFER3      = 0x2FF12,
        CC1200_AES_BUFFER2      = 0x2FF13,
        CC1200_AES_BUFFER1      = 0x2FF14,
        CC1200_AES_BUFFER0      = 0x2FF15,
};

enum {

        /* DATA FIFO Access */
        CC1200_SINGLE_TXFIFO = 0x003F, /* TXFIFO - Single accecss to Transmit FIFO */
        CC1200_BURST_TXFIFO  = 0x007F, /* TXFIFO - Burst accecss to Transmit FIFO */
        CC1200_SINGLE_RXFIFO = 0x00BF, /* RXFIFO - Single accecss to Receive FIFO */
        CC1200_BURST_RXFIFO  = 0x00FF, /* RXFIFO - Busrrst ccecss to Receive FIFO */

        CC1200_READ_REG      = 0x80,
        CC1200_BURST         = 0x40,
};

enum {
        CC1200_STATE_IDLE         = 0x00,
        CC1200_STATE_RX           = 0x10,
        CC1200_STATE_TX           = 0x20,
        CC1200_STATE_FSTXON       = 0x30,
        CC1200_STATE_CALIBRATE    = 0x40,
        CC1200_STATE_SETTLING     = 0x50,
        CC1200_STATE_RXFIFO_ERROR = 0x60,
        CC1200_STATE_TXFIFO_ERROR = 0x70,
};

enum {
        CC1200_CARRIER_SENSE_VALID = (1 << 1),
        CC1200_CARRIER_SENSE       = (1 << 2),
};
#endif

/* mbed Microcontroller Library
 * Copyright (c) 2006-2013 ARM Limited
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/** @{

   @ingroup    devices
   @file

   @brief  analog read support
*/

#include "mbed_assert.h"
#include "analogin_api.h"

#include "pelagic-types.h"
#include "cmsis.h"
#include "cmsis_os.h"           // TODO Ian: check - see pelagic.h includes
#include "pinmap.h"
#include "clk_freqs.h"
#include "PeripheralPins.h"

#define MAX_FADC            6000000
#define CHANNELS_A_SHIFT     5

bool analogin_initted = false;
osMutexId analogin_mutex;
osMutexDef(analogin_mutex);

/**
    @brief  initialize an analog port
    @param[out]  obj     analogin_t structure to initialize
    @param[in]   pin     pin name to setup as an ADC port

    @note   The port is setup first, and then the ADC itself is setup
            if it has not been so before.
*/

void
analogin_init(analogin_t *obj, PinName pin)
{

        obj->adc = (ADCName)pinmap_peripheral(pin, PinMap_ADC);
        MBED_ASSERT(obj->adc != (ADCName)NC);

        SIM->SCGC6 |= SIM_SCGC6_ADC0_MASK;

        uint32_t port = (uint32_t)pin >> PORT_SHIFT;
        SIM->SCGC5 |= 1 << (SIM_SCGC5_PORTA_SHIFT + port);

        pinmap_pinout(pin, PinMap_ADC);

        if (analogin_initted)
                return;

        analogin_mutex = osMutexCreate(osMutex(analogin_mutex));

        // bus clk
        uint32_t PCLK = bus_frequency();
        uint32_t clkdiv;
        for (clkdiv = 0; clkdiv < 4; clkdiv++) {
                if ((PCLK >> clkdiv) <= MAX_FADC)
                        break;
        }
        if (clkdiv == 4)                    //Set max div
                clkdiv = 0x7;

        ADC0->CFG1 = ADC_CFG1_ADLPC_MASK            // Low-Power Configuration
                     | ADC_CFG1_ADIV(clkdiv & 0x3)    // Clock Divide Select: (Input Clock)/8
                     | ADC_CFG1_ADLSMP_MASK           // Long Sample Time
                     | ADC_CFG1_MODE(3)               // (16)bits Resolution
                     | ADC_CFG1_ADICLK(clkdiv >> 2);  // Input Clock: (Bus Clock)/2

        ADC0->SC2 = ADC_SC2_REFSEL(0);      // Default Voltage Reference

        ADC0->SC3 = ADC_SC3_AVGE_MASK       // Hardware Average Enable
                    | ADC_SC3_AVGS(0);        // 4 Samples Averaged

        analogin_initted = true;
}

/**
    @brief  read a analog value from port
    @param[in]  obj port to read from
    @return a 16-bit analog value
*/

uint16_t
analogin_read_u16(analogin_t *obj)
{
        uint16_t value;

        // Only one thread may use the ADC at any given time.
        osMutexWait(analogin_mutex, osWaitForever);

        ADC0->CFG2 = ((obj->adc & (1 << CHANNELS_A_SHIFT)) ? 0 : ADC_CFG2_MUXSEL_MASK)            // ADxxb or ADxxa channels
                     | ADC_CFG2_ADACKEN_MASK  // Asynchronous Clock Output Enable
                     | ADC_CFG2_ADHSC_MASK    // High-Speed Configuration
                     | ADC_CFG2_ADLSTS(0);    // Long Sample Time Select

        // start conversion
        ADC0->SC1[0] = ADC_SC1_ADCH(obj->adc & ~(1 << CHANNELS_A_SHIFT));

        // Wait Conversion Complete
        while ((ADC0->SC1[0] & ADC_SC1_COCO_MASK) != ADC_SC1_COCO_MASK);

        // Return value
        value = ADC0->R[0];

        osMutexRelease(analogin_mutex);

        return value;
}

/** @} */

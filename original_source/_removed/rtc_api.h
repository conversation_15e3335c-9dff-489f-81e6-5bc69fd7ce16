/* mbed Microcontroller Library
 * Copyright (c) 2006-2013 ARM Limited
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#ifndef MBED_RTC_API_H
#define MBED_RTC_API_H

#include "device.h"

enum {
        RTC_STATUS_MASK         = 0x3,
        RTC_STATUS_UNKNOWN      = 0,    // state of XCAL not known
        RTC_STATUS_USE_XCAL     = 1,    // XCAL present
        RTC_STATUS_USE_SOFTWARE = 2,    // XCAL not present

        RTC_STATUS_HAVE_TIME    = 0x80,
};

void rtc_init(void);

uint8_t rtc_status(void);
uint32_t rtc_read(void);
void rtc_write(uint32_t time);
void rtc_save(uint32_t adjust);
void rtc_reset_time();

void rtc_tick(void const *arg);

uint32_t clock_read(void);

extern volatile uint32_t rtc_clock, epoch_clock;
#endif

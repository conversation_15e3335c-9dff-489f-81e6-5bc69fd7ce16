#include <stdint.h>
#include "kinetis.h"
#include "system_file.h"
#include "mcu_sleep.h"

#if defined(TARGET_DEVBOARD)
#define    SYSCLOCK_48MHZ    (47972352u)
#elif defined(TARGET_PLACO)
#define    SYSCLOCK_20MHZ    (20971520u)
#define    SYSCLOCK_48MHZ    (47972352u)
#endif

/* ----------------------------------------------------------------------------
   -- Core clock
   ---------------------------------------------------------------------------- */

/* ----------------------------------------------------------------------------
   -- SystemInit()
   ---------------------------------------------------------------------------- */

#if defined(TARGET_PLACO)

extern void device_pin_setup();

void
system_placo_clock()
{
        SIM->CLKDIV1 = (SIM_CLKDIV1_OUTDIV1(0x00) | SIM_CLKDIV1_OUTDIV4(0x03)); /* Set the system prescalers to safe value */
        /* SIM_SCGC5: PORTA=1 */
        SIM->SCGC5 |= SIM_SCGC5_PORTA_MASK;   /* Enable clock gate for ports to enable pin routing */

        if ((PMC->REGSC & PMC_REGSC_ACKISO_MASK) != 0x0U) {
#ifdef TARGET_VMS
                /* PMC_REGSC: ACKISO=1 */
                device_pin_setup();
#endif
                PMC->REGSC |= PMC_REGSC_ACKISO_MASK; /* Release IO pads after wakeup from VLLS mode. */
        }

#ifdef TARGET_VMS
        //
        // go back to sleep if waking up from VLLS & there's still
        // more sleeping to do. This is for boards with no or a malfunctioning XCAL
        // and wanting to sleep more than 18 hours which the upper limit of the LPTMR.
        //
        if ((RCM->SRS0 & RCM_SRS0_WAKEUP_MASK) != 0
            && SYS_REG_FILE->magic == SYS_REG_FILE_MAGIC
            && SYS_REG_FILE->sleep_remaining) {
                mcu_sleep(SYS_REG_FILE->sleep_remaining);
        }
#endif

#if OS_CLOCK == SYSCLOCK_20MHZ
        /* SIM_CLKDIV1: OUTDIV1=0,OUTDIV4=0 */
        SIM->CLKDIV1 = (SIM_CLKDIV1_OUTDIV1(0x00) | SIM_CLKDIV1_OUTDIV4(0x00)); /* Update system prescalers */
#elif OS_CLOCK == SYSCLOCK_48MHZ
        /* SIM_CLKDIV1: OUTDIV1=0,OUTDIV4=1 */
        SIM->CLKDIV1 = (SIM_CLKDIV1_OUTDIV1(0x00) | SIM_CLKDIV1_OUTDIV4(0x01)); /* Update system prescalers */
#else
#error "Unknown OS_CLOCK Value"
#endif

        /* SIM_SOPT2: PLLFLLSEL=0 */
        SIM->SOPT2 &= (uint32_t) ~(uint32_t)(SIM_SOPT2_PLLFLLSEL_MASK); /* Select FLL as a clock source for various peripherals */
        /* SIM_SOPT1: OSC32KSEL=3 */
        SIM->SOPT1 |= SIM_SOPT1_OSC32KSEL(0x03); /* LPO 1kHz oscillator drives 32 kHz clock for various peripherals */
        /* SIM_SOPT2: TPMSRC=1 */
        SIM->SOPT2 = (uint32_t)((SIM->SOPT2 & (uint32_t) ~(uint32_t)(
                                         SIM_SOPT2_TPMSRC(0x02)
                                 )) | (uint32_t)(
                                        SIM_SOPT2_TPMSRC(0x01)
                                ));      /* Set the TPM clock */
        /* MCG_SC: FCRDIV=0 */
        MCG->SC &= (uint8_t) ~(uint8_t)(MCG_SC_FCRDIV(0x07));
        /* Switch to FEI Mode */
        /* MCG_C1: CLKS=0,FRDIV=0,IREFS=1,IRCLKEN=1,IREFSTEN=0 */
        MCG->C1 = MCG_C1_CLKS(0x00) |
                  MCG_C1_FRDIV(0x00) |
                  MCG_C1_IREFS_MASK |
                  MCG_C1_IRCLKEN_MASK;
        /* MCG_C2: LOCRE0=0,RANGE0=0,HGO0=0,EREFS0=0,LP=0,IRCS=1 */
        MCG->C2 = (uint8_t)((MCG->C2 & (uint8_t) ~(uint8_t)(
                                     MCG_C2_LOCRE0_MASK |
                                     MCG_C2_RANGE0(0x03) |
                                     MCG_C2_HGO0_MASK |
                                     MCG_C2_EREFS0_MASK |
                                     MCG_C2_LP_MASK
                             )) | (uint8_t)(
                                    MCG_C2_IRCS_MASK
                            ));
#if OS_CLOCK == SYSCLOCK_20MHZ
        /* MCG_C4: DMX32=0,DRST_DRS=0 */
        MCG->C4 &= (uint8_t) ~(uint8_t)((MCG_C4_DMX32_MASK | MCG_C4_DRST_DRS(0x03)));
#elif OS_CLOCK == SYSCLOCK_48MHZ
        /* MCG_C4: DMX32=1,DRST_DRS=1 */
        MCG->C4 = (uint8_t)((MCG->C4 & (uint8_t) ~(uint8_t)(
                                     MCG_C4_DRST_DRS(0x02)
                             )) | (uint8_t)(
                                    MCG_C4_DMX32_MASK |
                                    MCG_C4_DRST_DRS(0x01)
                            ));
#endif
        /* OSC0_CR: ERCLKEN=0,EREFSTEN=0,SC2P=0,SC4P=0,SC8P=0,SC16P=0 */
        OSC0->CR = 0x00U;
        /* MCG_C5: ??=0,PLLCLKEN0=0,PLLSTEN0=0,PRDIV0=0 */
        MCG->C5 = MCG_C5_PRDIV0(0x00);
        /* MCG_C6: LOLIE0=0,PLLS=0,CME0=0,VDIV0=0 */
        MCG->C6 = MCG_C6_VDIV0(0x00);
        while((MCG->S & MCG_S_IREFST_MASK) == 0x00U) { /* Check that the source of the FLL reference clock is the internal reference clock. */
        }
        while((MCG->S & 0x0CU) != 0x00U) {    /* Wait until output of the FLL is selected */
        }

}
#endif

extern uint32_t __bss_start__;
extern uint32_t __bss_end__;

void
SystemInit (void)
{
        /* Disable the WDOG module */
        /* SIM_COPC: COPT=0,COPCLKS=0,COPW=0 */
        SIM->COPC = (uint32_t)0x00u;
        SMC->PMPROT = SMC_PMPROT_AVLLS_MASK | SMC_PMPROT_ALLS_MASK | SMC_PMPROT_AVLP_MASK;
        SCB->SCR = 0; // Normal sleep

#if defined(TARGET_DEVBOARD)
        /* System clock initialization */
        /* SIM_CLKDIV1: OUTDIV1=0,??=0,??=0,??=0,??=0,??=0,??=0,??=0,??=0,??=0,OUTDIV4=3,??=0,??=0,??=0,??=0,??=0,??=0,??=0,??=0,??=0,??=0,??=0,??=0,??=0,??=0,??=0,??=0 */
        SIM->CLKDIV1 = (SIM_CLKDIV1_OUTDIV1(0x00) | SIM_CLKDIV1_OUTDIV4(0x03)); /* Set the system prescalers to safe value */
        /* SIM_SCGC5: PORTC=1,PORTA=1 */
        SIM->SCGC5 |= (SIM_SCGC5_PORTC_MASK | SIM_SCGC5_PORTA_MASK); /* Enable clock gate for ports to enable pin routing */
        if ((PMC->REGSC & PMC_REGSC_ACKISO_MASK) != 0x0U) {
                /* PMC_REGSC: ACKISO=1 */
                PMC->REGSC |= PMC_REGSC_ACKISO_MASK; /* Release IO pads after wakeup from VLLS mode. */
        }
        /* SIM_CLKDIV1: OUTDIV1=0,??=0,??=0,??=0,??=0,??=0,??=0,??=0,??=0,??=0,OUTDIV4=1,??=0,??=0,??=0,??=0,??=0,??=0,??=0,??=0,??=0,??=0,??=0,??=0,??=0,??=0,??=0,??=0 */
        SIM->CLKDIV1 = (SIM_CLKDIV1_OUTDIV1(0x00) | SIM_CLKDIV1_OUTDIV4(0x01)); /* Update system prescalers */
        /* SIM_SOPT2: PLLFLLSEL=0 */
        SIM->SOPT2 &= (uint32_t) ~(uint32_t)(SIM_SOPT2_PLLFLLSEL_MASK); /* Select FLL as a clock source for various peripherals */
        /* SIM_SOPT1: OSC32KSEL=2 */
        SIM->SOPT1 &= ~SIM_SOPT1_OSC32KSEL_MASK;
        SIM->SOPT1 |= SIM_SOPT1_OSC32KSEL(0x02); /* System oscillator drives 32 kHz clock for various peripherals */

        /* SIM_SOPT2: TPMSRC=1 */
        SIM->SOPT2 = (uint32_t)((SIM->SOPT2 & (uint32_t) ~(uint32_t)(
                                         SIM_SOPT2_TPMSRC(0x02)
                                 )) | (uint32_t)(
                                        SIM_SOPT2_TPMSRC(0x01)
                                )); /* Set the TPM clock */
        /* PORTA_PCR18: ISF=0,MUX=0 */
        PORTA->PCR[18] &= (uint32_t) ~(uint32_t)((PORT_PCR_ISF_MASK | PORT_PCR_MUX(0x07)));
        /* PORTA_PCR19: ISF=0,MUX=0 */
        PORTA->PCR[19] &= (uint32_t) ~(uint32_t)((PORT_PCR_ISF_MASK | PORT_PCR_MUX(0x07)));
        /* MCG_SC: FCRDIV=0 */
        MCG->SC &= (uint8_t) ~(uint8_t)(MCG_SC_FCRDIV(0x07));
        /* Switch to FEI Mode */
        /* MCG_C1: CLKS=0,FRDIV=0,IREFS=1,IRCLKEN=1,IREFSTEN=0 */
        MCG->C1 = MCG_C1_CLKS(0x00) |
                  MCG_C1_FRDIV(0x00) |
                  MCG_C1_IREFS_MASK |
                  MCG_C1_IRCLKEN_MASK;
        /* MCG_C2: LOCRE0=0,RANGE0=2,HGO0=0,EREFS0=1,LP=0,IRCS=1 */
        MCG->C2 = (uint8_t)((MCG->C2 & (uint8_t) ~(uint8_t)(
                                     MCG_C2_LOCRE0_MASK |
                                     MCG_C2_RANGE0(0x01) |
                                     MCG_C2_HGO0_MASK |
                                     MCG_C2_LP_MASK
                             )) | (uint8_t)(
                                    MCG_C2_RANGE0(0x02) |
                                    MCG_C2_EREFS0_MASK |
                                    MCG_C2_IRCS_MASK
                            ));
        /* MCG_C4: DMX32=1,DRST_DRS=1 */
        MCG->C4 = (uint8_t)((MCG->C4 & (uint8_t) ~(uint8_t)(
                                     MCG_C4_DRST_DRS(0x02)
                             )) | (uint8_t)(
                                    MCG_C4_DMX32_MASK |
                                    MCG_C4_DRST_DRS(0x01)
                            ));
        /* OSC0_CR: ERCLKEN=1,??=0,EREFSTEN=0,??=0,SC2P=0,SC4P=0,SC8P=0,SC16P=0 */
        OSC0->CR = OSC_CR_ERCLKEN_MASK;
        /* MCG_C5: ??=0,PLLCLKEN0=0,PLLSTEN0=0,PRDIV0=0 */
        MCG->C5 = MCG_C5_PRDIV0(0x00);
        /* MCG_C6: LOLIE0=0,PLLS=0,CME0=0,VDIV0=0 */
        MCG->C6 = MCG_C6_VDIV0(0x00);
        while((MCG->S & MCG_S_IREFST_MASK) == 0x00U) { /* Check that the source of the FLL reference clock is the internal reference clock. */
        }
        while((MCG->S & 0x0CU) != 0x00U) {    /* Wait until output of the FLL is selected */
        }
        /* Initialization of the RTC_CLKIN pin */
        /* PORTC_PCR1: ISF=0,MUX=1 */
        PORTC->PCR[1] = (uint32_t)((PORTC->PCR[1] & (uint32_t) ~(uint32_t)(
                                            PORT_PCR_ISF_MASK |
                                            PORT_PCR_MUX(0x06)
                                    )) | (uint32_t)(
                                           PORT_PCR_MUX(0x01)
                                   ));

#elif defined(TARGET_PLACO)
        system_placo_clock();

        /* PORTA_PCR4: ISF=0,MUX=7 */
        PORTA->PCR[4] = (uint32_t)((PORTA->PCR[4] & (uint32_t) ~(uint32_t)(
                                            PORT_PCR_ISF_MASK
                                    )) | (uint32_t)(
                                           PORT_PCR_MUX(0x07)
                                   ));
        /* Initialization of the RCM module */
        /* RCM_RPFW: RSTFLTSEL=0 */
        RCM->RPFW &= (uint8_t) ~(uint8_t)(RCM_RPFW_RSTFLTSEL(0x1F));
        /* RCM_RPFC: RSTFLTSS=0,RSTFLTSRW=0 */
        RCM->RPFC &= (uint8_t) ~(uint8_t)(
                             RCM_RPFC_RSTFLTSS_MASK |
                             RCM_RPFC_RSTFLTSRW(0x03)
                     );
        /* Initialization of the FTFL_FlashConfig module */
        /* Initialization of the PMC module */
        /* PMC_LVDSC1: LVDACK=1,LVDIE=0,LVDRE=1,LVDV=0 */
        PMC->LVDSC1 = (uint8_t)((PMC->LVDSC1 & (uint8_t) ~(uint8_t)(
                                         PMC_LVDSC1_LVDIE_MASK |
                                         PMC_LVDSC1_LVDV(0x03)
                                 )) | (uint8_t)(
                                        PMC_LVDSC1_LVDACK_MASK |
                                        PMC_LVDSC1_LVDRE_MASK
                                ));
        /* PMC_LVDSC2: LVWACK=1,LVWIE=0,LVWV=0 */
        PMC->LVDSC2 = (uint8_t)((PMC->LVDSC2 & (uint8_t) ~(uint8_t)(
                                         PMC_LVDSC2_LVWIE_MASK |
                                         PMC_LVDSC2_LVWV(0x03)
                                 )) | (uint8_t)(
                                        PMC_LVDSC2_LVWACK_MASK
                                ));
        /* PMC_REGSC: BGEN=0,ACKISO=0,BGBE=0 */
        PMC->REGSC &= (uint8_t) ~(uint8_t)(
                              PMC_REGSC_BGEN_MASK |
                              PMC_REGSC_ACKISO_MASK |
                              PMC_REGSC_BGBE_MASK
                      );
        /* SMC_PMPROT: ??=0,AVLP=0,ALLS=0,AVLLS=0 */
        SMC->PMPROT = 0x00U;                /* Setup Power mode protection register */
        /* Common initialization of the CPU registers */
        /* PORTA_PCR20: ISF=0,MUX=7 */
        PORTA->PCR[20] = (uint32_t)((PORTA->PCR[20] & (uint32_t) ~(uint32_t)(
                                             PORT_PCR_ISF_MASK
                                     )) | (uint32_t)(
                                            PORT_PCR_MUX(0x07)
                                    ));
#else
#error No target defined
#endif
}

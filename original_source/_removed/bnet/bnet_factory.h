#ifndef __BNET_FACTORY_H__
#define __BNET_FACTORY_H__

enum {
        FACTORY_MODE_PCB      = 0,
        FACTORY_MODE_CASE     = 1,
        FACTORY_MODE_SHIP     = 2,
};

typedef struct
__attribute__ ((packed))
{
        uint8_t     type;
        board_uid_t from_uid;
        uint8_t     deploy_mode;
}
bnet_ft_start_t;

typedef struct
__attribute__ ((packed))
{
        uint8_t     type;
        imei_t      to_imei;
        board_uid_t from_uid;
        uint8_t     deploy_mode;
}
bnet_ft_target_t;

typedef struct
__attribute__ ((packed))
{
        uint8_t     type;
        board_uid_t from_uid;
        board_uid_t to_uid;
}
bnet_ft_begin_t;

typedef struct
__attribute__ ((packed))
{
        uint8_t         type;
        board_uid_t     from_uid;
        uint8_t         sequence;
        uint8_t         size;
        char            data[];
}
bnet_ft_update_t;

typedef struct
__attribute__ ((packed))
{
        uint8_t         type;
        board_uid_t     from_uid;
        uint8_t         result[2];
        imei_t          imei;
}
bnet_ft_finish_t;

typedef struct
__attribute__ ((packed))
{
        uint8_t         type;
        board_uid_t     to_uid;
        uint8_t         flags;
}
bnet_ft_ack_t;

#ifdef FACTORY_TEST_FIELD
void bnet_factory_timeout();
void bnet_factory_test_start();
void bnet_process_factory_begin(void *buffer, uint8_t bytes);
void bnet_process_factory_update(void *buffer, uint8_t bytes);
void bnet_process_factory_finish(void *buffer, uint8_t bytes);
#endif

#endif

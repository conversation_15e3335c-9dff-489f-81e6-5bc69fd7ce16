/**@{

    @ingroup    bnet
    @file
    @brief      BNET common routines and variables
*/

#include "pelagic.h"
#include "bnet.h"
#include "console.h"
#include "signals.h"
#include "firmware.h"
#include "radio.h"

volatile bool bnet_packet_watch;    ///< Display packets as received (for debugging)

uint8_t bnet_receive_buffer[BNET_PACKET_SIZE], bnet_send_buffer[BNET_PACKET_SIZE];

/**
    @brief  randomly delay - prevents transmissions collisions
*/

void
bnet_delay()
{
        uint32_t ms = (10 * (rand() & 0xf));

        if (ms == 0)
                return;

        osDelay(ms);
}

/** @} */

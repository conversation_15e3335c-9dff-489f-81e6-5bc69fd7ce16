/** @{

    @ingroup    bnet
    @file
    @brief      Console Send Packet support routines
*/

#include "pelagic.h"
#include "bnet.h"
#include "bnet_console.h"
#include "signals.h"
#include "radio.h"

/**
    @brief  send queued console data
    @param[in]  retry   true if this is a retry of a non-acknowledged packet
*/

void
bnet_console_send_data(bool retry)
{
        uint32_t bytes, count = bnet_console_tx_count;
        bnet_console_send_t *pkt = (bnet_console_send_t *) bnet_send_buffer;

        if (count == 0 || bnet_console_opened == false) {
                return;
        }

        if (retry == false)
                bnet_console_send_seq++;

        pkt->type     = BNET_TYPE_CONSOLE;
        pkt->op       = BNET_CONSOLE_SEND;
        pkt->sequence = bnet_console_send_seq;

        uid_set_me(pkt->from_uid);
        uid_copy(pkt->to_uid, bnet_console_uid);

        bnet_console_send_busy = true;

        bnet_console_lock();
        if (retry)
                bytes = bnet_console_send_size;
        else {
                bytes = count > BNET_CONSOLE_DATA_SIZE ? BNET_CONSOLE_DATA_SIZE : count;
                bnet_console_send_size = bytes;
        }

        pkt->length = bytes;
        for (int i = 0, out = bnet_console_tx_out; i < bytes; i++) {
                pkt->data[i] = bnet_console_tx_data[out];
                out = (out + 1) % BNET_CONSOLE_TX_SIZE;
        }
        bnet_console_unlock();

        radio_send(pkt, sizeof(bnet_console_send_t) + bytes);
}


/**
    @brief  check to see if the packet send has not been ack'ed, resend the packet
            or close the connection if attempts have been exceeded.
*/

void
bnet_console_send_timeout()
{
        if (bnet_console_send_busy == false || bnet_console_opened == false)
                return;

        if (bnet_console_send_retries >= BNET_CONSOLE_RETRIES) {
                bnet_console_reset();

#ifdef TARGET_CONSOLE
                osSignalSet(main_tid, BNET_CONSOLE_SIGNAL_CLOSE);
#endif
                return;
        }

        //
        // Ignore the first retry timeout - it may happen right on a tick boundary
        // (e.x. packet sent right before a tick happened..)
        //

        if (bnet_console_send_retries++ == 0) {
                return;
        }

        bnet_console_send_data(true);
}

/**
    @brief  process a console send acknowledgement packet
*/

void
bnet_console_process_send_ack(void *packet, uint8_t bytes)
{
        bnet_console_op_t *pkt = packet;

        if (bnet_console_send_busy == false || bnet_console_opened == false || pkt->sequence != bnet_console_send_seq)
                return; // might be for a previous packet

        // Acknowledgement received!
        bnet_console_lock();
        if (bnet_console_send_size < bnet_console_tx_count)
                bnet_console_tx_count    -= bnet_console_send_size;
        else
                bnet_console_tx_count = 0;

        bnet_console_tx_out       = (bnet_console_tx_out + bnet_console_send_size) % BNET_CONSOLE_TX_SIZE;
        bnet_console_send_busy    = false;
        bnet_console_send_retries = 0;
        bnet_console_send_size    = 0;
        bnet_console_unlock();

        bnet_console_send_data(false);
}

/**
    @brief  start transmission if one is not already in progress
*/

void
bnet_console_send_signal()
{
        if (bnet_console_send_busy || bnet_console_opened == false)
                return;

        bnet_console_send_data(false);
}

/** @} */

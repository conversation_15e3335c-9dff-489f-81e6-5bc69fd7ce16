/** @{

    @ingroup    ft
    @file
    @brief      In field factory test support

    @note       This was written to place onto a VMS and have it run thru a
                factory test broadcast. For the Mexico October 2015 deployment
                two VMSes managed to slip thru the Sala Terrace assembly line
                and remaining in factory test mode. This code broadcasts a start
                factory test packet once a second.

                INSTALL A NEW IMAGE ONCE THE DEVICES ARE UNSTUCK.
*/

#include "pelagic.h"
#include "bnet.h"
#include "bnet_factory.h"
#include "radio.h"
#include "modem.h"
#include "signals.h"

bool bnet_factory_busy = false, bnet_factory_reported = false;
uint32_t bnet_factory_ttl = 0;
board_uid_t bnet_factory_target;
bool bnet_factory_coverup;

extern osThreadId modem_tid;

/**
    @brief  set a new TTL for the test connection
*/

void
bnet_factory_live()
{
        bnet_factory_ttl = clock_read() + 30;
}

/**
    @brief  check to see if the test connect has seen no response for a while, and
            cleanup the connection.
*/

void
bnet_factory_timeout()
{
        if (bnet_factory_busy == false)
                return;

        if (bnet_factory_ttl >= clock_read())
                return;

        bnet_factory_reported = 0;
        bnet_factory_busy = false;

        if (bnet_factory_coverup == false)
                EVENT_LOG2(EVT_SYS_NOTE, "ft note", "msg", EVT_STRCONST, "ft timeout", "uid", EVT_UID, bnet_factory_target);

        bnet_factory_coverup = false;
        uid_clear(bnet_factory_target);
}

/**
    @brief  send a start factory test message
*/

void
bnet_factory_test_start()
{
        bnet_ft_start_t *pkt = (bnet_ft_start_t *)bnet_send_buffer;

        if (bnet_factory_busy)
                return;

        pkt->type = BNET_TYPE_FACTORY_TEST_START;
        pkt->deploy_mode = 1;
        uid_set_me(pkt->from_uid);

        radio_send(pkt, sizeof(bnet_ft_start_t));
}

/**
    @brief  send a factory test acknowledgement packet
    @param[in]  type    command ack
    @param[in]  flags   optional flags to send
*/

void
bnet_factory_ack(uint8_t type, uint8_t flags)
{
        bnet_ft_ack_t *ack = (bnet_ft_ack_t *)bnet_send_buffer;

        ack->type = type;
        uid_copy(ack->to_uid, bnet_factory_target);
        ack->flags = flags;

        radio_send(ack, sizeof(bnet_ft_ack_t));
}

/**
    @brief  process a factory begin received packet
    @param[in]  buffer  packet
    @param[in]  bytes   packet size
*/

void
bnet_process_factory_begin(void *buffer, uint8_t bytes)
{
        bnet_ft_begin_t *pkt = buffer;

        if (uid_match_me(pkt->to_uid) == false)
                return;

        if (uid_match(pkt->from_uid, bnet_factory_target) == false) {
                if (bnet_factory_busy)
                        return;
        }

        uid_copy(bnet_factory_target, pkt->from_uid);
        bnet_factory_live();
        bnet_factory_busy = true;

        bnet_factory_ack(BNET_TYPE_FACTORY_BEGIN_ACK, 0);
}

/**
    @brief  process a factory update packet, maintain connection, and send ack
    @param[in]  buffer  packet
    @param[in]  bytes   packet size
*/

void
bnet_process_factory_update(void *buffer, uint8_t bytes)
{
        bnet_ft_update_t *pkt = buffer;

        if (uid_match(pkt->from_uid, bnet_factory_target) == false)
                return;

        bnet_factory_live();

        pkt->data[pkt->size] = 0;

        uart_printf("> FT update %s\n", pkt->data);

        bnet_factory_ack(BNET_TYPE_FACTORY_UPDATE_ACK, 0);
}

/**
    @brief  process a factory finish packet, log the event, and tell the modem to connect
    @param[in]  buffer  packet
    @param[in]  bytes   packet size

*/

void
bnet_process_factory_finish(void *buffer, uint8_t bytes)
{
        bnet_ft_finish_t *pkt = buffer;
        uint16_t result;

        if (uid_match(pkt->from_uid, bnet_factory_target) == false)
                return;

        bnet_factory_ack(BNET_TYPE_FACTORY_FINISH_ACK, 1);

        if (bnet_factory_reported == true)
                return;

        result = get_uint16(pkt->result);
        bnet_factory_coverup = true;
        bnet_factory_ttl = clock_read() + 120;

        if (result == 0)
                osSignalSet(modem_tid, MODEM_SIGNAL_FORCE);

        EVENT_LOG4(EVT_SYS_NOTE, "note",
                   "msg", EVT_STRCONST, "ft finished",
                   "result", EVT_8BIT, result,
                   "uid", EVT_UID, pkt->from_uid,
                   "imei", EVT_IMEI, pkt->imei);

        bnet_factory_reported = true;
}

/** @} */

/** @{

    @ingroup    bnet
    @brief      BNET Processing Thread
*/

#include "pelagic.h"
#include "signals.h"
#include "alarm.h"
#include "console.h"
#include "firmware.h"
#include "led.h"
#include "radio.h"
#include "bnet.h"
#include "bnet_client.h"
#include "bnet_bond.h"
#include "bnet_console.h"
#include "bnet_factory.h"
#include "bnet_new_factory.h"
#include "power.h"
#include "board_info.h"

volatile bool bnet_sensor_watch;
volatile bool bnet_low_power = false;

typedef struct {
        uint8_t type;
        void (*process)(void *buffer, uint8_t bytes);
} bnet_packet_type_t;

const bnet_packet_type_t bnet_packet_types[] = {
        { .type = BNET_TYPE_SENSOR_REPORT, .process = bnet_process_sensor_report },
        { .type = BNET_TYPE_SENSOR_BOND, .process = bnet_process_sensor_bond },
        { .type = BNET_TYPE_FIRMWARE_CHECK, .process = bnet_process_firmware_check },
        { .type = BNET_TYPE_FIRMWARE_SEND, .process = bnet_process_firmware_send },
        { .type = BNET_TYPE_FIRMWARE_UPDATED, .process = bnet_process_firmware_updated },
        { .type = BNET_TYPE_PING, .process = bnet_process_ping },
        { .type = BNET_TYPE_CONSOLE, .process = bnet_process_console },
        { .type = BNET_TYPE_PONG, .process = bnet_process_pong },
        { .type = BNET_TYPE_PROVISION, .process = bnet_process_provision },

#ifdef HAVE_FACTORY_TEST
        { .type = BNET_TYPE_NEW_FT_ENTER_TEST_MODE, .process = bnet_process_new_ft_request },
        { .type = BNET_TYPE_NEW_FT_REPORT_VOLTAGES, .process = bnet_process_new_ft_request },
        { .type = BNET_TYPE_NEW_FT_ENTER_DEEP_SLEEP, .process = bnet_process_new_ft_request },
        { .type = BNET_TYPE_NEW_FT_START_SELF_TEST, .process = bnet_process_new_ft_request },
        { .type = BNET_TYPE_NEW_FT_SELF_TEST_STATUS, .process = bnet_process_new_ft_request },
        { .type = BNET_TYPE_NEW_FT_EXIT_TEST_MODE, .process = bnet_process_new_ft_request },
        { .type = BNET_TYPE_NEW_FT_SHIP, .process = bnet_process_new_ft_request },
#endif

#ifdef FACTORY_TEST_FIELD
        { .type = BNET_TYPE_FACTORY_BEGIN, .process = bnet_process_factory_begin },
        { .type = BNET_TYPE_FACTORY_UPDATE, .process = bnet_process_factory_update },
        { .type = BNET_TYPE_FACTORY_FINISH, .process = bnet_process_factory_finish },
#endif
};

/**
    @brief  send a beacon packet out containing IMEI (if set), UID and current time (if acquired).
*/

void
bnet_send_beacon()
{
        bnet_beacon_t *pkt = (bnet_beacon_t *)bnet_send_buffer;

        memset(pkt, 0, sizeof(bnet_beacon_t));

        pkt->type = BNET_TYPE_BEACON;
        uid_set_me(pkt->from_uid);

        if (board_info_have_imei)
                imei_set_me(pkt->from_imei);

        if (time_acquired)
                set_uint32(pkt->timestamp, rtc_read());

        radio_send(pkt, sizeof(bnet_beacon_t));
}

/**
    @brief  process thread signals
    @note   the following signals are handle
            - POWER_SIGNAL_CHANGE - anything below POWER_STATE_LOW the radio is turned off
            - SYS_SIGNAL_SHUTDOWN - turn off the radio
            - BNET_SIGNAL_RADIO_CYCLE - power off and then back on the radio
            - BNET_SIGNAL_RADIO_PING - send out a beacon packet
            - BNET_SIGNAL_RADIO_CALIBRATE - calibrate the radio
            - BNET_SIGNAL_CONSOLE_SEND - send out queued console data
            - ALARM_SIGNAL_BUZZER - handle various time out conditions for clean up purposes
*/

void
bnet_handle_signals(uint16_t signals)
{
        if (signals & POWER_SIGNAL_CHANGE) {
                switch (board_power_state) {
                case POWER_STATE_NORMAL:
                case POWER_STATE_EXCELLENT:
                        bnet_low_power = false;
                        if (radio_is_off() && (settings.radio.flags & RADIO_FLAG_OFF) == 0) {
                                radio_power_on();
                                EVENT_LOG(EVT_BNET_POWER_ON, "power on");
                        }
                        break;

                case POWER_STATE_LOW:
                case POWER_STATE_CRITICAL:
                        bnet_low_power  = true;
                        if (!radio_is_off()) {
                                radio_power_off();
                                EVENT_LOG(EVT_BNET_POWER_OFF, "power off");
                        }
                        break;
                case POWER_STATE_UNKNOWN:
                default:
                        printf("bnet_handle_signals: unknown power state: %d\n", board_power_state);
                }
        }

        if (signals & SYS_SIGNAL_SHUTDOWN) {
                radio_power_off();
                return;
        }

        if (signals & BNET_SIGNAL_RADIO_CYCLE) {
                if (!radio_is_off()) {
                        radio_power_off();
                        osDelay(1000);
                        radio_power_on();
                }
        }

        if (signals & BNET_SIGNAL_SETTINGS) {
                if (settings.radio.flags & RADIO_FLAG_OFF) {
                        if (!radio_is_off())
                                radio_power_off();
                } else if (radio_is_off() && !bnet_low_power) {
                        radio_power_on();
                }
        }

        if (signals & BNET_SIGNAL_RADIO_PING)
                bnet_send_beacon();

        if (signals & BNET_SIGNAL_RADIO_CALIBRATE)
                radio_calibrate();

        if (signals & BNET_SIGNAL_CONSOLE_SEND)
                bnet_console_send_signal();

        if (signals & ALARM_SIGNAL_BUZZER) {
                bnet_console_send_timeout();

                if (bnet_client_count > 0)
                        bnet_client_age();

#ifdef FACTORY_TEST_FIELD
                bnet_factory_timeout();
#endif
        }

#ifdef FACTORY_TEST_FIELD
        if (signals & BNET_SIGNAL_FT_START)
                bnet_factory_test_start();
#endif
}

/**
    @brief  BNET Processing Thread
    @note   The thread is responsible for handle all incoming packet received over the radio.
            The first byte of the packet determines the type and is dispatched appropriate to
            its handler.
*/

void
bnet_thread(void *arg)
{
        uint8_t bytes;
        int8_t rssi;
        uint16_t signals;
        alarm_t  buzzer;

        radio_init((settings.radio.flags & RADIO_FLAG_OFF) == 0);

        bnet_console_init();

        EVENT_LOG(EVT_BNET_INIT, "init");

        alarm_start_periodic(&buzzer, 1);

        for (;;) {
                bytes = radio_read_signal(bnet_receive_buffer, osWaitForever, &rssi, &signals);

                reboot_check_pending();

                if (signals)
                        bnet_handle_signals(signals);

                if (bytes == 0)
                        continue;

                if (bnet_packet_watch) {
                        uart_printf("bnet: bytes [%3d] rssi [%2d]: ", bytes, rssi);

                        for (int i = 0; i < bytes; i++)
                                uart_printf("%2.2x ", bnet_receive_buffer[i]);
                        uart_printf("\n");
                }

                const bnet_packet_type_t *packet_type = bnet_packet_types;
                uint8_t type = bnet_receive_buffer[0];

                for (int i = 0; i < ARRAY_SIZE(bnet_packet_types); i++, packet_type++) {
                        if (packet_type->type == type) {
                                packet_type->process(bnet_receive_buffer, bytes);
                                break;
                        }
                }
        }
}

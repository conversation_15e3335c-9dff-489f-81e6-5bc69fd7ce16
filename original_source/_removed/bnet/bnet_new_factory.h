#ifndef BNET_NEW_FACTORY_H_
#define BNET_NEW_FACTORY_H_

typedef enum {
        NEW_FT_RESPONSE_UNKNOWN        = 0x00,
        NEW_FT_RESPONSE_PASS           = 0x10,
        NEW_FT_RESPONSE_FAIL           = 0x20,
        NEW_FT_RESPONSE_TESTS_RUNNING  = 0x30,
        NEW_FT_RESPONSE_VOLTAGE_REPORT = 0x40,
        NEW_FT_RESPONSE_NOT_IN_FT_MODE = 0x50,
} bnet_new_ft_response_code_t;

typedef struct
__attribute__ ((packed))
{
        uint8_t     type;
        board_uid_t from_uid;
        imei_t      to_imei;
}
bnet_new_ft_request_t;

typedef struct
__attribute__ ((packed))
{
        uint8_t     type;
        imei_t      from_imei;
        board_uid_t to_uid;
}
bnet_new_ft_request_ack_t;

typedef struct
__attribute__ ((packed))
{
        uint8_t                      type;
        imei_t                       from_imei;
        board_uid_t                  to_uid;
        bnet_new_ft_response_code_t  response_code;
        uint16_t                     data[2];
        uint8_t                      message_size;
        char                         message[];
}
bnet_new_ft_response_t;


typedef struct
__attribute__ ((packed))
{
        uint8_t     type;
        board_uid_t from_uid;
        imei_t      to_imei;
}
bnet_new_ft_response_ack_t;

extern bool in_test_mode;
extern bool test_in_progress;


void factory_watch_loop();
void factory_test_thread(void const *arg);
void bnet_process_new_ft_request(void *buffer, uint8_t bytes);
void bnet_new_factory_request_ack(uint8_t type, imei_t target_imei);
void bnet_new_factory_respond(uint8_t code, uint16_t data[2]);
uint8_t make_test_go_now();

#endif

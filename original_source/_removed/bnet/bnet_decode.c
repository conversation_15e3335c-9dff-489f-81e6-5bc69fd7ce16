/**@{

    @file
    @ingroup    bnet
    @brief      BNET packet debugging dump
*/

#include "pelagic.h"
#include "bnet.h"
#include "bnet_console.h"
#include "bnet_factory.h"
#include "bnet_new_factory.h"

typedef struct  {
        uint8_t type;
        const char *name;
} bnet_packet_name_t;

const bnet_packet_name_t bnet_packet_names[] = {
        { .type = BNET_TYPE_OLD_TEMP, .name = "OLD_TEMP" },
        { .type = BNET_TYPE_OLD_TEMP_ACK, .name = "OLD_TEMP_ACK" },
        { .type = BNET_TYPE_SENSOR_REPORT, .name = "SENSOR_REPORT" },
        { .type = BNET_TYPE_SENSOR_REPORT_ACK, .name = "SENSOR_REPORT_ACK" },
        { .type = BNET_TYPE_SENSOR_ERROR, .name = "SENSOR_ERROR" },
        { .type = BNET_TYPE_SENSOR_ERROR_ACK, .name = "SENSOR_ERROR_ACK" },
        { .type = BNET_TYPE_SENSOR_BOND, .name = "SENSOR_BOND" },
        { .type = BNET_TYPE_SENSOR_BOND_ACK, .name = "SENSOR_BOND_ACK" },
        { .type = BNET_TYPE_SENSOR_WAKEUP, .name = "SENSOR_WAKEUP" },
        { .type = BNET_TYPE_OLD_FIRMWARE_CHECK, .name = "OLD_FW_CHECK" },
        { .type = BNET_TYPE_OLD_FIRMWARE_NONE, .name = "OLD_FW_NONE" },
        { .type = BNET_TYPE_OLD_FIRMWARE_AVAIL, .name = "OLD_FW_AVAIL" },
        { .type = BNET_TYPE_OLD_FIRMWARE_SEND, .name = "OLD_FW_SEND" },
        { .type = BNET_TYPE_OLD_FIRMWARE_DATA, .name = "OLD_FW_DATA" },
        { .type = BNET_TYPE_OLD_FIRMWARE_END, .name = "OLD_FW_END" },
        { .type = BNET_TYPE_BEACON, .name = "BEACON" },
        { .type = BNET_TYPE_FIRMWARE_CHECK, .name = "FW_CHECK" },
        { .type = BNET_TYPE_FIRMWARE_CHECK_ACK, .name = "FW_CHECK_ACK" },
        { .type = BNET_TYPE_FIRMWARE_SEND, .name = "FW_SEND" },
        { .type = BNET_TYPE_FIRMWARE_SEND_ACK, .name = "FW_SEND_ACK" },
        { .type = BNET_TYPE_FIRMWARE_UPDATED, .name = "FW_UPDATED" },
        { .type = BNET_TYPE_FIRMWARE_UPDATED_ACK, .name = "FW_UPDATED_ACK" },
        { .type = BNET_TYPE_PING, .name = "PING" },
        { .type = BNET_TYPE_PONG, .name = "PONG" },
        { .type = BNET_TYPE_CONSOLE, .name = "CONSOLE" },

        { .type = BNET_TYPE_FACTORY_TEST_START, .name = "FT_TEST_START" },
        { .type = BNET_TYPE_FACTORY_BEGIN, .name = "FT_BEGIN" },
        { .type = BNET_TYPE_FACTORY_BEGIN_ACK, .name = "FT_BEGIN_ACK" },
        { .type = BNET_TYPE_FACTORY_UPDATE, .name = "FT_UPDATE" },
        { .type = BNET_TYPE_FACTORY_UPDATE_ACK, .name = "FT_UPDATE_ACK" },
        { .type = BNET_TYPE_FACTORY_FINISH, .name = "FT_FINISH" },
        { .type = BNET_TYPE_FACTORY_FINISH_ACK, .name = "FT_FINISH_ACK" },
        { .type = BNET_TYPE_FACTORY_TEST_TARGET, .name = "FT_TEST_TARGET" },

        { .type = BNET_TYPE_PROVISION, .name = "PROVISION" },
        { .type = BNET_TYPE_PROVISION_ACK, .name = "PROVISION_ACK" },

        { .type = BNET_TYPE_NEW_FT_ENTER_TEST_MODE, .name = "BNET_TYPE_NEW_FT_ENTER_TEST_MODE" },
        { .type = BNET_TYPE_NEW_FT_REPORT_VOLTAGES, .name = "BNET_TYPE_NEW_FT_REPORT_VOLTAGES" },
        { .type = BNET_TYPE_NEW_FT_ENTER_DEEP_SLEEP, .name = "BNET_TYPE_NEW_FT_ENTER_DEEP_SLEEP" },
        { .type = BNET_TYPE_NEW_FT_START_SELF_TEST, .name = "BNET_TYPE_NEW_FT_START_SELF_TEST" },
        { .type = BNET_TYPE_NEW_FT_SELF_TEST_STATUS, .name = "BNET_TYPE_NEW_FT_SELF_TEST_STATUS" },
        { .type = BNET_TYPE_NEW_FT_EXIT_TEST_MODE, .name = "BNET_TYPE_NEW_FT_EXIT_TEST_MODE" },
        { .type = BNET_TYPE_NEW_FT_SHIP, .name = "BNET_TYPE_NEW_FT_SHIP" },
        { .type = BNET_TYPE_NEW_FT_RESPONSE, .name = "BNET_TYPE_NEW_FT_RESPONSE" },
};

const bnet_packet_name_t bnet_packet_console_names[] = {
        { .type = BNET_CONSOLE_OPEN, .name = "OPEN" },
        { .type = BNET_CONSOLE_OPEN_ACK, .name = "OPEN_ACK" },
        { .type = BNET_CONSOLE_CLOSE, .name = "CLOSE" },
        { .type = BNET_CONSOLE_CLOSE_ACK, .name = "CLOSE_ACK" },
        { .type = BNET_CONSOLE_SEND, .name = "SEND" },
        { .type = BNET_CONSOLE_SEND_ACK, .name = "SEND_ACK" },
        { .type = BNET_CONSOLE_BUSY, .name = "BUSY" },
        { .type = BNET_CONSOLE_NOTCONN, .name = "NOTCONN" },
        { .type = BNET_CONSOLE_KEEPALIVE, .name = "KEEPALIVE" },
        { .type = BNET_CONSOLE_KEEPALIVE_ACK, .name = "KEEPALIVE_ACK" },
};

/**
    @brief  decode a console packet
    @param[in]  buffer  console packet
    @param[in]  bytes   packet size
*/

void
bnet_decode_console(void *buffer, uint32_t bytes)
{
        bnet_console_op_t *pkt = buffer;
        const bnet_packet_name_t *name = bnet_packet_console_names;

        uart_printf("%Z > %Z ", pkt->from_uid, pkt->to_uid);

        for (int i = 0; i < ARRAY_SIZE(bnet_packet_console_names); i++, name++) {
                if (name->type == pkt->op) {
                        uart_printf("%-10s seq %3d", name->name, pkt->sequence);
                        return;
                }
        }

        uart_printf(" UNKNOWN 0x%x");
}

/**
    @brief  decode a packet
    @param[in]  buffer  BNET packet
    @param[in]  bytes   packet size
    @param[in]  rssi    RSSI value for packet
*/

void
bnet_decode_packet(void *buffer, uint32_t bytes, int8_t rssi)
{
        uint8_t type = *((uint8_t *)buffer);
        const bnet_packet_name_t *name = bnet_packet_names;
        bool found = false;

        uart_printf("[ %3d : %3d ] ", bytes, rssi);
        for (int i = 0; i < ARRAY_SIZE(bnet_packet_names); i++, name++) {
                if (name->type == type) {
                        uart_printf("%-17s ", name->name);
                        found = true;
                        break;
                } else if (name->type == (type ^ BNET_ACK)) {
                        uart_printf("[ACK] %-17s ", name->name);
                        found = true;
                        break;
                }
        }

        if (!found) {
                uart_printf("unknown ");
                uint8_t *pkt = buffer;
                for (int i = 0; i < bytes; i++)
                        uart_printf("%2.2x ", pkt[i]);

                uart_printf("\n");
                return;
        }

        switch (type) {

        // NEW FACTORY TESTS
        case BNET_TYPE_NEW_FT_ENTER_TEST_MODE:
        case BNET_TYPE_NEW_FT_REPORT_VOLTAGES:
        case BNET_TYPE_NEW_FT_ENTER_DEEP_SLEEP:
        case BNET_TYPE_NEW_FT_START_SELF_TEST:
        case BNET_TYPE_NEW_FT_SELF_TEST_STATUS:
        case BNET_TYPE_NEW_FT_EXIT_TEST_MODE:
        case BNET_TYPE_NEW_FT_SHIP: {
                bnet_new_ft_request_t *pkt = buffer;
                uint64_t to_imei = get_uint64(pkt->to_imei);
                uart_printf("from %Z to %6.6d%9.9d ",
                            pkt->from_uid,
                            (uint32_t)(to_imei / 1000000000), (uint32_t)(to_imei % 1000000000)
                           );

        }
        break;

        case (BNET_TYPE_NEW_FT_ENTER_TEST_MODE | BNET_ACK):
        case (BNET_TYPE_NEW_FT_REPORT_VOLTAGES | BNET_ACK):
        case (BNET_TYPE_NEW_FT_ENTER_DEEP_SLEEP | BNET_ACK):
        case (BNET_TYPE_NEW_FT_START_SELF_TEST | BNET_ACK):
        case (BNET_TYPE_NEW_FT_SELF_TEST_STATUS | BNET_ACK):
        case (BNET_TYPE_NEW_FT_EXIT_TEST_MODE | BNET_ACK):
        case (BNET_TYPE_NEW_FT_SHIP | BNET_ACK): {
                bnet_new_ft_request_ack_t *pkt = buffer;
                uint64_t from_imei = get_uint64(pkt->from_imei);
                uart_printf("from %6.6d%9.9d to %Z ",
                            (uint32_t)(from_imei / 1000000000), (uint32_t)(from_imei % 1000000000),
                            pkt->to_uid
                           );

        }
        break;

        case BNET_TYPE_NEW_FT_RESPONSE: {
                bnet_new_ft_response_t *pkt = buffer;
                uint64_t from_imei = get_uint64(pkt->from_imei);
                uart_printf("from %6.6d%9.9d to %Z ",
                            (uint32_t)(from_imei / 1000000000), (uint32_t)(from_imei % 1000000000),
                            pkt->to_uid
                           );
                uart_printf("code [0x%x] ", pkt->response_code);
                uart_printf("data [%d, %d] ", pkt->data[0], pkt->data[1]);
                if (pkt->message_size) {
                        uart_printf("message [%s] ", pkt->message);
                }
        }
        break;

        case (BNET_TYPE_NEW_FT_RESPONSE | BNET_ACK): {
                bnet_new_ft_response_ack_t *pkt = buffer;
                uint64_t to_imei = get_uint64(pkt->to_imei);
                uart_printf("from %Z to %6.6d%9.9d ",
                            pkt->from_uid,
                            (uint32_t)(to_imei / 1000000000), (uint32_t)(to_imei % 1000000000)
                           );
        }
        break;

        // OLD FACTORY TESTS
        case BNET_TYPE_FACTORY_TEST_START: {
                bnet_ft_start_t *pkt = buffer;
                uart_printf("< %Z mode %d", pkt->from_uid, pkt->deploy_mode);
        }
        break;
        case BNET_TYPE_FACTORY_BEGIN: {
                bnet_ft_begin_t *pkt = buffer;
                uart_printf("%Z > %Z", pkt->from_uid, pkt->to_uid);
        }
        break;
        case BNET_TYPE_FACTORY_BEGIN_ACK:
        case BNET_TYPE_FACTORY_UPDATE_ACK:
        case BNET_TYPE_FACTORY_FINISH_ACK: {
                bnet_ft_ack_t *pkt = buffer;
                uart_printf("> %Z flags 0x%x", pkt->to_uid, pkt->flags);
        }
        break;
        case BNET_TYPE_FACTORY_UPDATE: {
                bnet_ft_update_t *pkt = buffer;
                uart_printf("< %Z seq %d size %d", pkt->from_uid, pkt->sequence, pkt->size);
        }
        break;
        case BNET_TYPE_FACTORY_FINISH: {
                bnet_ft_finish_t *pkt = buffer;
                uint64_t imei = get_uint64(pkt->imei);
                uart_printf("< %Z result %d IMEI %6.6d%9.9d result %d", pkt->from_uid, (uint32_t)(imei / 1000000000),
                            (uint32_t)(imei % 1000000000), get_uint16(pkt->result));
        }
        break;
        case BNET_TYPE_FACTORY_TEST_TARGET: {
                bnet_ft_target_t *pkt = buffer;
                uint64_t imei = get_uint64(pkt->to_imei);
                uart_printf("< %Z > IMEI %6.6d%9.9d mode %d", pkt->from_uid, (uint32_t)(imei / 1000000000),
                            (uint32_t)(imei % 1000000000), pkt->deploy_mode);
        }
        break;

        case BNET_TYPE_PROVISION: {
                bnet_provision_t *pkt = buffer;
                uint64_t imei = get_uint64(pkt->to_imei);
                uart_printf("%Z > IMEI %6.6d%9.9d type %d mode %d",
                            pkt->from_uid,
                            (uint32_t)(imei / 1000000000), (uint32_t)(imei % 1000000000),
                            pkt->provision_type,
                            pkt->provision_mode
                           );
        }
        break;


        case BNET_TYPE_PROVISION_ACK: {
                bnet_provision_ack_t *pkt = buffer;
                uint64_t imei = get_uint64(pkt->from_imei);
                uart_printf("IMEI %6.6d%9.9d > %Z mode %d",
                            (uint32_t)(imei / 1000000000), (uint32_t)(imei % 1000000000),
                            pkt->to_uid,
                            pkt->provision_mode
                           );
        }
        break;

        case BNET_TYPE_SENSOR_REPORT: {
                bnet_sensor_report_t *pkt = buffer;
                uint8_t temp, temp_decimal;
                uint32_t timestamp = get_uint32((uint8_t*)pkt->sensor_data);

                uart_printf("%Z > %Z, voltage %d, records %d, timestamp %d, data ", pkt->from_uid, pkt->to_uid, pkt->sensor_voltage, (pkt->sensor_data_length - 4)/2, timestamp);
                for (int i = 4; i < pkt->sensor_data_length; i += 2) {
                        temp = (pkt->sensor_data[i+1] / 2) - 10;
                        temp_decimal = (pkt->sensor_data[i+1] % 2) == 0 ? 0 : 5;
                        uart_printf("[%s, %d.%d] ", (pkt->sensor_data[i] == 0 ? "dry" : "wet"), temp, temp_decimal);
                }

                uint8_t *raw_pkt = buffer;
                uart_printf("(");
                for (int i = 0; i < bytes; i++)
                        uart_printf("%2.2x ", raw_pkt[i]);
                uart_printf(")");
        }
        break;

        case BNET_TYPE_SENSOR_REPORT_ACK:
        case BNET_TYPE_SENSOR_ERROR_ACK:
        case BNET_TYPE_SENSOR_BOND_ACK:
        case BNET_TYPE_FIRMWARE_UPDATED_ACK: {
                bnet_ack_t *pkt = buffer;
                uart_printf("%Z > %Z", pkt->from_uid, pkt->to_uid);
        }
        break;

        case BNET_TYPE_SENSOR_ERROR:
                break;  // TODO


        case BNET_TYPE_SENSOR_BOND: {
                bnet_sensor_bond_req_t *pkt = buffer;
                uart_printf("< %Z", pkt->from_uid);
        }
        break;

        case BNET_TYPE_SENSOR_WAKEUP: {
                bnet_sensor_wakeup_t *pkt = buffer;
                uart_printf("> Sensor Wakeup %Z", pkt->to_uid);
        }
        break;

        case BNET_TYPE_BEACON: {
                bnet_beacon_t *pkt = buffer;

                if (bytes == 10)
                        uart_printf("old beacon");
                else {
                        uint64_t imei = get_uint64(pkt->from_imei);
                        uart_printf("< %Z IMEI %6.6d%9.9d timestamp %d",
                                    pkt->from_uid,
                                    (uint32_t)(imei / 1000000000), (uint32_t)(imei % 1000000000),
                                    get_uint32(pkt->timestamp));
                }
        }
        break;

        case BNET_TYPE_FIRMWARE_CHECK: {
                bnet_firmware_check_t *pkt = buffer;
                uart_printf("%Z > %Z type %d vers %d buildstamp %d", pkt->from_uid, pkt->to_uid, pkt->device_type, pkt->device_version, get_uint32(pkt->buildstamp));
        }
        break;

        case BNET_TYPE_FIRMWARE_CHECK_ACK: {
                bnet_firmware_check_ack_t *pkt = buffer;
                uart_printf("%Z > %Z avail %d", pkt->from_uid, pkt->to_uid, pkt->avail);
        }
        break;

        case BNET_TYPE_FIRMWARE_SEND: {
                bnet_firmware_send_t *pkt = buffer;
                uart_printf("%Z > %Z offset %d", pkt->from_uid, pkt->to_uid, get_uint32(pkt->offset));
        }
        break;

        case BNET_TYPE_FIRMWARE_SEND_ACK: {
                bnet_firmware_send_ack_t *pkt = buffer;

                uart_printf("%Z > %Z offset %d len %d", pkt->from_uid, pkt->to_uid, get_uint32(pkt->offset), pkt->length);
        }
        break;

        case BNET_TYPE_FIRMWARE_UPDATED: {
                bnet_firmware_updated_t *pkt = buffer;
                uart_printf("%Z > %Z buildstamp %d", pkt->from_uid, pkt->to_uid, get_uint32(pkt->buildstamp));
        }
        break;

        case BNET_TYPE_PING: {
                bnet_ping_t *pkt = buffer;

                uart_printf("%Z > ", pkt->from_uid);
                switch (pkt->ping_type) {
                case BNET_TYPE_IMEI: {
                        uint64_t imei = get_uint64(pkt->destination);
                        uart_printf("IMEI %6.6d%9.9d", (uint32_t)(imei / 1000000000), (uint32_t)(imei % 1000000000));
                }
                break;

                case BNET_TYPE_UID:
                        uart_printf("UID %Z", pkt->destination);
                        break;

                case BNET_BROADCAST:
                        uart_printf("NET");
                        break;

                default:
                        uart_printf("%d?", pkt->ping_type);
                        break;
                }

                uart_printf(" seq %3d", pkt->sequence);
        }
        break;

        case BNET_TYPE_PONG: {
                bnet_pong_t *pkt = buffer;
                int device_stage, device_type;

                uart_printf("%Z > %Z ", pkt->from_uid, pkt->to_uid);

                switch (pkt->pong_type) {
                case BNET_TYPE_IMEI: {
                        uint64_t imei = get_uint64(pkt->pong_data);
                        uart_printf("IMEI %6.6d%9.9d", (uint32_t)(imei / 1000000000), (uint32_t)(imei % 1000000000));
                }
                break;

                case BNET_TYPE_UID:
                        uart_printf("UID");
                        break;

                default:
                        uart_printf("%d?", pkt->pong_type);
                        break;
                }


                // For backwards-compatibility we have 2 device_types:
                //   Old, which just hold device_type
                //   New, which has has 0x80 set, stores device_type in the low 2 bits, and device_stage in the next 2 bits
                if (pkt->device_type & 0x80) {
                        device_stage = (pkt->device_type & 0x7F) >> 2;
                        device_type = (pkt->device_type & 0x7F) & 0x03;
                } else {
                        device_stage = -1;
                        device_type = pkt->device_type;
                }

                uart_printf(" type %d vers %d stage %d seq %3d", device_type, pkt->device_version, device_stage, pkt->sequence);

        }
        break;

        case BNET_TYPE_CONSOLE:
                bnet_decode_console(buffer, bytes);
                break;
        }
        uart_printf("\n");
}

/** @} */

/** @{

    @ingroup    bnet
    @file
    @brief      Main sensor work loop
*/

#include "pelagic.h"
#include "radio.h"
#include "bnet.h"
#include "bnet_sensor.h"
#include "system_file.h"
#include "firmware.h"

enum {
        STATE_CHANGE_LIMIT   = 6,
};

typedef enum {
        BNET_SENSOR_STATE_BOND = 0,
        BNET_SENSOR_STATE_REPORT,
        BNET_SENSOR_STATE_CHECK_FIRMWARE,
        BNET_SENSOR_STATE_FIRMWARE_UPDATED
} bnet_sensor_state_t;

void bnet_sensor_buzzer();
osTimerDef(bnet_sensor_timer, bnet_sensor_buzzer);
osTimerId bnet_sensor_timer_id;

bnet_sensor_state_t bnet_sensor_state = 0;

volatile bool bnet_sensor_timed_out = false;

/**
    @brief  called when the timer expired - related to waiting for packet ack
*/

void
bnet_sensor_buzzer()
{
        bnet_sensor_timed_out = true;
}

/**
    @brief  start the packet ack timer - create timer if need be
*/

void
bnet_sensor_create_timer()
{
        bnet_sensor_timed_out = false;

        if (bnet_sensor_timer_id == NULL) {
                bnet_sensor_timer_id = osTimerCreate(osTimer(bnet_sensor_timer), osTimerOnce, NULL);
        }

        osTimerStart(bnet_sensor_timer_id, BNET_SENSOR_TIMEOUT);
}

/**
    @brief  stop the timer
*/

void
bnet_sensor_timer_stop()
{
        osTimerStop(bnet_sensor_timer_id);
}

/**
    @brief  main sensor work loop
    @note   The loop runs thru several state changes which includes:
            - bond  with a VMS (if need be)
            - send a temperature report to bond VMS
            - check for a new available firmware - done after bonding (or rebond) with a VMS
            - send a firmware updated packet if the firmware was recently updated

            The state loop is allowed up to STATE_CHANGE_LIMIT changes before stopping.
            This is a paranoid check to make sure the sensor does not loop infinitely and
            drain the battery.
*/

void
bnet_sensor_work()
{
        bool check_firmware = false, reeval = false;

        if (SYS_REG_FILE->have_host) {
                bnet_sensor_state = BNET_SENSOR_STATE_REPORT;
                //uart_printf("work: node_id %d ttl %d slept %d\n", SYS_REG_FILE->node_id, SYS_REG_FILE->node_ttl, sensor_slept);
                int32_t ttl = SYS_REG_FILE->host_ttl;
                ttl -= (sensor_slept ? sensor_slept : 1);
                if (ttl >= 0) {
                        SYS_REG_FILE->host_ttl = ttl;
                        // Node looks okay for now..
                        radio_set_power_level(SYS_REG_FILE->radio_power);
                } else {
                        SYS_REG_FILE->host_ttl = 0;
                        reeval = true;
                        check_firmware = true;  // check firmware while we're at it
                }
        } else {
                bnet_sensor_state = BNET_SENSOR_STATE_BOND;
        }

        BNET_WATCH("work lvl %d ttl %d", SYS_REG_FILE->radio_power, SYS_REG_FILE->host_ttl);

        for (int loop = 0; loop < STATE_CHANGE_LIMIT; loop++) {
                BNET_WATCH("sensor state %d", bnet_sensor_state);

                switch (bnet_sensor_state) {
                case BNET_SENSOR_STATE_BOND:
                        if (bnet_sensor_bond() == false)
                                return;

                        bnet_sensor_state = BNET_SENSOR_STATE_REPORT;
                        check_firmware = true;
                        continue;

                case BNET_SENSOR_STATE_REPORT:
                        if (bnet_sensor_report(reeval)) {
                                if (SYS_REG_FILE->firmware_updated) {
                                        bnet_sensor_state = BNET_SENSOR_STATE_FIRMWARE_UPDATED;
                                        continue;
                                }

                                if (check_firmware == false)
                                        return; // done for now.

                                bnet_sensor_state = BNET_SENSOR_STATE_CHECK_FIRMWARE;
                        } else {
                                SYS_REG_FILE->have_host = false;
                                bnet_sensor_state = BNET_SENSOR_STATE_BOND;
                                reeval = false;
                        }
                        continue;

                case BNET_SENSOR_STATE_FIRMWARE_UPDATED:
                        if (bnet_sensor_firmware_updated())
                                SYS_REG_FILE->firmware_updated = false;

                        if (check_firmware == false)
                                return; // done for now.

                        // okay, there might be newer firmware available..
                        bnet_sensor_state = BNET_SENSOR_STATE_CHECK_FIRMWARE;
                        break;

                case BNET_SENSOR_STATE_CHECK_FIRMWARE:
                        if (bnet_sensor_firmware_check()) {
                                if (bnet_sensor_firmware_download()) {
                                        firmware_update(); // hello new firmware, and bye-bye for now.
                                }
                        }
                        return;

                }
        }
}

/** @} */

#ifndef __BNET_CLIENT_H__
#define __BNET_CLIENT_H__

enum {
        BNET_CLIENT_LIMIT   = 1,
        BNET_CLIENT_TIMEOUT = 1000,
        BNET_CLIENT_TTL     = 20,        // 20 seconds to keep the client connection alive
};

typedef struct {
        uint32_t     ttl;
        board_uid_t client_uid;
        bool        closed;
} bnet_client_t;

extern volatile int bnet_client_count;
extern bnet_client_t bnet_clients[BNET_CLIENT_LIMIT];

void  bnet_client_age();
bnet_client_t *bnet_find_client(board_uid_t uid, int setup_if_new);

#endif

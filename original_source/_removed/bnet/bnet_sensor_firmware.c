/** @{

    @ingroup    bnet
    @brief      BNET Sensor Firmware routines
*/

#include "pelagic.h"
#include "bnet.h"
#include "bnet_sensor.h"
#include "firmware_sensor.h"
#include "radio.h"
#include "system_file.h"

/**
    @brief  check to see if a new firmware is available to download
    @return true if a new firmware image is available
*/

bool
bnet_sensor_firmware_check()
{
        bnet_firmware_check_t *firmware = (bnet_firmware_check_t *) bnet_send_buffer;
        bnet_firmware_check_ack_t *ack = (bnet_firmware_check_ack_t *) bnet_receive_buffer;
        bool acked;

        firmware->type = BNET_TYPE_FIRMWARE_CHECK;
        uid_set_me(firmware->from_uid);
        uid_set_host(firmware->to_uid);

        firmware->device_type = BNET_DEVICE_SENSOR;
        firmware->device_version = build_target_version;
        set_uint32(firmware->buildstamp, build_timestamp);

        acked = bnet_sensor_send_packet(BNET_TYPE_FIRMWARE_CHECK_ACK, firmware, sizeof(bnet_firmware_check_t), ack, sizeof(bnet_firmware_check_ack_t), BNET_SENSOR_REPORT_TRIES);

        return (acked ? ack->avail : false);
}

/**
    @brief  download a new firmware image and store it in the MCU flash.
    @return true if the firmware was successfully downloaded.
*/

bool
bnet_sensor_firmware_download()
{
        uint32_t offset = 0;
        bool acked;
        bnet_firmware_send_t *pkt = (bnet_firmware_send_t *) bnet_send_buffer;
        bnet_firmware_send_ack_t *ack =  (bnet_firmware_send_ack_t *) bnet_receive_buffer;

        pkt->type = BNET_TYPE_FIRMWARE_SEND;
        uid_set_me(pkt->from_uid);
        uid_set_host(pkt->to_uid);

        firmware_erase();

        radio_set_rx_mode(RADIO_RX_MODE_HIGH);

        for (;;) {
                set_uint32(pkt->offset, offset);
                acked = bnet_sensor_send_packet(BNET_TYPE_FIRMWARE_SEND_ACK, pkt, sizeof(bnet_firmware_send_t), ack, 0, BNET_SENSOR_REPORT_TRIES);

                if (acked == false)
                        break;

                if (ack->length == 0)
                        break;

                uart_printf("[%d] ", offset);
                firmware_write(ack->data, ack->length);
                offset += ack->length;
        }

        if (acked == false || offset == 0) {
                BNET_WATCH("firmware-download failure");
                return false;
        }

        BNET_WATCH("firmware-download success! bytes=%d", offset);
        firmware_flush();

        return true;
}

/**
    @brief  send a firmware updated successfully packet to the VMS
    @return true if the packet acknowledged
*/

bool
bnet_sensor_firmware_updated()
{
        bnet_firmware_updated_t *firmware = (bnet_firmware_updated_t *) bnet_send_buffer;
        bool acked;

        firmware->type = BNET_TYPE_FIRMWARE_UPDATED;
        uid_set_me(firmware->from_uid);
        uid_set_host(firmware->to_uid);
        set_uint32(firmware->buildstamp, build_timestamp);
        firmware->build_tag_size = strlen(build_tag);
        memcpy(firmware->build_tag, build_tag, firmware->build_tag_size);

        acked = bnet_sensor_send_packet(BNET_TYPE_FIRMWARE_UPDATED_ACK, firmware, sizeof(bnet_firmware_updated_t) + firmware->build_tag_size, bnet_receive_buffer, sizeof(bnet_ack_t), BNET_SENSOR_REPORT_TRIES);

        BNET_WATCH("firmware-updated %s", acked ? "acked" : "NACK");

        return acked;
}

/** @} */

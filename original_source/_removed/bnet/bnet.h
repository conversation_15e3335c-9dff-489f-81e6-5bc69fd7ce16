#ifndef __BNET_H__
#define __BNET_H__

#define BNET_FIRMWARE_DATA_SIZE  (BNET_PACKET_SIZE - sizeof(bnet_firmware_send_ack_t))

enum {
        BNET_PACKET_SIZE  = 124,
};

typedef enum {
        BNET_ACK                          = 0x80, // OR'ed with type and sent back

        BNET_TYPE_OLD_TEMP                = 0x01,   // temperature packet with MCU
        BNET_TYPE_OLD_TEMP_ACK            = (BNET_ACK|BNET_TYPE_OLD_TEMP),

        BNET_TYPE_SENSOR_REPORT           = 0x10, // Sensor reporting
        BNET_TYPE_SENSOR_REPORT_ACK       = (BNET_TYPE_SENSOR_REPORT | BNET_ACK),
        BNET_TYPE_SENSOR_ERROR            = 0x11, // Sensor has issue
        BNET_TYPE_SENSOR_ERROR_ACK        = (BNET_TYPE_SENSOR_ERROR | BNET_ACK),
        BNET_TYPE_SENSOR_BOND             = 0x12, // request server to bond with
        BNET_TYPE_SENSOR_BOND_ACK         = (BNET_TYPE_SENSOR_BOND | BNET_ACK),
        BNET_TYPE_SENSOR_WAKEUP           = 0x13,
        BNET_TYPE_SENSOR_WAKEUP_ACK       = (BNET_ACK | BNET_TYPE_SENSOR_WAKEUP),

        BNET_TYPE_OLD_FIRMWARE_CHECK      = 0x20, // Check for new firmware
        BNET_TYPE_OLD_FIRMWARE_NONE       = 0x21, // No new firmware at this time
        BNET_TYPE_OLD_FIRMWARE_AVAIL      = 0x22, // New firmware available

        BNET_TYPE_OLD_FIRMWARE_SEND       = 0x23, // Send a packet of firmware
        BNET_TYPE_OLD_FIRMWARE_DATA       = 0x24, // response to send
        BNET_TYPE_OLD_FIRMWARE_END        = 0x25, // .. or if there is no more

        BNET_TYPE_FACTORY_TEST_START      = 0x30, // Tell device to start
        BNET_TYPE_FACTORY_BEGIN           = 0x31, // Device beginning tests
        BNET_TYPE_FACTORY_BEGIN_ACK       = (BNET_ACK | BNET_TYPE_FACTORY_BEGIN),
        BNET_TYPE_FACTORY_UPDATE          = 0x32, // Continuous update
        BNET_TYPE_FACTORY_UPDATE_ACK      = (BNET_ACK | BNET_TYPE_FACTORY_UPDATE),
        BNET_TYPE_FACTORY_FINISH          = 0x33, // device finish
        BNET_TYPE_FACTORY_FINISH_ACK      = (BNET_ACK|BNET_TYPE_FACTORY_FINISH),
        BNET_TYPE_FACTORY_TEST_TARGET     = 0x34,
        BNET_TYPE_FACTORY_TEST_TARGET_ACK = (BNET_ACK|BNET_TYPE_FACTORY_TEST_TARGET),
        BNET_TYPE_FACTORY_TEST_SLEEP      = 0x35,  // put the device to sleep for testing purposes.
        BNET_TYPE_FACTORY_TEST_SLEEP_ACK  = (BNET_ACK|BNET_TYPE_FACTORY_TEST_SLEEP),

        BNET_TYPE_PROVISION               = 0x36,  // put the device into a different run mode
        BNET_TYPE_PROVISION_ACK           = (BNET_ACK|BNET_TYPE_PROVISION),

        BNET_TYPE_BEACON                  = 0x40, // periodic beacon announcement

        BNET_TYPE_FIRMWARE_CHECK          = 0x50,
        BNET_TYPE_FIRMWARE_CHECK_ACK      = (BNET_TYPE_FIRMWARE_CHECK | BNET_ACK),
        BNET_TYPE_FIRMWARE_SEND           = 0x51,
        BNET_TYPE_FIRMWARE_SEND_ACK       = (BNET_TYPE_FIRMWARE_SEND | BNET_ACK),
        BNET_TYPE_FIRMWARE_UPDATED        = 0x52,
        BNET_TYPE_FIRMWARE_UPDATED_ACK    = (BNET_TYPE_FIRMWARE_UPDATED | BNET_ACK),

        BNET_TYPE_PING                    = 0x60,
        BNET_TYPE_PONG                    = (BNET_ACK | BNET_TYPE_PING),

        BNET_TYPE_CONSOLE                 = 0x70,

        BNET_TYPE_NEW_FT_ENTER_TEST_MODE  = 0x71,
        BNET_TYPE_NEW_FT_REPORT_VOLTAGES  = 0x72,
        BNET_TYPE_NEW_FT_ENTER_DEEP_SLEEP = 0x73,
        BNET_TYPE_NEW_FT_START_SELF_TEST  = 0x74,
        BNET_TYPE_NEW_FT_SELF_TEST_STATUS = 0x75,
        BNET_TYPE_NEW_FT_EXIT_TEST_MODE   = 0x76,
        BNET_TYPE_NEW_FT_SHIP             = 0x77,
        BNET_TYPE_NEW_FT_RESPONSE         = 0x7F,
} bnet_type_t;

typedef enum {
        BNET_DEVICE_VMS     = 1,
        BNET_DEVICE_SENSOR  = 2,
        BNET_DEVICE_CONSOLE = 3
} bnet_device_t;

// BNET_TYPE_TEMP - sent from a temperature sensor
typedef struct
__attribute__ ((packed))
{
        uint8_t        type;
        board_uid_t    to_uid;
        board_uid_t    from_uid;
        uint8_t        sensor_type;
        uint8_t        sensor_voltage;
        uint8_t        sensor_data_length;
        uint8_t        sensor_data[];
}
bnet_sensor_report_t;

typedef struct
__attribute__ ((packed))
{
        uint8_t        type;
        board_uid_t    to_uid;
        board_uid_t    from_uid;
}
bnet_ack_t;

typedef struct
__attribute__ ((packed))
{
        uint8_t       type;
        board_uid_t   from_uid;
}
bnet_sensor_bond_req_t;

typedef struct
__attribute__ ((packed))
{
        uint8_t     type;
        board_uid_t to_uid;
        board_uid_t from_uid;
        uint8_t     device_type;
        uint8_t     device_version;
        uint8_t     buildstamp[4];
}
bnet_firmware_check_t;

// Response is either FIRMWARE_AVAIL or FIRMWARE_NONE
typedef struct
__attribute__ ((packed))
{
        uint8_t     type;
        board_uid_t to_uid;
        board_uid_t from_uid;
        uint8_t     avail;
}
bnet_firmware_check_ack_t;

typedef struct
__attribute__ ((packed))
{
        uint8_t     type;
        board_uid_t to_uid;
        board_uid_t from_uid;
        uint8_t     offset[4];
}
bnet_firmware_send_t;

typedef struct
__attribute__ ((packed))
{
        uint8_t     type;
        board_uid_t to_uid;
        board_uid_t from_uid;
        uint8_t     offset[4];
        uint8_t     length;
        uint8_t     data[];
}
bnet_firmware_send_ack_t;

typedef struct
__attribute__ ((packed))
{
        uint8_t     type;
        board_uid_t to_uid;
        board_uid_t from_uid;
        uint8_t     buildstamp[4];
        uint8_t     build_tag_size;
        uint8_t     build_tag[];
}
bnet_firmware_updated_t;

typedef struct
__attribute__ ((packed))
{
        uint8_t     type;
        board_uid_t from_uid;
        imei_t      from_imei;
        uint8_t     timestamp[4];
}
bnet_beacon_t;

enum {
        BNET_TYPE_IMEI      = 1,
        BNET_TYPE_UID       = 2,
        BNET_BROADCAST      = 0xff,
};

typedef struct
__attribute__ ((packed))
{
        uint8_t     type;
        uint8_t     ping_type;
        board_uid_t from_uid;
        uint8_t     sequence;
        uint8_t     destination[];
}
bnet_ping_t;

typedef struct
__attribute__ ((packed))
{
        uint8_t     type;
        board_uid_t to_uid;
        board_uid_t from_uid;
        uint8_t     sensor_type;
}
bnet_sensor_wakeup_t;

typedef struct
__attribute__ ((packed))
{
        uint8_t     type;
        uint8_t     pong_type;
        board_uid_t to_uid;
        board_uid_t from_uid;
        uint8_t     sequence;
        uint8_t     device_type;
        uint8_t     device_version;
        uint8_t     pong_data[];
}
bnet_pong_t;

typedef struct
__attribute__ ((packed))
{
        uint8_t     type;
        board_uid_t to_uid;
        board_uid_t from_uid;
        uint8_t     op;
        uint8_t     sequence;
        uint8_t     length;
        uint8_t     data[];
}
bnet_console_t;

typedef struct
__attribute__ ((packed))
{
        uint8_t     type;
        uint8_t     provision_type;
        uint8_t     provision_mode;
        imei_t      to_imei;
        board_uid_t from_uid;
        uint8_t     reserved;
        uint8_t     reserved_also;
}
bnet_provision_t;

typedef struct
__attribute__ ((packed))
{
        uint8_t     type;
        board_uid_t to_uid;
        imei_t      from_imei;
        uint8_t     provision_mode;
        uint8_t     reserved;
        uint8_t     reserved_also;
}
bnet_provision_ack_t;

typedef enum {
        RX = 0,
        TX = 1,
} bnet_rx_tx_t;

typedef enum {
        PROVISION_TYPE_NONE = 0, // For safety
        PROVISION_TYPE_BROADCAST = 1,
        PROVISION_TYPE_IMEI = 2,
} bnet_provision_type_t;

#define BNET_WATCH(format, ...) { if (bnet_packet_watch) uart_printf("bnet: "format"\n", ##__VA_ARGS__); }
#define BNET_SENSOR_WATCH(report) { \
    if (bnet_sensor_watch) { \
        uart_printf("sensor: "); \
        bnet_decode_packet(report, sizeof(bnet_sensor_report_t), 0); \
        uart_printf("\n"); \
    } \
}

extern volatile bool bnet_packet_watch;
#ifdef TARGET_VMS
extern volatile int bnet_client_count;
extern volatile bool bnet_sensor_watch;
#endif

extern uint8_t bnet_receive_buffer[], bnet_send_buffer[];

void bnet_delay();
void bnet_send_test();

#ifdef TARGET_VMS
void bnet_process_sensor_report(void *buffer, uint8_t bytes);
void bnet_process_firmware_check(void *buffer, uint8_t bytes);
void bnet_process_firmware_send(void *buffer, uint8_t bytes);
void bnet_process_firmware_updated(void *buffer, uint8_t bytes);
void bnet_process_provision(void *buffer, uint8_t bytes);
#endif

void bnet_process_ping(void *data, uint8_t bytes);
void bnet_process_pong(void *data, uint8_t bytes);

void bnet_decode_packet(void *buffer, uint32_t bytes, int8_t rssi);

#endif

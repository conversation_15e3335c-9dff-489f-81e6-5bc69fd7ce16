/** @{

    @ingroup    bnet
    @brief      BNET (really radio) VMS settings
*/

#include "pelagic.h"
#include "signals.h"
#include "bnet.h"
#include "radio.h"

/**
    @brief  initialize radio settings to defaults
*/

void
radio_settings_init()
{
        settings.radio.flags = 0;
}

/**
    @brief process a radio setting command and notify bnet thread of new settings update
    @param[in]  argc    array size of argv
    @param[in]  argv    setting commands
    @return true if setting was successful
    @note   The following commands are support:
            - radio off - turn radio off
            - radio on - turn radio back on
*/

bool
radio_setting(int argc, char **argv)
{
        char *command = argv[0];

        if (argc != 1)
                return false;

        if (strcmp(command, "on") == 0) {
                settings.radio.flags &= ~RADIO_FLAG_OFF;
        } else if (strcmp(command, "off") == 0) {
                settings.radio.flags |= RADIO_FLAG_OFF;
        } else {
                return false;
        }

        EVENT_LOG1(EVT_RADIO_SET_ON, "radio set", "on", EVT_BOOL, !(settings.radio.flags & RADIO_FLAG_OFF));

        if (bnet_tid)
                osSignalSet(bnet_tid, BNET_SIGNAL_SETTINGS);

        return true;
}

/**
    @brief  dump bnet/radio settings to buffer
    @param[in]  buffer  area to dump settings
    @return bytes used
*/

int
radio_setting_dump(char *buffer)
{
        return sprintf(buffer, "radio %s\n", settings.radio.flags & RADIO_FLAG_OFF ? "off" : "on");
}
/** @} */

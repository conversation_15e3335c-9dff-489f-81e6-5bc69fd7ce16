/** @{

    @ingroup    bnet
    @file

    @brief  BNET VMS firmware support
*/

#include "pelagic.h"
#include "bnet.h"
#include "firmware.h"
#include "radio.h"

enum {
        BNET_SENSOR_FIRMWARE_UPDATED_TIME = 10,
};

board_uid_t last_sensor_firmware_updated;
uint32_t last_sensor_firmware_timestamp;

/**
    @brief  process a firmware check packet
    @note   Check to see if there's an available firmware stored here, and compares the
            buildstamp against what the sensor has. The acknowledgement is set to true if
            a new one is available, or false is there's no firmware stored or its an older image.
*/

void
bnet_process_firmware_check(void *buffer, uint8_t bytes)
{
        bnet_firmware_check_t *pkt = buffer;
        bnet_firmware_check_ack_t *ack = (bnet_firmware_check_ack_t *)bnet_send_buffer;

        if (firmware_sensor_partition.is_busy) {
                BNET_WATCH("firmware-check partition busy");
                return;
        }
        if (bytes != sizeof(bnet_firmware_check_t)) {
                BNET_WATCH("firmware-check got[%d] wanted[%d]", bytes, sizeof(bnet_firmware_check_t));
                return;
        }

        if (uid_match_me(pkt->to_uid) == false) {
                BNET_WATCH("firmware-check not for me");
                return;
        }

        ack->type = BNET_TYPE_FIRMWARE_CHECK_ACK;
        ack->avail = (get_uint32(pkt->buildstamp) < firmware_sensor_header.buildstamp);
        uid_copy(ack->to_uid, pkt->from_uid);
        uid_set_me(ack->from_uid);

        radio_send((uint8_t *) ack, sizeof(bnet_firmware_check_ack_t));

        BNET_WATCH("firmware-check uid[%Z] avail? %c",
                   ack->from_uid, ack->avail ? 'Y' : 'N');
}

/**
    @brief  process a firmware send packet
    @note   Acknowledge back a chunk of firmware data using the requested offset.
            No ack will be sent if the firmware partition is marked busy (new incoming
            firmware is being received via the modem).
            If no more bytes are available, set the returning length to zero.
*/

void
bnet_process_firmware_send(void * buffer, uint8_t bytes)
{
        bnet_firmware_send_t *pkt = buffer;
        bnet_firmware_send_ack_t *data_pkt = (bnet_firmware_send_ack_t *) bnet_send_buffer;
        uint32_t offset, total_bytes, count;

        if (bytes != sizeof(bnet_firmware_send_t)) {
                BNET_WATCH("firmware-send cnt got [%d] wanted [%d]", bytes, sizeof(bnet_firmware_send_t));
                return;
        }

        if (!uid_match_me(pkt->to_uid)) {
                BNET_WATCH("firmware-send not for me [%Z]", pkt->to_uid);
                return;
        }

        if (firmware_sensor_partition.is_busy) {
                BNET_WATCH("firmware partition busy", pkt->to_uid);
                return;
        }

        offset = get_uint32(pkt->offset);

        data_pkt->type = BNET_TYPE_FIRMWARE_SEND_ACK;
        uid_copy(data_pkt->to_uid, pkt->from_uid);
        uid_set_me(data_pkt->from_uid);
        set_uint32(data_pkt->offset, offset);

        total_bytes = firmware_sensor_header.length;// + sizeof(firmware_sensor_header);

        if (offset >= total_bytes) {
                count = 0;
        } else {
                count = total_bytes - offset;
                if (count > BNET_FIRMWARE_DATA_SIZE)
                        count = BNET_FIRMWARE_DATA_SIZE;

                count -= (count % 8);

                // Hack (??) for Gear Sensor not expecting a header
                if (offset == 0 && count >= 16)
                        count -= 16;

                if (flash_read_seek(&firmware_sensor_read, offset + sizeof(firmware_sensor_header)) == false)
                        count = 0;
                else if (flash_read_block(&firmware_sensor_read, data_pkt->data, count) != count)
                        count = 0;
        }

        data_pkt->length = count;

        radio_send(data_pkt, sizeof(bnet_firmware_send_ack_t) + count);

        //uart_printf("[%d: 0x%x]", offset, crc16(data_pkt->data, count));
        if (offset == 0)
                EVENT_LOG1(EVT_SENSOR_FIRMWARE_REQUEST, "firmware request", "uid", EVT_UID, pkt->from_uid);

        BNET_WATCH("firmware-send offset [%d] length [%d]",  offset, data_pkt->length);
}

/**
    @brief  process a firmware updated packet and log which sensor has new firmware with buildstamp & build_tag
*/

void
bnet_process_firmware_updated(void *buffer, uint8_t bytes)
{
        bnet_firmware_updated_t *pkt = buffer;
        bnet_ack_t *ack = (bnet_ack_t *)bnet_send_buffer;
        uint32_t buildstamp, now;

        if (bytes != sizeof(bnet_firmware_updated_t)+pkt->build_tag_size) {
                BNET_WATCH("firmware-updated cnt got [%d] wanted [%d]", bytes, sizeof(bnet_firmware_updated_t)+pkt->build_tag_size);
                return;
        }

        if (uid_match_me(pkt->to_uid) == false) {
                BNET_WATCH("firmware-updated not for me");
                return;
        }

        ack->type = BNET_TYPE_FIRMWARE_UPDATED_ACK;
        uid_set_me(ack->from_uid);
        uid_copy(ack->to_uid, pkt->from_uid);
        radio_send((uint8_t *) ack, sizeof(bnet_ack_t));

        buildstamp = get_uint32(pkt->buildstamp);
        pkt->build_tag[pkt->build_tag_size] = '\0';

        BNET_WATCH("firmware-updated uid[%Z] buildstamp[%d] build_tag[%s]", pkt->from_uid, buildstamp, pkt->build_tag);

        //
        // The sensor may not have received the ack, so don't flood the logs if the same notification came
        // in within a certain time period.
        //

        now = rtc_read();
        if ((last_sensor_firmware_timestamp + BNET_SENSOR_FIRMWARE_UPDATED_TIME) < now ||
            uid_match(last_sensor_firmware_updated, pkt->from_uid) == false) {
                last_sensor_firmware_timestamp = now;
                uid_copy(last_sensor_firmware_updated, pkt->from_uid);
                EVENT_LOG3(EVT_SENSOR_FIRMWARE_UPDATE, "sensor firmware update",
                           "uid", EVT_UID, pkt->from_uid,
                           "buildstamp", EVT_32BIT, buildstamp,
                           "tag", EVT_STRING, pkt->build_tag);
        }
}

/** @} */

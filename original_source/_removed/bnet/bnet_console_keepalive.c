/** @{

    @ingroup    bnet
    @file
    @brief      Console Keep Alive Thread
*/

#include "pelagic.h"
#include "bnet.h"
#include "bnet_console.h"
#include "radio.h"
#include "signals.h"

/**
    @brief  Sends out a periodic keepalive packet to the target system to maintain the connection
*/

void
bnet_console_keepalive_thread(void const *arg)
{
        osEvent result;
        int retries;
        uint32_t ttl = 0;
        bool acked;

        bnet_console_op_t ka = {
                .type  = BNET_TYPE_CONSOLE,
                .op    = BNET_CONSOLE_KEEPALIVE,
        };

        uid_set_me(ka.from_uid);

        ttl = clock_read() + BNET_CONSOLE_CLIENT_TTL;

        for (;;) {
                osDelay(1000);
                if (bnet_console_opened == false)
                        continue;

                if (ttl > clock_read())
                        continue;

                //uart_printf("[KA]");
                uid_copy(ka.to_uid, bnet_console_uid);

                acked = false;
                for (retries = 0; retries < BNET_CONSOLE_RETRIES && bnet_console_opened; retries++) {
                        radio_send((uint8_t *)&ka, sizeof(bnet_console_op_t));

                        result = osSignalWait(0, BNET_CONSOLE_ACK_TIMEOUT);
                        if (result.status != osEventTimeout) {
                                acked = true;
                                break;
                        }
                }

                if (bnet_console_opened) {
                        if (acked) {
                                ttl = clock_read() + BNET_CONSOLE_CLIENT_TTL;
                        } else {
                                bnet_console_opened = false;
                                osSignalSet(main_tid, BNET_CONSOLE_SIGNAL_CLOSE);
                                uart_printf("connection timed-out. closing.\n");
                        }
                }
        }
}

/** @} */

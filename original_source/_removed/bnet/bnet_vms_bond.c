/** @{

    @ingroup    bnet
    @file
    @brief      BNET VMS bonding support
*/

#include "pelagic.h"
#include "radio.h"
#include "bnet.h"
#include "bnet_bond.h"

board_uid_t bnet_last_bond_uid;
uint32_t bnet_last_bond_time;

enum {
        BNET_BOND_NOTIFY_TIME = 10,
};

/**
    @brief  process a sensor bond request
    @param[in]  buffer  packet to process
    @param[in]  bytes   packet size
*/

void
bnet_process_sensor_bond(void *buffer, uint8_t bytes)
{
        bnet_sensor_bond_req_t *bond = buffer;
        bnet_ack_t *ack = (bnet_ack_t *) bnet_send_buffer;
        uint32_t now;

        if (!time_acquired)
                return;

        if (bytes != sizeof(bnet_sensor_bond_req_t))
                return;

        ack->type = BNET_TYPE_SENSOR_BOND_ACK;
        uid_set_me(ack->from_uid);
        uid_copy(ack->to_uid, bond->from_uid);

        radio_send(ack, sizeof(bnet_ack_t));

        now = rtc_read();

        //
        // Prevent multiple erroneous bonding reports. The sensor
        // may not see the acknowledgement and resend the request - don't
        // log if the VMS saw the same bond request within BNET_BOND_NOTIFY_TIME seconds.
        //
        if ((now - bnet_last_bond_time) >= BNET_BOND_NOTIFY_TIME
            || uid_match(bnet_last_bond_uid, bond->from_uid) == false) {
                bnet_last_bond_time = now;
                uid_copy(bnet_last_bond_uid, bond->from_uid);
                EVENT_LOG1(EVT_BNET_SENSOR_BOND, "sensor bond", "uid", EVT_UID, bond->from_uid);
        }
}

/** @} */

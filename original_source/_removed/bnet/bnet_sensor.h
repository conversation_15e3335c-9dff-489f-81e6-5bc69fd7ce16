#ifndef __BNET_SENSOR_H__
#define __BNET_SENSOR_H__

enum {
        BNET_SENSOR_TTL                = (6*HOUR),
        BNET_SENSOR_REPORT_TRIES       = 12,
        BNET_SENSOR_REPORT_TRIES_PER_LEVEL = 2,

        BNET_SENSOR_FIRMWARE_TRIES     = 20,
        BNET_SENSOR_TIMEOUT            = 250
};

void bnet_sensor_work();
void bnet_sensor_firmware_available();
bool bnet_sensor_report(bool reeavl);
bool bnet_sensor_bond();
void bnet_sensor_create_timer();
void bnet_sensor_timer_stop();
bool bnet_sensor_firmware_check();
bool bnet_sensor_firmware_download();
bool bnet_sensor_firmware_updated();
bool bnet_sensor_send_packet(uint8_t ack_type, void *packet, uint8_t packet_size, void *ack_data, uint8_t expect_size, int attempts);

extern volatile bool bnet_sensor_timed_out;
#endif

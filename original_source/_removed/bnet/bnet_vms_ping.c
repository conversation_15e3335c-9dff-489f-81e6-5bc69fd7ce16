/** @{

    @ingroup    bnet
    @file

    @brief      BNET ping/pong common support
*/

#include "pelagic.h"
#include "provision.h"
#include "memmap.h"
#include "bnet.h"
#include "radio.h"
#ifdef TARGET_CONSOLE
#include "bnet_console.h"
#else
#include "board_info.h"
#endif

/**
    @brief  process a ping packet
    @note   There are three types of ping packets:
            - BNET_BROADCAST - any receiving board should respond back
            - BNET_TYPE_UID  - a board with UID should respond (can be any device type)
            - BNET_TYPE_IMEI - a VMS with the IMEI should respond
*/

void
bnet_process_ping(void *buffer, uint8_t bytes)
{
        bnet_ping_t *ping = buffer;
        bnet_pong_t *pong = (bnet_pong_t *) bnet_send_buffer;
        uint8_t  pong_bytes = 0;

        switch (ping->ping_type) {
        case BNET_BROADCAST:
                if (bytes != sizeof(bnet_ping_t))
                        return;
                break;

        case BNET_TYPE_UID:
                if (bytes != sizeof(bnet_ping_t) + sizeof(board_uid_t))
                        return;

                if (uid_match_me(ping->destination) == false)
                        return;
                break;

#ifdef TARGET_VMS
        case BNET_TYPE_IMEI:
                if (bytes != sizeof(bnet_ping_t) + sizeof(imei_t))
                        return;

                if (board_info_have_imei == false)
                        return;

                if (imei_partial_match(board_info_imei, ping->destination) == false)
                        return;

                break;
#endif

        default:
                return;
        }

        pong->type = BNET_TYPE_PONG;
#ifdef TARGET_VMS
        provision_header_t *header = (provision_header_t *) PROVISION_ADDR;

        // For backwards-compatibility we have 2 device_types:
        //   Old, which just hold device_type
        //   New, which has has 0x80 set, stores device_type in the low 2 bits, and device_stage in the next 2 bits
        pong->device_type = 0x80 | BNET_DEVICE_VMS | (header->stage << 2);
#elif defined(TARGET_CONSOLE)
        pong->device_type = BNET_DEVICE_CONSOLE;
#endif

        uid_copy(pong->to_uid, ping->from_uid);
        uid_set_me(pong->from_uid);
        pong->sequence = ping->sequence;

#ifdef TARGET_VMS
        if (board_info_have_imei) {
                pong->pong_type = BNET_TYPE_IMEI;
                imei_set_me(pong->pong_data);
                pong_bytes = sizeof(bnet_pong_t) + sizeof(imei_t);
        } else
#endif
        {
                pong->pong_type = BNET_TYPE_UID;
                pong_bytes = sizeof(bnet_pong_t);
        }

        if (ping->ping_type == BNET_BROADCAST)
                bnet_delay();

        radio_send((uint8_t *)pong, pong_bytes);
}

/**
    @brief  process a pong response
    @note   A pong is a response to a ping. For the VMS, this is used for debugging
            from the console. For the OTA console, this is used to obtain the UID
            of a target system before initialing an open request. (The sequence
            usually is - ping the network with a target IMEI, and get the UID.)
*/

void
bnet_process_pong(void *buffer, uint8_t bytes)
{
        bnet_pong_t *pong = buffer;
        uint64_t imei_number;
        int device_stage;

        if (uid_match_me(pong->to_uid) == false)
                return;

        switch (pong->pong_type) {
        case BNET_TYPE_IMEI:
                if (bytes != (sizeof(bnet_pong_t) + sizeof(imei_t)))
                        return;

                // For backwards-compatibility we have 2 device_types:
                //   Old, which just hold device_type
                //   New, which has has 0x80 set, stores device_type in the low 2 bits, and device_stage in the next 2 bits
                if (pong->device_type & 0x80) {
                        device_stage = (pong->device_type & 0x7F) >> 2;
                } else {
                        device_stage = -1;
                }

                imei_number = get_uint64(pong->pong_data);
                printf("pong UID %Z IMEI %6.6d%9.9d stage %d seq %d\n", pong->from_uid, (uint32_t)(imei_number / 1000000000),
                       (uint32_t)(imei_number % 1000000000), device_stage, pong->sequence);
#ifdef TARGET_CONSOLE
                if (bnet_console_capture_pong && bnet_console_have_pong == false) {
                        bnet_console_have_pong = true;
                        uid_copy(bnet_console_pong_uid, pong->from_uid);
                }
#endif
                break;

        case BNET_TYPE_UID:
                if (bytes != sizeof(bnet_pong_t))
                        return;
                printf("pong UID %Z seq %d\n", pong->from_uid, pong->sequence);
#ifdef TARGET_CONSOLE
                if (bnet_console_capture_pong && bnet_console_have_pong == false) {
                        bnet_console_have_pong = true;
                        uid_copy(bnet_console_pong_uid, pong->from_uid);
                }
#endif
                break;

        default:
                return;
        }
}

/** @} */

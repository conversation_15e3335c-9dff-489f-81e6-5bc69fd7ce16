/** @{

    @ingroup    bnet
    @file

    @brief      BNET VMS sensor packet processing
*/

#include "pelagic.h"
#include "radio.h"
#include "bnet.h"
#include "sensor_types.h"

log_sensor_t bnet_sensor_data;

/**
    @brief  process a sensor report packet. Assume this is a thermistor value, and log it.
*/

void
bnet_process_sensor_report(void *buffer, uint8_t bytes)
{
        bnet_sensor_report_t *report = buffer;
        bnet_ack_t *ack = (bnet_ack_t *) bnet_send_buffer;

        if (!time_acquired)
                return;

        if (bytes < sizeof(bnet_sensor_report_t)) {
                BNET_WATCH("sensor-report got[%d] wanted[%d]", bytes, sizeof(bnet_sensor_report_t));
                return;
        }

        if (uid_match_me(report->to_uid) == false) {
                BNET_WATCH("sensor-report not for me");
                return;
        }

        ack->type = BNET_TYPE_SENSOR_REPORT_ACK;
        uid_set_me(ack->from_uid);
        uid_copy(ack->to_uid, report->from_uid);
        radio_send(ack, sizeof(bnet_ack_t));

        uid_copy(bnet_sensor_data.sensor_uid, report->from_uid);
        bnet_sensor_data.sensor_type        = report->sensor_type;
        bnet_sensor_data.sensor_voltage     = report->sensor_voltage;
        bnet_sensor_data.sensor_data_length = report->sensor_data_length;
        memcpy(bnet_sensor_data.sensor_data, report->sensor_data, report->sensor_data_length);

        // This is where sensor data gets recorded
        boat_log_sensor(&bnet_sensor_data);

        BNET_SENSOR_WATCH(report);

        BNET_WATCH("report uid[%Z] type[%d]", report->from_uid, report->sensor_type);
}

/** @} */

/**@{

    @ingroup    bnet
    @file
    @brief      BNET Console common routines shared between VMS & console
*/

#include "pelagic.h"
#include "bnet.h"
#include "bnet_console.h"
#include "radio.h"
#include "signals.h"

uint32_t bnet_console_ttl = 0;
uint8_t bnet_console_send_seq, bnet_console_rx_seq;
volatile bool bnet_console_opened = false;
volatile uint16_t bnet_console_rx_in = 0, bnet_console_rx_out = 0, bnet_console_rx_count = 0;
volatile uint16_t bnet_console_tx_in = 0, bnet_console_tx_out = 0, bnet_console_tx_count = 0;

volatile bool bnet_console_send_busy;
uint32_t bnet_console_send_retries;
uint32_t bnet_console_send_size;

board_uid_t bnet_console_uid;

char bnet_console_tx_data[BNET_CONSOLE_TX_SIZE];
char bnet_console_rx_data[BNET_CONSOLE_RX_SIZE];

osMutexId bnet_console_mutex;
osMutexDef(bnet_console_mutex);

/**
    @brief  initialize console mutex
*/

void
bnet_console_init()
{
        bnet_console_mutex = osMutexCreate(osMutex(bnet_console_mutex));
}

/**
    @brief  lock (mutex hold) console
*/

void
bnet_console_lock()
{
        osMutexWait(bnet_console_mutex, osWaitForever);
}

/**
    @brief  unlock (mutex release) console
*/

void
bnet_console_unlock()
{
        osMutexRelease(bnet_console_mutex);
}

/**
    @brief  queue console data for transmission to target
    @param[in]  buffer  characters to send
    @param[in]  bytes   character count
    @note   On the console image this is used to send keyboard character to the target VMS.
            On the VMS image this is used to send printf()s to the target client/console image.
*/

void
bnet_console_write(char *buffer, uint32_t bytes)
{
        uint32_t count;

        for (int retries = 0; retries < 20 && bytes > 0 && bnet_console_opened; retries++) {
                bnet_console_lock();
                if (bnet_console_tx_count < BNET_CONSOLE_TX_SIZE) {
                        count = (BNET_CONSOLE_TX_SIZE - bnet_console_tx_count);
                        if (bytes < count)
                                count = bytes;

                        for (int i = 0; i < count; i++) {
                                bnet_console_tx_data[bnet_console_tx_in] = buffer[i];
                                bnet_console_tx_in = (bnet_console_tx_in + 1) % BNET_CONSOLE_TX_SIZE;
                        }

                        bnet_console_tx_count += count;
                        bytes -= count;

                }
                bnet_console_unlock();

                if (bnet_console_tx_count >= (BNET_CONSOLE_TX_SIZE / 2))
                        osSignalSet(bnet_tid, BNET_SIGNAL_CONSOLE_SEND);

                if (bytes)
                        osDelay(100);
        }
}

/**
    @brief  signal for the queued console data to be transmitted
*/

void
bnet_console_flush()
{
        if (bnet_console_opened == false || bnet_console_tx_count == 0)
                return;

        osSignalSet(bnet_tid, BNET_SIGNAL_CONSOLE_SEND);
}

/**
    @brief  send a console command acknowledgement packet
    @param[in]  to_uid  target UID to send acknowledgement
    @param[in]  response    operation to ack
    @param[in]  sequence    operation sequence to ack
*/

void
bnet_console_ack(board_uid_t to_uid, uint8_t response, uint8_t sequence)
{
        bnet_console_op_t *ack = (bnet_console_op_t *) bnet_send_buffer;

        ack->type     = BNET_TYPE_CONSOLE;
        ack->op       = response;
        ack->sequence = sequence;

        uid_set_me(ack->from_uid);
        uid_copy(ack->to_uid, to_uid);

        radio_send((uint8_t *)ack, sizeof(bnet_console_op_t));
}

/**
    @brief  initialize/reset all BNET console related variables
*/

void
bnet_console_reset()
{
        //uart_printf("CONSOLE RESET!!!\n");
        bnet_console_opened   = false;
        bnet_console_rx_count = 0;
        bnet_console_ttl      = 0;
        bnet_console_send_seq = 0;
        bnet_console_tx_out   = 0;
        bnet_console_tx_in    = 0;
        bnet_console_tx_count = 0;
        bnet_console_rx_seq   = 0;
        bnet_console_rx_out   = 0;
        bnet_console_rx_in    = 0;
        bnet_console_rx_count = 0;
        bnet_console_send_retries = 0;
        bnet_console_send_busy    = false;
        bnet_console_send_size    = 0;
        bnet_console_send_seq     = 0;
}

/** @} */

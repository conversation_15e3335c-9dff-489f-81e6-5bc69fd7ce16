/** @{
    @ingroup    bnet
    @file
    @brief      BNET Client (old protocol)

    @note       This is the old sensor client code. Once all sensors have been updated, this can be removed.
*/

#include "pelagic.h"
#include "bnet.h"
#include "bnet_client.h"

volatile int bnet_client_count = 0;
bnet_client_t bnet_clients[BNET_CLIENT_LIMIT];

/**
    @brief  find an existing client or setup a new one if requested
    @param[in]  uid     client UID
    @param[in]  setup_if_new    false if existing, otherwise setup a new entry (if there's room)
    @return
                - A new or existing client entry
                - NULL for a supposedly existing client that was not found, or
                  NULL if there all client slots are full
*/

bnet_client_t *
bnet_find_client(board_uid_t uid, int setup_if_new)
{
        bnet_client_t *client = bnet_clients, *newclient = NULL;

        for (int i = 0; i < BNET_CLIENT_LIMIT; i++, client++) {
                if (client->ttl == 0) {
                        if (setup_if_new && newclient == NULL)
                                newclient = client;
                        continue;
                }

                if (uid_match(uid, client->client_uid)) {
                        client->ttl = rtc_read() + BNET_CLIENT_TTL;
                        return client;
                }
        }

        if (newclient == NULL)
                return NULL;

        bnet_client_count++;
        uid_copy(newclient->client_uid, uid);
        newclient->ttl = rtc_read() + BNET_CLIENT_TTL;

        return newclient;
}

/**
    @brief  age client entries and removed if time-to-live goes to zero
*/

void
bnet_client_age()
{
        uint32_t now = rtc_read();
        bnet_client_t *client = bnet_clients;

        for (int i = 0; i < BNET_CLIENT_LIMIT; i++, client++) {
                if (!client->ttl)
                        continue;

                if (client->ttl <= now) {
                        BNET_WATCH("deactivating client");
                        client->ttl = 0;
                        bnet_client_count--;
                }
        }
}

/** @} */

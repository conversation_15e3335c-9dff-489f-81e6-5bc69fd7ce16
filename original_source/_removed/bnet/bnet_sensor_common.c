/** @{

    @ingroup    bnet
    @file

    @brief     BNET Sensor common routines
*/

#include "pelagic.h"
#include "bnet.h"
#include "bnet_sensor.h"
#include "radio.h"

/**
    @brief  send a given packet and wait for an ack.
    @param[in]  ack_type    acknowledgement type to wait for
    @param[in]  packet      packet to send
    @param[in]  packet_size packet in bytes
    @param[out] ack_data    optional - area to store acknowledgement
    @param[in]  expected_size   size of packet ack to expect
    @param[in]  attempts    retransmission attempts to try
*/

bool
bnet_sensor_send_packet(uint8_t ack_type, void *packet, uint8_t packet_size, void *ack_data, uint8_t expected_size, int attempts)
{
        bnet_ack_t *ack = ack_data;
        uint32_t bytes;

        for (int i = 0; i < attempts; i++) {
                radio_send(packet, packet_size);

                bnet_sensor_create_timer();
                while (bnet_sensor_timed_out == false) {
                        bytes = radio_read(ack, BNET_SENSOR_TIMEOUT, NULL);

                        if (bytes == 0) {
                                bnet_sensor_timer_stop();
                                break;
                        }

                        if (ack->type != ack_type)
                                continue;

                        if (expected_size) {
                                if (bytes != expected_size) {
                                        BNET_WATCH("check got[%d] wanted[%d]", bytes, expected_size);
                                        continue;
                                }
                        } else {
                                if (ack_type != BNET_TYPE_FIRMWARE_SEND_ACK)
                                        continue;

                                bnet_firmware_send_ack_t *fw_ack = ack_data;
                                if (bytes != sizeof(bnet_firmware_send_ack_t)+fw_ack->length)
                                        continue;
                        }

                        if (uid_match_me(ack->to_uid) == false)
                                continue;

                        if (uid_match_host(ack->from_uid) == false)
                                continue;

                        bnet_sensor_timer_stop();

                        return true;
                }
        }

        return false;
}

/** @} */

/** @{

    @ingroup    vms
    @file

    @brief      BNET Console VMS routines
*/

#include "pelagic.h"
#include "bnet.h"
#include "bnet_console.h"
#include "console.h"
#include "radio.h"
#include "signals.h"

/**
    @brief  check to see if UID is one already connected with
*/

bool
bnet_console_own(board_uid_t from_uid)
{
        return uid_match(from_uid, bnet_console_uid);
}

/**
    @brief  process a BNET_TYPE_CONSOLE packet
    @param[in]  packet  packet to process
    @param[in]  bytes   packet size
*/

void
bnet_process_console(void *packet, uint8_t bytes)
{
        bnet_console_op_t *pkt = packet;

        if (bytes < sizeof(bnet_console_op_t))
                return;

        if (bnet_console_opened && bnet_console_ttl < clock_read()) {
                uart_printf("[BNET: TTL EXPIRED]");
                bnet_console_reset();
        }

        if (uid_match_me(pkt->to_uid) == false)
                return;

        //uart_printf("[op%x]", pkt->op);
        if (pkt->op != BNET_CONSOLE_OPEN && pkt->op != BNET_CONSOLE_CLOSE) {
                if (bnet_console_opened == false) {
                        //uart_printf("NOT OPENED\n");
                        if (pkt->op != BNET_CONSOLE_NOTCONN)
                                bnet_console_ack(pkt->from_uid, BNET_CONSOLE_NOTCONN, 0);
                        return;
                } else if (bnet_console_own(pkt->from_uid) == false) {
                        //uart_printf("NOT FOR US!\n");
                        if (pkt->op != BNET_CONSOLE_NOTCONN)
                                bnet_console_ack(pkt->from_uid, BNET_CONSOLE_BUSY, 0);
                        return;
                }

                bnet_console_ttl = clock_read() + BNET_CONSOLE_TTL;
        }

        switch (pkt->op) {
        case BNET_CONSOLE_OPEN:
                bnet_console_process_open(packet, bytes);
                return;

        case BNET_CONSOLE_CLOSE:
                bnet_console_process_close(packet, bytes);
                return;

        case BNET_CONSOLE_SEND:
                bnet_console_process_send(packet, bytes);
                return;

        case BNET_CONSOLE_SEND_ACK:
                bnet_console_process_send_ack(packet, bytes);
                return;

        case BNET_CONSOLE_KEEPALIVE:
                bnet_console_ack(pkt->from_uid, BNET_CONSOLE_KEEPALIVE_ACK, pkt->sequence);
                return;

        case BNET_CONSOLE_NOTCONN:
                //uart_printf("NOT CONNECTED RECEIVED\n");
                bnet_console_reset();
                return;

        default:
                //uart_printf("bnet console: unknown op[0x%x]\n", pkt->op);
                return;
        }
}

/**
    @brief  process a console open request. send ack back if busy or not.
*/

void
bnet_console_process_open(void *packet, uint8_t bytes)
{
        bnet_console_op_t *pkt = (bnet_console_op_t *) packet;
        bool notify = false;

        if (bytes != sizeof(bnet_console_op_t))
                return;

        if (bnet_console_opened) {
                if (bnet_console_own(pkt->from_uid) == false) {
                        bnet_console_ack(pkt->from_uid, BNET_CONSOLE_BUSY, pkt->sequence);
                        return;
                }
        } else {
                bnet_console_ttl = clock_read() + BNET_CONSOLE_TTL;
                uid_copy(bnet_console_uid, pkt->from_uid);
                notify = true;
        }

        bnet_console_ack(pkt->from_uid, BNET_CONSOLE_OPEN_ACK, 0);
        bnet_console_opened = true;

        if (notify)
                EVENT_LOG(EVT_BNET_CONSOLE_OPEN, "bnet console open");
}

/**
    @brief  process a console close request.
*/

void
bnet_console_process_close(void *packet, uint8_t bytes)
{
        bnet_console_op_t *pkt = packet;

        bnet_console_ack(pkt->from_uid, BNET_CONSOLE_CLOSE_ACK, 0);

        if (bnet_console_opened && bnet_console_own(pkt->from_uid))
                bnet_console_reset();

        EVENT_LOG(EVT_BNET_CONSOLE_CLOSE, "boatnet console close");
}

/**
    @brief  process a console send request - aka keyboard input from client.
*/

void
bnet_console_process_send(void *packet, uint8_t bytes)
{
        bnet_console_send_t *pkt = packet;
        uint8_t *rxdata;
        bool notify = true;

        if (pkt->length + sizeof(bnet_console_send_t) != bytes) {
                //uart_printf("bnet: send got [%d] wanted[%d]\n", bytes, pkt->length + sizeof(bnet_console_send_t));
                return;
        }

        // haven't seen this sequence before.
        if (pkt->sequence != bnet_console_rx_seq) {
                rxdata = pkt->data;

                bnet_console_lock();
                for (int i = 0; i < pkt->length; i++) {
                        bnet_console_rx_data[bnet_console_rx_in] = rxdata[i];
                        bnet_console_rx_in = (bnet_console_rx_in + 1) % BNET_CONSOLE_RX_SIZE;

                        if (bnet_console_rx_count < BNET_CONSOLE_RX_SIZE) {
                                bnet_console_rx_count++;
                        } else {
                                bnet_console_rx_out = (bnet_console_rx_out + 1) % BNET_CONSOLE_RX_SIZE;
                        }
                }

                bnet_console_rx_seq = pkt->sequence;
                bnet_console_unlock();
        } else {
                //uart_printf("boatnet: saw rexmit packet\n");
                notify = false;
        }

        bnet_console_ack(pkt->from_uid, BNET_CONSOLE_SEND | BNET_CONSOLE_ACK, pkt->sequence);

        //pr_debug("boatnet: sent receive ack\n");

        if (notify)
                osSignalSet(console_tid, CONSOLE_SIGNAL_RX);
}

/**
    @brief  read available characters from the console input queue
    @param[out] buffer  area to store characters
    @param[in]  bytes   maximum size of buffer
    @return characters stored into buffer
*/

uint32_t
bnet_console_read(char *buffer, uint32_t bytes)
{
        uint32_t count = 0;

        bnet_console_lock();
        while (bnet_console_rx_count > 0 && bytes > 0) {
                *buffer++ = bnet_console_rx_data[bnet_console_rx_out];
                bnet_console_rx_out = (bnet_console_rx_out + 1) % BNET_CONSOLE_RX_SIZE;
                bytes--;
                bnet_console_rx_count--;
                count++;
        }
        bnet_console_unlock();

        return count;
}

/** @} */

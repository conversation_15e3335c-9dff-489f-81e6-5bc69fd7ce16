/** @{

    @ingroup    bnet
    @file
    @brief      BNET Sensor Reporting
*/

#include "pelagic.h"
#include "radio.h"
#include "bnet.h"
#include "bnet_sensor.h"
#include "temp.h"
#include "system_file.h"
#include "sensor_types.h"

/**
    @brief  send a thermistor report to a bonded VMS
    @note   This will send the thermistor value to a previously bonded VMS. At regular
            intervals (roughly every 6 hours) the routine is requested to reset the radio
            power level and then ramp up to re-evaulate the bond.
    @param[in]  reeval  true if the radio power level start low and then ramped up
    @return true if the report was successfully acked.
*/

bool
bnet_sensor_report(bool reeval)
{
        bool result;
        bnet_sensor_report_t *report = (bnet_sensor_report_t *) bnet_send_buffer;

        report->type         = BNET_TYPE_SENSOR_REPORT;
        uid_set_me(report->from_uid);
        uid_copy(report->to_uid, SYS_REG_FILE->host_uid);

        report->sensor_type  = SENSOR_THERMISTOR;
        set_uint16(report->sensor_data, thermistor_read());

        BNET_WATCH("report reeaval %d", reeval);

        if (reeval == false) {
                return bnet_sensor_send_packet(BNET_TYPE_SENSOR_REPORT_ACK,
                                               report, sizeof(bnet_sensor_report_t) + SENSOR_THERMISTOR_SIZE,
                                               bnet_receive_buffer, sizeof(bnet_ack_t), BNET_SENSOR_REPORT_TRIES);
        }

        for (int level = 0; level < RADIO_POWER_LEVEL_LIMIT; level++) {
                BNET_WATCH("report level %d", level);
                radio_set_power_level(level);
                result = bnet_sensor_send_packet(BNET_TYPE_SENSOR_REPORT_ACK,
                                                 report, sizeof(bnet_sensor_report_t) + SENSOR_THERMISTOR_SIZE,
                                                 bnet_receive_buffer, sizeof(bnet_ack_t), BNET_SENSOR_REPORT_TRIES);

                if (result) {
                        SYS_REG_FILE->radio_power = level;
                        SYS_REG_FILE->host_ttl = BNET_SENSOR_TTL;
                        return true;
                }
        }

        return false;
}

/** @} */

/** @{

    @ingroup    bnet
    @file
    @brief      BNET Console Client routines
*/

#include "pelagic.h"
#include "alarm.h"
#include "bnet.h"
#include "bnet_console.h"
#include "radio.h"
#include "signals.h"

extern osThreadId main_tid;

volatile bool bnet_waiting_for_open;

/**
    @brief  Send console command packet and wait for acknowledgement
    @param[in]  op  Console command (BNET_CONSOLE_*)
    @return true if acknowledgement was received.
*/

bool
bnet_console_send_command(uint8_t op)
{
        osEvent result;
        bnet_console_op_t cmd;

        cmd.type     = BNET_TYPE_CONSOLE;
        cmd.op       = op;
        cmd.sequence = 0;

        uid_set_me(cmd.from_uid);
        uid_copy(cmd.to_uid, bnet_console_uid);

        for (int retries = 0; retries < BNET_CONSOLE_RETRIES; retries++) {
                radio_send((uint8_t *)&cmd, sizeof(bnet_console_op_t));
                result = osSignalWait(BNET_CONSOLE_SIGNAL_CMD_ACK, 300);

                if (result.status == osEventSignal)
                        return true;
        }

        uart_printf("[send-cmd fail op=%x]\n", op);
        return false;
}

/**
    @brief  process a console packet
    @param[in]  packet  received packet
    @param[in]  bytes   packet length
*/

void
bnet_console_process(void *packet, uint8_t bytes)
{
        bnet_console_op_t *pkt = packet;
        bnet_console_t *data;

        if (pkt->type == BNET_TYPE_PONG) {
                bnet_process_pong(packet, bytes);
                return;
        }

        if (pkt->type != BNET_TYPE_CONSOLE)
                return;

        if (bytes < sizeof(bnet_console_op_t)) {
                //uart_printf("bnet: got [%d] wanted at least [%d] \n", bytes, sizeof(bnet_console_op_t));
                return;
        }

        if (uid_match_me(pkt->to_uid) == false) {
                //uart_printf("not for me\n");
                return;
        }


        if (bnet_waiting_for_open) {
                if (uid_match(pkt->from_uid, bnet_console_uid) == false) {
                        if (pkt->op != BNET_CONSOLE_NOTCONN)
                                bnet_console_ack(pkt->from_uid, BNET_CONSOLE_NOTCONN, 0);
                        return;
                }
                // Allow the command thru - there might be an out of order problem here.
        } else if ((bnet_console_opened == false && pkt->op != BNET_CONSOLE_OPEN_ACK)
                   || uid_match(pkt->from_uid, bnet_console_uid) == false) {
                //uart_printf("Uh, who? opened %d, op 0x%x, from %Z\n", bnet_console_opened, pkt->op, pkt->from_uid);

                if (pkt->op != BNET_CONSOLE_NOTCONN)
                        bnet_console_ack(pkt->from_uid, BNET_CONSOLE_NOTCONN, 0);
                return;
        }

        bnet_console_ttl = clock_read() + BNET_CONSOLE_CLIENT_TTL;
        switch (pkt->op) {
        case BNET_CONSOLE_SEND_ACK:
                bnet_console_process_send_ack(packet, bytes);
                break;

        case BNET_CONSOLE_SEND:
                data = packet;
                if (bytes != sizeof(bnet_console_send_t)+data->length) {
                        //uart_printf("send: got[%d] expected[%d]\n", bytes, sizeof(bnet_console_send_t)+data->length);
                        return;
                }

                if (data->sequence != bnet_console_rx_seq) {
                        serial_write(&console_uart, (char *)data->data, data->length);
                        bnet_console_rx_seq = data->sequence;
                }
                bnet_console_ack(bnet_console_uid, BNET_CONSOLE_SEND_ACK, data->sequence);
                break;

        case BNET_CONSOLE_KEEPALIVE_ACK:    // bnet_console_ttl is updated above which the thread will notice
                osSignalSet(bnet_console_keepalive_tid, BNET_CONSOLE_SIGNAL_KEEPALIVE_ACK);
                //uart_printf("[<KA ACK]");
                break;

        case BNET_CONSOLE_OPEN_ACK:
        case BNET_CONSOLE_CLOSE_ACK:
                osSignalSet(main_tid, BNET_CONSOLE_SIGNAL_CMD_ACK);
                break;
        }
}

/**
    @brief  attempt to connect to a server & send characters
    @param[in]  server_uid  system UID to attempt connection
    @note   The server_uid target system is sent a request for a console open,
            if that is successful the routine will loop waiting for characters
            from the keyboard, and send them to the target.
            Character sent by the target a handled by another thread (bnet_thread).
*/

void
bnet_console_client_connect(board_uid_t server_uid)
{
        char ch;
        uint16_t signals;

        bnet_console_reset();
        uid_copy(bnet_console_uid, server_uid);

        uart_printf("Trying %Z\n", server_uid);

        if (bnet_console_send_command(BNET_CONSOLE_OPEN) == false) {
                uart_printf("device did not respond.\n");
                return;
        }

        uart_printf("\nconnected - type <CTRL>-C to exit\n\n");
        bnet_console_opened = true;

        radio_flush();
        while (bnet_console_opened) {
                serial_read_signal(&console_uart, &ch, 1, &signals);

                if ((signals & BNET_CONSOLE_SIGNAL_CLOSE) != 0) {
                        uart_printf("\n** Connection has been lost.\n");
                        break;
                }

                if (bnet_console_opened == false)
                        break;

                if (ch == '\03') {
                        // Control C - end connection
                        bnet_console_send_command(BNET_CONSOLE_CLOSE);
                        bnet_console_reset();
                        uart_printf("\n** User closed connection\n");
                        return;
                }

                bnet_console_write(&ch, 1);
                bnet_console_flush();
        }

        uart_printf("\n** Connection closed\n");
}

/**
    @brief  send a command over and over to target system for testing purposes
    @note   the target system is opened, and the command "stats" is sent over and
            over. This exercises the code on both sides to see if everything is working.
    @param[in]  server_uid  target UID to connect to
*/

void
bnet_console_client_test(board_uid_t server_uid)
{
        uint32_t started;

        bnet_console_reset();
        uid_copy(bnet_console_uid, server_uid);

        uart_printf("Trying %Z\n", server_uid);
        bnet_waiting_for_open = true;

        if (bnet_console_send_command(BNET_CONSOLE_OPEN) == false) {
                bnet_waiting_for_open = false;
                uart_printf("device did not respond.\n");
                return;
        }

        uart_printf("\nconnected!\n");
        bnet_console_opened = true;
        bnet_waiting_for_open = false;
        radio_flush();

        started = rtc_read();

        while (bnet_console_opened) {
                if (have_esc()) {
                        // Control C - end connection
                        bnet_console_send_command(BNET_CONSOLE_CLOSE);
                        uart_printf("\nuser requested termination\n");
                        break;
                }

                char *stats = "stats\n";

                for (int i = 0; i < 6; i++) {
                        bnet_console_write(&stats[i], 1);
                        osDelay((rand() % 100) + 100);
                }
                bnet_console_flush();
                osDelay(((rand() % 10)*10) + 500);

        }

        bnet_console_reset();
        uart_printf("\nConnection closed after %d seconds\n", rtc_read() - started);
}

/**
    @brief  packet receipt thread - handles all incoming traffic
*/

void
bnet_thread(void const *arg)
{
        uint32_t bytes;
        uint16_t signals;
        int8_t rssi;
        alarm_t xmit_alarm;

        alarm_start_periodic(&xmit_alarm, 1);

        for (;;) {
                bytes = radio_read_signal(bnet_receive_buffer, osWaitForever, &rssi, &signals);

                if (signals & BNET_SIGNAL_CONSOLE_SEND)
                        bnet_console_send_signal();

                if (signals & ALARM_SIGNAL_BUZZER)
                        bnet_console_send_timeout();

                if (bytes) {
                        if (bnet_packet_watch)
                                bnet_decode_packet(bnet_receive_buffer, bytes, rssi);

                        bnet_console_process(bnet_receive_buffer, bytes);
                }
        }
}

/** @} */

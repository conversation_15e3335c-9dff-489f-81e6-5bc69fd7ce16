#ifndef __BNET_CONSOLE_H__
#define __BNET_CONSOLE_H__

enum {
        BNET_CONSOLE_ACK           = 0x80,

        BNET_CONSOLE_OPEN          = 0x01, // request to open console
        BNET_CONSOLE_OPEN_ACK      = (BNET_CONSOLE_OPEN | BNET_CONSOLE_ACK),
        BNET_CONSOLE_CLOSE         = 0x02, // close console
        BNET_CONSOLE_CLOSE_ACK     = (BNET_CONSOLE_CLOSE | BNET_CONSOLE_ACK),
        BNET_CONSOLE_SEND          = 0x03, // send data or receive input
        BNET_CONSOLE_SEND_ACK      = (BNET_CONSOLE_SEND | BNET_CONSOLE_ACK),

        BNET_CONSOLE_BUSY          = 0x04, // console opened by someone else
        BNET_CONSOLE_NOTCONN       = 0x05,// not connected
        BNET_CONSOLE_KEEPALIVE     = 0x06,
        BNET_CONSOLE_KEEPALIVE_ACK = (BNET_CONSOLE_KEEPALIVE | BNET_CONSOLE_ACK),

};

enum {
        BNET_CONSOLE_TTL               = 30, // how long before the connect times out
        BNET_CONSOLE_CLIENT_TTL        = 30,
        BNET_CONSOLE_RETRIES           = 10,

        BNET_CONSOLE_RX_SIZE           = 16,
        BNET_CONSOLE_TX_SIZE           = 1024,

        BNET_CONSOLE_ACK_TIMEOUT       = 500,
};

typedef struct
__attribute__ ((packed))
{
        uint8_t     type;
        board_uid_t to_uid;
        board_uid_t from_uid;
        uint8_t     op;
        uint8_t     sequence;
}
bnet_console_op_t;

typedef struct
__attribute__ ((packed))
{
        uint8_t     type;
        board_uid_t to_uid;
        board_uid_t from_uid;
        uint8_t     op;
        uint8_t     sequence;
        uint8_t     length;
        uint8_t     data[];
}
bnet_console_send_t;

#define BNET_CONSOLE_DATA_SIZE  (BNET_PACKET_SIZE - sizeof(bnet_console_send_t))

void bnet_console_init();
void bnet_process_console(void *packet, uint8_t bytes);

uint32_t bnet_console_read(char *buffer, uint32_t bytes);
void bnet_console_write(char *buffer, uint32_t bytes);
void bnet_console_process_open(void *packet, uint8_t bytes);
void bnet_console_process_close(void *packet, uint8_t bytes);
void bnet_console_process_send(void *packet, uint8_t bytes);
void bnet_console_process_send_ack(void *packet, uint8_t bytes);
void bnet_console_process_keep_alive(void *packet, uint8_t bytes);
void bnet_console_lock();
void bnet_console_unlock();
void bnet_console_ack(board_uid_t to_uid, uint8_t response, uint8_t sequence);
void bnet_console_flush();
void bnet_console_reset();
void bnet_console_send_timeout();
void bnet_console_send_signal();

#ifdef TARGET_CONSOLE
void bnet_console_client_connect(board_uid_t uid);
extern volatile bool bnet_waiting_for_open;
#endif

extern uint8_t bnet_console_send_seq, bnet_console_rx_seq;
extern volatile uint16_t bnet_console_rx_in, bnet_console_rx_out, bnet_console_rx_count;
extern volatile uint16_t bnet_console_tx_in, bnet_console_tx_out, bnet_console_tx_count;
extern volatile bool bnet_console_opened;
extern board_uid_t bnet_console_uid;

extern char bnet_console_tx_data[], bnet_console_rx_data[];

extern board_uid_t bnet_console_uid;
extern uint32_t bnet_console_ttl;

extern volatile bool bnet_console_send_busy;
extern uint32_t bnet_console_send_retries;
extern uint32_t bnet_console_send_size;


#ifdef TARGET_CONSOLE
extern volatile bool bnet_console_capture_pong, bnet_console_have_pong;
extern board_uid_t bnet_console_pong_uid;
extern osThreadId bnet_console_keepalive_tid;
#endif

#endif

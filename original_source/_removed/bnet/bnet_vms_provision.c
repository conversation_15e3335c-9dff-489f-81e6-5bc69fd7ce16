/** @{

    @ingroup    bnet
    @file

    @brief      BNET VMS provision packet processing
*/

#include "pelagic.h"
#include "radio.h"
#include "bnet.h"
#include "provision.h"
#include "mcu_sleep.h"

/**
    @brief  process a provision packet.
*/

void
bnet_process_provision(void *buffer, uint8_t bytes)
{
        bnet_provision_t *provision_pkt = buffer;
        bnet_provision_ack_t *ack = (bnet_provision_ack_t *) bnet_send_buffer;
        int sleep_time = 0;

        if (provision_pkt->provision_type == PROVISION_TYPE_IMEI) {
                if (!imei_match_me(provision_pkt->to_imei))
                        return;
        } else if (provision_pkt->provision_type != PROVISION_TYPE_BROADCAST) {
                uart_printf("Breaking for no broadcast match\n");
                return;
        }

        if (provision_pkt->provision_mode == PROVISION_STAGE_SHIPPING) {
                uart_printf("provision_shipping bnet_process_provision\n");
                provision_shipping();
                sleep_time = SHIPPING_SLEEP;
        } else if (provision_pkt->provision_mode == PROVISION_STAGE_DEPLOYED) {
                provision_deployed();
                sleep_time = 1;
        } else if (provision_pkt->provision_mode == PROVISION_STAGE_FACTORY) {
                provision_factory();
                sleep_time = 1;
        }

        ack->type = BNET_TYPE_PROVISION_ACK;
        ack->provision_mode = provision_pkt->provision_mode;
        uid_copy(ack->to_uid, provision_pkt->from_uid);
        imei_set_me(ack->from_imei);

        // Give plenty of time for the requestor to know you received this, and prevent any flash race conditions
        for (int i=0; i<10; i++) {
                radio_send((uint8_t *)ack, sizeof(bnet_provision_ack_t));
                osDelay(1000);
        }

        mcu_sleep(sleep_time);
}

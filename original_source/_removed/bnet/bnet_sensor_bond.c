/** @{

    @ingroup    bnet
    @file
    @brief      BNET Sensor Bonding
*/

#include "pelagic.h"
#include "radio.h"
#include "bnet.h"
#include "bnet_sensor.h"
#include "system_file.h"

/**
    @brief  wait for an ack from a VMS to bond with
    @param[in]  level   radio power level currently operating at
    @note   Wait for a response from any VMS - the VMS UID and power level
            will be stored in the system register file for later use.
    @return true if an ack was received.
*/

bool
bnet_sensor_wait_bond_ack(uint8_t level)
{
        bnet_ack_t *ack = (bnet_ack_t *) bnet_receive_buffer;
        int8_t rssi;
        uint8_t bytes;

        bnet_sensor_create_timer();

        while (bnet_sensor_timed_out == false) {
                bytes = radio_read(bnet_receive_buffer, BNET_SENSOR_TIMEOUT, &rssi);

                if (bytes == 0) {
                        bnet_sensor_timer_stop();
                        return false;
                }

                if (bytes != sizeof(bnet_ack_t)) {
                        BNET_WATCH("bond bytes got [%d] wanted [%d]", bytes, sizeof(bnet_ack_t));
                        continue;
                }

                if (ack->type != BNET_TYPE_SENSOR_BOND_ACK) {
                        BNET_WATCH("bond type [0x%x]", ack->type);
                        continue;
                }

                if (uid_match_me(ack->to_uid) == false) {
                        BNET_WATCH("bond not for me uid=[%Z]", ack->to_uid);
                        continue;
                }

                bnet_sensor_timer_stop();

                SYS_REG_FILE->radio_power = level;
                SYS_REG_FILE->host_ttl = BNET_SENSOR_TTL;
                SYS_REG_FILE->have_host = true;
                uid_copy(SYS_REG_FILE->host_uid, ack->from_uid);

                BNET_WATCH("bond uid power[%d] vms[%Z]",  level, ack->from_uid);
                return true;
        }

        return false;
}

/**
    @brief  bond with a VMS
    @note   The radio power level starts low and ramps up to RADIO_POWER_LEVEL_LIMIT.
            There are two attempts at each power level before moving up unless a
            response is received.

            Running thru the power levels is intended to bond with the closest VMS and
            save power at the same time.
*/

bool
bnet_sensor_bond()
{
        bnet_sensor_bond_req_t   req;

        BNET_WATCH("bond start");

        req.type = BNET_TYPE_SENSOR_BOND;
        uid_set_me(req.from_uid);

        for (int level = 0; level <= RADIO_POWER_LEVEL_LIMIT; level += 2) {
                radio_flush();
                radio_set_power_level(level);

                for (int attempts = 0; attempts < 2; attempts++) {
                        radio_send(&req, sizeof(req));
                        if (bnet_sensor_wait_bond_ack(level))
                                return true;
                }
        }

        BNET_WATCH("bond failure");
        SYS_REG_FILE->have_host = false;

        return false;
}

/** @} */

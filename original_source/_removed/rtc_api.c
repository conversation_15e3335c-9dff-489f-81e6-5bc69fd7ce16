#include "pelagic.h"
#include "rtc_api.h"
#include "PeripheralPins.h"
#include "system_file.h"
#include "alarm.h"

void rtc_tick(void const *arg);

osTimerDef(rtc_timer, rtc_tick);
osTimerId rtc_timer_id;

volatile uint32_t rtc_clock, epoch_clock;

void rtc_soft_init();

uint8_t
rtc_status()
{
        return (SYS_REG_FILE->rtc_status & RTC_STATUS_MASK);
}

void
rtc_restore_time(uint32_t adjust)
{
        if (SYS_REG_FILE->timestamp == 0)
                return;

        rtc_write(SYS_REG_FILE->timestamp + adjust);
        SYS_REG_FILE->timestamp = 0;

        if (SYS_REG_FILE->rtc_status & RTC_STATUS_HAVE_TIME) {
#ifdef TARGET_VMS
                time_acquired = true;
#endif
                SYS_REG_FILE->rtc_status &= ~RTC_STATUS_HAVE_TIME;
        }
}

void
rtc_reset_time()
{
        rtc_write(0);
        epoch_clock = 0;
        SYS_REG_FILE->timestamp = 0;

#ifdef TARGET_VMS
        time_acquired = false;
#endif
        SYS_REG_FILE->rtc_status &= ~RTC_STATUS_HAVE_TIME;
}

void
rtc_init(void)
{
        uint32_t test;
        uint32_t adjust = 0;

        if (rtc_status() == RTC_STATUS_USE_SOFTWARE) {
                rtc_soft_init();
                return;
        }

        // enable RTC clock
        SIM->SCGC6 |= SIM_SCGC6_RTC_MASK;
        pinmap_pinout(PinMap_RTC[0].pin, PinMap_RTC);        //Map RTC clk input (if not NC)
        SIM->SOPT1 &= ~SIM_SOPT1_OSC32KSEL_MASK;
        SIM->SOPT1 |= SIM_SOPT1_OSC32KSEL(RTC_CLKIN);

        // Don't clear the RTC if waking up from VLLS state.
        if ((RCM->SRS0 & RCM_SRS0_WAKEUP_MASK) == 0)
                RTC->TSR = 1;

        // enable counter
        RTC->SR |= RTC_SR_TCE_MASK;

        // Now test to make sure its actually running
        if (rtc_status() != RTC_STATUS_USE_XCAL) {
                test = RTC->TSR;
                osDelay(1100);

                if (test == RTC->TSR) {
                        RTC->SR &= ~RTC_SR_TCE_MASK;    // kill it
                        rtc_soft_init();
                        SYS_REG_FILE->rtc_status = RTC_STATUS_USE_SOFTWARE;
                        return;
                }

                SYS_REG_FILE->rtc_status = RTC_STATUS_USE_XCAL;
                adjust = 1;
        }

        rtc_restore_time(adjust);

        NVIC_SetVector(RTC_Seconds_IRQn, (uint32_t) rtc_tick);
        NVIC_EnableIRQ(RTC_Seconds_IRQn);
        RTC->IER |= RTC_IER_TSIE_MASK;
}

uint32_t
rtc_read(void)
{
        return rtc_clock;
}

void
rtc_write(uint32_t time)
{
        rtc_clock = time;

        if (rtc_status() != RTC_STATUS_USE_XCAL)
                return;

        int_disable();
        // disable counter
        RTC->SR &= ~RTC_SR_TCE_MASK;

        // we do not write 0 into TSR
        // to avoid invalid time
        if (time == 0)
                time = 1;

        // write seconds
        RTC->TSR = time;

        // re-enable counter
        RTC->SR |= RTC_SR_TCE_MASK;
        int_enable();
}

void
rtc_soft_init()
{
        rtc_timer_id = osTimerCreate(osTimer(rtc_timer), osTimerPeriodic, NULL);
        osTimerStart(rtc_timer_id, 1000);

        rtc_restore_time(0);
}

void
rtc_tick(void const *arg)
{
        rtc_clock++;
        epoch_clock++;
        alarm_tick();
}

void
rtc_save(uint32_t future)
{
        SYS_REG_FILE->timestamp = future + rtc_clock;

#ifdef TARGET_VMS
        if (time_acquired)
#endif
                SYS_REG_FILE->rtc_status |= RTC_STATUS_HAVE_TIME;
}

uint32_t
clock_read()
{
        return epoch_clock;
}

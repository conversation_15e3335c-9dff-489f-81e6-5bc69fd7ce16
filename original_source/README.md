## Prerequisites
#### Install the latest GNU ARM embedded processor toolchain

  * Download and install from https://launchpad.net/gcc-arm-embedded
  * Set $PATH to include the bin directory (e.g. `PATH=$PATH:/opt/gcc-arm-none-eabi-4_9-2015q2/arm-none-eabi/bin`)
  * LMXXX - I needed: PATH=/Users/<USER>/gcc-arm-none-eabi-4_9-2015q3/bin:$PATH

#### Install the latest JLink software from Segger

* Download and install from https://www.segger.com/jlink-software.html
* Under Mac OS X the package will install into /Applications/SEGGER

#### Install the xcode commandline tools

If you install the full xcode crap it's 7GB and takes forever.  If you go here

http://stackoverflow.com/questions/10335747/how-to-download-xcode-4-5-6-7-and-get-the-dmg-file

it will send you to here:

https://developer.apple.com/downloads/

Find the Command Line Tools (OS X $YOUR_VERSION) and click the "+" and there is a
link to a 157MB dmg that downloads instantly.

## Compiling
To compile for the VMS:

    $ make VERS=NN vms

where NN is the PCB version number.
05 = V.05 & V.051 Apr 2015
10 = V.06 & V1.0 (Fall 2015) (No NFC)
12 = V1.2 (2015-11-27)

For V1.0+ it is cc1200. To override this, give the RADIO=type option. (e.x. RADIO=cc1200). Use RADIO=none for boards that do not have any radios.


The following files will be created:

* images/vms-NN-RADIO-TAG.bin      - BIN format image to be placed on the board
* images/vms-NN-RADIO-TAG.elf      - ELF format image used for debugging
* images/vms-NN-RADIO-TAG.map      - Linker map

For example: images/vms-10-cc1200-v2.2.elf is for a VMS V1.0 board with a cc1200 and the git tag of v2.2

To compile for the sensor:

    $ make VERS=nn sensor

Makefile commands:

    $ make VERS=nn {vms,sensor,testbox,console} - compile vms, sensor, testbox or console images
    $ make VERS=nn {vms,sensor,testbox,console}-flash - compile image then flash
    $ make VERS=nn {vms,sensor,testbox,console}-clean - cleans up all objects, and related image files for VERS

    $ make buildall       - build vms, sensor, testbox and console for cc1200 and v.05 & v1.0 variations
    $ make superclean     - removes all compiled objects and images for all versions.


##### Radio Compile Flags
* RADIO=cc1200    compiles with the cc1200 driver and BNET code
* RADIO=none      compiles with no radio and no BNET code

##### VMS: Provision Compile Flags
* PROVISION=factory When erased/first booted, the board will start in Factory mode
* PROVISION=shipping When erased/first booted, the board will start in Shipping mode
* PROVISION=deployed When erased/first booted, the board will start in Deployed mode

##### VMS: Misc Compile Flags
* NO_SOLAR=1 solar_read will always give a solar present value

##### VMS: Factory Compile Flags
* NEW_FACTORY=1 Use the new factory code (for test fixtures)
* NO_FACTORY=1  compile without the factory code. Upon boot, the board will enter shipping mode.
* NO_GPS_LOCK=1 do not run the GPS satellite lock factory test
* STANDALONE=1  special compile mode intended for prototype testing. The board will imediatelly head to a factory test and not use the radio.
* SEND_DATA=1  use SEND_DATA to send the factory results instead of SEND_TEST. intended for local San Francisco assembly.

##### VMS: Factory Test Helpers
* FT_FIELD=1 compiles a normal VMS image with a some basic testbox code - intended to unstick VMSes that were installed in the field that are stuck in the factory test loop.

##### Stack Usage Compile Flag
* STACK_USAGE=1 debugging aid - used to estimate function call depth and stack usage. Use tools/avstack.pl to analyze.

## Testing An Old Release

* `git checkout TAG`
* `make clean`
* For general testing, the following should suffice:
  * `make VERS=nn vms-flash`
* For testing "from birth", (to ensure the settings stuff works, for example) use this:
  * `make PROVISION=shipping VERS=nn vms-flash-erase`
  * Apply "sun"
* To restore your tree: `git checkout master`

# Release Notes

### _Betas

#### VTS ******** -- commit Aug 12 2018

* add syncword command to console image, syntax:

   syncword [old | new]

   With no argument, simply report the current sync bytes (by reading from
   the 4 registers on the radio chip)

#### VTS ******** -- commit Aug 11 2018

* make 1.3 hardware use the new radio sync word

## VTS 2.6.11 -- commit Jul 30 2018

* Formal support for 1.3 hardware (3G modem) -- see betas below
* After FW upgrade, only gps/event flash partitions if record versions changed
* fix two minor bugs in modem_thread that prevented gofast mode from working
  exactly as expected
* add version 1.3 hardware to upload.sh
* Change lower bound for GPS periodic settings to 1 (from 15 and 45)
* send_fw and send_settings scripts moved to pdstools repo

### _Betas_

#### VTS ******** -- commit Jul 20 2018

* merge 3G debugging branch

#### VTS ******** -- commit Jul 20 2018

* After FW upgrade, only gps/event flash partitions if record versions changed

#### VTS ********-FOR_GABOR-n Jul 6 2018

* special branch for debugging problems with 3G modem
* increase modem power on delay to 10 seconds
* add gsm on, off, check, disable and enable commands
* increase modem receive timeout to 30 seconds for debugging (reverted)
* additional instrumentation for gsm watch to monitor FW update
* add crc expected and received to firmware crc error event
* on firmware update, shut the modem down gracefully before rebooting

#### VTS ******** -- commit Jun 17 2018

* fix two minor bugs in modem_thread that prevented gofast mode from working
  exactly as expected

#### VTS ******** -- commit Jun 15 2018

* add version 1.3 hardware to upload.sh

#### VTS ******** -- commit Jun 15 2018

* Add support for 3G modem on version 1.3 hardware
  * continue to use old radio sync word by -DUSE_OLD_SYNCWORD
* Various tools/script updates
* Change lower bound for GPS periodic settings to 1 (from 15 and 45)

## VTS 2.6.10 -- commit Apr 29 2018

* run GPS in hires for the entirely of the wake from park decision
* add `parking use_gps` boolean settings to control whether or not
  GPS is used by parking/wakeup code
* add helm job to watch the GPS, it will emit an event message
  when GPS fix is acquired or lost
* before parking, if battery level is 3.3v (BATTERY_MODEM_VOLTAGE_PREPARK)
  then upload data
* refactor/simplify the parking logic (only the park side, the wake-from-park
  is unchanged) -- the FW now has 5 minutes of location samples at 1 minute
  granularity allowing questions like where was I 5 minutes ago
* fix bug where if no sun, the extra parked and upside down bits
  would be erroneously cleared
* add a "gofast" mode to the modem and appropriate settings
  * modem gofast_interval _seconds_
  * modem gofast_volts _battery_potatoes_
  When battery is above gofast_volts _and_ last upload is
  more than gofast_interval, then attempt an upload
* increment PDS_EVENT_LOG_VERSION/EVT_MSG_VERSION
* fix bug where RTC would be lost across FW upgrades

### _Betas_

#### VTS 2.6.9.7 -- commit Apr 14 2018

* run GPS in hires for the entirely of the wake from park decision
* add `parking use_gps` boolean settings to control whether or not
  GPS is used by parking/wakeup code
* make the gpswatch helm job work correctly when low-res GPS loses fix

#### VTS 2.6.9.6 -- commit Apr 8 2018

* add helm job to watch the GPS, it will emit an event message
  when GPS fix is acquired or lost
* before parking, if battery level is 3.3v (BATTERY_MODEM_VOLTAGE_PREPARK)
  then upload data
* refactor/simplify the parking logic (only the park side, the wake-from-park
  is unchanged)

#### VTS 2.6.9.5 -- commit Apr 4 2018

* fix bug where if no sun, the extra parked and upside down bits
  would be erroneously cleared
  
#### VTS ******* -- commit Apr 3 2018

* add a "gofast" mode to the modem and appropriate settings
  * modem gofast_interval _seconds_
  * modem gofast_volts _battery_potatoes_
  When battery is above gofast_volts _and_ last upload is
  more than gofast_interval, then attempt an upload

#### VTS ******* -- commit Apr 2 2018

* previous commit was not quite right...
  * PDS_EVENT_LOG_VERSION is the version number used in the event log
  * EVT_MSG_VERSION was used by the script that creates labels.rb
  * make them the same

#### VTS ******* -- commit Apr 2 2018

* late increment to EVT_MSG_VERSION

#### VTS ******* -- commit Apr 1 2018

* bug hunt for losing RTC across FW upgrades

## VTS 2.6.9 -- commit Mar 31 2018

* steal two bits from flags byte of the board_log event:
  * 0x4 means the VTS is upside-down
  * 0x8 means the VTS is parked
* increase default park_log_interval and park_time to 30 and 60 minutes respectively
* add new event for when we wake from park because we didn't have enough
  battery to run GPS
* fix minor bug that prevented correct battery logging after a boot where
  the battery was normal
* in very low light, the charge flags from the hardware are garbage so
  send zeroes in the board_log event

### _Betas_

#### VTS ******* -- commit Mar 19 2018

* fix race/crash caused by parking code attempting uploads while modem upload
  was already in progress
* turn on GPS during parked heartbeat
* remove deprecated dock support and settings

## VTS 2.6.8 -- commit Mar 7 2018

* bundle up the changes in the 2.6.7.x series

### _Betas_

#### VTS 2.6.7.3 -- commit Feb 28 2018

* fix bug with new event parameter signed types

#### VTS ******* -- commit Feb 26 2018

* improve wake from park logic to filter GPS reading with bad PDOP values

#### VTS ******* -- commit Feb 23 2018

* fix bug that prevented parking on water when GPS is in low-res mode
* change parking logic to consider PDOP when making GPS based decisions
* add support to event logs for signed integer types
* fix time adjust event log to used a signed type

## VTS 2.6.7 -- commit Feb 7 2018

* fix bug in which VTSes waking from shipping mode would have zeroes instead
  of correct defaults for parking settings

## VTS 2.6.6 -- commit Feb 7 2018

* fix broken default setting for parking metres_allowed
* add info to parking events to help see how decisions are being made
  * lat, lon and time of park event
  * metres_allowed when deciding if we are still parked

## VTS 2.6.5 -- commit Feb 5 2018

* move upload stats logging to `modem_attempt_upload()` so it gets reported correctly
  when called as part of a heartbeat upload

## VTS 2.6.4 -- commit Feb 3 2018

* add `set gps freq` command
  * controls the logging of GPS coordinates
  * a value between 0 and 254, default is 1
  * for a given value N, location will be logged every N seconds
  * 0 is magic, disables GPS location logging

## VTS 2.6.3 -- commit Feb 3 2018

* add event log of how many bytes were send and received in a connection

### _Betas_

#### VTS ******* -- commit Feb 1 2018

* make board_log() log data whether or not we have an accurate date from GPS

#### VTS 2.6.2.4 -- commit Jan 2018

* before going to park sleep, there was an unconditional modem upload attempt.
  This logic was wrong (particularly when sleeping and waking frequently as might
  happen when parked on water). It has been made conditional to correctly honour 
  the other modem timing settings

#### VTS 2.6.2.3 -- commit Jan 2018

* modem success upload messages were not being send to the DB -- fix
* make new parking settings, "set parking metres_allowed" -- this parameter
  used to be hard coded as 50 metres
* change accel thread to not reset the park_time clock until movement has been
  detected at least 5 times in the last 32 seconds
* when waking from park and checking with GPS, take 10 location samples and make
  sure that at least 6 of them fall within the metres_allowed radius

#### VTS 2.6.2.2 -- commit Jan 2018

* make modem settings accessible to set command
* fix modem thread so that those settings are actually used

#### VTS 2.6.2.1 -- commit Jan 2018

* internal dev tag for next gen 1.3 hardware (3g-modem branch)

## VTS 2.6.2 -- commit Jan 18 2018

Gather up the fixes from the betas. They were:

* create new event types for:
  * wake from park and fail to get a GPS fix
  * wake from park but go back to sleep because gps says your location
    is only N metres from where the device went into park sleep
  * wake from park because you've moved N metres from park location
  * heartbeat, periodic uploads during park
  * GPS override count - when accelerometer shows movement but GPS disagrees
* fix code that reads the CHRG and FAULT lines
  * these feed into the flags parameter of the board_log record
  * correct interpretation of those flags is a backend job that may
    or may not already be done
* fix bug where modem power on time was not counted (in park mode)
* fix long standing leap year bug *and* increment boat/gps log version to 6
* add ability to set park mode parameters (all values in seconds):

    * `set parking heartbeat_interval <NNN>`

      each `heartbeat_interval` logged data will be uploaded even when
      parked

    * `set parking park_log_interval <NNN>`

      if not yet parked, log accelerometer deviation data every _NNN_ seconds

    * `set park_time <NNN>`

      when stationary for _NNN_ seconds, enter park mode (sleep)
* Fix a bug that made the entry into park mode overly aggressive (when a boat
  was moving very slowly)
* Change the way GPS time adjustment events are logged such that they go to
  the server
* add a park command to allow a lab user to temporarily disable parking
  mode (this is not available over the air)

### _Betas_

#### VTS 2.6.1.5 -- commit Jan 2018

* create new event types for:
  * wake from park and fail to get a GPS fix
  * wake from park but go back to sleep because gps says your location is only N metres from where the device went into park sleep
  * wake from park because you've moved N metres from park location
  * heartbeat, periodic uploads during park
  * GPS override count - when accelerometer shows movement but GPS disagrees

N.B. all but the last event was already there but were being sent as SYS_NOTE (meaning they could not easily be disambiguated)

#### VTS 2.6.1.4 -- commit Jan 2018

* fix code that reads the CHRG and FAULT lines
  * these feed into the flags parameter of the board_log record
  * correct interpretation of those flags is a backend job that may or may not already be done

#### VTS 2.6.1.3 -- commit Jan 2018

* fix bug where modem power on time was not counted (in park mode)
* fix long standing leap year bug *and* increment boat/gps log version to 6

#### VTS 2.6.1.2 -- commit Jan 2018

* add ability to set park mode parameters (all values in seconds):

    * `set parking heartbeat_interval <NNN>`

      each `heartbeat_interval` logged data will be uploaded even when
      parked

    * `set parking park_log_interval <NNN>`

      if not yet parked, log accelerometer deviation data every _NNN_ seconds

    * `set park_time <NNN>`

      when stationary for _NNN_ seconds, enter park mode (sleep)

* Fix a bug that made the entry into park mode overly aggressive (when a boat
  was moving very slowly)
* Change the way GPS time adjustment events are logged such that they go to
  the server

#### VTS 2.6.1.1 -- internal release Dec 2017

* add a park command to allow a lab user to temporarily disable parking
  mode (this is not available over the air)

## VTS 2.6.1 -- released Nov 2017

* power management improvements around park mode
    * before entering park sleep, attempt to upload data
    * reduce false parks and wakeups by using the GPS to augment the
      accelerometer
    * updates battery level table according to real lab measurements
    * add a new "good enough" battery level to define when it is ok
      for a VTS to fully boot
    * fix to not start bnet and the radio unless the VTS is leaving
      park mode
    * add heartbeat (12 hours) that uploads data even during park
    * add logic to slow heartbeat by 3x when battery is particularly low
* bug fix to flash initialization on power up

## VTS 2.6.0 -- released between Oct 7 & Nov 25, 2017

* add an auto-detected park mode
    * park after stationary for 15 minutes, wake on significant
      significant movement or heading change
    * log board status every 5 minutes
    * log accelerometer deviations
    * re-enable compass heading code
* fix bug that prevented compass heading code from working
* allow broadcast provisioning

## VTS 2.5.0 -- commit Jan 2017

* various gear sensor and event logging fixes

## VTS 2.4.3.2

* NFC robustness fix

## VTS 2.4.3.1

* factory test fix: send modem status during each network update

## VTS 2.4.3 -- tagged Dec 2016

* factory test fix to prevent test device timeout
* fix temperature sensor code to support negative temps

## VTS 2.4.2 -- tagged Dec 2016

* add remote provisioning
* initial gear sensor support

## VTS 2.4.1 -- tagged Dec 2016

* Add support for v1.25 hardware that excludes humidity sensor
* Factory test updates:
    * GPS timeout is now 10 minutes
    * GSM test fixes

## VTS 2.4.0 -- tagged Dec 2016

* NFC fixes
* solar fixes
* boot race fixes
* improve provisioning protocol

## Historical

_historical/old information elided_

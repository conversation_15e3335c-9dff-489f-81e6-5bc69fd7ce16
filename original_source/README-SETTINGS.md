# VMS Settings & Commands
August 19, 2015

The VMS firmware supports a growing list of configuration options that can be set remotely by the cell modem or on the console. Settings are stored in a high memory area of the internal flash.

The settings area is versioned - any change in the version will cause the firmware to reset the area and lose any previous settings.

### GPS Settings

`gps run {lowres|hires|periodic}` *default: lowres*

Sets the gps run mode into
* lowres: uses GPS lower power mode, accuracy maybe reduced.
* hires: continuous GPS reporting, high power consumption.
* periodic: cycles on and off the GPS see `gps periodic`.

`gps idle {off|on}` *default: off*

When GPS idling is on, the firmware will monitor the device location and determine if the boat has been sitting stationary for more than 5 minutes.
* off: turn off gps idling
* on: turn on gps idle

`gps idle ON OFF` *default: 15 300*

When GPS idling is on and the firmware has detected the boat is stationary, the GPS is placed into a periodic mode with the idle settings.
* ON: seconds, GPS run time. min: 15 secs, max: 3600
* OFF: seconds, GPS off time. min: 15, max: 3600

`gps idle-meters METERS` *default: 20*

This setting is used to determine the movement threshold. The boat must not have moved more than METERS for more than the number of minutes defined by idle-time before the boat is considered stationary.
* METERS: units in meters. min: 10, max: 500

`gps idle-time MINUTES` *default: 5*

This setting is used to determine the stationary time threshold.
* MINUTES: units in minutes. min: 5, max: 60

`gps periodic ON OFF` *default: 15 45*

When the GPS run mode is `periodic`, this setting is used to set the cycle.
* ON: secs, time to stay on. min: 15 secs, max: 3600
* OFF: secs, time off, min: 15 secs, max: 3600
*NOTE: its recommend to have the ON value be greater than 5 minutes/600 seconds, otherwise the GPS may not acquire a satellite lock.*

`gps dock ON OFF` *default: 30 900*

The firmware can place the GPS into a shorter on/longer off periodic mode when the boat enters into a dock location if this setting is turned on. See the `nap dock` setting to turn this on.

* ON: secs, time to stay on. min: 15 secs, max: 3600
* OFF: secs, time off, min: 15 secs, max: 3600

`gps send precision`

The satellite count and DOP information is recorded with each position.

`gps send accel-raw`

The current raw accelerometer values (x,y,z along with compass x,y,z) are recorded with each position.

`gps send none`

This command clears any requests for additional information to be record with the position.

### NAP Settings

There are three nap settings to place the board into a deep sleep - all peripherals are shutdown, the MCU is in a deep sleep state, and a timer has been setup to restart the board at a given time.

There's a 4th setting is a GPS setting, when the boat enters a dock, the GPS can be placed into a docked-mode which is similar to a idle mode - a short on duration, and a very long off.

`nap {day|night|time START END|off}` *default: off*
The firmware will deep-sleep the board during a given time period. Only one period may be active.

* day: sleep between sunrise and sundown.
* night: sleep between sundown and sunrise.
* time START END: sleep between the UTC hours of START and END, may span midnight. Military format. (0-23)
* off: turn off deep sleep

`nap dock {on|off}` *default: off*

The GPS can be placed into a longer idle mode if the firmware detects the boat has entered a dock. See `gps dock` to see the idle periods.

`nap now SECONDS`
The board is immediately put to the sleep for *SECONDS*.

### EVENT Settings

Normally, the firmware only reports warning events - device command failure, flash area full, incorrect settings given, etc. The firmware can be told to report on profiling and normal events for particular sources.

`event info SRCBITS` *default: 0*

Report on events for various sources that are of type EVT_INFO. Generally, this is used for debugging.
* SRCBITS: a bitmask number represent the sources to report on. Hex  maybe used if '0xNN' format is used. See common/event.h for the list.

`event profile SRCBITS` *default: 0*

Report on events that are of type EVT_PROF - this is used for profiling various firmware operations.
* SRCBITS: a bitmask number represent the sources to report on. Hex  maybe used if '0xNN' format is used. See common/event.h for the list.

### DOCK settings

Multiple dock locations can be set to help determine when the firmware should upload the payload, help in gps idling, and other operations. Currently up to 32 docks may be specified.

`dock LAT LNG` *default: no docks*

Tell the firmware there is a dock at the given location.
* LAT: latitude in decimal format, NN.NNNNNN or -NN.NNNNN
* LNG: latitude in decimal format, NN.NNNNNN or -NN.NNNNN

`dock remove LAT LNG`

Remove a dock from the list.

`dock clear`

Remove all docks that have been set.

### PROVISION Settings

`provision factory [DELAY]`

The device is placed back into factory mode and the device is rebooted. This should be only the setting sent to the device. The [DELAY] argument is optional and will sleep the device for DELAY seconds before starting the factory provisioning cycle.

`provision deploy [DELAY]`

The device is placed back into deployment mode and the device is rebooted. This should be only the setting sent to the device. The [DELAY] argument is optional and will sleep the device for DELAY seconds before starting deployment provisioning cycle.


### REBOOT setting

`reboot`

The device is rebooted after this setting is received. This should be the last setting sent to the device.

### RESET setting

`reset`

This setting/command will reset all the settings back to their defaults. Its recommended a `reboot` setting is issued after this setting.

### Shell Interface

To set or issue a setting via the shell, use the command `set` followed by the desired setting. For example, `set dock 12.345 -6.78`, or `set dock remove 12.345 -6.78`

`set` by itself will display all the current settings.

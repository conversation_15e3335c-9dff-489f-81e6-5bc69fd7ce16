GPS & Event Logs Overview
=========================

Currently there are two logging areas in the external flash:

- The GPS Log :  contains the GPS positioning information and remote sensor readings
- The Event Log  : stores the image events (modem power on time, on board sensor readings, encountered errors, etc)

GPS Log V5
==========

BoatOS V2.2 introduced a new GPS log format that is able to extend the format with the need to bump the version number.

The ideally the event log should be folded in the GPS log and this new format lays the groundwork for that.

Each entry contains the minimum data:

* A record marker byte (value 0xE8) - use to ensure a record is valid.
* A header byte.
    - Bits 0-3 are the entry type GPS position, remote sensor, onboard sensor - NA , event type - NA
    - Bits 4-5 are the timestamp delta size. This can be a full 32-bit timestamp, a 8-bit or 16-bit delta,
    - Bit 6 - if set means the timestamp is to incremented by one second. and Bits 4-5 are ignored.
    - Bit 7 - if set means the header has additional bytes associated with it (not implemented currently)
* A optional timestamp in the form of:
    - 1 or 2 byte delta depending on how the header bits 4-5 are set
    - a 4 byte full timestamp if bits 4-5 indicates a full timestamp
    - NO bytes if bit 6 is set.

For both a GPS position and remote sensor entry type, a GPS record will immediately follow the above.

A remote sensor record will follow the GPS record.

GPS Record
==========

The GPS record will contain a minimum of a single header byte:

* GPS Header Byte
    - Bits 0-1 are the latitude delta size - 1, 2 or a full latitude in 4 bytes.
    - Bits 2-3 are the longitude delta size - 1, 2 or a full longitude in 4 bytes.
    - Bit 4 set indicates the gps position was obtain while the GPS chip was in high resolution mode.
    - Bit 5 set means a GPS speed & heading (4 bytes) record follows the GPS position deltas
    - Bit 6 set means a accelerometer heading (2 bytes) record follows GPS & an optional speed heading
      (the accelerometer current does not compute this but the image is setup to log this)
    - Bit 7 set means an extend header byte follows immediately after the GPS position deltas

GPS Positions
=============

* Latitude Record
     - 1 or 2 byte integer SIGNED delta depending on how the gps header bits 0-1 are set
     - a 4 byte full latitude if a full position is indicated.

* Longitude Record
     - 1 or 2 byte integer SIGNED delta depending on how the gps header bits 2-3 are set
     - a 4 byte full latitude if a full position is indicated.

Both positions are integer format with 5 points precision.

Speed Record
============

* Byte 0-1 speed unsigned integer with 2 precision points
* Byte 2-3 heading unsigned integer 0-359 (in degrees)

Accelerometer Heading Record
============================

* Byte 0-1 accelerometer computed heading unsigned integer 0-359

GPS Extended Header
===================

* Bit 0 set means a GPS precision (satellites in view, and *DOP values) record follows
* Bit 1 set means the accelerometer x,y,z values and magnetometer x,y,z values follow

GPS Precision
=============
* Byte 0 Satellites in view (0 to 255)
(the following three bytes are in integer 2 point precision, unsigned)
* Byte 1 HDOP
* Byte 2 VDOP
* Byte 3 PDOP

Accelerometer Values
====================
All values are 16-bit SIGNED.

* Byte 0-1 accelerometer X axis
* Byte 2-3 accelerometer Y axis
* Byte 4-5 accelerometer Z axis
* Byte 6-7 magnetometer X axis
* Byte 8-9 magnetometer Y axis
* Byte 10-11 magnetometer Z axis

Remote Sensor Record
====================

The remote sensor record follows all other GPS records.

The header byte is:

* Bit 0-3 sensor type. Current only two types are dealt with
    - Type 1 Thermistor
    - Type 15 Prototype
* Bit 4-6 UNIMPLEMENTED are mean to be a sensor index to the sensor uid to save space
  for multiple readings from the same sensor in the current page
* Bit 8 UNIMPLEMENTED indicates Bits 4-6 represent a sensor index and no UID follows.
  Unset means a UID follows

 * Bytes 1-11 - The 10 byte sensor UID - currently the Freescale processor UID.

 For Thermistor Sensor Type

 * Byte 12-13 - 16-bit unsigned thermistor analog value

 For Prototype Sensor Type

 * Byte 12 - remaining record length
 * Byte 13 to N  - sensor data

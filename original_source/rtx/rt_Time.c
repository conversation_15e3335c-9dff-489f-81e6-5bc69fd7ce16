/*----------------------------------------------------------------------------
 *      CMSIS-RTOS  -  RTX
 *----------------------------------------------------------------------------
 *      Name:    RT_TIME.C
 *      Purpose: Delay and interval wait functions
 *      Rev.:    V4.70
 *----------------------------------------------------------------------------
 *
 * Copyright (c) 1999-2009 KEIL, 2009-2013 ARM Germany GmbH
 * All rights reserved.
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *  - Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *  - Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *  - Neither the name of ARM  nor the names of its contributors may be used
 *    to endorse or promote products derived from this software without
 *    specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED. IN NO EVENT SHALL COPYRIGHT HOLDERS AND CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
 * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
 * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *---------------------------------------------------------------------------*/

#include "rt_TypeDef.h"
#include "RTX_Config.h"
#include "rt_Task.h"
#include "rt_Time.h"

/*----------------------------------------------------------------------------
 *      Global Variables
 *---------------------------------------------------------------------------*/

/* Free running system tick counter */
U32 os_time;


/*----------------------------------------------------------------------------
 *      Functions
 *---------------------------------------------------------------------------*/


/*--------------------------- rt_time_get -----------------------------------*/

U32
rt_time_get (void)
{
        /* Get system time tick */
        return (os_time);
}


/*--------------------------- rt_dly_wait -----------------------------------*/

void
rt_dly_wait (U16 delay_time)
{
        /* Delay task by "delay_time" */
        rt_block (delay_time, WAIT_DLY);
}


/*--------------------------- rt_itv_set ------------------------------------*/

void
rt_itv_set (U16 interval_time)
{
        /* Set interval length and define start of first interval */
        os_tsk.run->interval_time = interval_time;
        os_tsk.run->delta_time = interval_time + (U16)os_time;
}


/*--------------------------- rt_itv_wait -----------------------------------*/

void
rt_itv_wait (void)
{
        /* Wait for interval end and define start of next one */
        U16 delta;

        delta = os_tsk.run->delta_time - (U16)os_time;
        os_tsk.run->delta_time += os_tsk.run->interval_time;
        if ((delta & 0x8000) == 0) {
                rt_block (delta, WAIT_ITV);
        }
}

/*----------------------------------------------------------------------------
 * end of file
 *---------------------------------------------------------------------------*/


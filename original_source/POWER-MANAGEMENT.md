# BoatOS Power Management

The power state starts off in normal upon boot. Every 5 minute, the power state is reevaluated and the two threads - gps and boatnet (radio) - will be notified appropriately when the power state changes. The modem thread considers the power level independently when it wakes up.

## Normal Power State (V3.3)

The normal power state is when the battery has a healthy charge and is able to power all appropriate devices.

* GPS: running in periodic mode - 15 seconds on, 45 seconds in stand-by mode.
* GSM Modem will attempt to upload at intervals determined by boat location.

## Low Power State (V3.1)

The low power state is typical during extended cloudy days where little to no sun is available.

* GPS: placed into standby mode - coordinate logging disabled.
* GSM Modem will not attempt uploads unless a success upload has not occur 12 hour or more.

## Critical Power State (V3.0)

When the power level reached this power state, all devices are shutdown, and the board hibernates for a given period. (Currently 8 hours). Upon wakeup the board will check the power level and if its at or above the Low Power level (V3.1), otherwise the board will go back to sleep.

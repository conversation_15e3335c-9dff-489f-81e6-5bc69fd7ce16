source [find interface/jlink.cfg]

transport select swd

source [find target/swj-dp.tcl]

if { [info exists CHIPNAME] } {
    set _CHIPNAME $CHIPNAME
} else {
    set _CHIPNAME kl16
}

if { [info exists ENDIAN] } {
    set _ENDIAN $ENDIAN
} else {
    set _ENDIAN little
}

# Work-area is a space in RAM used for flash programming
# By default use 4kB
if { [info exists WORKAREASIZE] } {
   set _WORKAREASIZE $WORKAREASIZE
} else {
   set _WORKAREASIZE 0x1000
}

if { [info exists CPUTAPID] } {
    set _CPUTAPID $CPUTAPID
} else {
    set _CPUTAPID 0x0bc11477
}

swj_newdap $_CHIPNAME cpu -irlen 4 -expected-id $_CPUTAPID

set _TARGETNAME $_CHIPNAME.cpu
target create $_TARGETNAME cortex_m -endian $_ENDIAN -chain-position $_CHIPNAME.cpu

# It is important that "kinetis mdm check_security" is called for
# 'examine-end' event and not 'eximine-start'. Calling it in 'examine-start'
# causes "kinetis mdm check_security" to fail the first time openocd
# calls it when it tries to connect after the CPU has been power-cycled.
$_CHIPNAME.cpu configure -event examine-end {
    kinetis mdm check_security
}

$_TARGETNAME configure -work-area-phys 0x20000000 -work-area-size $_WORKAREASIZE -work-area-backup 0

set _FLASHNAME $_CHIPNAME.flash
flash bank $_FLASHNAME kinetis 0 0 0 0 $_TARGETNAME

# Table 5-1. Clock Summary of KL25 Sub-Family Reference Manual
# specifies up to 1MHz for VLPR mode.
adapter_khz 1000

if {![using_hla]} {
   # if srst is not fitted use SYSRESETREQ to
   # perform a soft reset
   cortex_m reset_config sysresetreq
}

$_TARGETNAME configure -event reset-init {
    # Table 5-1. Clock Summary of KL25 Sub-Family Reference Manual
    # specifies up to 24MHz for run mode; Table 17 of Sub-Family Data
    # Sheet rev4 lists 25MHz as the maximum frequency.
    adapter_khz 1000
}


$_TARGETNAME configure -event gdb-attach {
    halt
}

init
arm semihosting enable

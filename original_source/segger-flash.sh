#!/bin/sh

# find JLinkExe (include default macos path)
JLINKEXE=`PATH=$PATH:/Applications/SEGGER/JLink:/opt/SEGGER/JLink/ which JLinkExe`

IMAGE=$1

if [ ! -f "$JLINKEXE" ]; then
    echo "The Segger utilties are not installed on this machine."
    echo "$JLINKEXE is missing."
    exit 3
fi

if [ "$IMAGE"x = x ]; then
    echo "Usage: $0 image-name"
    exit 1
fi

if [ ! -f "$IMAGE" ]; then
    echo "$IMAGE: does not exist."
    exit 2
fi

if [ "${IMAGE##*.}" != "bin" ] && [ "${IMAGE##*.}" != "hex" ]; then
    echo "$IMAGE is not a .bin or .hex file."
    exit 4
fi

echo "Attempting to flash $IMAGE"

case $2 in
  sensor)
    erase="erase"
    ;;
  erase|factory*)
    erase="erase"
    ;;
esac

if [ "$2" = "sensor" ]; then
  $JLINKEXE -device CC1310F128 -si JTAG <<EOF
  r
  h
  h
  erase
  loadfile "$IMAGE"
  g
  exit
EOF
else
  read -r -d '' script <<-EOF
    r
    h
    h
    $erase
    w4 0x40041000  0x0
    loadbin $IMAGE 0
    g
    exit
EOF
  if [[ $2 = factory* ]]; then
    while true; do
      echo "$script" | $JLINKEXE -device MKL16Z256xxx4 -if swd -speed 4000 | egrep '^O.K' | awk '{print "\a"$1}'
      sleep 2
    done
  else
    echo "$script" | $JLINKEXE -device MKL16Z256xxx4 -if swd -speed 4000
  fi
fi

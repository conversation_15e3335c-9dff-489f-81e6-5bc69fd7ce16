/** @{

    @ingroup    common
    @file
    @brief      Firmware update
*/

#include "pelagic.h"
#include "gpio_api.h"
#include "firmware.h"
#include "memmap.h"
#include "led.h"
#include "system_file.h"
#ifdef TARGET_VMS
#include "board_info.h"
#endif

bool firmware_updated = 0;

#ifdef HAVE_LED
#define DIE() { led_on(LED_RED); for (;;) ; }
#else
#define DIE() { for (;;) ;  }
#endif

/**
    @brief  move new firmware into place
    @param[in]  image   pointer (to flash area) where the new image is located
    @param[in]  length  image size
    @note   There is some magic here. This routine and all others called MUST BE IN
            RAM, not FLASH. The old image is overwritten and dependent routines used
            to move things will be inaccessible.
*/


//! PORT Ian: This function takes a pointer to a new image from *somewhere* and
//!            copies it into flash starting at 0, blowing away whatever's there.
SRAM_TEXT_SECTION
void
firmware_move_into_place(uint8_t *image, uint32_t length)
{
        flash_result_t result;

        //
        // From this point on any routine that is located in flash will not be accessible.
        //

        int_disable(); // Never to see another interrupt this session..

        // move things into place
        for (int offset = 0; offset < length; offset += KINETIS_SECTOR_SIZE) {
                memcpy(the_buffer.firmware, image + offset, KINETIS_SECTOR_SIZE);

                if ((result = kinetis_flash_erase_sector(offset)) != FLASH_SUCCESS) {
                        DIE();
                }

                if ((result = kinetis_flash_write(offset, the_buffer.firmware, KINETIS_SECTOR_SIZE)) != FLASH_SUCCESS) {
                        DIE();
                }
        }

        SYS_REG_FILE->reboot_reason = REBOOT_FIRMWARE;

        // And good bye!
        NVIC_SystemReset();
}

/**
    @brief  begin the firmware update after passing verification
*/

void
firmware_update()
{
        firmware_header_t *header = (firmware_header_t *) FIRMWARE_ADDR;
        uint8_t *image = (void *)(FIRMWARE_ADDR + sizeof(firmware_header_t));
        uint16_t crc;

        printf("firmware update: length %d, crc16 %x, device %d, vers %d\n",
               header->length, header->crc16, header->device_hardware, header->device_version);

        if (header->length > FIRMWARE_SIZE) {
                EVENT_LOG2(EVT_FIRMWARE_SIZE_ERROR, "firmware size error", "header", EVT_32BIT, header->length, "stored", EVT_32BIT, FIRMWARE_SIZE);
                return;
        }

        crc = crc16(image, header->length);
        if (crc != header->crc16) {
                EVENT_LOG2(EVT_FIRMWARE_IMAGE_CRC_ERROR, "firmware image crc", "got", EVT_16BIT, crc, "expected", EVT_16BIT, header->crc16);
                return;
        }


#ifdef TARGET_VMS
        board_info_set_build_crc(header->crc16);
        rtc_save(0);
#endif

        firmware_move_into_place(image, header->length);
}

/** @} */

#ifndef __MEMMAP_H__
#define __MEMMAP_H__

enum {
        // FIRMWARE_ADDR           = (128*1024),
        FIRMWARE_ADDR           = 0x08020000, // 128k, end of linker-defined flash
        FIRMWARE_SIZE           = (120*1024), // VMS firmware can be 120KB
        // why is there a 4k gap between firmware and provision? 248k-->252k

        // TODO Ian: confirm appropriate locations and sizes
        // Pretty sure this is fine actually
        PROVISION_ADDR          = (252 * 1024), 
        PROVISION_SIZE          = 1024,

        SETTINGS_ADDR           = (253 * 1024),
        SETTINGS_SIZE           = 1024,

        BOARD_FAULT_ADDR        = (254 * 1024),
        BOARD_FAULT_SIZE        = (1024),

        BOARD_INFO_ADDR         = (255 * 1024),
        BOARD_INFO_SIZE         = (1024),
        // So 4k allocated at end of flash for little details
};

#endif

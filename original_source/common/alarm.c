/** @{

    @ingroup    common
    @file

    @brief  Second resolution alarm
*/

#include "pelagic.h"
#include "alarm.h"
#include "signals.h"
#include "gpio_api.h"
#include "gpio_irq_api.h"
#include "system_file.h"

volatile alarm_t *alarm_head = NULL;

void alarm_start_common(alarm_t *alarm, int32_t seconds);

/**
    @brief  Walk thru all pending alarms.
    @note   Called once a second (from the RTC tick interrupt or a RTX timer depending on
            an XCAL is installed and functioning) to scan all pending alarms and
            for those that expire - either call the registered handler or
            signal a thread. For periodic alarms, the alarm is re-initialized.
*/

void
alarm_tick()
{
        /* Go through all the pending TimerEvents */
        while (1) {
                if (alarm_head == NULL) {
                        return;
                }

                if ((alarm_head->timestamp - epoch_clock) > 0)
                        return;

                alarm_t *p = (alarm_t *) alarm_head;
                alarm_head = alarm_head->next;

                if (p->buzzer) {
                        *(p->buzzer) = true;
                } else {
                        osSignalSet(p->thread_id, ALARM_SIGNAL_BUZZER);
                }

                if (p->periodic) {
                        alarm_start_common(p, p->periodic);
                }
        }
}

/**
    @brief  return how many seconds are left in an alarm
    @return seconds until an alarm is to fire. 0 if alarm expired already.
*/

uint32_t
alarm_remain(alarm_t *alarm)
{
        if (alarm->timestamp > epoch_clock)
                return alarm->timestamp - epoch_clock;
        else
                return 0;
}

/**
    @brief  internal common alarm routine to setup an alarm, and insert it into
            the alarm list.
    @param[in]  alarm   alarm to setup
    @param[in]  seconds seconds for alarm
*/

void
alarm_start_common(alarm_t *alarm, int32_t seconds)
{
        int32_t timestamp = epoch_clock + seconds;
        alarm->timestamp = timestamp;


        /* disable interrupts for the duration of the function */
        int_disable();

        /* Go through the list until we either reach the end, or find
           an element this should come before (which is possibly the
           head). */

        alarm_t *prev = NULL, *p = (alarm_t *)alarm_head;
        while (p != NULL) {
                /* check if we come before p */
                if ((timestamp - p->timestamp) < 0) {
                        break;
                }
                /* go to the next element */
                prev = p;
                p = p->next;
        }
        /* if prev is NULL we're at the head */
        if (prev == NULL) {
                alarm_head = alarm;
        } else {
                prev->next = alarm;
        }
        /* if we're at the end p will be NULL, which is correct */
        alarm->next = p;

        int_enable();
}

/**
    @brief  cancel an alarm
*/

void
alarm_cancel(alarm_t *alarm)
{
        int_disable();

        // remove this object from the list
        if (alarm_head == alarm) {
                // first in the list, so just drop me
                alarm_head = alarm->next;
        } else {
                // find the object before me, then drop me
                alarm_t *p = (alarm_t *) alarm_head;
                while (p != NULL) {
                        if (p->next == alarm) {
                                p->next = alarm->next;
                                break;
                        }
                        p = p->next;
                }
        }

        int_enable();
}

/**
    @brief  setup a periodic repeating alarm that signals a thread.
    @param[in]  alarm   alarm to setup
    @param[in]  seconds second to run
*/

void
alarm_start_periodic(alarm_t *alarm, int32_t seconds)
{
        alarm->periodic = seconds;
        alarm->buzzer = NULL;
        alarm->thread_id = osThreadGetId();
        alarm_start_common(alarm, seconds);
}

/**
    @brief  setup a one-off alarm that sets a variable when expires
    @param[in]  alarm   alarm to setup
    @param[in]  seconds seconds to run
    @param[out] buzzer  variable to set to true when alarm goes off
*/

void
alarm_start_set(alarm_t *alarm, int32_t seconds, volatile bool *buzzer)
{
        alarm->periodic = 0;
        alarm->buzzer = buzzer;
        *buzzer = false;
        alarm->thread_id = osThreadGetId();
        alarm_start_common(alarm, seconds);
}

/**
    @brief  setup a one-off alarm that signals a thread
    @param[in]  alarm   alarm to setup
    @param[in]  seconds seconds to run
*/

void
alarm_start(alarm_t *alarm, int32_t seconds)
{
        alarm->periodic = 0;
        alarm->buzzer = NULL;
        alarm->thread_id = osThreadGetId();
        alarm_start_common(alarm, seconds);
}

/** @} */

#ifndef __BITS_H__
#define __BITS_H__

uint16_t get_uint16(uint8_t *data);
void set_uint16(uint8_t *data, uint16_t value);

uint32_t get_uint32(uint8_t *data);
void set_uint32(uint8_t *data, uint32_t value);

uint64_t get_uint64(uint8_t *data);
void set_uint64(uint8_t *data, uint64_t value);

uint8_t *store_uint16(uint8_t *data, uint16_t value);
uint8_t *store_uint32(uint8_t *data, uint32_t value);
#endif

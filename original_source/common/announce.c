/** @{

    @ingroup    common
    @file
*/

#include "pelagic.h"
#include "clk_freqs.h"
/**
    @brief  announce the board booted with some info
*/

void
announce(const char *name)
{
        uart_printf("BoatOS %s - Pelagic Data Systems (c) 2015-2018\n", name);
        uart_printf("Built %u Tag %s Target %d\n", build_timestamp, build_tag, build_target_version);
        uart_printf("Clocks cpu %"PRIu32"mhz bus %"PRIu32"mhz\n", OS_CLOCK / 1000000, bus_frequency() / 1000000);
        uart_printf("Chip UID %Z\n", board_uid);
}

/** @} */

/** @{

	@ingroup	common
	@file
	@brief		Date routines
*/

#include "pelagic.h"

enum {
        DAYSPERWEEK     = 7,
        DAYSPERNORMYEAR = 365U,
        DAYSPERLEAPYEAR = 366U,
        SECSPERDAY      = 86400UL,
        SECSPERHOUR     = 3600UL,
        SECSPERMIN      = 60UL
};

const uint16_t days_per_month[2][12] = {
        /* Normal years.  */
        { 0, 31, 59, 90, 120, 151, 181, 212, 243, 273, 304, 334 },
        /* Leap years.  */
        { 0, 31, 60, 91, 121, 152, 182, 213, 244, 274, 305, 335 }
};

const uint8_t days_in_month[2][13] = {
        { 0, 31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31 },
        { 0, 31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31 }
};

/**
	@brief	convert a date time into seconds since Pelagic Epoch
	@param[in]	date	date time structure to convert
	@return datetime in seconds since Epoch
*/
uint32_t
datetime_to_seconds(datetime_t *date)
{
        return date_to_seconds(date->year, date->month, date->day) + (date->second + (date->hour * SECSPERHOUR) + (date->minute * SECSPERMIN));
}

static inline int8_t
check_if_leap(uint16_t year)
{
        /*
         * If the year is a century year not divisible by 400
         * then it is not a leap year, otherwise if year divisible by
         * four then it is a leap year
         */
        if ((year % 100) == 0)
                return ((year % 400) == 0);
        else
                return ((year % 4) == 0);
}

/**
	@brief	a date (no time) into seconds since Pelagic Epoch
	@param[in]	year	format YYYY
	@param[in]	month	format 1-12
	@param[in]	day		format 1-31
	@return date in seconds from Epoch.
*/
uint32_t
date_to_seconds(uint16_t year, uint8_t month, uint8_t day)
{
        uint32_t secs = 0;
        uint16_t years = year - EPOCH_YEAR; // how many years since 2014
        int countleap = 0;
        int is_leap;

        secs = years * (SECSPERDAY * 365);  // seconds to Jan 1 this year

        // figure out how many leap years have happened between 2014 and now
        // do _not_ include the current year
        for (int i = 0; i < years; i++) {
                if (check_if_leap((EPOCH_YEAR + i)))
                        countleap++;
        }

        secs += (countleap * SECSPERDAY);   // add the leap year correction

        // add secs up to the current month (array/table lookup)
        // select array based on leap year
        is_leap = check_if_leap((EPOCH_YEAR + years)) ? 1 : 0;
        secs += (days_per_month[is_leap][month - 1] * SECSPERDAY);

        secs += ((day - 1) * SECSPERDAY);   // add days of the current month

        return secs;
}

/*
	@brief	convert secons (since Epoch) to Date Time
	@param[in]	time	time in Epoch seconds to convert
	@param[out]	date	structure to hold date time conversion
*/
void
seconds_to_datetime(uint32_t time, datetime_t *date)   /* Body */
{
        uint32_t   day, year, tmp;
        int8_t     leap;

        /* Number of days */
        day = time / 86400;

        /* Remain Seconds */
        time -= day * 86400;

        /* Calculate the hour */
        date->hour = (time / 3600);
        time -= ((uint32_t) date->hour * 3600);

        /* Calculate the minute */
        date->minute = (time / 60);
        time -= ((uint32_t) date->minute * 60);

        /* The second */
        date->second = time;

        /* Calculate the year */
        day += 365;                    /* Add offset day, the year starts from 1969 */
        year = 4 * (day / 1461);       /* The number days in each four years is 1461 (days) */
        day -= year * 1461 / 4;        /* The remain days */
        tmp = day / 365;               /* Remain years */
        if (tmp == 4) tmp = 3;         /* The maximum of remain years is equal to 3 */
        day -= tmp * 365;              /* remain days of this year */
        date->year = year + tmp + EPOCH_YEAR - 1;

        /* calculate the month */
        /* Find out if we are in a leap year. */
        leap = check_if_leap(date->year);
        date->month = 1;

        while (day >= days_in_month[leap][date->month]) {
                day -= days_in_month[leap][date->month];
                date->month++;
        }

        /* calculate the day */
        date->day = day;
        /* first day is 1*/
        date->day++;
}

/**
	@brief	display Epoch seconds in a human format
	@param[in]	timestamp	Epoch seconds to display
*/
void
datetime_display(uint32_t timestamp)
{
        datetime_t dt;

        // Magic here - Epoch is from 2014, deployment was in 2015.. any timestamp
        // before 2015 is really a counter since boot until the real time has been
        // acquired yet.
        if (timestamp > 1000000) {
                seconds_to_datetime(timestamp, &dt);
                printf("[%2.2d/%2.2d/%2.2d %2.2d:%2.2d:%2.2d]", dt.year - 2000, dt.month, dt.day, dt.hour, dt.minute, dt.second);
        } else {
                printf("[ +%d ]", timestamp);
        }
}

/**
	@brief	obtain timezone offset from longitude position
	@param[in]	longitude	longitude (in floating point format)
	@return seconds offset (+/-) from UTC for longitude position
*/
int32_t
timezone_offset(double longitude)
{
        return (int32_t) (((longitude * 24.0)/360.0) * 3600);
}

/** @} */

#ifndef __FACTORY_TEST_H__
#define __FACTORY_TEST_H__

#include <stdint.h>

typedef enum  {
        FT_SUCCESS              = 0,

        FT_GPS_UNRESPONSIVE     = 10,
        FT_GPS_BACKUP_FAIL      = 11,
        FT_GPS_LOCK_FAIL        = 12,

        FT_TEMP_FAIL            = 20,

        FT_WINBOND_UNRESPONSIVE = 30,
        FT_WINBOND_IO_FAIL      = 31,

        FT_ACCEL_UNRESPONSIVE   = 40,
        FT_ACCEL_IRQ_FAIL       = 41,
        FT_ACCEL_UNEVEN         = 42,

        FT_POWER_FAIL           = 50,

        FT_MODEM_UNREPONSIVE    = 60,
        FT_MODEM_SEND_FAIL      = 61,
        FT_MODEM_SEND_TIMEOUT   = 62,

        FT_HUMIDITY_UNREPONSIVE = 70,
        FT_HUMIDITY_FAIL        = 71,


        FT_NO_REPONSE           = 9999,
} ft_result_t;

enum {
        FT_VERSION              = 1,
        FACTORY_ACK_FLAG_DEPLOY = 0x1,  // Head to deployment
};

typedef struct {
        char           *name;
        ft_result_t    (*test)();
} ft_device_t;

#ifdef HAVE_FT_STANDALONE
void factory_test_standalone();
#else
void factory_test(uint8_t, uint8_t);
void wait_for_test_request(bool);
void run_tests_and_report_results();
#endif

void new_factory_test();

void ft_update(const char *message, ...);
void ft_device_result(const char *message, ...);

ft_result_t gps_factory_test();
ft_result_t temp_factory_test();
ft_result_t winbond_factory_test();
ft_result_t power_factory_test();
ft_result_t modem_factory_test();

#ifdef HAVE_ACCEL
ft_result_t accel_factory_test();
#endif

#ifdef HAVE_HUMIDITY
ft_result_t humidity_factory_test();
#endif

ft_result_t modem_factory_test_and_report(ft_result_t test_results, uint8_t deploy_mode);

extern const int ft_device_count;
extern const ft_device_t ft_devices[];
extern int ft_results_length;

#endif

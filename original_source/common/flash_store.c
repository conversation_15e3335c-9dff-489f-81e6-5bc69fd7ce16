#include "pelagic.h"
#include "us_ticker_api.h"

// DANGER: - DO NOT CHANGE THIS UNLESS PAGE_SIZE ON THE DEVICE STRUCTURES ARE ADJUSTED
// also, this buffer is shared among several modules.
uint8_t the_cache[FLASH_PAGE_SIZE] __attribute__ (( aligned (4) ));  // mostly used by the bodytrace code dumping the boat log.

void flash_store_setup(flash_partition_t *part);

#ifdef TARGET_VMS
flash_partition_t *partitions[] = {
        &boatlog_partition,
        &event_partition,
        &firmware_partition,
        &firmware_sensor_partition,
};

void
flash_store_init()
{
        for (int i = 0; i < ARRAY_SIZE(partitions); i++)
                flash_store_setup(partitions[i]);

        EVENT_LOG(EVT_FS_INIT, "init");
}

void
flash_store_erase_all()
{
        for (int i = 0; i < ARRAY_SIZE(partitions); i++)
                flash_store_erase(partitions[i]);
}

void
flash_store_reset(flash_partition_t *part)
{
        part->pages_used     = 0;
        part->bytes_stored   = 0;
        part->page_read      = 0;
        part->staging_offset = 0;
        part->staging_page   = 0;
        part->sequence       = 1;
}

bool
flash_store_data_erased(void *data, const int bytes)
{
        for (int i = 0; i < bytes; i++) {
                if (((uint8_t *)data)[i] != 0xff) {
                        return false;
                }
        }

        return true;
}

bool
flash_store_page_valid(flash_partition_t *part, flash_page_t *page, uint32_t address)
{
        uint16_t crc;

        if (page->header.magic != FLASH_STORE_MAGIC) {
                return false;
        }

        if (page->header.free_marker == FREE_MARKER)
                // don't compute CRC because the ONLY the marker was updated and not the CRC when
                // the page was freed.
                return true;

        crc = crc16(page, page->header.size+sizeof(flash_page_header_t));

        if (crc != page->footer.crc16) {
                return false;
        }

        return true;
}

void
flash_store_erase(flash_partition_t *part)
{
        const flash_device_t *dev = part->device;
        uint32_t started, bytes;

        flash_store_set_busy(part, true);

        dev->keep_on(true);
        started = clock_read();

        for (bytes = 0; bytes < part->size; bytes += dev->sector_size) {
                bool need_erase = false;
                uint32_t sector_address = part->offset + bytes;

                for (int page = 0; page < dev->pages_per_sector; page++) {
                        uint32_t page_address = sector_address + (page * dev->page_size);
                        if (dev->read(page_address, the_cache, dev->page_size) != FLASH_SUCCESS) {
                                uart_printf("ERASE: read error page address 0x%x\n", page_address);
                                need_erase = true;
                                break;
                        }

                        if (!flash_store_data_erased(the_cache, dev->page_size)) {
                                need_erase = true;
                                break;
                        }
                }

                if (need_erase == false)
                        continue;

                if (dev->erase(sector_address) != FLASH_SUCCESS)
                        uart_printf("ERASE: page %d erase error \n", sector_address);
        }

        dev->keep_on(false);
        flash_store_reset(part);

        flash_store_set_busy(part, false);

        EVENT_LOG3(EVT_FS_ERASED, "fs erased",
                   "partition", EVT_STRCONST, part->name,
                   "secs", EVT_32BIT, clock_read() - started,
                   "bytes", EVT_32BIT, part->size);
}


void
flash_store_setup(flash_partition_t *part)
{
        flash_page_header_t header;
        flash_device_t *dev = part->device;
        bool force_erase = false;
        uint32_t page, start_page = 0, end_page = 0, start_sequence = 0, end_sequence = 0;
        uint32_t free_sequence = 0, free_page = 0;
        uint32_t pages_found = 0, bytes_found = 0;

        part->pages_total = part->size / dev->page_size;

        part->mutex.mutex = part->mutex_data;
        part->lock = osMutexCreate(&part->mutex);

        flash_store_reset(part);

        if (part->is_raw)
                return;

        dev->keep_on(true);

        // Locate the starting point in the circular buffer. (ring start is set to zero.)
        for (page = 0; page < part->pages_total; page++) {
                if (dev->read(part->offset + (page * dev->page_size), &header, sizeof(header)) != FLASH_SUCCESS) {
                        force_erase = true;
                        break;
                }

                if (flash_store_data_erased(&header, sizeof(header)))
                        continue;

                if (header.magic != FLASH_STORE_MAGIC) {
                        uart_printf("page %d bad magic 0x%x\n", page, header.magic);
                        force_erase = true;
                        break;
                }

                // printf("[p %d s %d free? %c] ", page, header.size, header.free_marker == FREE_MARKER ? 'Y' : 'N');

                if (header.size == 0 || header.size > FLASH_DATA_SIZE) {
                        EVENT_LOG3(EVT_FS_SIZE_ERROR, "fs size error",
                                   "partition", EVT_STRCONST, part->name,
                                   "page", EVT_32BIT, page,
                                   "size", EVT_32BIT, header.size);
                        force_erase = true;
                        break;
                }

                if (header.free_marker == FREE_MARKER) {
                        if (header.sequence == 0)
                                continue;

                        if (free_sequence < header.sequence) {
                                free_sequence = header.sequence;
                                free_page = page;
                        }

                        continue;
                }

                pages_found++;
                bytes_found += header.size;

                if (start_sequence == 0 || start_sequence > header.sequence) {
                        start_sequence = header.sequence;
                        start_page = page;
                }

                if (end_sequence == 0 || end_sequence < header.sequence) {
                        end_sequence = header.sequence;
                        end_page = page;
                }

        }

        dev->keep_on(false);

        if (force_erase) {
                uart_printf("forcing erase\n");
                flash_store_erase(part);
                return;
        }

        part->pages_used   = pages_found;
        part->bytes_stored = bytes_found;

        if (start_sequence) {
                part->sequence     = end_sequence + 1;
                part->page_read    = start_page;
                part->staging_page = (end_page + 1) % part->pages_total;
        } else if (free_sequence) {
                part->sequence     = free_sequence + 1;
                part->staging_page = (free_page + 1) % part->pages_total;
                part->page_read    = part->staging_page;
        }

}

bool
flash_store_partial_okay(flash_partition_t *part, uint32_t bytes)
{
        uint32_t avail;

        if (part->staging_offset == 0)
                return false;

        avail = ((part->is_raw ? part->device->page_size : FLASH_DATA_SIZE) - part->staging_offset);

        return (avail >= bytes);
}

flash_result_t
flash_store_write(flash_partition_t *part, uint8_t *data, uint32_t bytes)
{
        uint32_t avail, count;
        uint8_t *staging_data;
        const uint32_t page_size = (part->is_raw ? part->device->page_size : FLASH_DATA_SIZE);
        flash_page_t  *staging_page = (flash_page_t *)part->staging_buffer;
        flash_result_t result;

        while (bytes > 0) {
                if (part->is_full)
                        break;

                avail = (page_size - part->staging_offset);
                count = (bytes > avail) ? avail : bytes;

                //
                // don't allow a write to span multiple pages unless its a raw partition.
                // this prevents data corruption in the logs where power is lost or the
                // MCU reboots unexpectedly
                //

                if (!part->is_raw && (count < bytes && bytes < page_size)) {
                        result = flash_store_flush(part);
                        if (result != FLASH_SUCCESS)
                                return result;

                        continue;   // loop around again.
                }

                if (part->is_raw)
                        staging_data = &part->staging_buffer[part->staging_offset];
                else
                        staging_data = &staging_page->data[part->staging_offset];

                memcpy(staging_data, data, count);

                bytes -= count;
                data += count;
                part->staging_offset += count;
                part->bytes_stored += count;

                if (part->staging_offset >= page_size) {
                        result = flash_store_flush(part);
                        if (result != FLASH_SUCCESS)
                                return result;
                }
        }

        return FLASH_SUCCESS;
}

bool
flash_store_lock(flash_partition_t *part, uint32_t timeout)
{
        return (osMutexWait(part->lock, timeout) == osOK);
}

void
flash_store_unlock(flash_partition_t *part)
{
        osMutexRelease(part->lock);
}

void
flash_store_set_busy(flash_partition_t *part, bool busy)
{
        flash_store_lock(part, osWaitForever);
        part->is_busy = busy;
        flash_store_unlock(part);
}

#endif

flash_result_t
flash_store_flush(flash_partition_t *part)
{
        flash_page_header_t header;
        flash_device_t *dev = part->device;
        uint32_t address = part->offset + (part->staging_page * dev->page_size);
        flash_result_t result;
        bool need_erase = false;
        uint32_t wiped_bytes = 0, wiped_pages = 0;

        // check to make sure there's actually something to flush.
        if (part->staging_offset == 0)
                return FLASH_SUCCESS;

        // Need to check the entire sector if on an erase sector boundary
        if ((address & (dev->sector_size - 1)) == 0) {
                if (!part->is_raw) {
                        // loop through the sector to make sure each page has been erased.
                        for (int page = 0; page < dev->sector_size; page += dev->page_size) {
                                dev->read(address + page, (uint8_t *)&header, sizeof(header));

                                if (flash_store_data_erased(&header, sizeof(header)))
                                        continue;

                                if (header.magic == FLASH_STORE_MAGIC && header.free_marker == NOT_FREE_MARKER) {
                                        wiped_bytes += header.size;
                                        wiped_pages++;
                                }

                                need_erase = true;
                        }
                } else {
                        need_erase = true;
                }

                if (need_erase) {
                        // printf("sector %d erasing. to wipe %d bytes, %d pages\n", address, wiped_bytes, wiped_pages);
                        result = dev->erase(address);

                        if (result != FLASH_SUCCESS) {
                                EVENT_LOG3(EVT_FS_ERASE_ERROR, "erase error",
                                           "partition", EVT_STRCONST, part->name,
                                           "result", EVT_8BIT, result,
                                           "address", EVT_32BIT, address);
                                part->is_full = 1;
                                return FLASH_ERR_FAULT;
                        }

                        if (!part->is_raw &&
                            part->staging_page >= part->page_read &&
                            part->page_read < (part->staging_page + dev->pages_per_sector)) {

                                part->pages_used   -= wiped_pages;
                                part->bytes_stored -= wiped_bytes;
                                part->page_read     = ((part->page_read + dev->pages_per_sector) & ~(dev->pages_per_sector-1)) % part->pages_total;
                        }
                }
        } else {
                result = dev->read(address, (uint8_t *)&header, sizeof(header));

                if (!flash_store_data_erased(&header, sizeof(header))) {
                        EVENT_LOG2(EVT_FS_PARTIAL_ERASED, "partial erase",
                                   "partition", EVT_STRCONST, part->name,
                                   "address", EVT_32BIT, address);
                        part->is_full = 1;
                        uart_printf("partial erase address result %d 0x%x\n", result, address);
                        return FLASH_ERR_FAULT;
                }
        }


        if (!part->is_raw) {
                flash_page_t *stage_page = (flash_page_t *) part->staging_buffer;

                stage_page->header.size = part->staging_offset;

                if (stage_page->header.size > FLASH_DATA_SIZE)
                        stage_page->header.size = FLASH_DATA_SIZE;
                else if (stage_page->header.size < FLASH_DATA_SIZE)
                        memset(&stage_page->data[stage_page->header.size], 0xff, FLASH_DATA_SIZE - stage_page->header.size);

                stage_page->header.magic       = FLASH_STORE_MAGIC;
                stage_page->header.free_marker = NOT_FREE_MARKER;
                stage_page->header.sequence    = part->sequence++;
                stage_page->footer.crc16       = crc16(stage_page, stage_page->header.size+sizeof(flash_page_header_t));
        }

        result = dev->write(address, part->staging_buffer, dev->page_size);

        if (result != FLASH_SUCCESS) {
                EVENT_LOG3(EVT_FS_WRITE_ERROR, "write error", "partition", EVT_STRCONST, part->name, "result", EVT_32BIT, result, "sector", EVT_32BIT, address);
                part->is_full = 1;
                return result;
        }

        // uart_printf("flash_store_flush: wrote sector %d.\n", address);

        part->staging_offset = 0;

        if (part->pages_used < part->pages_total)
                part->pages_used++;

        if (part->is_raw) {
                if (part->pages_used >= part->pages_total) {
                        part->is_full = 1;
                        EVENT_LOG1(EVT_FS_FULL, "fs full", "partition", EVT_STRCONST, part->name);
                } else {
                        part->staging_page++;
                }
        } else {
                part->staging_page = (part->staging_page + 1) % part->pages_total;
        }


        return FLASH_SUCCESS;
}

void
flash_store_page_mark_free(flash_partition_t *part, uint32_t page_num)
{
        flash_device_t *dev = part->device;
        flash_page_header_t header;
        uint32_t address;
        flash_result_t result;

        if (page_num == part->staging_page) {
                part->bytes_stored -= part->staging_offset;
                part->staging_offset = 0;
                return;
        }

        uint8_t free_marker = FREE_MARKER;
        address = part->offset + (page_num * dev->page_size);

        result = dev->read(address, &header, sizeof(header));
        if (result != FLASH_SUCCESS) {
                return;
        }

        if (header.magic != FLASH_STORE_MAGIC || header.free_marker != NOT_FREE_MARKER) {
                uart_printf("MARK FREE page %d magic 0x%x, marker 0x%x\n", page_num, header.magic, header.free_marker);
                return;
        }

        result = dev->write(address + FREE_MARKER_OFFSET, &free_marker, 1);

        if (result != FLASH_SUCCESS) {
                return;
        }

        if (header.size <= part->bytes_stored)
                part->bytes_stored -= header.size;
        else
                part->bytes_stored = 0;

        if (part->pages_used > 0)
                part->pages_used--;

        if (page_num == part->page_read)
                part->page_read = (part->page_read + 1) % part->pages_total;
}

void
flash_store_dump_hex(flash_partition_t *part)
{
        flash_read_t read;
        uint32_t bytes = part->bytes_stored;

        printf("dumping %d bytes\n", bytes);

        flash_read_init(part, &read, the_cache);

        for (int i = 0; i < bytes; i++) {
                if ((i%16) == 0) {
                        printf("\n0x%4.4x:", i);
                }

                printf(" %2.2x", flash_read_uint8(&read));
        }
        printf("\n");
}

#ifndef __RADIO_H__
#define __RADIO_H__

enum {
        RADIO_FLAG_OFF  = 0x1,
        RADIO_POWER_LEVEL_LIMIT = 14,
};

typedef enum {
        RADIO_RX_MODE_LOW = 0,
        RADIO_RX_MODE_HIGH,
} radio_rx_mode_t;

bool radio_init(bool power_on);
void radio_send(void *buffer, int bytes);
int  radio_read(void *buffer, uint32_t millisecs, int8_t *rssi);
int  radio_read_signal(void *buffer, uint32_t millisecs, int8_t *rssi, uint16_t *signals);
void radio_set_power_level(int8_t level);
void radio_set_rx_mode(radio_rx_mode_t mode);
void radio_power_off();
void radio_power_on();
bool  radio_is_off();
void radio_flush();
void radio_pin_setup();
void radio_stats();
void radio_chip_status();
void radio_calibrate();

#endif

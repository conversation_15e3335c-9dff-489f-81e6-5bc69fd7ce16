#ifndef __PROVISION_H__
#define __PROVISION_H__

extern uint32_t provision_sleep;

typedef enum {
        FACTORY_TEST_NOT_RUN = 0,
        FACTORY_TEST_PASSED  = 1,
        FACTORY_TEST_FAILED  = 2
} factory_test_state_t;

enum {
        PROVISION_MAGIC      = 0xB105BEEF,
        SHIPPING_SLEEP       = (10*MINUTE),
        FACTORY_SLEEP        = 10,
        SOLAR_DEPLOY_WAIT    = 30,
};

typedef enum {
        PROVISION_STAGE_FACTORY  = 0,     ///< Head to factory test
        PROVISION_STAGE_SHIPPING = 1,     ///< Wait for solar and then deploy
        PROVISION_STAGE_DEPLOYED = 2      ///< Device deployed - don't wait
} provision_stage_t;

typedef struct {
        uint32_t    magic;
        uint32_t    stage;
        uint32_t    buildstamp;
        uint32_t    test_state;
        uint8_t     sleep_until_darkness;
        uint8_t     light_count;
        uint8_t     unused1;
        uint8_t     unused2;
} provision_header_t;

void provision_board();
void provision_deployed();
void provision_factory();
void provision_shipping();
void shutdown_components_and_sleep(provision_header_t *header);

void  gps_factory_provision();
void winbond_factory_provision();
void accel_factory_provision();
void radio_factory_provision();

void set_factory_test_state(factory_test_state_t);
void set_sleep_until_darkness(uint8_t);
void set_light_count(uint8_t);


#endif

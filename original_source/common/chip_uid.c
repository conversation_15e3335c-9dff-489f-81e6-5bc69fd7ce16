/** @{

    @ingroup    common
    @file
*/

#include "stm32l4xx.h"
#include "chip_uid.h"
#include <stdint.h>

/**
    @brief  read the MCU 10-byte unique serial id
    @param[out] uid   area to store UID
*/

// TODO Ian: Verify this is the right way to get UID for this chip
void
chip_uid(uint8_t *uid)
{
        // STM32 stores UID in 96-bit (12-byte) value across 3 registers
        // at fixed address 0x1FFF7590
        uint32_t *uid_addr = (uint32_t *)0x1FFF7590;
        
        // Read UID low word (bytes 0-3)
        uint32_t value = uid_addr[0];
        uid[0] = value & 0xff;
        uid[1] = (value >> 8) & 0xff;
        uid[2] = (value >> 16) & 0xff;
        uid[3] = (value >> 24) & 0xff;
        
        // Read UID middle word (bytes 4-7)
        value = uid_addr[1];
        uid[4] = value & 0xff;
        uid[5] = (value >> 8) & 0xff;
        uid[6] = (value >> 16) & 0xff;
        uid[7] = (value >> 24) & 0xff;
        
        // Read UID high word (bytes 8-11)
        value = uid_addr[2];
        uid[8] = value & 0xff;
        uid[9] = (value >> 8) & 0xff;
        // Note: STM32 has 12-byte UID, but function returns 10 bytes
        // If you need all 12 bytes, add these lines:
        // uid[10] = (value >> 16) & 0xff;
        // uid[11] = (value >> 24) & 0xff;
}

/** @} */

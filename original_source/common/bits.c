/** @{

    @ingroup    common
    @file
    @brief      Bit manipulation routines
    @note       The routines exist to get around a ARM-v6m MCUs inability to
                do unaligned half-word (16-bit), word (32-bit), and double word (64-bit) loads and stores.
*/

#include <stdint.h>
#include "bits.h"

/**
    @brief  retrieve a 16-bit unsigned integer
    @param[in]  data    area to get 16-bit unsigned integer
    @return 16-bit unsigned integer
*/

uint16_t
get_uint16(uint8_t *data)
{
        return data[0] | (data[1] << 8);
}

/**
    @brief  store a 16-bit unsigned integer
    @param[out] data    area to store 16-bit unsigned integer
    @param[in]  value   16-bit unsigned value to store
*/

void
set_uint16(uint8_t *data, uint16_t value)
{
        data[0] = value & 0xff;
        data[1] = (value >> 8) & 0xff;
}

/**
    @brief  retrieve a 32-bit unsigned integer
    @param[in]  data    area to get 32-bit unsigned integer
    @return 32-bit unsigned integer
*/

uint32_t
get_uint32(uint8_t *data)
{
        return (data[0] | (data[1] << 8) | (data[2] << 16) | (data[3] << 24));
}

/**
    @brief  store a 32-bit unsigned integer
    @param[out] data    area to store 32-bit unsigned integer
    @param[in]  value   32-bit unsigned value to store
*/


void
set_uint32(uint8_t *data, uint32_t value)
{
        data[0] = value & 0xff;
        data[1] = (value >> 8) & 0xff;
        data[2] = (value >> 16) & 0xff;
        data[3] = (value >> 24) & 0xff;
}

/**
    @brief  retrieve a 64-bit unsigned integer
    @param[in]  data    area to get 64-bit unsigned integer
    @return 64-bit unsigned integer
*/


uint64_t
get_uint64(uint8_t *data)
{
        return ( ((uint64_t) data[0]) |
                 (((uint64_t)data[1]) << 8) |
                 (((uint64_t)data[2]) << 16) |
                 (((uint64_t)data[3]) << 24) |
                 (((uint64_t)data[4]) << 32) |
                 (((uint64_t)data[5]) << 40) |
                 (((uint64_t)data[6]) << 48) |
                 (((uint64_t)data[7]) << 56));

}

/**
    @brief  store a 64-bit unsigned integer
    @param[out] data    area to store 64-bit unsigned integer
    @param[in]  value   64-bit unsigned value to store
*/

void
set_uint64(uint8_t *data, uint64_t value)
{
        data[0] = value & 0xff;
        data[1] = (value >> 8) & 0xff;
        data[2] = (value >> 16) & 0xff;
        data[3] = (value >> 24) & 0xff;
        data[4] = (value >> 32) & 0xff;
        data[5] = (value >> 40) & 0xff;
        data[6] = (value >> 48) & 0xff;
        data[7] = (value >> 56) & 0xff;
}

/**
    @brief  store a 16-bit unsigned integer and return updated pointer
    @param[out] data    area to store 16-bit unsigned integer
    @param[in]  value   16-bit unsigned value to store
    @return data pointer incremented by 2
*/

uint8_t *
store_uint16(uint8_t *data, uint16_t value)
{
        *data++ = value & 0xff;
        *data++ = (value >> 8) & 0xff;

        return data;
}

/**
    @brief  store a 32-bit unsigned integer and return updated pointer
    @param[out] data    area to store 32-bit unsigned integer
    @param[in]  value   32-bit unsigned value to store
    @return data pointer incremented by 4
*/

uint8_t *
store_uint32(uint8_t *data, uint32_t value)
{
        *data++ = value & 0xff;
        *data++ = (value >> 8) & 0xff;
        *data++ = (value >> 16) & 0xff;
        *data++ = (value >> 24) & 0xff;

        return data;
}

/** @} */

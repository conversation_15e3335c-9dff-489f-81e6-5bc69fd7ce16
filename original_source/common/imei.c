/** @{

    @ingroup    common
    @file

    @brief  IMEI support
*/

#include "pelagic.h"
#include "imei.h"

#ifdef TARGET_VMS
#include "board_info.h"
#endif

/**
    @brief  compares two IMEIs to see if they match
    @param[in]  imei1   first IMEI to compare
    @param[in]  imei2   second IMEI to compare
    @return true if IMEIs match
*/

bool
imei_match(imei_t imei1, imei_t imei2)
{
        for (int i = 0; i < sizeof(imei_t); i++) {
                if (imei1[i] != imei2[i])
                        return false;
        }

        return true;
}

/**
    @brief  copy IMEI from one area to another
    @param[in]  src    IMEI to copy from
    @param[out] dest   area to copy to
*/

void
imei_copy(imei_t dest, imei_t src)
{
        memcpy(dest, src, sizeof(imei_t));
}

#ifdef TARGET_VMS
/**
    @brief  check to see if IMEI matches this board
    @param[in]  imei    IMEI to compare
    @return true if IMEI matches the board
*/

bool
imei_match_me(imei_t imei)
{
        return (board_info_have_imei && imei_match(imei, board_info_imei));
}

/**
    @brief  returns the IMEI of this board
    @param[out] me  area to store IMEI
*/

void
imei_set_me(imei_t me)
{
        imei_copy(me, board_info_imei);
}
#endif

/**
    @brief  attempt to match a partial IMEI comparing right to left.
    @param[in]  imei    IMEI to match again
    @param[in]  partial_imei    a partial IMEI to compare
    @return true if a match was found

    @note   This will attempt to match the last digits supplied against
            a full IMEI number. For example if the partial IMEI is
            789 it will match a full IMEI of 123456789, but not
            78912345.
*/

bool
imei_partial_match(imei_t imei, imei_t partial_imei)
{
        uint64_t imei_num, partial_num;
        uint8_t  imei_digit, partial_digit;
        bool match = true;

        imei_num = get_uint64(imei);
        partial_num = get_uint64(partial_imei);

        while (partial_num) {
                imei_digit = imei_num % 10;
                imei_num = imei_num / 10;
                partial_digit = partial_num % 10;
                partial_num = partial_num / 10;

                if (partial_digit != imei_digit)
                        match = false;
        }

        // This, and imei_match_14 are a terrible hack in January 2016 to work around
        //    buggy testboxes in China. Take out as soon as we replace those devices!
        if (!match)
                match = imei_match_14(imei, partial_imei);

        return match;
}

bool
imei_match_14(imei_t imei, imei_t partial_imei)
{
        uint64_t imei_num, partial_num;
        uint8_t  imei_digit, partial_digit;

        imei_num = get_uint64(imei);
        partial_num = get_uint64(partial_imei);

        imei_num = imei_num / 10;

        while (partial_num) {
                imei_digit = imei_num % 10;
                imei_num = imei_num / 10;
                partial_digit = partial_num % 10;
                partial_num = partial_num / 10;

                if (partial_digit != imei_digit)
                        return false;
        }

        return true;
}

/**
    @brief  convert an IMEI string into an IMEI number
    @param[in]  str ascii string to convert (e.x. "10234")
    @param[out] imei    area to store integer IMEI.
*/

void
imei_to_i(char *str, imei_t imei)
{
        uint64_t number = 0;
        int digits = 0;

        while (digits < 15 && *str) {
                if (*str >= '0' && *str <= '9') {
                        number = (number * 10) + (*str - '0');
                        digits++;
                }
                str++;
        }

        set_uint64(imei, number);
}


/**
    @brief  Test whether an IMEI string is valid
    @param[in]  str ascii string to convert (e.x. "10234")
*/

bool
imei_valid_str(char *str)
{
        bool valid = true;
        int digit_count = 0;

        while (digit_count < 15 && *str) {
                if (*str < '0' || *str > '9') {
                        valid = false;
                } else {
                        digit_count++;
                }
                str++;
        }

        return valid && digit_count == 15;
}

/** @} */

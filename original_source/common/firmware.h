#ifndef __FIRMWARE_H__
#define __FIRMWARE_H__

typedef enum {
        DEVICE_VMS    = 1,
        DEVICE_SENSOR = 2
} device_hardware_t;

typedef struct
__attribute__ ((packed))
{
        uint32_t    length;
        uint16_t    crc16;
        uint8_t     device_hardware;
        uint8_t     device_version;
        uint32_t    buildstamp;
}
firmware_header_t;

void firmware_was_updated();
void firmware_update();
void firmware_acknowledge_receipt(uint8_t device, uint8_t version, uint32_t buildstamp, uint8_t *uid);
void firmware_sensor_init();

extern firmware_header_t firmware_sensor_header;
extern flash_read_t firmware_sensor_read;
extern uint8_t firmware_sensor_cache[];

extern bool firmware_updated;
#endif

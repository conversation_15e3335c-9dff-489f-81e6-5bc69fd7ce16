/** @{

    @ingroup    common
    @file
    @brief  LED support
*/

#include "pelagic.h"
#include "led.h"
#include "gpio_api.h"

gpio_t led_green, led_red;
#ifdef TARGET_PLACO
gpio_t led_blue;
#endif

enum {
#if defined(TARGET_SENSOR)
        LED_OFF = 0,
        LED_ON  = 1,
#else
        LED_OFF = 1,
        LED_ON  = 0
#endif
};

/**
    @brief initialize LED pins
*/

void
led_init()
{
        gpio_init_out_ex(&led_green, LED_GREEN, LED_OFF);
        gpio_init_out_ex(&led_red, LED_RED, LED_OFF);

#ifdef TARGET_PLACO
        gpio_init_out_ex(&led_blue, LED_BLUE, LED_OFF);
#endif
}

/**
    @brief obtain the gpio_t for a given LED
    @param[in]  led wanted LED
    @return a gpio_t representing the led
*/

gpio_t *
led_port(int led)
{
        switch (led) {
        case LED_RED:
                return &led_red;
#ifdef TARGET_PLACO
        case LED_BLUE:
                return &led_blue;
#endif
        default:
                return &led_green;
        }
}

/**
    @brief  turn all LEDs off
*/

void
led_power_off()
{
        led_off(LED_RED);
        led_off(LED_GREEN);
#ifdef TARGET_PLACO
        led_off(LED_BLUE);
#endif
}

/**
    @brief  turn a LED on.
    @param[in]  led LED to turn on
    @note   this routine is in RAM since it is used by the firmware update code to let
            someone know that something went really, really wrong.
*/

SRAM_TEXT_SECTION
void
led_on(int led)
{
        gpio_write(led_port(led), LED_ON);
}

/**
    @brief turn a LED off
    @param[in]  led LED to turn off
*/

void
led_off(int led)
{
        gpio_write(led_port(led), LED_OFF);
}

/**
    @brief  toggle on/off a LED
    @param[in]  led LED to turn on off.
*/

void
led_toggle(int led)
{
        gpio_t *port = led_port(led);

        gpio_write(port, !gpio_read(port));
}

/** @} */

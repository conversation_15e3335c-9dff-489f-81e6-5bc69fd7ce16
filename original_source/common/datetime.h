#ifndef __DATETIME_H__
#define __DATETIME_H__

enum {
        EPOCH_YEAR = 2014
};

typedef struct {
        uint16_t    year;
        uint8_t     month;
        uint8_t     day;

        uint8_t     hour;
        uint8_t     minute;
        uint8_t     second;
} datetime_t;

uint32_t datetime_to_seconds(datetime_t *date);
uint32_t date_to_seconds(uint16_t year, uint8_t month, uint8_t day);
void seconds_to_datetime(uint32_t time, datetime_t *date);
void datetime_display(uint32_t timestamp);

int32_t timezone_offset(double longitude);
#endif

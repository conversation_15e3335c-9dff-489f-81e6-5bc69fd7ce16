#include "rt_TypeDef.h"
#include "pelagic.h"

/**
    @brief errno implementation

    @note Simple function that returns a pointer to the task specific
    errno.  Required because our toolchain libm.a is built expecting a
    __errno function.
*/
volatile int *
__errno(void)
{
        return &(((struct OS_TCB *)osThreadGetId())->errno);
}

// memcpy is pulled from newlib

/* Nonzero if either X or Y is not aligned on a "long" boundary.  */
#define UNALIGNED(X, Y) \
  (((long)X & (sizeof (long) - 1)) | ((long)Y & (sizeof (long) - 1)))

/* How many bytes are copied each iteration of the 4X unrolled loop.  */
#define BIGBLOCKSIZE    (sizeof (long) << 2)

/* How many bytes are copied each iteration of the word copy loop.  */
#define LITTLEBLOCKSIZE (sizeof (long))

/* Threshhold for punting to the byte copier.  */
#define TOO_SMALL(LEN)  ((LEN) < BIGBLOCKSIZE)

/**
    @brief  classic memory move
    @param[out] dst0    destination address
    @param[in]  src0    source address
    @param[in]  len0    bytes to move

    @note   this routine live in RAM to be used with the firmware update.
            Taken from the newlib distribution.
*/

SRAM_TEXT_SECTION void *
memcpy(void *restrict dst0, const void *restrict src0, int len0)
{
        char *dst = dst0;
        const char *src = src0;
        long *aligned_dst;
        const long *aligned_src;

        /* If the size is small, or either SRC or DST is unaligned,
           then punt into the byte copy loop.  This should be rare.  */
        if (!TOO_SMALL(len0) && !UNALIGNED (src, dst)) {
                aligned_dst = (long*)dst;
                aligned_src = (const long*)src;

                /* Copy 4X long words at a time if possible.  */
                while (len0 >= BIGBLOCKSIZE) {
                        *aligned_dst++ = *aligned_src++;
                        *aligned_dst++ = *aligned_src++;
                        *aligned_dst++ = *aligned_src++;
                        *aligned_dst++ = *aligned_src++;
                        len0 -= BIGBLOCKSIZE;
                }

                /* Copy one long word at a time if possible.  */
                while (len0 >= LITTLEBLOCKSIZE) {
                        *aligned_dst++ = *aligned_src++;
                        len0 -= LITTLEBLOCKSIZE;
                }

                /* Pick up any residual with a byte copier.  */
                dst = (char*)aligned_dst;
                src = (char*)aligned_src;
        }

        while (len0--)
                *dst++ = *src++;

        return dst0;
}

/**
    @brief  classic memory set
    @param[out]  dst0   destination to set values
    @param[in]  value0  value to set
    @param[in]  len0    bytes to set
*/

void *
memset(void *restrict dst0, int value0, int len0)
{
        unsigned char *dst = dst0, value = value0;

        while (len0-- > 0) {
                *dst++ = value;
        }

        return dst0;
}

/**
    @brief  string length
    @param[in]  str string to count
    @return bytes found in string (excluding null)
*/

int
strlen(const char *str)
{
        int size = 0;

        while (*str++)
                size++;

        return size;
}

/**
    @brief  attempts to find the length of the string, but never scans beyond a specific point
    @param[in]  str string to count
    @param[in]  maxlen  limit byte count - do not go beyond this
    @return bytes found
*/

int
strnlen(const char *str, int maxlen)
{
        int len = 0;

        // NOTE: Don't check for less-than < 0 on maxlen
        // strnlen allows a negative max length value

        while (*str++ && maxlen--) {
                len++;
        }

        return len;
}

/**
    @brief classic string copy - move string with null byte
    @param[out] dst destination string
    @param[in]  src source string
    @return the dst plus length of string
*/

char *
strcpy(char *dst, const char *src)
{
        while ((*dst++ = *src++) != 0)
                ;

        return dst;
}

/**
    @brief classic strcmp - compares to strings
    @param[in]  p1  first string
    @param[in]  p2  second string
    @return 0 if strings are equal
*/

int
strcmp(const char *p1, const char *p2)
{
        const unsigned char *s1 = (const unsigned char *) p1;
        const unsigned char *s2 = (const unsigned char *) p2;
        unsigned char c1, c2;

        do {
                c1 = (unsigned char) *s1++;
                c2 = (unsigned char) *s2++;
                if (c1 == '\0')
                        return c1 - c2;
        } while (c1 == c2);

        return c1 - c2;
}

/**
    @brief classic strcmp - compares to strings with byte limit
    @param[in]  p1  first string
    @param[in]  p2  second string
    @param[in]  n   do not scan beyond byte limit
    @return 0 if strings are equal
*/

int
strncmp(const char *s1, const char *s2, int n)
{
        unsigned char c1 = 0, c2 = 0;

        while (n > 0) {
                c1 = (unsigned char) *s1++;
                c2 = (unsigned char) *s2++;

                if (c1 == '\0' || c1 != c2)
                        return c1 - c2;
                n--;
        }

        return c1 - c2;
}

/**
    @brief  the absolute value of a number
    @param[in]  i   integer
    @return the absolute value of i
*/

int
abs (int i)
{
        return i < 0 ? -i : i;
}

/**
    @brief  convert a string to an integer
    @param[in]  str string to convert
    @return the integer of str
    @note the following formats are support:
            - "-NN" negative integer
            - "0xNN" hexadecimal string
*/
int
atoi(char *str)
{
        int number = 0, digit;
        int base = 10, neg = 0;

        while (*str == ' ')
                str++;

        if (*str == '-') {
                neg = 1;
                str++;
        }

        if (str[0] == '0' && str[1] == 'x') {
                base = 16;
                str += 2;
        }

        while (*str) {
                if (*str >= '0' && *str <= '9') {
                        digit = *str - '0';
                } else if (base == 16) {
                        if (*str >= 'a' && *str <= 'f')
                                digit = (*str - 'a') + 10;
                        else if (*str >= 'A' && *str <= 'F') {
                                digit = (*str - 'A') + 10;
                        } else {
                                str++;
                                continue;
                        }
                } else {
                        str++;
                        continue;
                }

                number = (number * base) + digit;
                str++;
        }

        return neg ? -number : number;
}

int32_t random_seed = 123;

/**
    @brief  initialize the randon function generator with a seed
*/

void
random_setup_seed()
{
        random_seed = board_uid[0];

        for (int i = 1; i < BOARD_UID_SIZE; i++) {
                random_seed = ((int32_t) board_uid[i]) ^ random_seed;
        }
}

/**
    @brief  obtain a random-ish number
    @return random number
*/

int32_t
rand(void)
{
        random_seed = ((random_seed * 1103515245) + (rtc_read() & 0xffff)) & 0x7fffffff;

        return random_seed;
}

/**
    @brief  convert a string to a floating point number
    @param[in]  s   string to convert
    @return a floating point number
*/

double
atof(char* s)
{
        long  integer_part = 0;
        double decimal_part = 0.0;
        double decimal_pivot = 0.1;
        int isdecimal = 0, isnegative = 0;

        char c;
        while ( ( c = *s++) )  {
                // skip special/sign chars
                if (c == '-') {
                        isnegative = 1;
                        continue;
                }
                if (c == '+') continue;
                if (c == '.') {
                        isdecimal = 1;
                        continue;
                }

                if (!isdecimal) {
                        integer_part = (10 * integer_part) + (c - 48);
                } else {
                        decimal_part += decimal_pivot * (double)(c - 48);
                        decimal_pivot /= 10.0;
                }
        }
        // add integer part
        decimal_part += (double)integer_part;

        // check negative
        if (isnegative)  decimal_part = - decimal_part;

        return decimal_part;
}

/** @} */

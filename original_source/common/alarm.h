#ifndef __ALARM_H__
#define __ALARM_H__

typedef struct alarm_s {
        int32_t         timestamp;
        int32_t         periodic;
        volatile bool   *buzzer;
        osThreadId      thread_id;
        struct alarm_s  *next;
} alarm_t;

void alarm_init();
void alarm_start(alarm_t *alarm, int32_t seconds);
void alarm_start_periodic(alarm_t *alarm, int32_t seconds);
void alarm_start_set(alarm_t *alarm, int32_t seconds, volatile bool *buzzer);
void alarm_cancel(alarm_t *alarm);
uint32_t alarm_remain(alarm_t *alarm);
void alarm_tick();

#endif

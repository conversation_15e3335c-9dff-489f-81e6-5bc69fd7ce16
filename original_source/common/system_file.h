#ifndef __SYSTEM_FILE_H__
#define __SYSTEM_FILE_H__

#ifdef TARGET_SENSOR
#include "pelagic-types.h"
#endif

// Forward declarations for STM32 types (actual includes in .c file)
#ifndef TARGET_TEST
#include <stdint.h>
#endif

// System register file is a 32-byte area that retains power all the way
// down to VLLS0 - handy for persistence between power cycles
// Now implemented using STM32L431 RTC backup registers for true VBAT persistence
//

enum {
        SYS_REG_FILE_MAGIC  = 0xbee1,
        SYS_REG_FILE_SIZE   = 32,            // size of system file in bytes
        SYS_REG_FILE_NUM_REGS = 8,           // number of 32-bit backup registers needed (32 bytes / 4 bytes)

        REBOOT_FAULT_LIMIT  = 64,
};

typedef struct
__attribute__ ((packed))
{
        uint16_t    magic;
        uint8_t     reboot_reason;
        uint8_t     fault_count;
        uint32_t    timestamp;

        union {
                struct {
                        uint32_t    pc;
                        uint32_t    lr;
                } fault;

                struct {
                        uint32_t    code;
                        uint32_t    tid;
                } os_error;

        };
        uint8_t     rtc_status;
        uint8_t     firmware_updated;
#ifdef TARGET_SENSOR
        uint8_t     radio_power;
        uint16_t    host_ttl;
        board_uid_t host_uid;
        uint8_t     have_host;
#else
        uint8_t    _unused1[2]; // pad out for alignment
        uint32_t   sleep_remaining;
#endif
}
system_register_file_t;

typedef enum {
        REBOOT_NORMAL    = 0, // Normal reboot/power on
        REBOOT_FIRMWARE  = 1, // Firmware upgraded
        REBOOT_AUTO      = 2, // Automatic reboot
        REBOOT_CRITICAL  = 3, // Coming out of emergency shutdown due low power
        REBOOT_SHIPPING  = 4, // In Shipping Mode – check periodically
        REBOOT_FACTORY   = 5, // In Factory Mode – check frequently
        REBOOT_NAP       = 6, // Napping
        REBOOT_ACCEL_NAP = 7, // Wake to check for movement

        REBOOT_NOT_FAULT = 9,

        REBOOT_FAULT     = 10, // Hard Fault
        REBOOT_OSERROR   = 11 // RTX OS Error
} reboot_reason_t;

// RTC backup register access functions
// These handle the low-level backup domain setup and register access
int sys_file_backup_init(void);
void sys_file_backup_write_reg(uint32_t reg_index, uint32_t data);
uint32_t sys_file_backup_read_reg(uint32_t reg_index);
void sys_file_sync_to_backup(void);  // Force sync current data to backup registers

// High-level system file interface
#ifdef TARGET_TEST
extern system_register_file_t test_sys_reg_file;
#define SYS_REG_FILE (&test_sys_reg_file)
#else
// For STM32L431, we use a virtual pointer that maps to backup registers
extern system_register_file_t *sys_reg_file_ptr;
#define SYS_REG_FILE (sys_reg_file_ptr)
#endif

void sys_file_init();
void sys_file_clear();

// Macro to automatically sync after modifying SYS_REG_FILE
// Usage: SYS_REG_FILE_UPDATE(SYS_REG_FILE->reboot_reason = REBOOT_FAULT);
#ifndef TARGET_TEST
#define SYS_REG_FILE_UPDATE(expr) do { (expr); sys_file_sync_to_backup(); } while(0)
#else
#define SYS_REG_FILE_UPDATE(expr) (expr)
#endif

/*
 * IMPORTANT SETUP NOTES FOR STM32L431 RTC BACKUP REGISTERS:
 *
 * 1. EARLY INITIALIZATION: sys_file_init() must be called early in main()
 *    before any other sys_file operations.
 *
 * 2. BACKUP DOMAIN SETUP: The implementation automatically handles:
 *    - PWR clock enabling (__HAL_RCC_PWR_CLK_ENABLE())
 *    - Backup domain access (HAL_PWR_EnableBkUpAccess())
 *    - RTC handle initialization for backup register access
 *
 * 3. PERSISTENCE: Data is automatically synchronized to/from backup registers:
 *    - On sys_file_init(): loads from backup registers
 *    - On any modification: saves to backup registers
 *    - Call sys_file_sync_to_backup() to force immediate sync
 *
 * 4. POWER REQUIREMENTS:
 *    - VBAT must be connected for true persistence across power loss
 *    - Without VBAT, data persists through resets but not power loss
 *
 * 5. REGISTER USAGE: Uses RTC backup registers 0-7 (32 bytes total)
 *    - Each register is 32-bit (4 bytes)
 *    - Total: 8 registers × 4 bytes = 32 bytes (matches SYS_REG_FILE_SIZE)
 */

#endif

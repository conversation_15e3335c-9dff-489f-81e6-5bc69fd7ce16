#ifndef __FLASH_STORE_H__
#define __FLASH_STORE_H__

// Include RTOS headers for mutex types
#include "cmsis_os.h"
#include <stdbool.h>
#include "stm32l4xx.h"

typedef enum {
        FLASH_SUCCESS = 0,

// Hardware errors - mostly Kinetis errors
        FLASH_ERR_PROTECTED,    // Attempted to write to a protected area
        FLASH_ERR_ACCESS,       // Accces error
        FLASH_ERR_COLLISION,    // Collision - attempting to write while reading from same area
        FLASH_ERR_RUNTIME,      // Runtime timeout error
        FLASH_ERR_FAULT,        // Some general fault

// Software error checks
        FLASH_ERR_GUARDED,      // attempted to write in where the app lives.
        FLASH_ERR_ALIGNMENT     // address is not aligned on a word or sector
} flash_result_t;


// TODO Ian: these numbers are probably wrong/old
enum {
        // FLASH_PAGE_SIZE   = 256,
        FLASH_STORE_MAGIC = 0xF00B,
        FLASH_DATA_SIZE   = 238,

        NOT_FREE_MARKER   = 0x99,
        FREE_MARKER       = 0,
        FREE_MARKER_OFFSET = 3,
};


typedef struct {
        flash_result_t    (*erase)(uint32_t address);
        flash_result_t    (*write)(uint32_t address, void *buffer, int bytes);
        flash_result_t    (*read)(uint32_t address, void *buffer, int bytes);
        void              (*keep_on)(bool power_on);

        const uint16_t  page_size;
        const uint16_t  sector_size;
        const uint16_t  pages_per_sector;
} flash_device_t;

extern flash_device_t stm_flash_device;

typedef struct {
        const char       *name;        // name - for debugging
        const uint32_t   offset;       // offset within the entire flash device
        const uint32_t   size;         // size of this partition in bytes
        flash_device_t   *const device;
        const bool       is_raw;       // don't use page headers - intended as tmp storage.

        uint32_t         pages_total;  // partition size in pages

        bool        is_full;           // set non-zero if full
        bool        is_busy;           // partition is busy and should not be written to. (uploading, erasing, etc)

        uint32_t    page_read;

        uint32_t    sequence;
        uint32_t    pages_used;          // total flash pages used
        uint32_t    bytes_stored;        // how many bytes stored (may be gaps in pages)

        uint8_t     *staging_buffer;
        uint32_t    staging_page;       // Where the staging page is
        uint16_t     staging_offset;     // the offset into the buffer

#ifdef TARGET_VMS
        osMutexId     lock;
        osMutexDef_t  mutex;
        uint32_t      mutex_data[4];
#endif
} flash_partition_t;

typedef struct
__attribute__ ((packed))
{
        uint16_t        magic;
        uint8_t         size;
        uint8_t         free_marker;
        uint32_t        sequence;
        uint8_t         imei[8];
}
flash_page_header_t;

typedef struct
__attribute__ ((packed))
{
        uint16_t        crc16;
}
flash_page_footer_t;

// Each flash page has a header to determine if its occupied or not.
typedef struct
__attribute__ ((packed))
{
        flash_page_header_t header;
        uint8_t             data[FLASH_DATA_SIZE];
        flash_page_footer_t footer;
}
flash_page_t;

typedef struct  {
        flash_partition_t   *partition; // which partition reading from
        uint32_t            page;       // the page within the partition
        uint8_t             *cache;
        uint32_t            offset;     // byte offset
        uint16_t            position;   // the position within the cache
        volatile bool       is_eof;     // End of the partition hit.
} flash_read_t;

void flash_store_init();
flash_result_t flash_store_flush(flash_partition_t *part);
void flash_store_erase(flash_partition_t *part);
flash_result_t flash_store_write(flash_partition_t *part, uint8_t *data, uint32_t bytes);
void flash_store_dump_hex(flash_partition_t *part);
void flash_store_erase_all();
void flash_store_setup(flash_partition_t *part);
void flash_store_page_mark_free(flash_partition_t *part, uint32_t page_num);
bool flash_store_partial_okay(flash_partition_t *part, uint32_t bytes);
bool flash_store_page_valid(flash_partition_t *part, flash_page_t *page, uint32_t address);
bool flash_store_lock(flash_partition_t *part, uint32_t timeout);
void flash_store_unlock(flash_partition_t *part);
void flash_store_set_busy(flash_partition_t *part, bool busy);


void flash_read_init(flash_partition_t *part, flash_read_t *read, uint8_t *cache);
uint32_t flash_read_block(flash_read_t *read, void *buffer, uint32_t bytes);
bool flash_read_seek(flash_read_t *read, uint32_t offset);
uint32_t flash_read_page(flash_read_t *read, void *buffer, uint32_t *page_num);
bool flash_store_data_erased(void *data, const int bytes);
uint8_t flash_read_uint8(flash_read_t *read);
uint16_t flash_read_uint16(flash_read_t *read);
uint32_t flash_read_uint32(flash_read_t *read);


#ifdef TARGET_VMS
extern flash_partition_t boatlog_partition,
       firmware_partition, firmware_sensor_partition,
       event_partition, bnet_partition;
#endif

#ifdef TARGET_SENSOR
extern flash_partition_t firmware_partition;
#endif

extern uint8_t the_cache[FLASH_PAGE_SIZE];

#endif

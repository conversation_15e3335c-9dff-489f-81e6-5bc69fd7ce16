/** @{

    @ingroup    vms
    @file

    @brief      flash storage streaming
*/

#include "pelagic.h"

/**
    @brief  fill the streaming buffer from flash storage, or set things
            to the staging buffer if the stream has reached that.
    @param[in]  read    flash stream
    @return true if fill was successful
*/

bool
flash_read_fill(flash_read_t *read)
{
        flash_partition_t *part = read->partition;
        uint32_t address = part->offset + (read->page * part->device->page_size);
        flash_result_t result;

        // just in the staging buffer - don't read flash
        if (read->page == part->staging_page) {
                return true;
        }

        result = part->device->read(address, read->cache, part->device->page_size);

        if (result != FLASH_SUCCESS) {
                uart_printf("flash_read_fill: error [%d]\n", result);
                read->is_eof = true;
                return false;
        }

        if (part->is_raw)
                return true;

        if (flash_store_page_valid(part, (flash_page_t *) read->cache, address) == true)
                return true;
        else {
                uart_printf("flash_read_fill: part [%s] page [%d] not valid \n", part->name, read->page);
        }

        read->is_eof = true;
        return false;
}

/**
    @brief  initialize a stream
    @param[in]  part    flash partition to stream from
    @param[out] read    stream structure to initialize
    @param[in]  cache    buffer area to cache data into
*/

void
flash_read_init(flash_partition_t *part, flash_read_t *read, uint8_t *cache)
{
        read->partition = part;
        read->page      = part->is_raw ? 0 : part->page_read;
        read->position  = 0;
        read->offset    = 0;
        read->cache     = cache;
        read->is_eof    = false;

        flash_read_fill(read);
}

/**
    @brief  seek to a given position within a partition
    @param[in]  read    flash stream
    @param[in]  offset  new position to seek to in bytes
    @return true if seek was successful. false if a read error occured,
            or the offset was beyond the partition size.
*/

bool
flash_read_seek(flash_read_t *read, uint32_t offset)
{
        flash_partition_t *part = read->partition;

        if (part->is_raw) {
                uint32_t page = (offset & ~(part->device->page_size - 1));

                read->position = offset - page;

                if (read->page == page)
                        return true;

                read->page = page;
                read->offset = offset;

                return flash_read_fill(read);
        }

        if (offset >= part->bytes_stored)
                return false;

        int32_t position = (offset - read->offset);

        // nothing to be done -- most common case
        if (position == 0)
                return true;

        if (position < 0) {
                // moving backwards - start from the beginning
                read->page      = part->page_read;
                read->position  = 0;
                read->offset     = 0;

                if (flash_read_fill(read) == false)
                        return false;
        }

        flash_page_t *page = (flash_page_t *) read->cache;

        for (;;) {
                uint32_t remaining = offset - read->offset;
                uint32_t left_in_page = page->header.size - read->position;

                if (remaining <= left_in_page) {
                        read->offset += remaining;
                        read->position = offset % part->device->page_size;
                        return true;
                }

                read->page = (read->page + 1) % part->pages_total;
                read->offset += left_in_page;
                read->position = 0;

                if (flash_read_fill(read) == false)
                        return false;
        }
}

/**
    @brief  read data from a "raw" (unmanaged) partition
    @param[in]  read    flash stream
    @param[out] buffer  area to store data
    @param[in]  bytes   count requested
    @return bytes read
*/

uint32_t
flash_read_block_raw(flash_read_t *read, void *buffer, uint32_t bytes)
{
        flash_partition_t *part = read->partition;
        const uint16_t page_size = part->device->page_size;
        uint8_t *data = (uint8_t *) buffer;
        int count, bytes_read = 0;

        while (bytes > 0) {
                if (read->page == part->staging_page) {
                        count = part->staging_offset - read->position;
                        if (count <= 0)
                                return bytes_read;

                        if (count > bytes)
                                count = bytes;

                        memcpy(data, &part->staging_buffer[read->position], count);
                        read->position += count;
                        read->offset += count;
                        return (bytes_read + count); // cannot go beyond the staging buffer regardless of what's left
                }

                if (read->position >= page_size) {
                        read->page++;
                        read->position = 0;

                        if (read->page != part->staging_page) {
                                if (!flash_read_fill(read)) {
                                        return bytes_read;
                                }
                        }

                        continue;
                }

                count = page_size - read->position;
                if (count > bytes)
                        count = bytes;

                memcpy(data, &read->cache[read->position], count);
                read->position += count;
                read->offset += count;
                bytes -= count;
                data += count;
                bytes_read += count;
        }

        return bytes_read;
}

/**
    @brief  read data from a managed flash partition
    @param[in]  read    flash stream
    @param[out] buffer  area to store data
    @param[in]  bytes   count requested
    @return bytes read
*/

uint32_t
flash_read_block(flash_read_t *read, void *buffer, uint32_t bytes)
{
        if (read->partition->is_raw)
                return flash_read_block_raw(read, buffer, bytes);

        flash_partition_t *part = read->partition;
        flash_page_t *page;
        uint8_t *data = (uint8_t *) buffer;
        int count, bytes_read = 0;

        while (bytes > 0) {
                if (read->is_eof)
                        return bytes_read;

                if (read->page == part->staging_page) {
                        page = (flash_page_t *)part->staging_buffer;
                        count = part->staging_offset - read->position;

                        if (count <= 0) {
                                read->is_eof = true;
                                return bytes_read;
                        }

                        if (count > bytes)
                                count = bytes;

                        memcpy(data, &page->data[read->position], count);
                        read->position += count;
                        read->offset += count;

                        bytes_read += count;

                        if (read->position >= part->staging_offset)
                                read->is_eof = true;

                        return bytes_read;
                }

                page = (flash_page_t *) read->cache;

                if (read->position >= page->header.size) {
                        read->page = (read->page + 1) % part->pages_total;
                        read->position = 0;

                        if (read->page != part->staging_page) {
                                if (!flash_read_fill(read))
                                        return bytes_read;
                        }

                        continue;
                }

                count = page->header.size - read->position;
                if (count > bytes)
                        count = bytes;

                memcpy(data, &page->data[read->position], count);
                read->position += count;
                read->offset += count;
                bytes -= count;
                data += count;
                bytes_read += count;
        }

        return bytes_read;
}

/**
    @brief  read the next available flash page from stream
    @param[in]  read    flash stream
    @param[out] buffer  area to store the page
    @param[out] page_number the flash page read
    @return bytes read - depends on how much was stored in the page
*/

uint32_t
flash_read_page(flash_read_t *read, void *buffer, uint32_t *page_number)
{
        flash_partition_t *part = read->partition;
        flash_page_t *page = (flash_page_t *) read->cache;
        int bytes;

        for (;; ) {
                if (read->is_eof)
                        return 0;

                if (read->page == part->staging_page) {
                        flash_page_t  *staging_page = (flash_page_t *)part->staging_buffer;
                        bytes = part->staging_offset - read->position;

                        if (bytes <= 0) {
                                read->is_eof = true;
                                return 0;
                        }

                        memcpy(buffer, &staging_page->data[read->position], FLASH_DATA_SIZE);
                        read->is_eof = true;
                        *page_number = part->staging_page;
                        return bytes;
                }

                if (read->position >= page->header.size) {
                        read->page = (read->page + 1) % part->pages_total;
                        read->position = 0;

                        if (read->page != part->staging_page) {
                                if (!flash_read_fill(read)) {
                                        read->is_eof = true;
                                        return 0;
                                }
                        }

                        continue;
                }

                memcpy(buffer, &page->data, page->header.size);
                read->position += page->header.size;
                *page_number = read->page;

                return page->header.size;
        }
}

/**
    @brief  read a byte from a flash stream
    @param[in]  read    flash stream
    @return byte read
*/

uint8_t
flash_read_uint8(flash_read_t *read)
{
        uint8_t ch;
        flash_read_block(read, &ch, 1);
        return ch;
}

/**
    @brief  read a 16-bit word from flash stream
    @param[in]  read    flash stream
    @return unsigned 16-bit word
*/

uint16_t
flash_read_uint16(flash_read_t *read)
{
        uint16_t value;

        value = flash_read_uint8(read);
        value |= ((uint16_t) flash_read_uint8(read)) << 8;

        return value;
}

/**
    @brief  read a 32-bit word from flash stream
    @param[in]  read    flash stream
    @return unsigned 32-bit word
*/

uint32_t
flash_read_uint32(flash_read_t *read)
{
        uint32_t value;

        value = (uint32_t) flash_read_uint8(read);
        value |= ((uint32_t) flash_read_uint8(read)) << 8;
        value |= ((uint32_t) flash_read_uint8(read)) << 16;
        value |= ((uint32_t) flash_read_uint8(read)) << 24;

        return value;
}

/** @} */

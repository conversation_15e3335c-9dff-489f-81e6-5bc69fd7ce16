#include "pelagic.h"
#include "chip_uid.h"

#ifdef TARGET_SENSOR
#include "system_file.h"
#endif

board_uid_t board_uid;

void
uid_init()
{
        chip_uid(board_uid);
}

bool
uid_match(board_uid_t uid1, board_uid_t uid2)
{
        for (int i = 0; i < BOARD_UID_SIZE; i++) {
                if (uid1[i] != uid2[i])
                        return false;
        }

        return true;
}

void
uid_copy(board_uid_t dst, board_uid_t src)
{
        memcpy(dst, src, sizeof(board_uid_t));
}

void
uid_clear(board_uid_t uid)
{
        memset(uid, 0, sizeof(board_uid_t));
}

bool
uid_match_me(board_uid_t uid)
{
        return uid_match(board_uid, uid);
}

void
uid_set_me(board_uid_t uid)
{
        uid_copy(uid, board_uid);
}

#ifdef TARGET_SENSOR
bool
uid_match_host(board_uid_t uid)
{
        return uid_match(uid, SYS_REG_FILE->host_uid);
}

void
uid_set_host(board_uid_t uid)
{
        uid_copy(uid, SYS_REG_FILE->host_uid);
}
#endif

void
str_to_uid(char *str, board_uid_t uid)
{
        int digit = 0;
        bool top_nibble = true;
        int number;

        while (digit < 20 && *str) {
                if (*str >= '0' && *str <= '9') {
                        number = *str - '0';
                } else if (*str >= 'A' && *str <= 'F') {
                        number = 10 + (*str - 'A');
                } else if (*str >= 'a' && *str <= 'f') {
                        number = 10 + (*str - 'a');
                } else {
                        number = -1;
                }

                if (number > -1) {
                        if (top_nibble) {
                                uid[digit] = (number << 4);
                                top_nibble = false;
                        } else {
                                uid[digit] = uid[digit] | number;
                                top_nibble = true;
                                digit++;
                        }
                }
                str++;
        }
}

#ifndef __EVENT_H__
#define __EVENT_H__

//
// Event Log Structure
// Byte 0, Bits 4..0: Event Number
// Byte 0, Bits 7..5: Source Lower
// Byte 1, Bits 1..0: Source upper
// Byte 1, Bits 3..2: Level (0-3)
// Byte 1, Bits 6..4: Param count (0 - 7)
// Byte 1, Bit 7: Full Timestamp
// if Full Timestamp is set then
//  Byte 2 - 5: Full timestamp value
// else
//  Byte 2: Timestamp increment
// end
// param_header Bytes 2 + t to 2+t+((param count + 1)/2)
// params Bytes 2 + t + param_header size ..  n

enum {
        EVT_NUM_SHIFT             = 0,
        EVT_NUM_MASK              = 0x1f,

        EVT_SOURCE_SHIFT          = 5,
        EVT_SOURCE_MASK           = 0x1f,

        EVT_LEVEL_SHIFT           = 10,
        EVT_LEVEL_MASK            = 0x3,

        EVT_HEADER_EVENT_MASK     = 0x1fff,

        EVT_HEADER_PARAMS_SHIFT   = 12,

        EVT_HEADER_PARAMS_MASK    = 0x7,

        EVT_FULL_TIMESTAMP_BIT    = 0x8000,

        EVT_RECORD_MARKER         = 0x99,

        // extracted and used by libpds
        EVT_MSG_VERSION           = 6,
};


typedef enum {
// do not change the following comment, its used for message extraction
// Up to 32 possible event sources
// begin sources
        EVT_SYS       = 0, // General system
        EVT_BOATNET   = 1, // Boat net
        EVT_FS        = 2, // Flash store
        EVT_FLASH     = 3, // Winbond flash
        EVT_SERIAL    = 4, // Serial port
        EVT_GPS       = 5, // GPS thread
        EVT_HELM      = 6, // Helm thread
        EVT_POWER     = 7, // Power related
        EVT_FIRMWARE  = 8, // Related to firmware updates
        EVT_BT        = 9, // bodytrace interactions
        EVT_RADIO     = 10, // Radio interface
        EVT_MODEM     = 11, // Modem thread
        EVT_REBOOT    = 12, // Reboot thread
        EVT_ACCEL     = 13, // accel thread
        EVT_LOG       = 14, // logs (cell signal, energy) embedded within the event log
        EVT_FAULT     = 15, // fatal errors
        EVT_EVENT     = 16, // event event! (mostly about settings)
        EVT_SETTING   = 17, // settings
        EVT_NAP       = 18, // nap settings
        EVT_SATELLITE = 19, // Satellite Related
        EVT_HUMIDITY  = 20, // Humidity sensor related
        EVT_MEMS      = 21, // MEMS - Accelerometer & Magnetometer chip.
        EVT_SENSOR    = 22, // Sensor device - mostly firmware update notices.
        EVT_PDS       = 23, // PDS messages
        EVT_NFC	  = 24, // NFC messages
// end sources
} event_source_t;

typedef enum {
// do not change the following comment, its used for message extraction
// begin levels
        EVT_INFO        = 0,    // general information - typically debugging
        EVT_PROF        = 1,    // profiling events (modem on duration, bytes transfered, etc)
        EVT_NOTE        = 2,    // event to always send to server
        EVT_ERR         = 3,    // problematic error (board fault, device issue, etc.)
// end levels
} event_level_t;

#define EVENT_T(source, level, num) ((level << EVT_LEVEL_SHIFT) | (source << EVT_SOURCE_SHIFT) | num)

typedef enum {
// do not change the following comment, its used for message extraction
// begin events
        EVT_SYS_BOOT                    = EVENT_T(EVT_SYS, EVT_NOTE, 1),
        EVT_SYS_TIME_SET                = EVENT_T(EVT_SYS, EVT_NOTE, 2),
        EVT_SYS_TIME_ADJUST             = EVENT_T(EVT_SYS, EVT_NOTE, 3),
        EVT_SYS_BUILD                   = EVENT_T(EVT_SYS, EVT_NOTE, 4),
        EVT_SYS_NOTE                    = EVENT_T(EVT_SYS, EVT_NOTE, 5),    // Free format logging

        EVT_BOARD_LOG                   = EVENT_T(EVT_LOG, EVT_NOTE, 1),
        EVT_CELL_LOG                    = EVENT_T(EVT_LOG, EVT_NOTE, 2),

        EVT_FIRMWARE_UPGRADE_MSG_SENT   = EVENT_T(EVT_FIRMWARE, EVT_INFO, 1),
        EVT_FIRMWARE_UPGRADE_MSG_FAIL   = EVENT_T(EVT_FIRMWARE, EVT_ERR, 2),
        EVT_FIRMWARE_IMAGE_CRC_ERROR    = EVENT_T(EVT_FIRMWARE, EVT_ERR, 3),
        EVT_FIRMWARE_SIZE_ERROR         = EVENT_T(EVT_FIRMWARE, EVT_ERR, 4),
        EVT_FIRMWARE_UPDATED            = EVENT_T(EVT_FIRMWARE, EVT_NOTE, 5),

        EVT_FS_INIT                     = EVENT_T(EVT_FS, EVT_INFO, 1),
        EVT_FS_SIZE_ERROR               = EVENT_T(EVT_FS, EVT_ERR, 2),
        EVT_FS_NOT_ERASED               = EVENT_T(EVT_FS, EVT_ERR, 3),
        EVT_FS_ERASED                   = EVENT_T(EVT_FS, EVT_INFO, 4),
        EVT_FS_PARTIAL_ERASED           = EVENT_T(EVT_FS, EVT_ERR, 5),
        EVT_FS_ERASE_ERROR              = EVENT_T(EVT_FS, EVT_ERR, 6),
        EVT_FS_WRITE_ERROR              = EVENT_T(EVT_FS, EVT_ERR, 7),
        EVT_FS_FULL                     = EVENT_T(EVT_FS, EVT_INFO, 8),

        EVT_GPS_INIT                    = EVENT_T(EVT_GPS, EVT_INFO, 1),
        EVT_GPS_COMMAND_ERR             = EVENT_T(EVT_GPS, EVT_ERR, 2),
        EVT_GPS_COMMAND_NOT_CONFIRMED   = EVENT_T(EVT_GPS, EVT_ERR, 3),
        EVT_GPS_POWER_OFF               = EVENT_T(EVT_GPS, EVT_PROF, 4),
        EVT_GPS_POWER_ON                = EVENT_T(EVT_GPS, EVT_PROF, 5),
        EVT_GPS_HIGH_RES                = EVENT_T(EVT_GPS, EVT_PROF, 6),
        EVT_GPS_STATIONARY              = EVENT_T(EVT_GPS, EVT_NOTE, 7),
        EVT_GPS_MOVING                  = EVENT_T(EVT_GPS, EVT_NOTE, 8),
        EVT_GPS_SET_IDLE                = EVENT_T(EVT_GPS, EVT_NOTE, 9),
        EVT_GPS_SET_PERIODIC            = EVENT_T(EVT_GPS, EVT_NOTE, 10),
        EVT_GPS_SET_RUN_MODE            = EVENT_T(EVT_GPS, EVT_NOTE, 11),
        EVT_GPS_STATE_CHANGE            = EVENT_T(EVT_GPS, EVT_NOTE, 12), // DEPRECATED
        EVT_GPS_DOCK_ADD                = EVENT_T(EVT_GPS, EVT_NOTE, 13), // DEPRECATED
        EVT_GPS_DOCK_REMOVE             = EVENT_T(EVT_GPS, EVT_NOTE, 14), // DEPRECATED
        EVT_GPS_DOCK_CLEAR              = EVENT_T(EVT_GPS, EVT_NOTE, 15), // DEPRECATED
        EVT_GPS_SET_IDLE_MODE           = EVENT_T(EVT_GPS, EVT_NOTE, 16),
        EVT_GPS_SET_DOCK                = EVENT_T(EVT_GPS, EVT_NOTE, 17), // DEPRECATED
        EVT_GPS_LOW_RES                 = EVENT_T(EVT_GPS, EVT_PROF, 19),
        EVT_GPS_PERIODIC                = EVENT_T(EVT_GPS, EVT_PROF, 21),
        EVT_GPS_DOCKED                  = EVENT_T(EVT_GPS, EVT_PROF, 22), // DEPRECATED
        EVT_GPS_NORMAL                  = EVENT_T(EVT_GPS, EVT_PROF, 23),

        EVT_REBOOT_INIT                 = EVENT_T(EVT_REBOOT, EVT_INFO, 1),
        EVT_REBOOT_START                = EVENT_T(EVT_REBOOT, EVT_NOTE, 2),
        EVT_REBOOT_CRITCAL_START        = EVENT_T(EVT_REBOOT, EVT_INFO, 3), // DEPRECATED
        EVT_REBOOT_TIMEOUT              = EVENT_T(EVT_REBOOT, EVT_ERR, 4),
        EVT_REBOOT_SLEEP_CRITCAL        = EVENT_T(EVT_REBOOT, EVT_NOTE, 5), // DEPRECATED
        EVT_REBOOT_GRACEFUL             = EVENT_T(EVT_REBOOT, EVT_INFO, 6), // DEPRECATED
        EVT_REBOOT_SET_TIME             = EVENT_T(EVT_REBOOT, EVT_INFO, 7),
        EVT_REBOOT_FINAL                = EVENT_T(EVT_REBOOT, EVT_NOTE, 8),

        EVT_ACCEL_INIT                  = EVENT_T(EVT_ACCEL, EVT_INFO, 1),
        EVT_ACCEL_POWER_OFF             = EVENT_T(EVT_ACCEL, EVT_INFO, 2),
        EVT_ACCEL_CAPSIZED              = EVENT_T(EVT_ACCEL, EVT_NOTE, 3),
        EVT_ACCEL_UPRIGHT               = EVENT_T(EVT_ACCEL, EVT_NOTE, 4),
        EVT_ACCEL_STATIONARY            = EVENT_T(EVT_ACCEL, EVT_NOTE, 5),
        EVT_ACCEL_PARKED                = EVENT_T(EVT_ACCEL, EVT_NOTE, 6),
        EVT_ACCEL_MOVING                = EVENT_T(EVT_ACCEL, EVT_NOTE, 7),
        EVT_ACCEL_WFP_NO_GPS            = EVENT_T(EVT_ACCEL, EVT_NOTE, 8),
        EVT_ACCEL_WFP_GBTS              = EVENT_T(EVT_ACCEL, EVT_NOTE, 9),
        EVT_ACCEL_WFP_MOVED             = EVENT_T(EVT_ACCEL, EVT_NOTE, 10),
        EVT_ACCEL_HEARTBEAT             = EVENT_T(EVT_ACCEL, EVT_NOTE, 11),
        EVT_ACCEL_GPS_OVERRIDE          = EVENT_T(EVT_ACCEL, EVT_NOTE, 12), // DEPRECATED
        EVT_ACCEL_WFP_LOW_BATTERY       = EVENT_T(EVT_ACCEL, EVT_NOTE, 13),

        EVT_BT_INIT                     = EVENT_T(EVT_BT, EVT_INFO, 1),
        EVT_BT_CRC_ERR                  = EVENT_T(EVT_BT, EVT_ERR, 2),
        EVT_BT_CMD_MISMATCH             = EVENT_T(EVT_BT, EVT_ERR, 3),
        EVT_BT_XMIT_ERR                 = EVENT_T(EVT_BT, EVT_ERR, 4),
        EVT_BT_STATUS_ERR               = EVENT_T(EVT_BT, EVT_ERR, 5),
        EVT_BT_READ_TIMEOUT             = EVENT_T(EVT_BT, EVT_ERR, 6),
        EVT_BT_READ_CHUNK_ERR           = EVENT_T(EVT_BT, EVT_ERR, 7),
        EVT_BT_READ_CHUNK_TOO_BIG       = EVENT_T(EVT_BT, EVT_ERR, 8),

        EVT_HELM_INIT                   = EVENT_T(EVT_HELM, EVT_INFO, 1),
        EVT_HELM_RUNNING                = EVENT_T(EVT_HELM, EVT_INFO, 2),
        EVT_HELM_PAST_SUNSET            = EVENT_T(EVT_HELM, EVT_INFO, 3),
        EVT_HELM_BEFORE_SUNSET          = EVENT_T(EVT_HELM, EVT_INFO, 4),
        EVT_HELM_SUN_RECALC             = EVENT_T(EVT_HELM, EVT_INFO, 5),
        EVT_HELM_GPS_FIX                = EVENT_T(EVT_HELM, EVT_NOTE, 6),
        EVT_HELM_GPS_NOFIX              = EVENT_T(EVT_HELM, EVT_NOTE, 7),

        EVT_MODEM_INIT                  = EVENT_T(EVT_MODEM, EVT_INFO, 1),
        EVT_MODEM_HOLD                  = EVENT_T(EVT_MODEM, EVT_INFO, 2),
        EVT_MODEM_RELEASED              = EVENT_T(EVT_MODEM, EVT_INFO, 3),
        EVT_MODEM_NETWORK               = EVENT_T(EVT_MODEM, EVT_INFO, 4),
        EVT_MODEM_VERSION               = EVENT_T(EVT_MODEM, EVT_INFO, 5),
        EVT_MODEM_VERSION_ERROR         = EVENT_T(EVT_MODEM, EVT_ERR, 6),
        EVT_MODEM_ACTIVE                = EVENT_T(EVT_MODEM, EVT_NOTE, 7),
        EVT_MODEM_RUN                   = EVENT_T(EVT_MODEM, EVT_INFO, 8),
        EVT_MODEM_LOG_SUCCESS           = EVENT_T(EVT_MODEM, EVT_NOTE, 12),
        EVT_MODEM_LOG_FAIL              = EVENT_T(EVT_MODEM, EVT_ERR, 9),
        EVT_MODEM_SLEEP                 = EVENT_T(EVT_MODEM, EVT_PROF, 10),
        EVT_MODEM_DEBUG                 = EVENT_T(EVT_MODEM, EVT_INFO, 11),
        EVT_MODEM_BYTES                 = EVENT_T(EVT_MODEM, EVT_NOTE, 13),

        EVT_POWER_NORMAL                = EVENT_T(EVT_POWER, EVT_NOTE, 1),
        EVT_POWER_LOW                   = EVENT_T(EVT_POWER, EVT_NOTE, 2),
        EVT_POWER_EXCELLENT             = EVENT_T(EVT_POWER, EVT_NOTE, 3), // Was OVERCHARGE
        EVT_POWER_NAP                   = EVENT_T(EVT_POWER, EVT_NOTE, 4),
        EVT_POWER_CRITICAL              = EVENT_T(EVT_POWER, EVT_NOTE, 5),
        EVT_POWER_ACCEL_NAP             = EVENT_T(EVT_POWER, EVT_NOTE, 6),

        EVT_BNET_INIT                   = EVENT_T(EVT_BOATNET, EVT_INFO, 1),
        EVT_BNET_CONSOLE_OPEN           = EVENT_T(EVT_BOATNET, EVT_INFO, 2),
        EVT_BNET_CONSOLE_CLOSE          = EVENT_T(EVT_BOATNET, EVT_INFO, 3),
        EVT_BNET_POWER_ON               = EVENT_T(EVT_BOATNET, EVT_INFO, 4),
        EVT_BNET_POWER_OFF              = EVENT_T(EVT_BOATNET, EVT_INFO, 5),
        EVT_BNET_SENSOR_BOND            = EVENT_T(EVT_BOATNET, EVT_NOTE, 6),

        EVT_FAULT_HARD                  = EVENT_T(EVT_FAULT, EVT_ERR, 1),
        EVT_FAULT_OSERROR               = EVENT_T(EVT_FAULT, EVT_ERR, 2),

        EVT_EVENT_SET_INFO              = EVENT_T(EVT_EVENT, EVT_INFO, 1),
        EVT_EVENT_SET_PROFILE           = EVENT_T(EVT_EVENT, EVT_INFO, 2),

        EVT_SETTING_FAILED              = EVENT_T(EVT_SETTING, EVT_ERR, 1),
        EVT_SETTING_NOT_FOUND           = EVENT_T(EVT_SETTING, EVT_ERR, 2),

        EVT_RADIO_INIT                  = EVENT_T(EVT_RADIO, EVT_INFO, 1),
        EVT_RADIO_NOT_PRESENT           = EVENT_T(EVT_RADIO, EVT_ERR, 2),
        EVT_RADIO_XMIT_TIMEOUT          = EVENT_T(EVT_RADIO, EVT_ERR, 3),
        EVT_RADIO_FIFO_ERROR            = EVENT_T(EVT_RADIO, EVT_ERR, 4),
        EVT_RADIO_POWER_ON              = EVENT_T(EVT_RADIO, EVT_PROF, 5),
        EVT_RADIO_POWER_OFF             = EVENT_T(EVT_RADIO, EVT_PROF, 6),
        EVT_RADIO_SET_ON                = EVENT_T(EVT_RADIO, EVT_NOTE, 7),

        EVT_NAP_DAY                     = EVENT_T(EVT_NAP, EVT_NOTE, 1),
        EVT_NAP_NIGHT                   = EVENT_T(EVT_NAP, EVT_NOTE, 2),
        EVT_NAP_TIME                    = EVENT_T(EVT_NAP, EVT_NOTE, 3),

        EVT_NAP_SET_OFF                 = EVENT_T(EVT_NAP, EVT_NOTE, 4),
        EVT_NAP_SET_DAY                 = EVENT_T(EVT_NAP, EVT_NOTE, 5),
        EVT_NAP_SET_NIGHT               = EVENT_T(EVT_NAP, EVT_NOTE, 6),
        EVT_NAP_SET_TIME                = EVENT_T(EVT_NAP, EVT_NOTE, 7),
        EVT_NAP_SET_DOCK                = EVENT_T(EVT_NAP, EVT_NOTE, 8),        // DEPRECATED

        EVT_FLASH_INIT                  = EVENT_T(EVT_FLASH, EVT_INFO, 1),
        EVT_FLASH_NOT_PRESENT           = EVENT_T(EVT_FLASH, EVT_ERR, 2),

        EVT_HUMIDITY_INIT               = EVENT_T(EVT_HUMIDITY, EVT_INFO, 1),
        EVT_HUMIDITY_NOT_PRESENT        = EVENT_T(EVT_HUMIDITY, EVT_ERR, 2),
        EVT_HUMIDITY_MOISTURE_THRESHOLD = EVENT_T(EVT_HUMIDITY, EVT_ERR, 3),

        EVT_NFC_INIT                    = EVENT_T(EVT_NFC, EVT_INFO, 1),
        EVT_NFC_NOT_PRESENT             = EVENT_T(EVT_NFC, EVT_ERR, 2),

        EVT_MEMS_INIT                   = EVENT_T(EVT_MEMS, EVT_INFO, 1),
        EVT_MEMS_NOT_PRESENT            = EVENT_T(EVT_MEMS, EVT_ERR, 2),

        EVT_SENSOR_FIRMWARE_REQUEST     = EVENT_T(EVT_SENSOR, EVT_NOTE, 1),
        EVT_SENSOR_FIRMWARE_UPDATE      = EVENT_T(EVT_SENSOR, EVT_NOTE, 2),
        EVT_SENSOR_FIRMWARE_ERR         = EVENT_T(EVT_SENSOR, EVT_NOTE, 3),

        EVT_PDS_LOG_SEND                = EVENT_T(EVT_PDS, EVT_INFO, 1),
        EVT_PDS_XMIT_FAIL               = EVENT_T(EVT_PDS, EVT_ERR, 2),
        EVT_PDS_CHUNK_SUCCESS           = EVENT_T(EVT_PDS, EVT_INFO, 3),
        EVT_PDS_LOG_SUCCESS             = EVENT_T(EVT_PDS, EVT_INFO, 4),
        EVT_PDS_MSG_HEADER_READ_ERR     = EVENT_T(EVT_PDS, EVT_ERR, 5),
        EVT_PDS_MSG_FIRMWARE            = EVENT_T(EVT_PDS, EVT_INFO, 7),
        EVT_PDS_MSG_UNKNOWN             = EVENT_T(EVT_PDS, EVT_ERR, 8),
        EVT_PDS_MSG_STORE_ERR           = EVENT_T(EVT_PDS, EVT_ERR, 9),
        EVT_PDS_MSG_SUCCESS             = EVENT_T(EVT_PDS, EVT_INFO, 10),
        EVT_PDS_MSG_COUNT_ERR           = EVENT_T(EVT_PDS, EVT_ERR, 11),
        EVT_PDS_MSG_COUNT               = EVENT_T(EVT_PDS, EVT_INFO, 12),
        EVT_PDS_MSG_ACK                 = EVENT_T(EVT_PDS, EVT_INFO, 13),
        EVT_PDS_SENDING                 = EVENT_T(EVT_PDS, EVT_INFO, 14),
        EVT_PDS_MSG_SETTINGS            = EVENT_T(EVT_PDS, EVT_INFO, 15),
        EVT_PDS_SETTINGS_SEND_ERR       = EVENT_T(EVT_PDS, EVT_ERR, 16),
        EVT_PDS_SETTINGS_SUCCESS        = EVENT_T(EVT_PDS, EVT_INFO, 17),
        EVT_PDS_UNKNOWN_DEVICE_TYPE     = EVENT_T(EVT_PDS, EVT_ERR, 18),

// end events
} event_t;

typedef enum {
        // parameter types
        // this is what is stuffed into the datastream encoding parameter types
        // the actual allocation of storage for these types is in event.c, so...
        // IF YOU ADD SOMETHING HERE, MAKE SURE YOU EDIT THE switch() STATEMENT
        // THERE!
// dont change this
// begin types
        EVT_8BIT        = 0,
        EVT_16BIT       = 1,
        EVT_32BIT       = 2,
        EVT_STRING      = 3,

        EVT_POINT       = 4,
        EVT_BOOL        = 5,

        EVT_UID         = 6,

        EVT_IMEI        = 7,

        EVT_S8BIT       = 8,
        EVT_S16BIT      = 9,
        EVT_S32BIT      = 10,

        // pseudo-values more used for on-board formatting
        EVT_STRCONST    = 100, // EVT_STRING

        EVT_HEX         = 0x1000,
        EVT_FORMAT_MASK = 0xf000,
// end types
} event_type_t;


typedef struct {
        event_type_t    type;
        uint32_t        arg;
        char            *desc;
} event_param_t;

void event_log(event_t event, char *desc, int args, event_param_t *params);

#define EVENT_LOG(event, msg) { event_log(event, msg, 0, NULL); }
#define EVENT_LOG1(event, msg, arg1_desc, arg1_type, arg1) { \
    event_param_t _evt_param = { .desc = arg1_desc, .type = arg1_type, .arg = (uint32_t) arg1 }; \
    event_log(event, msg, 1, &_evt_param); \
}

#define EVENT_LOG2(event, msg, arg1_desc, arg1_type, arg1, arg2_desc, arg2_type, arg2) { \
    event_param_t _evt_params[2] = { \
	{ .desc = arg1_desc, .type = arg1_type, .arg = (uint32_t) arg1 }, \
	{ .desc = arg2_desc, .type = arg2_type, .arg = (uint32_t) arg2 }, \
    }; \
    event_log(event, msg, 2, _evt_params); \
}

#define EVENT_LOG3(event, msg, arg1_desc, arg1_type, arg1, arg2_desc, arg2_type, arg2, arg3_desc, arg3_type, arg3) { \
    event_param_t _evt_params[3] = { \
	{ .desc = arg1_desc, .type = arg1_type, .arg = (uint32_t) arg1 }, \
	{ .desc = arg2_desc, .type = arg2_type, .arg = (uint32_t) arg2 }, \
	{ .desc = arg3_desc, .type = arg3_type, .arg = (uint32_t) arg3 }, \
    }; \
    event_log(event, msg, 3, _evt_params); \
}

#define EVENT_LOG4(event, msg, arg1_desc, arg1_type, arg1, arg2_desc, arg2_type, arg2, arg3_desc, arg3_type, arg3, arg4_desc, arg4_type, arg4) { \
    event_param_t _evt_params[4] = { \
	{ .desc = arg1_desc, .type = arg1_type, .arg = (uint32_t) arg1 }, \
	{ .desc = arg2_desc, .type = arg2_type, .arg = (uint32_t) arg2 }, \
	{ .desc = arg3_desc, .type = arg3_type, .arg = (uint32_t) arg3 }, \
	{ .desc = arg4_desc, .type = arg4_type, .arg = (uint32_t) arg4}, \
    }; \
    event_log(event, msg, 4, _evt_params); \
}

extern bool event_show;

void event_init();
void event_dump();
void event_log_reset();

#ifndef TARGET_TEST
void event_log_read(flash_read_t *read, event_t *event, uint32_t *timestamp, int *param_count, event_param_t *params);
#endif

#endif

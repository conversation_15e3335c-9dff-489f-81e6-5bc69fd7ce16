/** @{

    @ingroup    common
    @file
    @brief      Board Fault routines
*/

#include "pelagic.h"
#include "board_fault.h"
#include "kinetis_flash.h"
#include "memmap.h"
#include "led.h"
#include "wait_api.h"
#include "system_file.h"
#include <stdarg.h>

volatile uint32_t fault_r0, fault_r1, fault_r2, fault_r3, fault_r12, fault_lr, fault_pc, fault_psr;
volatile bool board_fault = false;

extern volatile bool board_initted;

osThreadId svcThreadGetId (void);
void error(const char* fmt, ...);

/**
    @brief  increment the fault counter, and reboot the MCU.
*/

void
board_fault_save()
{
        rtc_save(0);
        SYS_REG_FILE->fault_count++;

        // And good bye!
        NVIC_SystemReset();
}

/**
    @brief  check to see at boot if the board had faulted, and log the error if so.
*/

void
board_fault_check()
{
        if (SYS_REG_FILE->reboot_reason <= REBOOT_NOT_FAULT)
                return;

        switch (SYS_REG_FILE->reboot_reason) {
        case REBOOT_OSERROR:
                EVENT_LOG2(EVT_FAULT_OSERROR, "fault oserror", "code", EVT_32BIT,  SYS_REG_FILE->os_error.code, "tid", EVT_32BIT|EVT_HEX, SYS_REG_FILE->os_error.tid);
                break;
        case REBOOT_FAULT:
                EVENT_LOG2(EVT_FAULT_HARD, "hard fault", "pc", EVT_32BIT|EVT_HEX, SYS_REG_FILE->fault.pc, "lr", EVT_32BIT|EVT_HEX, SYS_REG_FILE->fault.lr);
                break;
        }

        SYS_REG_FILE->reboot_reason = REBOOT_NORMAL;
}

/**
    @brief  handle RTX fatal error, store the code, and reboot the board.
    @param[in]  error_code
                            - OS_ERR_STK_OVF: thread overflowed it stack
                            - OS_ERR_FIFO_OVF fifo overflow see http://www.keil.com/support/docs/3604.htm
                            - OS_ERR_TIMER_OVF the SysTicker handler is blocked

*/

void
os_error (uint32_t error_code)
{
        board_fault = 1;

        sys_file_init();
        SYS_REG_FILE->os_error.tid = (uint32_t) svcThreadGetId();

        __disable_irq();

        SYS_REG_FILE->reboot_reason = REBOOT_OSERROR;
        SYS_REG_FILE->os_error.code = error_code;

        uart_printf("FAULT: os_error code %d tid 0x%x\n", error_code, SYS_REG_FILE->os_error.tid);
        board_fault_save();
}

/**
    @brief  MBED/RTX support called from MBED_ASSERT.
    @note   In all cases this is called when various pin routines are called
            with wrong parameters. Should ONLY be seen in development and not deployment.
*/

void
die()
{
        int_disable();

        for (;;) {
#ifdef HAVE_LED
                led_toggle(LED_RED);
                wait_ms(500);
#endif
        }
}

/**
    @brief  MBED support routine called from MBED_ASSERT. See die()
*/

void
mbed_assert_internal(const char *expr, const char *file, int line)
{
        error("file %s line %d", expr, file, line);
}

/**
    @brief  get register values from faulted state, log it, and reboot.
*/

void
pop_registers_from_fault_stack(uint32_t * hardfault_args)
{
        board_fault = 1;

        sys_file_init();

        fault_r0  = hardfault_args[0];
        fault_r1  = hardfault_args[1];
        fault_r2  = hardfault_args[2];
        fault_r3  = hardfault_args[3];
        fault_r12 = hardfault_args[4];
        fault_psr = hardfault_args[7];

        SYS_REG_FILE->fault.lr = fault_lr = hardfault_args[5];
        SYS_REG_FILE->fault.pc = fault_pc = hardfault_args[6];
        SYS_REG_FILE->reboot_reason = REBOOT_FAULT;

        // Critical: Ensure fault data is immediately saved to backup registers
        // This must happen before any potential system reset
#ifndef TARGET_TEST
        sys_file_sync_to_backup();
#endif

        if (!board_initted) {
                for (;;)
                        ;
        }

        uart_printf("\nHARD FAULT: pc [0x%"PRIx32"] lr [0x%"PRIx32"] psr [0x%"PRIx32"]\n", fault_pc, fault_lr, fault_psr);
        board_fault_save();
}

/**
    @brief  MCU Hard Fault handler
    @note   The MCU will call the Hard Fault handler when the code attempts to write to
            read only memory, unaligned memory access, stack overflow, and other fatal conditions.
            The register states are stored off onto the stack, and then calls pop_registers_from_fault_stack
            to log the fault.
*/

__attribute__((naked))
void HardFault_Handler(void)
{
        __asm volatile
        (
                " mov r0, lr										\n"
                " mov r1, #4										\n"
                " and r1, r0										\n"
                " cmp r1, #0										\n"
                " beq is_equal										\n"
                " mrs r0, psp										\n"
                " b is_done											\n"
                "is_equal:											\n"
                " mrs r0, msp										\n"
                "is_done:											\n"
                " ldr r1, [r0, #24]									\n"
                " mov lr,r1\n"
                " ldr r2, .handler2_address_const					\n"
                " bx r2												\n"
                "  .align 2\n"
                " .handler2_address_const: .word pop_registers_from_fault_stack	\n"
        );
}

/** @} */

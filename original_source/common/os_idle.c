#include "cmsis.h"
#include "cmsis_os.h"
#include "us_ticker_api.h"

extern void lptmr_set(unsigned short count);
extern volatile int us_ticker_inited;

/**
    @brief  called when RTX has no threads running at the moment
    @note   It may be possible in the future to get to a lower power consumption
            state when switching to another class processor with additional
            millisecond resolution timers. The LPTMR is used currently by the
            radio drivers in an interrupt context (i.e. osDelay() cannot be used then
            as a subsitute).

            The issue is SysTick runs at 1ms interrupt period - i.e. 1000 interrupts
            per second. This causes the MCU to kick into a high state when processing
            the interrupt. It is possible to suspend Systick when the OS is idle
            (thru os_suspend()) and thus save more power -- however there is no
            available timer available to use on the Freescale KL16.
*/

void
os_idle_demon (void)
{

        /* The idle demon is a system thread, running when no other thread is      */
        /* ready to run.                                                           */

#if 1
        for (;;) {
                asm volatile ("wfi");
        }
#else
        uint32_t sleep, started, stopped;
        int forever = 1;
        if (!us_ticker_inited)
                us_ticker_init();

        for (;;) {
                sleep = os_suspend();

                if (sleep) {
                        if (sleep == 0xffff) {
                                forever = 1;
                        } else {
                                forever = 0;
                                sleep = sleep * 1000;
                                if (sleep > 0xffff)
                                        sleep = 0xfffe;


                                lptmr_set(sleep);
                        }
                        started = us_ticker_read();
                        asm volatile ("wfi");
                        stopped = us_ticker_read();

                        if (!forever) {
                                if (started > stopped) {
                                        sleep = (0xffffffff - started) + stopped;
                                } else {
                                        sleep = stopped - started;
                                }
                                sleep = (sleep + 999) / 1000;
                        }
                }

                os_resume(sleep);
        }
#endif
}

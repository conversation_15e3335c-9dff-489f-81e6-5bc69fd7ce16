#ifndef __PELAGIC_H__
#define __PELAGIC_H__

#include <stdint.h>
#include <inttypes.h>
#include <math.h>

#include "pelagic-types.h"
#include "bits.h"

#define SRAM_TEXT_SECTION  __attribute__ ((section (".sram.text")))

#include "cmsis.h"
#include "cmsis_os.h"                   // TODO Ian: Is this an OK fix for MutexId unknown type name?
#include "serial_api.h"
#include "serial_api_stubs.h"           // TODO: <PERSON> merge this into serial_api or something
#include "rtc_api.h"
// #include "spi_api.h"

#include "hardware.h"

#include "flash_store.h"
#include "kinetis_flash.h"
#include "datetime.h"
#include "crc16.h"
#include "event.h"

#include "imei.h"
#include "uid.h"

#ifdef TARGET_VMS
#include "boat_log.h"
#include "cell_signal.h"
#include "nmea.h"
#include "gps.h"
#include "accel.h"
#include "settings.h"
#endif

// TODO Ian: implement
#include "port_stubs.h"
#include "serial_wire_debug.h"

#include "stats.h"

#ifdef TARGET_TEST
#define int_disable()
#define int_enable()
#include <stdio.h>
#include <string.h>
#else
#define int_disable() __disable_irq()
#define int_enable() __enable_irq()
#endif

#define ARRAY_SIZE(x)   (sizeof(x) / sizeof(x[0]))

#define	HERE()	printf("%s:%d\n", __FILE__, __LINE__)
#define	unless(x)	if (!(x))

enum {
        SHARED_BUFFER_SIZE = 1024,
};


void reboot_check_pending();

int32_t rand(void);

#ifndef TARGET_TEST
void printf(const char *, ...);
int sprintf(char *buf, const char *fmt, ...);
int atoi(char *str);
double atof(char *str);
void *memcpy(void *restrict dst0, const void *restrict src0, int size);
void *memset(void *restrict dst0, int value0, int len0);
int strlen(const char *str);
char *strcpy(char *dst, const char *src);
int strcmp(const char *s1, const char *s2);
int strncmp(const char *s1, const char *s2, int n);
int strnlen(const char *s, int maxlen);
void puts(const char *);
#endif

volatile int *__errno(void);

int abs(int n);
void hex_dump(uint8_t *data, int bytes);

void uart_printf(const char *, ...);
bool have_esc();
bool display_more(int *lines);
void die();

void announce(const char *name);

#ifdef TARGET_SENSOR
bool uid_match_host(board_uid_t uid);
void uid_set_host(board_uid_t uid);
#endif

// extern spi_t  spi1_bus;

#ifdef HAVE_RADIO
extern osThreadId bnet_tid;
#endif

extern osThreadId gps_tid, helm_tid, reboot_tid;

#ifdef TARGET_CONSOLE
extern osThreadId main_tid;
#endif

extern volatile bool board_fault;

extern board_uid_t board_uid;

extern serial_t console_uart;

extern int32_t random_seed;

extern volatile bool time_acquired;
extern const uint32_t build_timestamp, build_target_version;
extern const char build_tag[];

typedef union {
        uint8_t     firmware[SHARED_BUFFER_SIZE];
        uint8_t     modem[SHARED_BUFFER_SIZE];
        uint8_t     settings[SHARED_BUFFER_SIZE];
        uint8_t     test[SHARED_BUFFER_SIZE];
} shared_buffer_t;

extern shared_buffer_t the_buffer;
extern uint8_t compress_buffer[];

extern volatile int demo_mode;

#ifdef TARGET_SENSOR
extern uint16_t sensor_slept;
#endif

#endif

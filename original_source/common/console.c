/** @{

    @ingroup    common
    @file
    @brief Console routines
*/

#include "pelagic.h"
#include "console.h"
#ifdef HAVE_BNET
#include "bnet.h"
#include "bnet_console.h"
#endif

/**
    @brief  Display characters on the console (both serial and OTA)
    @param[in]  buffer  character to display
    @param[in]  bytes   character count
*/

void
console_write(char *buffer, uint32_t bytes)
{
        #ifdef DEBUG
        ITM_SendStringSized(buffer, bytes);
        #else
        serial_write(&console_uart, buffer, bytes);
        #endif

#if defined(TARGET_VMS) && defined(HAVE_BNET)
        if (bnet_console_opened)
                bnet_console_write(buffer, bytes);
#endif
}

/**
    @brief  get a console character from keyboard or OTA console
    @return a character! (duh)
*/

char
console_getchar()
{
        char ch;

#if defined(TARGET_VMS) && defined(HAVE_BNET)
        uint16_t signals;

        for (;;) {
                if (bnet_console_rx_count && bnet_console_read(&ch, 1) == 1)
                        return ch;

                if (serial_read_signal(&console_uart, &ch, 1, &signals) == 1)
                        return ch;
        }
#else
        serial_read(&console_uart, &ch, 1);
        return ch;
#endif
}

/**
    @brief  check to see if a character is available to read
    @return true if there's something
*/

bool
console_available()
{
        if (serial_available(&console_uart))
                return true;

#if defined(TARGET_VMS) && defined(HAVE_BNET)
        if (bnet_console_rx_count)
                return true;
#endif

        return false;
}

/**
    @brief  send out any queued characters for display. (for VMS & BNET)
*/

void
console_flush()
{
#if defined(TARGET_VMS) && defined(HAVE_BNET)
        bnet_console_flush();
#endif
}

/**
    @brief  see if an <ESC> has been typed
*/

bool
have_esc()
{
        char ch;

        while (console_available()) {
                ch = console_getchar();

                if (ch == '\033')
                        return true;
        }

        return false;
}

/**
    @brief  asks the user if more stuff is to be display if more than 30 lines have been shown
    @param[inout]   lines   how many lines have been displayed so far, set to 0.
    @return true continue to display more, false request has been made to stop
*/

bool
display_more(int *lines)
{
        char ch;
        ++*lines;

        if (*lines < 30)
                return true;

        *lines = 0;

        printf("-- more (press 'q' or esc to quit) -- ");
        ch = console_getchar();

        if (ch == 'q' || ch == '\033')
                return false;

        return true;
}

/** @} */

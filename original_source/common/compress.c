/** @{

    @ingroup    common
    @file

    @brief      Interface to Heatshrink
*/

#include "pelagic.h"
#include "compress.h"
#include "heatshrink_encoder.h"

heatshrink_encoder hs_encoder;

// the buffer needs some padding just in case the compress increases the size.
uint8_t compress_buffer[COMPRESS_BUFFER_SIZE + 200];

/**
    @brief  compress a given buffer into the compress_buffer area
    @param[in]  data    area to compress
    @param[in]  data_size   area size in bytes
    @return     compressed size. a 0 will be returned if the size was larger than
                the original data.
*/

uint32_t
compress_flatten(uint8_t *data, uint32_t data_size)
{
        uint32_t sunk = 0, compressed = 0, count;
        HSE_poll_res result;

        heatshrink_encoder_reset(&hs_encoder);

        while (sunk < data_size) {
                heatshrink_encoder_sink(&hs_encoder, &data[sunk], data_size - sunk, &count);
                sunk += count;

                if (sunk == data_size) {
                        heatshrink_encoder_finish(&hs_encoder);
                }

                do {                    /* "turn the crank" */
                        result = heatshrink_encoder_poll(&hs_encoder, &compress_buffer[compressed], COMPRESS_BUFFER_SIZE - compressed, &count);
                        compressed += count;

                        // the compressed version is greater than the original data, bail out.
                        if (compressed >= data_size) {
                                //uart_printf("compress: compress %d > data %d\n", compressed, data_size);
                                return 0;
                        }
                } while (result == HSER_POLL_MORE);

                if (sunk == data_size) {
                        heatshrink_encoder_finish(&hs_encoder);
                }
        }

        return compressed;
}

/** @} */

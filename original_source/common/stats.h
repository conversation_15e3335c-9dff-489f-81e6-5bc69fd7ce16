#ifndef __STATS_H__
#define __STATS_H__

extern struct gps_stats {
        uint32_t    crc_errors;
        uint32_t    sentences;
        uint32_t    last_sentence_time;
} gps_stats;

typedef struct serial_stat_s {
        uint32_t    tx_bytes;
        uint32_t    rx_bytes;
        uint32_t    interrupts;
        uint32_t    dma_rx_interrupts;
        uint32_t    dma_tx_interrupts;

        uint32_t    rx_overflow;
} serial_stat_t;

extern serial_stat_t serial_stats[3];

typedef struct log_stats {
        uint32_t    wakeups;
        uint32_t    boat_log_transmits;
        uint32_t    boat_log_failures;
        uint32_t    last_boat_log_time;
        uint32_t    event_transmits;
        uint32_t    event_failures;
        uint32_t    last_event_time;
} log_stats_t;

extern log_stats_t log_stats;

extern struct helm_stats {
        uint32_t    wakeups;
} helm_stats;

#endif

#ifndef __SIGNALS_H__
#define __SIGNALS_H__

typedef enum {
        // Per thread signals
        HELM_SIGNAL_ACTION                = 0x0001,

        MODEM_SIGNAL_FORCE                = 0x0001,
        MODEM_SIGNAL_ENTER_DOCK           = 0x0002, // DEPRECATED
        MODEM_SIGNAL_PARKING              = 0x0004, // called by parking

        BNET_SIGNAL_RADIO_PING            = 0x0001, // Send out a ping packet
        BNET_SIGNAL_RADIO_CYCLE           = 0x0002, // Power cycle the radio
        BNET_SIGNAL_SETTINGS              = 0x0004, // Settings update
        BNET_SIGNAL_RADIO_CALIBRATE       = 0x0008, // Calibrate the radio
        BNET_SIGNAL_CONSOLE_SEND          = 0x0010, // console data available to send
        BNET_SIGNAL_FT_START              = 0x0020, // send factory test start packet

        BNET_CONSOLE_SIGNAL_KEEPALIVE_ACK = 0x0001,
        BNET_CONSOLE_SIGNAL_CMD_ACK       = 0x0002,
        BNET_CONSOLE_SIGNAL_CLOSE         = 0x0004,

        CONSOLE_SIGNAL_RX                 = 0x0001,
        CONSOLE_SIGNAL_OPEN               = 0x0002,

        GPS_SIGNAL_SETTINGS               = 0x0001,
        GPS_SIGNAL_STATE_CHANGE           = 0x0002,


        ACCEL_SIGNAL_IRQ1                 = 0x0001,
        ACCEL_SIGNAL_IRQ2                 = 0x0001,

        REBOOT_SIGNAL_NORMAL              = 0x0001,
        REBOOT_SIGNAL_NAP                 = 0x0002,
        REBOOT_SIGNAL_CRITICAL            = 0x0004,
        REBOOT_SIGNAL_PARKED              = 0x0008,
        REBOOT_SIGNAL_STILL_PARKED        = 0x0010,


        // Global Signals
        ALARM_SIGNAL_BUZZER               = 0x0080,

        SYS_SIGNAL_SHUTDOWN               = 0x0100,
        POWER_SIGNAL_CHANGE               = 0x0200,

        SYS_SIGNAL_ACQUIRED_TIME          = 0x0400,

        IO_SIGNAL_WINBOND                 = 0x0800,
        IO_SIGNAL_RADIO_RX                = 0x1000,
        IO_SIGNAL_RADIO_TX                = 0x2000,
        IO_SIGNAL_SERIAL_RX               = 0x4000,
        IO_SIGNAL_SERIAL_TX               = 0x8000
} signal_t;

#endif

/** @{

    @ingroup    common
    @file

    @note   The System File Register is a 32-byte area in memory which
            is always retained even in the VLLS* states. BoatOS exploits this by
            retaining information across boots without needing to wear
            out the flash.

            For STM32L431, this is implemented using RTC backup registers
            which are powered by VBAT and survive complete power loss.
*/

#include "pelagic.h"
#include "system_file.h"

#ifndef TARGET_TEST
// Include STM32 HAL for backup register access
#include "stm32l4xx_hal_rtc_ex.h"
#include "stm32l4xx_hal_pwr.h"
#include "stm32l4xx_hal_rcc.h"

// Static storage for the system register file structure
static system_register_file_t sys_reg_file_data;
system_register_file_t *sys_reg_file_ptr = &sys_reg_file_data;

// RTC handle for backup register access
static RTC_HandleTypeDef hrtc_backup;
static bool backup_domain_initialized = false;
#endif

#ifndef TARGET_TEST
/**
    @brief  Initialize the backup domain for RTC backup register access
    @return 0 on success, -1 on error

    This function must be called early in main() before any backup register access.
    It handles all the necessary setup for accessing RTC backup registers.
*/
int
sys_file_backup_init(void)
{
        if (backup_domain_initialized) {
                return 0;
        }

        // Enable PWR clock - required for backup domain access
        __HAL_RCC_PWR_CLK_ENABLE();

        // Enable access to backup domain
        HAL_PWR_EnableBkUpAccess();

        // Initialize RTC handle for backup register access
        hrtc_backup.Instance = RTC;

        // We don't need full RTC initialization for backup registers,
        // but we need to ensure the backup domain is accessible
        backup_domain_initialized = true;

        return 0;
}

/**
    @brief  Write data to a backup register
    @param  reg_index   Register index (0-7 for our 32-byte structure)
    @param  data        32-bit data to write
*/
void
sys_file_backup_write_reg(uint32_t reg_index, uint32_t data)
{
        if (reg_index >= SYS_REG_FILE_NUM_REGS) {
                return; // Invalid register index
        }

        if (!backup_domain_initialized) {
                sys_file_backup_init();
        }

        HAL_RTCEx_BKUPWrite(&hrtc_backup, reg_index, data);
}

/**
    @brief  Read data from a backup register
    @param  reg_index   Register index (0-7 for our 32-byte structure)
    @return 32-bit data from backup register
*/
uint32_t
sys_file_backup_read_reg(uint32_t reg_index)
{
        if (reg_index >= SYS_REG_FILE_NUM_REGS) {
                return 0; // Invalid register index
        }

        if (!backup_domain_initialized) {
                sys_file_backup_init();
        }

        return HAL_RTCEx_BKUPRead(&hrtc_backup, reg_index);
}

/**
    @brief  Load system register file from backup registers
*/
static void
sys_file_load_from_backup(void)
{
        uint32_t *data_ptr = (uint32_t *)&sys_reg_file_data;

        for (int i = 0; i < SYS_REG_FILE_NUM_REGS; i++) {
                data_ptr[i] = sys_file_backup_read_reg(i);
        }
}

/**
    @brief  Save system register file to backup registers
*/
static void
sys_file_save_to_backup(void)
{
        uint32_t *data_ptr = (uint32_t *)&sys_reg_file_data;

        for (int i = 0; i < SYS_REG_FILE_NUM_REGS; i++) {
                sys_file_backup_write_reg(i, data_ptr[i]);
        }
}

/**
    @brief  Force synchronization of current data to backup registers

    This function can be called to ensure the current system register file
    data is immediately written to backup registers. Normally this happens
    automatically, but this can be used for critical updates.
*/
void
sys_file_sync_to_backup(void)
{
        sys_file_save_to_backup();
}
#endif

/**
    @brief  initializes the system file register if need be

    For STM32L431, this function:
    1. Initializes the backup domain if needed
    2. Loads the current data from backup registers
    3. Validates and initializes the structure if needed
    4. Saves any changes back to backup registers
*/
void
sys_file_init()
{
#ifndef TARGET_TEST
        // Initialize backup domain access
        if (sys_file_backup_init() != 0) {
                printf("ERROR: Failed to initialize backup domain\n");
                return;
        }

        // Load current data from backup registers
        sys_file_load_from_backup();
#endif

        bool            new_firmware = false;
        uint32_t        timestamp = SYS_REG_FILE->timestamp; // never toss timestamp

        if (SYS_REG_FILE->magic == SYS_REG_FILE_MAGIC) {
                if (SYS_REG_FILE->reboot_reason != REBOOT_FIRMWARE) {
#ifndef TARGET_TEST
                        // Data is valid, no need to reinitialize
                        // But ensure it's saved to backup registers in case of corruption
                        sys_file_save_to_backup();
#endif
                        return;
                }
                new_firmware = true;
        } else if (SYS_REG_FILE->magic != 0 && SYS_REG_FILE->magic != 0xffff) {
                new_firmware = true;
        }

        printf("about to zero %d bytes of SYS_REG_FILE\n", sizeof(system_register_file_t));
        memset(SYS_REG_FILE, 0, sizeof(system_register_file_t));

        SYS_REG_FILE->magic            = SYS_REG_FILE_MAGIC;
        SYS_REG_FILE->reboot_reason    = new_firmware ? REBOOT_FIRMWARE : REBOOT_NORMAL;
        SYS_REG_FILE->firmware_updated = new_firmware;
        printf("resetting timestamp to: %d\n", timestamp);
        SYS_REG_FILE->timestamp        = timestamp;

#ifndef TARGET_TEST
        // Save the initialized data to backup registers
        sys_file_save_to_backup();
#endif
}

/**
    @brief  Clear the system file register

    This function clears the system register file and saves the cleared
    state to backup registers.
*/
void
sys_file_clear()
{
        memset(SYS_REG_FILE, 0, sizeof(system_register_file_t));

#ifndef TARGET_TEST
        // Save the cleared data to backup registers
        sys_file_save_to_backup();
#endif
}

/** @} */

#include "pelagic.h"
#include "temp.h"
#include "analogin_api.h"
#include "gpio_api.h"
#include "pinmap.h"
#ifdef TARGET_VMS
#include "factory_test.h"
#endif

analogin_t thermistor_pin;

bool thermistor_initted = false;

void
thermistor_init()
{
#ifndef TARGET_DEVBOARD
        if (thermistor_initted)
                return;

        analogin_init(&thermistor_pin, ADC_TEMP);
        thermistor_initted = true;
#endif
}

uint16_t
thermistor_read()
{
#ifdef TARGET_PLACO
        uint16_t value;

        value =  analogin_read_u16(&thermistor_pin);
        return value;
#else
        return 1;
#endif
}

#if 0
ft_result_t
thermistor_factory_test()
{
        uint16_t temp;
        thermistor_init();

        temp = thermistor_read();

        if (temp == 0xffff || temp < 100) {
                ft_update("thermistor: not valid (value 0x%x) FAIL", temp);
                return FT_THERMISTOR_FAIL;
        } else {
                ft_device_result("thermistor %d", temp);
                ft_update("thermistor: test pass");
                return FT_SUCCESS;
        }
}
#endif

/** @{

    @ingroup    vms
    @file
    @brief      Provision Support
*/

#include "pelagic.h"
#include "system_file.h"
#include "provision.h"
#include "memmap.h"
#include "signals.h"
#include "gpio_api.h"
#include "power.h"
#include "gps.h"
#include "modem.h"
#include "mcu_sleep.h"

#ifdef HAVE_LED
#include "led.h"
#endif

#ifdef HAVE_ACCEL
#include "accel.h"
#endif

#ifdef HAVE_RADIO
#include "radio.h"
#endif

#ifdef HAVE_FACTORY_TEST
#include "factory_test.h"
#endif


void (*const provision_devices[])() = {
        gps_factory_provision,
#ifdef HAVE_ACCEL
        accel_factory_provision,
#endif
#ifdef HAVE_RADIO
        radio_factory_provision
#endif
};

uint32_t provision_sleep;

/**
    @brief  update flash to a given provision stage
    @param[in]  stage   stage to save
*/

void
provision_update(provision_stage_t stage)
{
        provision_header_t provision;

        provision.magic = PROVISION_MAGIC;
        provision.stage = stage;
        provision.buildstamp = build_timestamp;
        /*
         * These are weird, we only care about them in PROVISION_STAGE_FACTORY
         * mode.
         */
        provision.test_state = FACTORY_TEST_NOT_RUN;
        provision.sleep_until_darkness = 0;
        provision.light_count = 0;

        kinetis_flash_erase_sector(PROVISION_ADDR);
        kinetis_flash_write(PROVISION_ADDR, &provision, sizeof(provision));
        osDelay(100);
}

/**
    @brief  update to factory mode
*/

void
provision_factory()
{
        uart_printf("[FLASH] Setting Mode to Factory\n");
        rtc_reset_time();
        provision_update(PROVISION_STAGE_FACTORY);
}

/**
    @brief  update to shipping mode
*/

void
provision_shipping()
{
        uart_printf("[FLASH] Setting Mode to Shipping\n");
        SYS_REG_FILE->reboot_reason = REBOOT_SHIPPING;
        provision_update(PROVISION_STAGE_SHIPPING);
}

/**
    @brief  update to deployed mode
*/

void
provision_deployed()
{
        uart_printf("[FLASH] Setting Mode to Deployed\n");
        provision_update(PROVISION_STAGE_DEPLOYED);
}


/**
    @brief return Factory test state
*/

bool
factory_test_state()
{
        provision_header_t *header = (provision_header_t *) PROVISION_ADDR;

        return header->test_state;
}

// These should only be used during factory test, so they should always come after
//    provision_board calls provision_factory
void
set_factory_test_state(factory_test_state_t state)
{
        provision_header_t *header = (provision_header_t *) PROVISION_ADDR;
        provision_header_t new_header = *header;

        new_header.test_state = state;
        kinetis_flash_erase_sector(PROVISION_ADDR);
        kinetis_flash_write(PROVISION_ADDR, &new_header, sizeof(new_header));
}

void
set_sleep_until_darkness(uint8_t sleep)
{
        provision_header_t *header = (provision_header_t *) PROVISION_ADDR;
        provision_header_t new_header = *header;

        new_header.sleep_until_darkness = sleep;
        kinetis_flash_write(PROVISION_ADDR, &new_header, sizeof(new_header));
}

void
set_light_count(uint8_t count)
{
        provision_header_t *header = (provision_header_t *) PROVISION_ADDR;
        provision_header_t new_header = *header;

        new_header.light_count = count;
        kinetis_flash_write(PROVISION_ADDR, &new_header, sizeof(new_header));
}

extern void gps_hold_provision(), radio_hold_provision();

#ifndef HAVE_FT_STANDALONE
/**
    @brief  deal with provisioning
    @note   Call shortly after boot to determine how the board should
            proceed from a deployment standpoint. There are several checks
            done:
            - If the board have been previously provisioned and an image reflash occurred
              then go back to waiting for deployment stage
            - When the stage is waiting for deployment, wait for solar to appear for
              30 seconds otherwise go back to sleep
            - When the stage is factory deployment, wait for solar to appear and then
              head to factory testing, otherwise go to sleep (briefly)
            - When the stage is deployed, proceed as normal
*/

void
provision_board()
{
        provision_header_t *header = (provision_header_t *) PROVISION_ADDR;

        if (header->magic != PROVISION_MAGIC) {
#ifdef HAVE_FACTORY_TEST
                provision_factory();
#else
                provision_shipping();
#endif
        }

#ifdef FORCE_FACTORY
        if (header->stage != PROVISION_STAGE_FACTORY)
                provision_factory();
#elif defined FORCE_SHIPPING
        if (header->stage != PROVISION_STAGE_SHIPPING)
                provision_shipping();
#elif defined FORCE_DEPLOYED
        if (header->stage != PROVISION_STAGE_DEPLOYED) {
                provision_deployed();
        }
#endif


        header = (provision_header_t *) PROVISION_ADDR;

        // Returning here proceeds to boot normal VMS mode
        if (header->stage == PROVISION_STAGE_FACTORY) {
#ifdef HAVE_FACTORY_TEST

#endif

        } else if (header->stage == PROVISION_STAGE_SHIPPING) {
                // Solar must be present twice in a row to trigger deployment
                // I tried to store the first reading in header->light_count and mcu_sleep,
                //   but I never got the header->light_count value back when we booted.
                // Punting for now with osSignalWait.

                if (solar_present(SOLAR_DEPLOY_WAIT)) {
#ifdef NO_SOLAR
                        uart_printf("[SHIPPING MODE]: Skipping second wait time because compiled with NO_SOLAR.\n");
#else
                        uart_printf("[SHIPPING MODE]: Solar is present once; waiting %d seconds.\n", SHIPPING_SLEEP);
                        osSignalWait(0, SHIPPING_SLEEP * 1000);
#endif

                        if (solar_present(SOLAR_DEPLOY_WAIT)) {
                                uart_printf("deploying board\n");
                                provision_deployed();
                                return;
                        }
                }
        } else if (header->stage == PROVISION_STAGE_DEPLOYED)  {
                // handle the case where the board has been deployed,but a reflashing occurred that wasn't an over-the-air update
                // (i.e. using a programmer to place the boards back into deployment)
                // Call provision_deployed() to update the buildstamp.
                if (header->buildstamp != build_timestamp && SYS_REG_FILE->reboot_reason == REBOOT_FIRMWARE)
                        provision_deployed();
                return;
        }

        shutdown_components_and_sleep(header);
}
#endif

void
shutdown_components_and_sleep(provision_header_t *header)
{
        uart_printf("provision stage: ");
        switch (header->stage) {
        case PROVISION_STAGE_FACTORY:
                uart_printf("factory\n");
                break;
                ;;
        case PROVISION_STAGE_SHIPPING:
                uart_printf("shipping\n");
                break;
                ;;
        case PROVISION_STAGE_DEPLOYED:
                uart_printf("deployed\n");
                break;
                ;;
        default:
                uart_printf("unknown (%d)\n", header->stage);
                ;;
        }

        // if not coming out provisioning, then make sure things are shutdown.
        if (SYS_REG_FILE->reboot_reason != REBOOT_SHIPPING) {
                for (int i = 0; i < ARRAY_SIZE(provision_devices); i++) {
                        (*provision_devices[i])();
                }

                SYS_REG_FILE->reboot_reason = REBOOT_SHIPPING;
        }

        mcu_sleep(SHIPPING_SLEEP);
}

/**
    @brief  parse provisioning settings
    @note   the following commands are supported:
            - provision factory <SECS> force factory deployment and optionally sleep SECS
            - provision wait <SECS> force wait for deployment and optionally sleep SECS
            - provision deploy <SECS> force deployment and optionally sleep SECS

            The setting will cause the board to reboot
    @param[in]  argc    argv array size
    @param[in]  argv    setting array
    @return true if setting was successful
*/

bool
provision_setting(int argc, char **argv)
{
        if (argc == 0)
                return false;

        if (strcmp(argv[0], "factory") == 0) {
                provision_factory();
        } else if (strcmp(argv[0], "shipping") == 0) {
                provision_shipping();
        } else if (strcmp(argv[0], "deploy") == 0) {
                provision_deployed();
        } else {
                return false;
        }

        if (argc == 2)
                provision_sleep = atoi(argv[1]);

        osSignalSet(reboot_tid, REBOOT_SIGNAL_NORMAL); // Reboot the board
        return true;
}

/**
    @brief  dump the provision settings
    @param[in]  buffer  area to dump settings
    @return bytes dumped
*/

int
provision_setting_dump(char *buffer)
{
        provision_header_t *header = (provision_header_t *) PROVISION_ADDR;
        char *name;

        switch (header->stage) {
        case PROVISION_STAGE_FACTORY:
                name = "factory";
                break;

        case PROVISION_STAGE_SHIPPING:
                name = "shipping";
                break;

        case PROVISION_STAGE_DEPLOYED:
                return sprintf(buffer, "provision deployed\n");

        default:
                return 0;
        }

        return sprintf(buffer, "provision %s %d\n", name, provision_sleep);
}

/** @} */

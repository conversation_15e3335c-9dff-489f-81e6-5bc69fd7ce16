/** @{

    @ingroup    common
    @file

    @brief  MCU Sleep
*/

#include "pelagic.h"
#include "mcu_sleep.h"
#include "pinmap.h"
#include "system_file.h"
#include "device_power.h"

#ifdef HAVE_LED
#include "led.h"
#endif

#ifdef HAVE_DIP
#include "dip.h"
#endif

enum {
        VLLS1_SLEEP_LIMIT = 65535,  // LPTMR is only a 16-bit counter.
};

extern volatile bool board_initted;

/**
    @brief shutdown any remaining pins and/or devices before sleeping
*/

void
final_power_off()
{
#ifdef HAVE_LED
        led_power_off();
#endif

#ifdef HAVE_DIP
        dip_power_off();
#endif

#if defined(TARGET_VMS)
        device_power_all_off();
#endif
        serial_power_off(&console_uart);
}

/**
    @brief  place the MCU into VLLS* sleep state
    @param[in]  seconds time to sleep in seconds
    @note   The MCU will sleep in one of two VLLS modes:
            - VLLS0 (when XCAL is present) lowest possible power consumption.
              This uses the RTC alarm which can sleep up to 2^32-1 seconds.
            - VLLS1 (no XCAL) slightly higher power consumption.
              The low power timer (LPTMR) is used which can sleep up to 64K seconds
              or 18 hours. The startup code will check to see if the MCU needs to
              continue to sleep in this case.

            In both modes, clocks are disabled, and RAM is not powered and
            all contents lost. The only thing to survive is the System Register File.
*/

void
mcu_sleep(uint32_t seconds)
{
        // TODO Ian: implement for STM32
//         uint8_t level;

//         if (board_initted)
//                 final_power_off();

//         int_disable();

//         SMC->PMPROT = SMC_PMPROT_AVLLS_MASK | SMC_PMPROT_ALLS_MASK | SMC_PMPROT_AVLP_MASK;
//         rtc_save(seconds);

//         if (rtc_status() == RTC_STATUS_USE_XCAL) {
//                 RTC->TAR = seconds + RTC->TSR;
//                 RTC->IER |= RTC_IER_TAIE_MASK;
//                 LLWU->ME |= LLWU_ME_WUME5_MASK;  // LLWU Module 5 is RTC alarm
//                 SMC->PMPROT = SMC_PMPROT_AVLLS_MASK | SMC_PMPROT_ALLS_MASK | SMC_PMPROT_AVLP_MASK;
//                 level = 0;
//         } else {
//                 // Make sure LPTMR is running
//                 SIM->SCGC5 |= SIM_SCGC5_LPTMR_MASK;

//                 // Reset it
//                 LPTMR0->CSR = 0;
//                 LPTMR0->PSR = LPTMR_PSR_PCS(1) | LPTMR_PSR_PRESCALE(9); // LPO as source, 1024 prescaler to get roughly 1 second tick

// #ifdef TARGET_SENSOR
//                 // Sensor should never use VLLS1 - its only if the XCAL failed.
//                 if (seconds > VLLS1_SLEEP_LIMIT)
//                         seconds = VLLS1_SLEEP_LIMIT;
// #else
//                 if (seconds > VLLS1_SLEEP_LIMIT) {
//                         SYS_REG_FILE->sleep_remaining = seconds - VLLS1_SLEEP_LIMIT;
//                         seconds = VLLS1_SLEEP_LIMIT;
//                 } else {
//                         SYS_REG_FILE->sleep_remaining = 0;
//                 }
// #endif

//                 /* Set the compare register */
//                 LPTMR0->CMR   = (seconds * 1000) / 1024; // 1 tick is 1.024 seconds, adjust for it.
//                 /* Enable interrupt */
//                 LPTMR0->CSR  |= LPTMR_CSR_TIE_MASK;
//                 /* Start the timer */
//                 LPTMR0->CSR  |= LPTMR_CSR_TEN_MASK;
//                 LLWU->ME     |= LLWU_ME_WUME0_MASK; // LLWU Module 0 is LPTMR
//                 level = 1;
//         }

//         /* Set the STOPM field to 0b100 for VLLS1 mode */
//         SMC->PMCTRL  &= ~SMC_PMCTRL_STOPM_MASK;
//         SMC->PMCTRL  |= SMC_PMCTRL_STOPM(0x4);
//         SMC->STOPCTRL = SMC_STOPCTRL_VLLSM(level);
//         SMC->STOPCTRL; // yes, its valid C and used to just read a volatile variable, no assignment

//         SCB->SCR = 1<<SCB_SCR_SLEEPDEEP_Pos;

        __DSB();
        __WFI();
}

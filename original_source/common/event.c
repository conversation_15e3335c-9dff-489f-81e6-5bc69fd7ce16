/** @{

    @ingroup common
    @file
    @brief  Event Log
*/

#include "pelagic.h"

enum {
        EVENT_ENTRIES       = 64,        // needs to be a power of 2
};

typedef struct {
        event_t         event;
        uint32_t        timestamp;
        char            *desc;
        int             param_count;
        event_param_t   params[6];
} event_entry_t;

static const char *level_names[] = {
        "info",
        "prof",
        "note",
        "error"
};

static const char *source_names[] = {
        "sys",
        "bnet",
        "fs",
        "flash",
        "serial",
        "gps",
        "helm",
        "power",
        "firmware",
        "bt",
        "radio",
        "modem",
        "reboot",
        "accel",
        "log",
        "fault",
        "event",
        "setting",
        "nap",
        "satellite",
        "humidity",
        "mems",
        "sensor",
        "pds"
};

#ifdef TARGET_VMS
osMutexId event_display_mutex;
osMutexDef(event_display_mutex);
#endif

uint8_t event_buffer[128];

uint32_t event_time = 0;
uint8_t event_in = 0, event_out = 0;

event_entry_t event_entries[EVENT_ENTRIES];

bool event_show = true;

#ifdef TARGET_VMS
/**
    @brief  initialize event logger
*/

void
event_init()
{
        event_display_mutex = osMutexCreate(osMutex(event_display_mutex));
}
#endif

/**
    @brief  display an event entry
    @param[in]  entry   event entry to display
    @param[in]  from_log
                        - false if being called from EVENT_LOG*()
                        - true if display from the log
    @note strings are copied into the flash store but not the
          ring buffer log. In most cases event_display will be called
          from EVENT_LOG*() to display events as they happen, and
          the string can be displayed. Otherwise, the string will be
          displayed as a "*" unless it was constant pointer.
*/

void
event_display(event_entry_t *entry, int from_log)
{
        event_param_t *p = entry->params;
        uint32_t source  = (entry->event >> EVT_SOURCE_SHIFT) & EVT_SOURCE_MASK,
                 level   = (entry->event >> EVT_LEVEL_SHIFT) & EVT_LEVEL_MASK;

#ifdef TARGET_VMS
        osMutexWait(event_display_mutex, osWaitForever);
#endif

        datetime_display(entry->timestamp);

        // sanity check - make sure source & level do not overindex the labels
        if ( source < ARRAY_SIZE(source_names) && level < ARRAY_SIZE(level_names)) {
                printf(" %-8s:%-5s %s", source_names[source], level_names[level], entry->desc);
        } else {
                printf(" #%d:#%d:%2d %s", source, level, entry->event & EVT_NUM_MASK, entry->desc);
        }

        for (int idx = 0; idx < entry->param_count; p++, idx++) {

                // a string param may be a buffer on the stack or reused later -- just skip it.
                switch (p->type & ~EVT_FORMAT_MASK) {
                case EVT_STRCONST:
                        printf(" %s=[%s]", p->desc, (char *)p->arg);
                        break;

                case EVT_STRING:
                        if (from_log)
                                printf(" %s=[*]", p->desc);
                        else
                                printf(" %s=[%s]", p->desc, (char *)p->arg);
                        break;

                case EVT_BOOL:
                        printf(" %s=%s", p->desc, p->arg ? "true" : "false");
                        break;

                case EVT_POINT:
                        printf(" %s=%d", p->desc, p->arg);
                        break;

                case EVT_UID:
                        printf(" %Z", (void *)p->arg);
                        break;

                default:
                        printf(p->type & EVT_HEX ? " %s=0x%x" : " %s=%d", p->desc, p->arg);
                        break;
                }
        }

        printf("\n");

#ifdef TARGET_VMS
        osMutexRelease(event_display_mutex);
#endif
}

/**
    @brief  dump the events from the ring buffer (not flash store)
*/

void
event_dump()
{
        event_entry_t *entry;
        int idx = event_out;

        while (idx != event_in) {
                entry = &event_entries[idx];
                event_display(entry, 1);
                idx = (idx + 1) % EVENT_ENTRIES;
        }
}

#ifdef TARGET_VMS
/**
    @brief  normalize an event type for storage (aka change EVT_STRCONST to EVT_STRING)
*/

uint8_t
event_type_normalize(event_type_t type)
{
        switch (type) {
        case EVT_STRCONST:
                return EVT_STRING;
        default:
                return type & ~EVT_FORMAT_MASK;
        }
}

/**
    @brief  store an event into the flash store
    @param[in]  event   event type (EVT_{SYS, NAP, MODEM}_*)
    @param[in]  param_count params array size
    @param[in]  params  event arguments
    @note   The event will not be stored for the following conditions
            - The event source is EVT_FLASH or EVT_FS to prevent infinite recursion.
            - The level is INFO or PROF and source is not set in the events log settings
            - The event partition is locked or busy
*/

void
event_store(event_t event, int param_count, event_param_t *params)
{
        int len, level, source;
        uint16_t header = 0;
        uint8_t param_header[4] = { 0,0,0,0 }, *data;
        uint32_t now = rtc_read(), record_size;
        uint32_t time_delta;
        bool full_record;

        level = (event >> EVT_LEVEL_SHIFT) & EVT_LEVEL_MASK;
        source = (event >> EVT_SOURCE_SHIFT) & EVT_SOURCE_MASK;

        if (level == EVT_INFO && ((1 << source) & settings.events.info) == 0)
                return;
        else if (level == EVT_PROF && ((1 << source) & settings.events.profile) == 0)
                return;

        switch (source) {
        case EVT_FLASH:
        case EVT_FS:
                // TODO - have a separate RAM based buffer to track flash issues..
                // Don't record anything coming from the flash chip or store - it will cause
                // an infinite recursion..
                return;
        }

        if (flash_store_lock(&event_partition, 1000) == false) {
                printf("timeout on event part lock\n");
                return;
        }

        if (event_partition.is_busy) {
                flash_store_unlock(&event_partition);
                return;
        }

        time_delta = now - event_time;
        event_time = now;
        full_record = false;

        for (;;) {
                event_param_t *p = params;

                header = event | (param_count << EVT_HEADER_PARAMS_SHIFT);

                if (time_delta >= 256 || full_record)
                        header |= EVT_FULL_TIMESTAMP_BIT;

                data = event_buffer;
                *data++ = EVT_RECORD_MARKER;
                data = store_uint16(data, header);

                if (header & EVT_FULL_TIMESTAMP_BIT) {
                        data = store_uint32(data, now);
                        full_record = true;
                } else {
                        *data++ = time_delta;
                }

                for (int i = 0; i < param_count; i++) {
                        param_header[(i/2)] |= (event_type_normalize(params[i].type) << ((i&1)*4));
                }

                if (param_count) {
                        int bytes = (param_count + 1)/2;
                        memcpy(data, param_header, bytes);
                        data += bytes;
                }

                for (int idx = 0; idx < param_count; idx++, p++) {
                        switch (p->type & ~EVT_FORMAT_MASK) {
                        case EVT_8BIT:
                        case EVT_S8BIT:
                        case EVT_BOOL:
                                *data++ = p->arg;
                                break;

                        case EVT_16BIT:
                        case EVT_S16BIT:
                                data = store_uint16(data, p->arg);
                                break;

                        case EVT_32BIT:
                        case EVT_S32BIT:
                        case EVT_POINT:
                                data = store_uint32(data, p->arg);
                                break;

                        case EVT_STRCONST:
                        case EVT_STRING:
                                len = strlen((char *) p->arg);
                                *data++ = len;
                                memcpy(data, (void *)p->arg, len);
                                data += len;
                                break;

                        case EVT_UID:
                                memcpy(data, (void *)p->arg, BOARD_UID_SIZE);
                                data += BOARD_UID_SIZE;
                                break;

                        case EVT_IMEI:
                                memcpy(data, (void *)p->arg, sizeof(imei_t));
                                data += sizeof(imei_t);
                                break;
                        }
                }

                record_size = data - event_buffer;
                if (full_record)
                        break;

                if (flash_store_partial_okay(&event_partition, record_size))
                        break;

                full_record = true;
        }

        flash_store_write(&event_partition, event_buffer, record_size);
        flash_store_unlock(&event_partition);
}

#endif

/**
    @brief log an event - usually called from EVENT_LOG*(...)
    @param[in]  event   event type to log
    @param[in]  desc    event type description (for displaying)
    @param[in]  param_count params array size
    @param[in]  params  optional event parameters to log
*/

void
event_log(event_t event, char *desc, int param_count, event_param_t *params)
{
        event_entry_t *entry;

#ifdef TARGET_VMS
        event_store(event, param_count, params);
#endif

        int_disable();
        entry = &event_entries[event_in];
        event_in = (event_in + 1) % EVENT_ENTRIES;

        if (event_in == event_out) {
                event_out = (event_out + 1) % EVENT_ENTRIES;
        }
        int_enable();

        entry->event = event;
        entry->timestamp = rtc_read();
        entry->param_count = param_count;
        entry->desc = desc;

        memcpy(entry->params, params, sizeof(event_param_t) * param_count);

        if (event_show)
                event_display(entry, 0);
}

#ifdef TARGET_VMS
/**
    @brief reset event log deltas - called after the event log is uploaded
*/

void
event_log_reset()
{
        event_time = 0;
}

/**
    @brief  read an event from the event log flash partition (for debugging)
    @param[in]  read    flash stream for reading
    @param[out] event   event retrieved
    @param[out] timestamp   event timestamp in Epoch seconds
    @param[out] param_count parameters retrieved
    @param[out] params      parameter values
*/

void
event_log_read(flash_read_t *read, event_t *event, uint32_t *timestamp, int *param_count, event_param_t *params)
{
        uint16_t header = 0;
        uint8_t type, len, marker, param_headers[3];
        static board_uid_t uid;

        marker = flash_read_uint8(read);

        if (marker != EVT_RECORD_MARKER)
                return;

        header = flash_read_uint16(read);
        *event = header & EVT_HEADER_EVENT_MASK;
        *param_count = (header >> EVT_HEADER_PARAMS_SHIFT) & EVT_HEADER_PARAMS_MASK;

        if (header & EVT_FULL_TIMESTAMP_BIT)
                *timestamp = flash_read_uint32(read);
        else
                *timestamp = *timestamp + flash_read_uint8(read);

        if (*param_count > 7)
                return;             // sanity check..

        for (int i = 0; i < (*param_count + 1) / 2; i++)
                param_headers[i] = flash_read_uint8(read);

        for (int i = 0; i < *param_count; i++, params++) {
                type = param_headers[(i / 2)];
                type = (i & 1) ? (type >> 4) : (type & 0xf);

                params->type = type;
                switch (type) {
                case EVT_8BIT:
                case EVT_BOOL:
                        params->arg = flash_read_uint8(read);
                        break;

                case EVT_16BIT:
                        params->arg = flash_read_uint16(read);
                        break;

                case EVT_32BIT:
                case EVT_POINT:
                        params->arg = flash_read_uint32(read);
                        break;

                case EVT_STRCONST:
                case EVT_STRING:
                        len = flash_read_uint8(read);
                        while (len-- > 0)
                                flash_read_uint8(read);
                        break;
                case EVT_UID:
                        flash_read_block(read, uid, sizeof(uid));
                        params->arg = (uint32_t)uid;
                        break;
                }
        }
}

/**
    @brief  initialize event settings to defaults
*/

void
event_settings_init()
{
        settings.events.info = 0;
        settings.events.profile = 0;
}

/**
    @brief  parse event setting
    @param[in]  argc    argv array size
    @param[in]  argv    setting arguments
    @return true if setting was successful
    @note   The following commands are supported:
            - event info NUM  events bitfield to set to log event level info (EVT_INFO)
            - event profile NUM  events bitfield to set to log event level profile (EVT_PROF)
*/

bool
event_setting(int argc, char **argv)
{
        char *command = argv[0], *arg = argv[1];

        if (argc != 2)
                return false;

        if (strcmp(command, "profile") == 0) {
                settings.events.profile = atoi(arg);
                EVENT_LOG1(EVT_EVENT_SET_PROFILE, "set profile", "flags", EVT_32BIT | EVT_HEX, settings.events.profile);
        } else if(strcmp(command, "info") == 0) {
                settings.events.info = atoi(arg);
                EVENT_LOG1(EVT_EVENT_SET_INFO, "set info", "flags", EVT_32BIT | EVT_HEX, settings.events.info);
        } else {
                return false;
        }

        return true;
}

/**
    @brief  dump event settings
    @param[in]  buffer  area to store settings
    @return bytes used
*/

int
event_setting_dump(char *buffer)
{
        int len;
        len = sprintf(buffer, "event info 0x%x\n", settings.events.info);
        len += sprintf(buffer+len, "event profile 0x%x\n", settings.events.profile);
        return len;
}
#endif

/** @} */

#ifndef __PELAGIC_TYPES_H__
#define __PELAGIC_TYPES_H__

#include <inttypes.h>
#include <stdbool.h>

// enum  {
//         true    = 1,
//         false   = 0
// };
// typedef uint8_t bool;

enum {
        BOARD_UID_SIZE = 10
};

typedef uint8_t board_uid_t[BOARD_UID_SIZE];

enum {
        MINUTE   = 60,
        HOUR     = 60 * MINUTE,
        DAY      = 24 * HOUR,

        KILOBYTE = (1024),
        MEGABYTE = (1024*1024),
};


#endif

#ifndef __SENSOR_TYPES_H__
#define __SENSOR_TYPES_H__

enum {
        SENSOR_TYPE_MASK        = 0x0F,
        SENSOR_THERMISTOR       = 1,
        SENSOR_TEMPERATURE      = 2,
        SENSOR_SALINITY         = 3,
        SENSOR_DEPTH            = 4,
        SENSOR_HUMIDITY         = 5,
        SENSOR_WATER_LEVEL      = 6,
        SENSOR_WIND_SPEED       = 7,
        SENSOR_WIND_DIRECTION   = 8,
        SENSOR_PRECIPITATION    = 9,
        SENSOR_SOLAR            = 10,
        SENSOR_POWER            = 11,
        SENSOR_MOTOR_SPEED      = 12,
        SENSOR_PROTOTYPE        = 13,
        SENSOR_IMMERSION_A      = 14,
        SENSOR_IMMERSION_B      = 15,
};

enum {
        SENSOR_THERMISTOR_SIZE = 2,
};

#endif

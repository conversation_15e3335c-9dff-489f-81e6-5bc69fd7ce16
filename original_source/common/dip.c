/** @{

    @ingroup    common
    @file
    @brief  DIP support
    @note   This is for v0.051 hardware and earlier.
*/

#include "pelagic.h"
#include "gpio_api.h"
#include "gpio_irq_api.h"
#include "dip.h"
#include "pinmap.h"

gpio_t dip1_pin, dip2_pin, dip3_pin;

/**
    @brief  initialize DIP pins
*/

void
dip_init()
{
        gpio_init_in(&dip1_pin, DIP1);
        gpio_init_in(&dip2_pin, DIP2);
        gpio_init_in(&dip3_pin, DIP3);
}

/**
    @brief  set pins in pull down state to save power during sleep
*/

void
dip_power_off()
{
        pin_mode(DIP1, PullDown);
        pin_mode(DIP2, PullDown);
        pin_mode(DIP3, PullDown);
}

/**
    @brief  check to see if a DIP switch is set on
    @param[in]  dip DIP1, DIP2, or DIP3 to check
    @return true if dip is set on.
*/

int
dip_on(int dip)
{
        switch(dip) {
        case DIP1:
                return !gpio_read(&dip1_pin);
        case DIP2:
                return !gpio_read(&dip2_pin);
        case DIP3:
                return !gpio_read(&dip3_pin);
        }

        return 0;
}

/** @} */

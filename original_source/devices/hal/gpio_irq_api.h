
/** \addtogroup hal */
/** @{*/
/* mbed Microcontroller Library
 * Copyright (c) 2006-2013 ARM Limited
 * SPDX-License-Identifier: Apache-2.0
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#ifndef MBED_GPIO_IRQ_API_H
#define MBED_GPIO_IRQ_API_H

#include "device.h"
#include "pinmap.h"

#if DEVICE_INTERRUPTIN

#ifdef __cplusplus
extern "C" {
#endif

/** GPIO IRQ events
 */
typedef enum {
    IRQ_NONE,
    IRQ_RISE,
    IRQ_FALL
} gpio_irq_event;

/** GPIO IRQ HAL structure. gpio_irq_s is declared in the target's HAL
 */
typedef struct gpio_irq_s gpio_irq_t;

typedef void (*gpio_irq_handler)(uintptr_t context, gpio_irq_event event);

/**
 * \defgroup hal_gpioirq GPIO IRQ HAL functions
 *
 * # Defined behavior
 * * ::gpio_irq_init initializes the GPIO IRQ pin
 * * ::gpio_irq_init attaches the interrupt handler
 * * ::gpio_irq_free releases the GPIO IRQ pin
 * * ::gpio_irq_set enables/disables pin IRQ event
 * * ::gpio_irq_enable enables GPIO IRQ
 * * ::gpio_irq_disable disables GPIO IRQ
 *
 * # Undefined behavior
 * * Calling other function before ::gpio_irq_init
 *
 * @{
 */

/**
 * \defgroup hal_gpioirq_tests GPIO IRQ HAL tests
 * The GPIO IRQ HAL tests ensure driver conformance to defined behaviour.
 *
 * To run the GPIO IRQ hal tests use the command:
 *
 *     mbed test -t <toolchain> -m <target> -n tests-mbed_hal_fpga_ci_test_shield-gpio_irq
 *
 */

/** Initialize the GPIO IRQ pin
 *
 * @param obj     The GPIO object to initialize
 * @param pin     The GPIO pin name
 * @param handler The handler to be attached to GPIO IRQ
 * @param context The context to be passed back to the handler (context != 0, 0 is reserved)
 * @return -1 if pin is NC, 0 otherwise
 */
int gpio_irq_init(gpio_irq_t *obj, PinName pin, gpio_irq_handler handler, uintptr_t context);

/** Release the GPIO IRQ PIN
 *
 * @param obj The gpio object
 */
void gpio_irq_free(gpio_irq_t *obj);

/** Enable/disable pin IRQ event
 *
 * @param obj    The GPIO object
 * @param event  The GPIO IRQ event
 * @param enable The enable flag
 */
void gpio_irq_set(gpio_irq_t *obj, gpio_irq_event event, uint32_t enable);

/** Enable GPIO IRQ
 *
 * This is target dependent, as it might enable the entire port or just a pin
 * @param obj The GPIO object
 */
void gpio_irq_enable(gpio_irq_t *obj);

/** Disable GPIO IRQ
 *
 * This is target dependent, as it might disable the entire port or just a pin
 * @param obj The GPIO object
 */
void gpio_irq_disable(gpio_irq_t *obj);

/** Get the pins that support all GPIO IRQ tests
 *
 * Return a PinMap array of pins that support GPIO IRQ.
 * The array is terminated with {NC, NC, 0}.
 *
 * Targets should override the weak implementation of this
 * function to provide the actual pinmap for GPIO IRQ testing.
 *
 * @return PinMap array
 */
const PinMap *gpio_irq_pinmap(void);

/**@}*/

#ifdef __cplusplus
}
#endif

#endif

#endif

/** @}*/

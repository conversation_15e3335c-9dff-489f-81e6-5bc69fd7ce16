#ifndef ACCEL_LSM6DSO32_H
#define ACCEL_LSM6DSO32_H

// TODO Ian: find imu driver implementation
// see lsm303d.c/accel.h for previous driver
// Note these definitions came from petr's code

// IMU address definitions
#define REG_LSM6DSO32_ADDR_PAGE_SEL   			(0x02)
#define REG_LSM6DSO32_ADDR_EMB_FUNC_EN_A   		(0x04)
#define REG_LSM6DSO32_ADDR_EMB_FUNC_EN_B   		(0x05)
#define REG_LSM6DSO32_ADDR_PAGE_ADDRESS   		(0x08)
#define REG_LSM6DSO32_ADDR_PAGE_VALUE   		(0x09)
#define REG_LSM6DSO32_ADDR_EMB_FUNC_INT1   		(0x0A)
#define REG_LSM6DSO32_ADDR_FSM_INT1_A   		(0x0B)
#define REG_LSM6DSO32_ADDR_FSM_INT1_B   		(0x0C)
#define REG_LSM6DSO32_ADDR_EMB_FUNC_INT2   		(0x0E)
#define REG_LSM6DSO32_ADDR_FSM_INT2_A  	 		(0x0F)
#define REG_LSM6DSO32_ADDR_FSM_INT2_B   		(0x10)
#define REG_LSM6DSO32_ADDR_EMB_FUNC_STATUS   	(0x12)
#define REG_LSM6DSO32_ADDR_FSM_STATUS_A   		(0x13)
#define REG_LSM6DSO32_ADDR_FSM_STATUS_B   		(0x14)
#define REG_LSM6DSO32_ADDR_PAGE_RW   			(0x17)

#define REG_LSM6DSO32_ADDR_EMB_FUNC_FIFO_CFG   	(0x44)
#define REG_LSM6DSO32_ADDR_FSM_ENABLE_A   		(0x46)
#define REG_LSM6DSO32_ADDR_FSM_ENABLE_B   		(0x47)
#define REG_LSM6DSO32_ADDR_FSM_LONG_COUNTER_L   (0x48)
#define REG_LSM6DSO32_ADDR_FSM_LONG_COUNTER_H   (0x49)
#define REG_LSM6DSO32_ADDR_FSM_LONG_COUNTER_CLEA (0x4A)
#define REG_LSM6DSO32_ADDR_FSM_OUTS1   			(0x4C)
#define REG_LSM6DSO32_ADDR_FSM_OUTS2   			(0x4D)
#define REG_LSM6DSO32_ADDR_FSM_OUTS3   			(0x4E)
#define REG_LSM6DSO32_ADDR_FSM_OUTS4   			(0x4F)
#define REG_LSM6DSO32_ADDR_FSM_OUTS5   			(0x50)
#define REG_LSM6DSO32_ADDR_FSM_OUTS6   			(0x51)
#define REG_LSM6DSO32_ADDR_FSM_OUTS7   			(0x52)
#define REG_LSM6DSO32_ADDR_FSM_OUTS8   			(0x53)
#define REG_LSM6DSO32_ADDR_FSM_OUTS9   			(0x54)
#define REG_LSM6DSO32_ADDR_FSM_OUTS10  			(0x55)
#define REG_LSM6DSO32_ADDR_FSM_OUTS11   		(0x56)
#define REG_LSM6DSO32_ADDR_FSM_OUTS12   		(0x57)
#define REG_LSM6DSO32_ADDR_FSM_OUTS13   		(0x58)
#define REG_LSM6DSO32_ADDR_FSM_OUTS14   		(0x59)
#define REG_LSM6DSO32_ADDR_FSM_OUTS15   		(0x5A)
#define REG_LSM6DSO32_ADDR_FSM_OUTS16   		(0x5B)

#define REG_LSM6DSO32_ADDR_EMB_FUNC_ODR_CFG_B   (0x5F)
#define REG_LSM6DSO32_ADDR_STEP_COUNTER_L   	(0x62)
#define REG_LSM6DSO32_ADDR_STEP_COUNTER_H   	(0x63)
#define REG_LSM6DSO32_ADDR_EMB_FUNC_SRC   		(0x64)
#define REG_LSM6DSO32_ADDR_EMB_FUNC_INIT_A   	(0x66)
#define REG_LSM6DSO32_ADDR_EMB_FUNC_INIT_B   	(0x67)

// IMU command definitions
#define REG_LSM6DSO32_ADDR_PAGE_SEL   			(0x02)
#define REG_LSM6DSO32_ADDR_EMB_FUNC_EN_A   		(0x04)
#define REG_LSM6DSO32_ADDR_EMB_FUNC_EN_B   		(0x05)
#define REG_LSM6DSO32_ADDR_PAGE_ADDRESS   		(0x08)
#define REG_LSM6DSO32_ADDR_PAGE_VALUE   		(0x09)
#define REG_LSM6DSO32_ADDR_EMB_FUNC_INT1   		(0x0A)
#define REG_LSM6DSO32_ADDR_FSM_INT1_A   		(0x0B)
#define REG_LSM6DSO32_ADDR_FSM_INT1_B   		(0x0C)
#define REG_LSM6DSO32_ADDR_EMB_FUNC_INT2   		(0x0E)
#define REG_LSM6DSO32_ADDR_FSM_INT2_A  	 		(0x0F)
#define REG_LSM6DSO32_ADDR_FSM_INT2_B   		(0x10)
#define REG_LSM6DSO32_ADDR_EMB_FUNC_STATUS   	(0x12)
#define REG_LSM6DSO32_ADDR_FSM_STATUS_A   		(0x13)
#define REG_LSM6DSO32_ADDR_FSM_STATUS_B   		(0x14)
#define REG_LSM6DSO32_ADDR_PAGE_RW   			(0x17)

#define REG_LSM6DSO32_ADDR_EMB_FUNC_FIFO_CFG   	(0x44)
#define REG_LSM6DSO32_ADDR_FSM_ENABLE_A   		(0x46)
#define REG_LSM6DSO32_ADDR_FSM_ENABLE_B   		(0x47)
#define REG_LSM6DSO32_ADDR_FSM_LONG_COUNTER_L   (0x48)
#define REG_LSM6DSO32_ADDR_FSM_LONG_COUNTER_H   (0x49)
#define REG_LSM6DSO32_ADDR_FSM_LONG_COUNTER_CLEA (0x4A)
#define REG_LSM6DSO32_ADDR_FSM_OUTS1   			(0x4C)
#define REG_LSM6DSO32_ADDR_FSM_OUTS2   			(0x4D)
#define REG_LSM6DSO32_ADDR_FSM_OUTS3   			(0x4E)
#define REG_LSM6DSO32_ADDR_FSM_OUTS4   			(0x4F)
#define REG_LSM6DSO32_ADDR_FSM_OUTS5   			(0x50)
#define REG_LSM6DSO32_ADDR_FSM_OUTS6   			(0x51)
#define REG_LSM6DSO32_ADDR_FSM_OUTS7   			(0x52)
#define REG_LSM6DSO32_ADDR_FSM_OUTS8   			(0x53)
#define REG_LSM6DSO32_ADDR_FSM_OUTS9   			(0x54)
#define REG_LSM6DSO32_ADDR_FSM_OUTS10  			(0x55)
#define REG_LSM6DSO32_ADDR_FSM_OUTS11   		(0x56)
#define REG_LSM6DSO32_ADDR_FSM_OUTS12   		(0x57)
#define REG_LSM6DSO32_ADDR_FSM_OUTS13   		(0x58)
#define REG_LSM6DSO32_ADDR_FSM_OUTS14   		(0x59)
#define REG_LSM6DSO32_ADDR_FSM_OUTS15   		(0x5A)
#define REG_LSM6DSO32_ADDR_FSM_OUTS16   		(0x5B)

#define REG_LSM6DSO32_ADDR_EMB_FUNC_ODR_CFG_B   (0x5F)
#define REG_LSM6DSO32_ADDR_STEP_COUNTER_L   	(0x62)
#define REG_LSM6DSO32_ADDR_STEP_COUNTER_H   	(0x63)
#define REG_LSM6DSO32_ADDR_EMB_FUNC_SRC   		(0x64)
#define REG_LSM6DSO32_ADDR_EMB_FUNC_INIT_A   	(0x66)
#define REG_LSM6DSO32_ADDR_EMB_FUNC_INIT_B   	(0x67)

#endif /* ACCEL_LSM6DSO32_H */

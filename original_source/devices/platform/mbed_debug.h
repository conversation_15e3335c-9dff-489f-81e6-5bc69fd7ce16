/* mbed Microcontroller Library
 * Copyright (c) 2006-2019 ARM Limited
 * SPDX-License-Identifier: Apache-2.0
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#ifndef MBED_DEBUG_H
#define MBED_DEBUG_H
#include <stdio.h>
#include <stdarg.h>

#ifdef __cplusplus
extern "C" {
#endif

/** \addtogroup platform-public-api */
/** @{*/

/**
 * \defgroup platform_debug Debug functions
 * @{
 */

static inline void debug(const char *format, ...);
static inline void debug_if(int condition, const char *format, ...);

/** Output a debug message
 *
 * @param format printf-style format string, followed by variables
 */
static inline void debug(const char *format, ...)
{
#ifndef NDEBUG
    // For simplicity, just format to a buffer and use printf
    char buffer[256];
    va_list args;
    va_start(args, format);
    vsprintf(buffer, format, args);  // Use project's vsprintf
    va_end(args);
    printf("%s", buffer);  // Use project's printf that outputs over SWO
#endif
}


/** Conditionally output a debug message
 *
 * NOTE: If the condition is constant false (== 0) and the compiler optimization
 * level is greater than 0, then the whole function will be compiled away.
 *
 * @param condition output only if condition is true (!= 0)
 * @param format printf-style format string, followed by variables
 */
static inline void debug_if(int condition, const char *format, ...)
{
#ifndef NDEBUG
    if (condition) {
        // For simplicity, just format to a buffer and use printf
        char buffer[256];
        va_list args;
        va_start(args, format);
        vsprintf(buffer, format, args);  // Use project's vsprintf
        va_end(args);
        printf("%s", buffer);  // Use project's printf that outputs over SWO
    }
#endif
}


#ifdef __cplusplus
}
#endif

#endif

/**@}*/

/**@}*/


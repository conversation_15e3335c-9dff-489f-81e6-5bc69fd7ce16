#include "accel_lsm6dso32.h"

#include "accel.h"

// TODO Ian: translate this shit to implement accel interface

bool accel_init(void) { /* TODO */ }
void accel_pin_setup(void) { /* TODO */ }

void accel_power_on(void) { /* TODO */ }
void accel_power_off(void) { /* TODO */ }
bool accel_read_data(axis_t *data) { /* TODO */ }

void accel_irq_enable(void) { /* TODO */ }

bool accel_should_stay_in_nap_mode() { /* TODO */ }
void log_accel_deviations(event_t evt, uint16_t stationary_time) { /* TODO */ }
bool accel_upsidedown(void) { /* TODO */ }

//! Note this implementation came from petr's code

// LSM6DSO32_t * 	LSM6DSO32_Init(uint8_t IIC_Controller, uint8_t slave_adderess, GPIO_PIN power)
// {
// // 	LSM6DSO32_t * gyro = (LSM6DSO32_t *)sysmalloc(sizeof(LSM6DSO32_t));
// // 	memset((void*)gyro,0,sizeof(LSM6DSO32_t));
// // 	gyro->device 						= sysmalloc(sizeof(HAL_IIC_DEVICE_t ));
// // 	gyro->device->Config 	   			= IIC_DEVICE(IIC_NO_USE_EXPANDER,0,IIC_CLOCK_MODE_100KHZ,IIC_DEVICE_ADDRESS__MODE_7BIT, slave_adderess << 1);
// // 	gyro->device->Controller 			= (uint8_t)IIC_Controller;

// // 	volatile uint8_t reg;
// // 	reg = LSM6DSO32_RegRead	(gyro,  LSM6DSO32_REG_WHO_AM_I);
// // 	LSM6DSO32_RegWrite  ( gyro, LSM6DSO32_REG_CTRL3_C , 0x1);
// // 	PlatformDelayMs(1);


// // 	LSM6DSO32_RegWrite  ( gyro, LSM6DSO32_REG_CTRL1_XL , 0x50);

// // 	LSM6DSO32_RegWrite  ( gyro, LSM6DSO32_REG_CTRL2_G ,	0x50);



// // 	LSM6DSO32_RegWrite  ( gyro, LSM6DSO32_REG_CTRL3_C , 0x40);


// // 	LSM6DSO32_RegWrite  ( gyro,  LSM6DSO32_REG_FIFO_CTRL_4,0);


// // 	LSM6DSO32_RegWrite  ( gyro, LSM6DSO32_REG_INT2_CTRL_1,0);


// // 	LSM6DSO32_RegWrite  ( gyro, LSM6DSO32_REG_INT2_CTRL_2,0);


// // 	LSM6DSO32_RegWrite  ( gyro, LSM6DSO32_REG_FUNC_CFG_ACCESS , (1 << 6));
// // 	reg = LSM6DSO32_RegRead	( gyro, LSM6DSO32_REG_FUNC_CFG_ACCESS );

// // 	reg = LSM6DSO32_RegRead	( gyro, LSM6DSO32_REG_MASTER_CONFIG  );
// // /*

// // 	LSM6DSO32_RegWrite  ( gyro, LSM6DSO32_REG_MASTER_CONFIG , reg | (1 << 5));
// // 	PlatformDelayMs(5);
// // 	LSM6DSO32_RegWrite  ( gyro, LSM6DSO32_REG_MASTER_CONFIG ,  (1 << 5));

// // 	LSM6DSO32_RegWrite  ( gyro, LSM6DSO32_REG_MASTER_CONFIG ,  0);
// // 	LSM6DSO32_RegWrite  ( gyro, LSM6DSO32_REG_MASTER_CONFIG , (1 << 4));
// // */
// // 	//LSM6DSO32_RegWrite  ( gyro, LSM6DSO32_REG_MASTER_CONFIG , 0x8c);
// // 	//LSM6DSO32_RegWrite  ( gyro, LSM6DSO32_REG_MASTER_CONFIG , 0x08);


// // 	reg = LSM6DSO32_RegRead	( gyro, LSM6DSO32_REG_MASTER_CONFIG);

// // 	LSM6DSO32_RegWrite  ( gyro, LSM6DSO32_REG_FUNC_CFG_ACCESS , 0);


// // 	//return gyro;

// // 	reg = LSM6DSO32_RegRead	(gyro,  LSM6DSO32_REG_WHO_AM_I);

// // 	LSM6DSO32_RegWrite  ( gyro, LSM6DSO32_REG_FUNC_CFG_ACCESS , 0x40);
// // 	LSM6DSO32_RegWrite  ( gyro, LSM6DSO32_REG_MASTER_CONFIG , 0x4c);
// // 	LSM6DSO32_RegWrite  ( gyro, LSM6DSO32_REG_SLV0_ADD  , 0x3d);// 0x1e << 1 | 0x1);
// // 	LSM6DSO32_RegWrite  ( gyro, LSM6DSO32_REG_SLV0_SUBADD  , 0x4f);
// // 	LSM6DSO32_RegWrite  ( gyro, LSM6DSO32_REG_SLV0_CONFIG  , 0x1);

// // 	LSM6DSO32_RegWrite  ( gyro, LSM6DSO32_REG_MASTER_CONFIG , 0x4c);
// // 	LSM6DSO32_RegWrite  ( gyro, LSM6DSO32_REG_FUNC_CFG_ACCESS , 0x0);

// // 	reg = LSM6DSO32_RegRead	( gyro, LSM6DSO32_REG_OUTX_H_A);
// // 	PlatformDelayMs(20);
// // 	reg = LSM6DSO32_RegRead	( gyro, LSM6DSO32_REG_STATUS_MASTER_MAINPAGE );
// // 	PlatformDelayMs(20);
// // 	LSM6DSO32_RegWrite  ( gyro, LSM6DSO32_REG_FUNC_CFG_ACCESS , 0x40);
// // 	reg = LSM6DSO32_RegRead	( gyro,	LSM6DSO32_REG_STATUS_MASTER);
// // //	LSM6DSO32_RegWrite  ( gyro, LSM6DSO32_REG_SLV0_CONFIG  , 0x8);
// // 	PlatformDelayMs(1);
// // 	reg = LSM6DSO32_RegRead	( gyro, LSM6DSO32_SENSOR_HUB_1);
// // 	LSM6DSO32_RegWrite  ( gyro, LSM6DSO32_REG_FUNC_CFG_ACCESS , 0x0);


// // 	reg = LSM6DSO32_SENSOR_HUB_Get(gyro, 0);
// // 	return gyro;
// }

// uint8_t			LSM6DSO32_RegRead	( LSM6DSO32_t * Instance, uint8_t  Address)
// {
// 	// uint8_t buffer[3];
// 	// HAL_IIC_Transaction_Pooled((HAL_IIC_DEVICE_t *)Instance->device, &Address, 1, (char*)&buffer ,2);
// 	// return buffer[0];
// }
// ErrCode_t		LSM6DSO32_RegWrite  ( LSM6DSO32_t * Instance, uint8_t  Address, uint8_t  data)
// {
// 	// uint8_t buffer[3];
// 	// buffer[0] = Address;
// 	// buffer[1] = data;
// 	// buffer[2] = data;
// 	// return HAL_IIC_Transaction_Pooled((HAL_IIC_DEVICE_t *)Instance->device, buffer, 3, buffer ,0);
// }



// uint8_t		LSM6DSO32_SENSOR_HUB_Get(LSM6DSO32_t * Instance, uint8_t index)
// {
// 	// uint8_t reg;


// 	// LSM6DSO32_RegWrite  ( Instance, LSM6DSO32_REG_FUNC_CFG_ACCESS , 0x40);
// 	// reg = LSM6DSO32_RegRead	( Instance,	LSM6DSO32_REG_STATUS_MASTER);
// 	// reg = LSM6DSO32_RegRead	( Instance, LSM6DSO32_SENSOR_HUB_1 + index);
// 	// LSM6DSO32_RegWrite  ( Instance, LSM6DSO32_REG_FUNC_CFG_ACCESS , 0x0);

// 	// return reg;
// }

// ErrCode_t		LSM6DSO32_Selftest  ( LSM6DSO32_t * Instance, uint8_t * id)
// {
// 	// uint8_t reg;
// 	// reg = LSM6DSO32_RegRead	( Instance, LSM6DSO32_REG_WHO_AM_I);

// 	// if(id)*id = reg;

// 	// if(reg == 0x6c)
// 	// 	return ErrOK;
// 	// return ErrError;
// }


// float		LSM6DSO32_Temperature ( LSM6DSO32_t * Instance)
// {
// 	// int16_t reg;
// 	// reg = LSM6DSO32_RegRead	( Instance, LSM6DSO32_REG_OUT_TEMP_L);
// 	// reg |= LSM6DSO32_RegRead	( Instance, LSM6DSO32_REG_OUT_TEMP_H) << 8;
// 	// volatile float temperature = ( reg / 256.0) + 25.0;
// 	// return temperature;
// }



#ifndef __HARDWARE_H__
#define __HARDWARE_H__

// TODO Ian: Track down references to non-existent pins and remove

enum {
    // Power
    PWR_SOL                = PA_0,      // input
    PWR_SHDN               = PB_1,      // output (I/O symbol in schematic)
    PWR_FAULT              = PA_1,      // input
    PWR_CHRG               = PA_4,      // input

    // GNSS
    GNS_EN                 = PB_13,     // output   - Drive VCC
    GNS_Vbckp              = PB_8,      // output   - Powers backup register domain, must be on at startup
    GNS_RESET              = PB_14,     // output   - Active low
    GNS_PPS                = PB_12,     // input
    GNS_TX                 = PB_11,     // LPUART1_TX, AF8 (rx/tx swapped in schematic)
    GNS_RX                 = PA_3_ALT0, // LPUART1_RX, AF8 (rx/tx swapped in schematic)
    // See PeripheralPins.c for alternate pin definitions where needed

    // Modem (GSM)
    MDM_EN                 = PA_12,     // output
    MDM_PKEY               = PA_11,     // output
    MDM_TX                 = PA_9,      // USART1_TX, AF7 (rx/tx swapped in schematic)
    MDM_RX                 = PA_10,     // USART1_RX, AF7 (rx/tx swapped in schematic)
    MDM_NFC_FD             = PA_2,      // input    //! break from schematic name for clarity

    // IMU
    IMU_POWER              = PB_5,      // output
    IMU_INT                = PC_13,     // input
    IMU_SCL                = PB_6,      // I2C1_SCL, AF4
    IMU_SDA                = PB_7,      // I2C1_SDA, AF4

    // Flash
    WINBOND_SCK           = PA_5,      // SPI1_SCK, AF5
    WINBOND_MISO          = PA_6,      // SPI1_MISO, AF5
    WINBOND_MOSI          = PA_7,      // SPI1_MOSI, AF5
    WINBOND_CS            = PB_0,      // output (I/O symbol in schematic)
    WINBOND_WP            = PB_2,      // output (I/O symbol in schematic)

    // Debug / Misc
    MCO_OUT                = PA_8,      // MCO (test point only)
    MCURxD                 = PA_15,     // USART2_RX, AF3
    MCUTxD                 = PB_10      // USART3_TX, AF7
};

// Being a curmudgeon about pin names matching schematic
#define CONSOLE_TX   MCUTxD
#define CONSOLE_RX   MCURxD

#endif // _HARDWAERE_H

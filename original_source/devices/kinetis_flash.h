#ifndef __KINETIS_FLASH_H__
#define __KINETIS_FLASH_H__

enum {
        KINETIS_SECTOR_SIZE = 1024
};

flash_result_t kinetis_flash_erase_sector(uint32_t address);
flash_result_t kinetis_flash_verify_erase(uint32_t address, int length);
flash_result_t kinetis_flash_program_word(uint32_t address, void *data);
flash_result_t kinetis_flash_program_sector(uint32_t address, void *data);
flash_result_t kinetis_flash_write(uint32_t address, void *data, int bytes);
flash_result_t kinetis_flash_read(uint32_t address, void *data, int bytes);

#endif

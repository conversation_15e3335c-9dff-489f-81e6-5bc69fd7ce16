#ifndef MAG_IIS2MDC_H
#define MAG_IIS2MDC_H

// TODO Ian: find imu driver implementation

//! Note these definitions came from petr's code
/*
#define REG_IIS2MDC_ADDR_OFFSET_X_REG_L  (0x45)
#define REG_IIS2MDC_ADDR_OFFSET_X_REG_H  (0x46)
#define REG_IIS2MDC_ADDR_OFFSET_Y_REG_L  (0x47)
#define REG_IIS2MDC_ADDR_OFFSET_Y_REG_H  (0x48)
#define REG_IIS2MDC_ADDR_OFFSET_Z_REG_L  (0x49)
#define REG_IIS2MDC_ADDR_OFFSET_Z_REG_H  (0x4A)

#define REG_IIS2MDC_ADDR_CFG_REG_A  	 (0x60)
#define REG_IIS2MDC_ADDR_CFG_REG_B  	 (0x61)
#define REG_IIS2MDC_ADDR_CFG_REG_C  	 (0x62)
#define REG_IIS2MDC_ADDR_INT_CRTL_REG    (0x63)
#define REG_IIS2MDC_ADDR_INT_SOURCE_REG  (0x64)
#define REG_IIS2MDC_ADDR_INT_THS_L_REG   (0x65)
#define REG_IIS2MDC_ADDR_INT_THS_H_REG   (0x66)
#define REG_IIS2MDC_ADDR_STATUS_REG   	 (0x67)
#define REG_IIS2MDC_ADDR_OUTX_L_REG  	 (0x68)
#define REG_IIS2MDC_ADDR_OUTX_H_REG 	 (0x69)
#define REG_IIS2MDC_ADDR_OUTY_L_REG      (0x6A)
#define REG_IIS2MDC_ADDR_OUTY_H_REG 	 (0x6B)
#define REG_IIS2MDC_ADDR_OUTZ_L_REG  	 (0x6C)
#define REG_IIS2MDC_ADDR_OUTZ_H_REG  	 (0x6D)
*/
#define REG_IIS2MDC_ADDR_WHO_AM_I  		 (0x4F)
#define REG_IIS2MDC_ADDR_TEMP_OUT_L_REG  (0x6E)
#define REG_IIS2MDC_ADDR_TEMP_OUT_H_REG  (0x6F)

#endif /* MAG_IIS2MDC_H */
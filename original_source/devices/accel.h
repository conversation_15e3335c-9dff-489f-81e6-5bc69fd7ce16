#ifndef	__ACCEL_H__
#define	__ACCEL_H__

#include "pelagic.h"

typedef struct {
        int16_t	x, y, z;
} axis_t;

bool accel_init(void);
void accel_pin_setup(void);

void accel_power_on(void);
void accel_power_off(void);
bool accel_read_data(axis_t *data);

void accel_irq_enable(void);

void mag_power_on(void);
void mag_power_off(void);
bool mag_read_data(axis_t *data);

bool accel_should_stay_in_nap_mode();
void log_accel_deviations(event_t evt, uint16_t stationary_time);
bool accel_upsidedown(void);

extern osThreadId accel_tid;

extern volatile int16_t heading_current, device_raw_heading;
extern volatile int16_t device_pitch, device_roll;
extern volatile axis_t accel_current, mag_current;

extern volatile bool device_capsized;
extern bool enable_park;

#endif	/* __ACCEL_H__ */

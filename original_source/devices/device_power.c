#include "pelagic.h"
#include "device_power.h"
#include "gpio_api.h"
#include "pinmap.h"
//
// This is mostly a hack for the current V1 boards
//

#ifdef HAVE_PREV1
gpio_t winbond_power_pin;
gpio_t accel_power_pin;
#else
gpio_t accel_flash_power_pin;
#endif

extern gpio_t winbond_cs_pin, lsm303d_cs_pin;
uint32_t power_bitmask = 0;

void
device_power_init()
{
        // setup SPI1 - winbond & accelerometer
#ifdef HAVE_WINBOND     //! PORT Ian: not using ext flash
        spi_init(&spi1_bus,  SPI1_MOSI, SPI1_MISO, SPI1_SCLK, NC);
        spi_format(&spi1_bus, 8, 0, 0);
        spi_frequency(&spi1_bus, 10000000);
        spi_power_off(&spi1_bus);
#endif

#ifdef HAVE_V1
        // gpio_init_out(&lsm303d_cs_pin, IMU_CS);
        // gpio_init_out(&winbond_cs_pin, WINBOND_CS);
// TODO Ian: Set up new IMU init -- new IMU uses I2C, Int, power switch
//! PORT: Probably moderate changes required here to get IMU running

#ifdef HAVE_PREV1
        gpio_init_out_ex(&winbond_power_pin, WINBOND_WP, 0);    //! PORT: verify WP == power
        gpio_init_out_ex(&accel_power_pin, IMU_POWER, 0);
#else
        // gpio_init_out_ex(&accel_flash_power_pin, ACCEL_FLASH_PWR_SWITCH, 0);
#endif
        // gpio_write(&lsm303d_cs_pin, 0);
        // gpio_write(&winbond_cs_pin, 0);
#endif
}

void
device_power_all_off()
{
        device_power_off(POWER_ACCEL);
        device_power_off(POWER_WINBOND);
}

void
device_power_off(int device)
{
        power_bitmask &= ~(1 << device);

        if (power_bitmask == 0) {
        // TODO Ian: Update for STM power mgmt w/ new IMU etc.

//                 spi_power_off(&spi1_bus);
// #ifdef HAVE_V1
//                 gpio_write(&winbond_cs_pin, 0);
//                 gpio_write(&lsm303d_cs_pin, 0);
// #ifdef HAVE_PREV1
//                 gpio_write(&winbond_power_pin, 0);
//                 gpio_write(&accel_power_pin, 0);
// #else
//                 gpio_write(&accel_flash_power_pin, 0);
// #endif
// #endif

        }
}

void
device_power_on(int device)
{
        uint32_t prev = power_bitmask;

        power_bitmask |= (1 << device);

        if (prev)
                return;


        // spi_power_on(&spi1_bus);

// #ifdef HAVE_V1
//         pin_function(IMU_CS, 1);
//         pin_function(WINBOND_CS, 1);

//         gpio_write(&winbond_cs_pin, 1);
//         gpio_write(&lsm303d_cs_pin, 1);

// #ifdef HAVE_PREV1
//         gpio_write(&winbond_power_pin, 1);
//         gpio_write(&accel_power_pin, 1);
// #else
//         gpio_write(&accel_flash_power_pin, 1);
// #endif
// #endif
}

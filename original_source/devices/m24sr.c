#include "pelagic.h"
#include "hardware.h"
#include "i2c_api.h"
#include "gpio_api.h"
#include "factory_test.h"
#include "nfc.h"

enum {
        SELECT_FILE      = 0xa4,
        READBIN          = 0xb0,
        UPDATE_BINARY    = 0xd6,
        READ_BINARY      = 0xb0,
        VERIFY       = 0x20,
        GETI2CSESSION    = 0x26,
};

static uint8_t ndef_getI2Csession_cmd[] = {
        0x26,
};

/* sec 5.6.3 */
static uint8_t  ndef_selectFile_ndef_app_cmd[] = {
        0x02, 0x00, SELECT_FILE, 0x04, 0x00, 0x07,
        0xd2, 0x76, 0x00, 0x00, 0x85, 0x01, 0x01,
        0x00
};

/* sec 5.6.5 */
static uint8_t  ndef_selectFile_ndef_file_cmd[] = {
        0x02, 0x00, SELECT_FILE, 0x00, 0x0c, 0x02,
        0x00, 0x01, // NDEF file
};

/* sec 5.6.8 */
static uint8_t  ndef_updateBinary_ndeflen_cmd[] = {
        0x02, 0x00, UPDATE_BINARY, 0x00, 0x00, 0x02,
        // need <u16 len> here
};

/* sec 5.6.7 */
static uint8_t  ndef_readBinary_msg_cmd[] = {
        0x02, 0x00, READ_BINARY,
        0x00, 0x00,  // offset 0
        /* need to add add <u8 len> */
};

/* sec 5.6.8 */
static uint8_t  ndef_updateBinary_msg_cmd[] = {
        0x02, 0x00, UPDATE_BINARY,
        0x00, 0x02, // offset 2
        /* need to add add <u8 len>, <data> */
};

/* sec 5.4 */
static uint8_t ndef_deselect_cmd[] = {
        0xc2,
};

static char sample[] = {
        '\xd1', // NFS header, WKT, short, begin, end
        '\x01', // type is 1 byte
        '\x12', // payload is 18 bytes
        '\x54', // text record
        '\x02',
        'e', 'n',
        // 15 ascii chars here
};

static i2c_t i2c_bus;

static uint16_t ComputeCrc14443(const uint8_t *Data, int Length);

static bool
m24sr_write(const uint8_t *data, int size)
{
        int bytes;
        uint16_t crc = ComputeCrc14443(data, size);

        osDelay(200);
        bytes = i2c_write(&i2c_bus, NFC_I2C_ADDR, (char *)data, size, 0);

        if (bytes == size) {
                i2c_byte_write(&i2c_bus, crc & 0xff);
                i2c_byte_write(&i2c_bus, (crc >> 8) & 0xff);
                i2c_stop(&i2c_bus);

                uart_printf("m24sr_write: ");
                for (int i = 0; i < bytes; i++)
                        uart_printf("%02x", data[i]);
                uart_printf(" %02x%02x\n", crc & 0xff, (crc >> 8));

                return true;
        }

        uart_printf("m24sr_write: ");
        if (bytes == I2C_ERROR_BUS_BUSY) {
                uart_printf("bus busy\n");
        } else if (bytes == I2C_ERROR_NO_SLAVE) {
                uart_printf("no slave (%x)\n", NFC_I2C_ADDR);
        } else {
                uart_printf("short, bytes=%i, cmd=0x%x sz=%d\n", bytes, data[0], size);
        }
        return false;
}

static bool
m24sr_read(uint8_t *data, int size)
{
        int bytes = i2c_read(&i2c_bus, NFC_I2C_ADDR, (char *)data, size, 1);

        uart_printf("m24sr_read: ");
        for (int i = 0; i < bytes; i++) {
                if (i == 1 || (bytes - i) == 2) uart_printf(" ");
                uart_printf("%02x", data[i]);
        }
        if (bytes > 2) {
                uint16_t crc = ComputeCrc14443(data, bytes - 2);
                if (data[bytes-2] == (crc & 0xff) &&
                    data[bytes-1] == (crc >> 8)) {
                        uart_printf(" good");
                } else {
                        uart_printf(" BAD");
                }
        }
        uart_printf("\n");

        if (bytes == size)
                return true;

        uart_printf("m24sr_read: ");
        if (bytes == I2C_ERROR_BUS_BUSY) {
                uart_printf("bus busy\n");
        } else if (bytes == I2C_ERROR_NO_SLAVE) {
                uart_printf("no slave (%x)\n", NFC_I2C_ADDR);
        } else {
                uart_printf("short %d of %d\n", bytes, size);
        }
        return false;
}

static bool
m24sr_write_ndeflen(int len)
{
        uint8_t data[16];
        int i;

        i = sizeof(ndef_updateBinary_ndeflen_cmd);
        memcpy(data, ndef_updateBinary_ndeflen_cmd, i);
        data[i++] = (len >> 8) & 0xff;
        data[i++] = (len & 0xff);

        if (!m24sr_write(data, i)) return false;
        osDelay(100);
        if (!m24sr_read(data, 2+3)) return false;
        osDelay(100);

        return true;
}

static bool
m24sr_setup(char *msg)
{
        uint8_t reply[64];
        uint8_t data[64];
        gpio_t power_pin;
        static bool m24sr_pin_initted = false;

        if (m24sr_pin_initted)
                return true;

        uart_printf("Attempting m24sr_setup\n");

        gpio_init_out_ex(&power_pin, PTB19, 0);
        gpio_write(&power_pin, 1);
        osDelay(100);

        i2c_init(&i2c_bus, IMU_SDA, IMU_SCL);

        i2c_power_on(&i2c_bus);
        osDelay(200);

        if (!i2c_write(&i2c_bus, NFC_I2C_ADDR, (char *)ndef_getI2Csession_cmd, sizeof(ndef_getI2Csession_cmd), true)) {
                goto out;
        }

        // select NDEF App
        if (!m24sr_write(ndef_selectFile_ndef_app_cmd, sizeof(ndef_selectFile_ndef_app_cmd))) {
                goto out;
        }
        // read 2+3 bytes
        if (!m24sr_read(reply, 2+3)) {
                goto out;
        }

        if (!m24sr_write(ndef_selectFile_ndef_file_cmd, sizeof(ndef_selectFile_ndef_file_cmd))) {
                goto out;
        }
        if (!m24sr_read(reply, 2+3)) {
                goto out;
        }
        if (!m24sr_write_ndeflen(0)) {
                goto out;
        }

        int len1 = sizeof(ndef_updateBinary_msg_cmd);
        int len2 = sizeof(sample);
        int len3 = strlen(msg);

        //XXX assert(len1+len2+1 < sizeof(data));
        memcpy(data, ndef_updateBinary_msg_cmd, len1);
        data[len1] = (len2+len3) & 0xff;
        memcpy(data+len1+1, sample, len2);

        memcpy(data+len1+1+len2, msg, len3);
        len2 += len3;

        osDelay(100);
        if (!m24sr_write(data, len1+len2+1)) {
                goto out;
        }

        osDelay(100);
        if (!m24sr_read(reply, 2+2)) {
                goto out;
        }

        osDelay(20);
        if (!m24sr_write(reply, 2)) {
                goto out;
        }

        osDelay(20);
        if (!m24sr_read(reply, 2+3)) {
                goto out;
        }

        if (!m24sr_write_ndeflen(len2)) {
                goto out;
        }

        len1 = sizeof(ndef_readBinary_msg_cmd);
        memcpy(data, ndef_readBinary_msg_cmd, sizeof(ndef_readBinary_msg_cmd));
        data[len1] = len2+2;
        if (!m24sr_write(data, len1+1)) {
                goto out;
        }

        osDelay(100);
        if (!m24sr_read(reply, 3+2+len2+2)) {
                goto out;
        }

        osDelay(20);
        if (!m24sr_write(ndef_deselect_cmd, sizeof(ndef_deselect_cmd))) {
                goto out;
        }
        if (!m24sr_read(reply, 2+1)) {
                goto out;
        }

        m24sr_pin_initted = true;

out:
        i2c_power_off(&i2c_bus);
        gpio_write(&power_pin, 0);
        if (!m24sr_pin_initted)
                uart_printf("m24sr: setup failed\n");

        return m24sr_pin_initted;
}



bool
m24sr_have_chip(void)
{
#if 0
        uint8_t serial2_cmd[] = { M24SR_READ_SERIAL2_0, M24SR_READ_SERIAL2_1 };
        uint8_t serial_id[4];

        if (m24sr_write(serial2_cmd, sizeof(serial2_cmd), false) == false)
                return false;

        if (m24sr_read(serial_id, sizeof(serial_id)) == false)
                return false;

        if (serial_id[0] != M24SR_CHIP_ID && serial_id[0] != SI7021_CHIP_ID)
                return false;
#endif
        return true;
}

bool
nfc_init(char *msg)
{
        if (!m24sr_have_chip()) {
                EVENT_LOG1(EVT_NFC_NOT_PRESENT, "not present", "chip", EVT_STRCONST, "m24sr");
                return false;
        }

        while (!m24sr_setup(msg)) {
                osDelay(1000);
        }


        EVENT_LOG(EVT_NFC_INIT, "m24sr init");
        return true;
}

#if 0

#ifdef HAVE_FACTORY_TEST
ft_result_t
nfc_factory_test(void)
{
        uint16_t value;

        ft_update("m24sr: start");
        m24sr_pin_setup();
        m24sr_power_on();

        if (m24sr_reset() == false) {
                ft_update("m24sr: no response");
                m24sr_power_off();
                return FT_NFC_UNREPONSIVE;
        }

        if (m24sr_have_chip() == false) {
                ft_update("m24sr: chip not present");
                m24sr_power_off();
                return FT_NFC_UNREPONSIVE;
        }

        ft_update("m24sr: chip present.");

        value = m24sr_nfc_read();

        m24sr_power_off();

        ft_device_result("m24sr nfc %d", value);

        if (value == 0 || value > NFC_TEST_LIMIT) {
                ft_update("m24sr: value error read=[%d]", value);
                return FT_NFC_FAIL;
        }

        ft_update("m24sr: success");
        return FT_SUCCESS;
}
#endif

#endif

//-----------------------------------------------------------------------------
// Routines to compute the ISO 14443 CRCs (ITU-V.41)
//-----------------------------------------------------------------------------

static uint16_t
UpdateCrc14443(uint8_t ch, uint16_t *lpwCrc)
{
        ch = (ch ^ (uint8_t) ((*lpwCrc) & 0x00FF));
        ch = (ch ^ (ch << 4));
        *lpwCrc =   (*lpwCrc >> 8) ^ ((uint16_t) ch << 8) ^
                    ((uint16_t) ch << 3) ^ ((uint16_t) ch >> 4);
        return (*lpwCrc);
}

static uint16_t
ComputeCrc14443(const uint8_t *Data, int Length)
{
        uint8_t chBlock;
        uint16_t wCrc=0x6363;

        do {
                chBlock = *Data++;
                UpdateCrc14443(chBlock, &wCrc);
        } while (--Length);

        return wCrc;        /* transmit low byte first! */
}

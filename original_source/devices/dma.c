// #include "pelagic.h"
// #include "kinetis.h"
// #include "dma.h"

// static int dma_initted = 0;

// static struct dma_isr_handler {
//         dma_irq_handler_t   handler;
//         uint32_t    param;
// } dma_irq_handlers[4];

// void
// dma_init()
// {
//         if (dma_initted)
//                 return;

//         // Enable clock gating for the DMA and DMA MUX
//         SIM->SCGC6 |= SIM_SCGC6_DMAMUX_MASK;
//         SIM->SCGC7 |= SIM_SCGC7_DMA_MASK;

//         dma_initted = 1;
// }


// void
// dma_register_handler(IRQn_Type channel, dma_irq_handler_t handler, uint32_t param)
// {
//         dma_init();

//         dma_irq_handlers[channel].handler = handler;
//         dma_irq_handlers[channel].param = param;

//         NVIC_SetPriority(channel + DMA0_IRQn, 2);
//         NVIC_EnableIRQ(channel + DMA0_IRQn);
// }

// void
// DMA0_IRQHandler()
// {
//         dma_irq_handlers[0].handler(dma_irq_handlers[0].param);
// }

// void
// DMA1_IRQHandler()
// {
//         dma_irq_handlers[1].handler(dma_irq_handlers[1].param);
// }

// void
// DMA2_IRQHandler()
// {
//         dma_irq_handlers[2].handler(dma_irq_handlers[2].param);
// }

// void
// DMA3_IRQHandler()
// {
//         dma_irq_handlers[3].handler(dma_irq_handlers[3].param);
// }

#include "mag_iis2mdc.h"
#include "accel.h"

// TODO <PERSON>: translate this shit to implement mag interface
void mag_power_on(void) { /* TODO */ }
void mag_power_off(void) { /* TODO */ }
bool mag_read_data(axis_t *data) { /* TODO */ }

// TODO Ian: are these needed or is the device so simple?
// bool mag_init(void) { /* TODO */ }
// void mag_pin_setup(void) { /* TODO */ }

//! Note this implementation came from petr's code

// IIS2MDC_t * 	IIS2MDC_Init(uint8_t IIC_Controller, uint8_t slave_adderess, GPIO_PIN power)
// {
// 	IIS2MDC_t * magnet 					= (IIS2MDC_t *)sysmalloc(sizeof(IIS2MDC_t));
// 	magnet->device 						= sysmalloc(sizeof(HAL_IIC_DEVICE_t ));
// 	magnet->device->Config 	   			= IIC_DEVICE(IIC_NO_USE_EXPANDER,0,IIC_CLOCK_MODE_100KHZ,IIC_DEVICE_ADDRESS__MODE_7BIT, slave_adderess << 1);
// 	magnet->device->Controller 			= (uint8_t)IIC_Controller;
// 	magnet->power = power;


// 	IIS2MDC_SelfTrest	(magnet);

// 	return magnet;
// }

// ErrCode_t		IIS2MDC_SelfTrest	( IIS2MDC_t * Instance)
// {
// 	uint8_t err = 0;
// 	uint8_t Reg = IIS2MDC_RegRead	( Instance, REG_IIS2MDC_ADDR_WHO_AM_I);
// 	if( Reg != 0x40)
// 		err |= 1;

// 	if(Reg == 0x40)
// 	{
// 		printf("Test ok\n");
// 	}

// 	return (err != 0) ? ErrOK : ErrError;
// }

// uint8_t			IIS2MDC_RegRead	( IIS2MDC_t * Instance, uint8_t  Address)
// {
// 	uint8_t buffer[3];
// 	HAL_IIC_Transaction_Pooled((HAL_IIC_DEVICE_t *)Instance->device, &Address, 1, buffer ,1);
// 	return buffer[0];
// }
// ErrCode_t		IIS2MDC_RegWrite  ( IIS2MDC_t * Instance, uint8_t  Address, uint8_t  data)
// {
// 	uint8_t buffer[3];
// 	buffer[0] = Address;
// 	buffer[1] = 1;
// 	buffer[2] = data;
// 	return HAL_IIC_Transaction_Pooled((HAL_IIC_DEVICE_t *)Instance->device, buffer, 3, buffer ,0);
// }

// #define IIS2MDC_REG_WHO_AM_I 0x4f

// ErrCode_t		IIS2MDC_Selftest	( IIS2MDC_t * Instance, uint8_t * chip_id)
// {
// 	uint8_t reg;
// 	reg = IIS2MDC_RegRead	( Instance,IIS2MDC_REG_WHO_AM_I);

// 	if(chip_id)*chip_id = reg;

// 	return ErrOK;
// }


// float		IIS2MDC_Temperature_inC	( IIS2MDC_t * Instance)
// {
// 	uint16_t regl, regh;
// 	regl = IIS2MDC_RegRead	( Instance,REG_IIS2MDC_ADDR_TEMP_OUT_L_REG);
// 	regh = IIS2MDC_RegRead	( Instance,REG_IIS2MDC_ADDR_TEMP_OUT_H_REG);
// 	int16_t reg = regh << 8 | regl;

// 	printf("Temp 0x%04x\n",reg);

// 	return ErrOK;
// }

#include "pelagic.h"
#include "kinetis.h"
#include "kinetis_flash.h"

#define FLASH_CMD_READ1S            0x01        // Verify a given address is all 1s
#define FLASH_CMD_PROGRAM_CHECK     0x02        // Check to see if address is within margin read levels
#define FLASH_CMD_READ_RESOURCE     0x03        // Read flash IFR or version ID
#define FLASH_CMD_PROGRAM_LONG_WORD 0x06        // Program 4-bytes
#define FLASH_CMD_ERASE_SECTOR      0x09        // Erase (set all 1s) a sector
#define FLASH_CMD_READ1S_BLOCK      0x40        // Verify flash block is erased and then release security
#define FLASH_CMD_READ_ONCE         0x41        // Read 4 bytes of 64 byte field
#define FLASH_CMD_PROGRAM_ONCE      0x43        // Program 4 bytes of 64 byte field
#define FLASH_CMD_ERASE_ALL         0x44        // Erase all the blocks
#define FLASH_CMD_VERIFY_BACKDOOR   0x45        // Release MCU security if backdoor access matches


#define FLASH_LOAD_ADDRESS(addr)  { FTFA->FCCOB1 = (addr >> 16) & 0xff;  FTFA->FCCOB2 = (addr >> 8) & 0xff;  FTFA->FCCOB3 = addr & 0xff; }


SRAM_TEXT_SECTION
static flash_result_t
kinetis_flash_run_command()
{
        uint32_t status;
        // There cannot be any interrupts while the flash chip is running

        // Clear out any errors
        FTFA->FSTAT = FTFA_FSTAT_FPVIOL_MASK | FTFA_FSTAT_ACCERR_MASK | FTFA_FSTAT_RDCOLERR_MASK;
        FTFA->FSTAT = FTFA_FSTAT_CCIF_MASK;

        while ((FTFA->FSTAT & FTFA_FSTAT_CCIF_MASK) == 0)
                ;

        status = FTFA->FSTAT;

        if (status & FTFA_FSTAT_FPVIOL_MASK)
                return FLASH_ERR_PROTECTED;

        if (status & FTFA_FSTAT_ACCERR_MASK)
                return FLASH_ERR_ACCESS;

        if (status & FTFA_FSTAT_RDCOLERR_MASK)
                return FLASH_ERR_COLLISION;

        if (status & FTFA_FSTAT_MGSTAT0_MASK)
                return FLASH_ERR_RUNTIME;

        return FLASH_SUCCESS;
}

flash_result_t flash_erase_last_err = 0;

SRAM_TEXT_SECTION
flash_result_t
kinetis_flash_erase_sector(uint32_t address)
{
        int         result;

        if (address & (KINETIS_SECTOR_SIZE-1))
                return FLASH_ERR_ALIGNMENT;


        FTFA->FCCOB0 = FLASH_CMD_ERASE_SECTOR;
        FLASH_LOAD_ADDRESS(address);

        result = kinetis_flash_run_command();

        if (result != FLASH_SUCCESS) {
                flash_erase_last_err = result;
                return result;
        }

        return kinetis_flash_verify_erase(address, KINETIS_SECTOR_SIZE);
}

SRAM_TEXT_SECTION
flash_result_t
kinetis_flash_verify_erase(uint32_t address, int length)
{
        int result;

        if ((address & 0x3) || (length & 0x3)) {
                return FLASH_ERR_ALIGNMENT;
        }

        FTFA->FCCOB0 = FLASH_CMD_READ1S;
        FLASH_LOAD_ADDRESS(address);

        FTFA->FCCOB4 = (length >> 10) & 0xff;
        FTFA->FCCOB5 = (length >> 2) & 0xff;
        FTFA->FCCOB6 = 0;

        result = kinetis_flash_run_command();

        return result;
}

SRAM_TEXT_SECTION
flash_result_t
kinetis_flash_program_word(uint32_t address, void *param)
{
        uint8_t *data = (uint8_t *) param;
        int result;

        if (address & 0x3) {
                return FLASH_ERR_ALIGNMENT;
        }

        FTFA->FCCOB0 = FLASH_CMD_PROGRAM_LONG_WORD;
        FLASH_LOAD_ADDRESS(address);

        FTFA->FCCOB4 = data[3];
        FTFA->FCCOB5 = data[2];
        FTFA->FCCOB6 = data[1];
        FTFA->FCCOB7 = data[0];

        result =  kinetis_flash_run_command();

        return result;
}

SRAM_TEXT_SECTION
uint32_t
kinetis_flash_get_size()
{
        uint32_t bytes = (SIM->FCFG2 & SIM_FCFG2_MAXADDR0_MASK) >> 11;

        // Check for a second bank..
        if (SIM->FCFG2 & (1<<23))
                bytes += (SIM->FCFG2 & SIM_FCFG2_MAXADDR1_MASK) >> 3;

        return bytes;
}

flash_result_t
kinetis_flash_read(uint32_t address, void *data, int bytes)
{
        memcpy(data, (void *)address, bytes);
        return FLASH_SUCCESS;
}

SRAM_TEXT_SECTION // routine is used by firmware update
flash_result_t
kinetis_flash_write(uint32_t address, void *param, int bytes)
{
        int result;
        uint8_t *data = (uint8_t *) param;

        while (bytes > 0) {
                result = kinetis_flash_program_word(address, data);

                if (result != FLASH_SUCCESS)
                        return result;

                address += 4;
                data +=4;
                bytes -= 4;
        }

        return FLASH_SUCCESS;
}

void
kinetis_flash_keep_on(bool keep_on)
{
        return;
}

flash_device_t kinetis_flash_device = {
        .read             = kinetis_flash_read,
        .write            = kinetis_flash_write,
        .erase            = kinetis_flash_erase_sector,
        .keep_on          = kinetis_flash_keep_on,
        .page_size        = FLASH_PAGE_SIZE,
        .sector_size      = KINETIS_SECTOR_SIZE,
        .pages_per_sector = KINETIS_SECTOR_SIZE / FLASH_PAGE_SIZE
};

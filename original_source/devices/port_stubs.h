#ifndef PORT_STUBS_H
#define PORT_STUBS_H

#include <stdint.h>
#include "flash_store.h"

// TODO Ian: Implement later
uint32_t clock_read();  // gps_thread.c
void rtc_save();        // helm.c
void rtc_reset_time();  // provision.c

extern volatile uint32_t rtc_clock, epoch_clock;


// DMA section is custom I think :(
// dma.c/h
// void
// dma_init();

// void
// dma_register_handler(IRQn_Type channel, dma_irq_handler_t handler, uint32_t param);

// void
// DMA0_IRQHandler();

// void
// DMA1_IRQHandler();

// void
// DMA2_IRQHandler();

// void
// DMA3_IRQHandler();


void core_util_critical_section_exit();
void core_util_critical_section_enter();


flash_result_t stm_flash_write(uint32_t address, void *data, int bytes);
flash_result_t stm_flash_read(uint32_t address, void *data, int size);
flash_result_t stm_flash_erase_sector(uint32_t address);

// Timing functions that work without HAL_GetTick
//! PORT Ian: Reliable delay function that doesn't depend on HAL_GetTick() working
//! Uses existing wait_us() API which works with us_ticker_read() directly
void port_delay_ms(uint32_t milliseconds);

#endif

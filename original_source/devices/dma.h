// #ifndef __DMA_H__
// #define __DMA_H__

// //
// // See the KL16 reference manual DMA request sources p. 69-70
// // Rev 3.2 October 2013

// typedef enum {
//         DMA_MUX_UART0_RX_SLOT = 2,
//         DMA_MUX_UART0_TX_SLOT = 3,
//         DMA_MUX_UART1_RX_SLOT = 4,
//         DMA_MUX_UART1_TX_SLOT = 5,
//         DMA_MUX_UART2_RX_SLOT = 6,
//         DMA_MUX_UART2_TX_SLOT = 7,
//         DMA_MUX_SPI0_RX_SLOT = 16,
//         DMA_MUX_SPI0_TX_SLOT = 17,
//         DMA_MUX_SPI1_RX_SLOT = 18,
//         DMA_MUX_SPI1_TX_SLOT = 19
// } dma_mux_slot_t;

// typedef enum {
// #if defined(TARGET_PLACO)
//         DMA_UART0_TX_CHANNEL = 0,
// #elif defined(TARGET_DEVBOARD)
//         DMA_UART2_TX_CHANNEL = 0,
// #endif
//         DMA_UART1_RX_CHANNEL = 1,
//         DMA_SPI1_TX_CHANNEL = 2,
//         DMA_SPI1_RX_CHANNEL = 3,
// } dma_channel_t;


// typedef void (*dma_irq_handler_t)(uint32_t param);

// void dma_init();
// void dma_register_handler(IRQn_Type channel, dma_irq_handler_t handler, uint32_t param);

// #endif
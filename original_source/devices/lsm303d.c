#include "pelagic.h"
#include "accel.h"
#include "gpio_api.h"
#include "gpio_irq_api.h"
#include "device_power.h"
#include "pinmap.h"
#include "signals.h"
#include "factory_test.h"
#include "temp.h"

enum {
        LSM303D_ACC_FS_MASK = (0x18),
        LSM303D_ACC_FS_2G   = (0x00), /* Full scale 2g */
        LSM303D_ACC_FS_4G   = (0x08), /* Full scale 4g */
        LSM303D_ACC_FS_8G   = (0x10), /* Full scale 8g */
        LSM303D_ACC_FS_16G  = (0x18), /* Full scale 16g */

        /* Accelerometer Anti-Aliasing Filter */
        ANTI_ALIASING_773   = (0X00),
        ANTI_ALIASING_362   = (0X40),
        ANTI_ALIASING_194   = (0X80),
        ANTI_ALIASING_50    = (0XC0),

        /************************************************/
        /*  Magnetometer section defines        */
        /************************************************/

        /* Magnetometer Sensor Full Scale */
        LSM303D_MAG_FS_MASK = (0x60),
        LSM303D_MAG_FS_2G   = (0x00), /* Full scale 2 gauss */
        LSM303D_MAG_FS_4G   = (0x20), /* Full scale 4 gauss */
        LSM303D_MAG_FS_8G   = (0x40), /* Full scale 8 gauss */
        LSM303D_MAG_FS_12G  = (0x60), /* Full scale 12 gauss */

        LSM303D_ACC_MIN_POLL_PERIOD_MS = 1,
        LSM303D_MAG_MIN_POLL_PERIOD_MS = 5,


        ACC_G_MAX_POS   =       1495040,    /** max positive value acc [ug] */
        ACC_G_MAX_NEG   =       1495770,    /** max negative value acc [ug] */
        MAG_G_MAX_POS   =       983520,     /** max positive value mag [ugauss] */
        MAG_G_MAX_NEG   =       983040,     /** max negative value mag [ugauss] */

        FUZZ = 0,
        FLAT = 0,

        /* Address registers */
        REG_WHOAMI_ADDR      = (0x0F), /** Who am i address register */
        REG_CNTRL0_ADDR      = (0x1F), /** CNTRL0 address register */
        REG_CNTRL1_ADDR      = (0x20), /** CNTRL1 address register */
        REG_CNTRL2_ADDR      = (0x21), /** CNTRL2 address register */
        REG_CNTRL3_ADDR      = (0x22), /** CNTRL3 address register */
        REG_CNTRL4_ADDR      = (0x23), /** CNTRL4 address register */
        REG_CNTRL5_ADDR      = (0x24), /** CNTRL5 address register */
        REG_CNTRL6_ADDR      = (0x25), /** CNTRL6 address register */
        REG_CNTRL7_ADDR      = (0x26), /** CNTRL7 address register */

        REG_ACC_DATA_ADDR    = (0x28), /** Acc. data low address register */
        REG_MAG_DATA_ADDR    = (0x08), /** Mag. data low address register */
        REG_TEMP_DATA_ADDR   = (0x05), /** Temp. data low address register */

        REG_GEN_MAG_ADDR     = (0x12), /** INT_CTRL_REG_M address register */
        INT_SRC_REG_M_ADDR   = (0x13), /** INT_SRC_REG_M address register */
        REG_GEN_MAG_THR_ADDR = (0x14), /** INT_THS_L_M address register */
        MIG_THRESHOLD_ADDR_H = (0x15), /** INT_THS_H_M address register */
        REG_GEN1_AXIS_ADDR   = (0x30), /** INT_GEN1_REG address register */
        INT_GEN1_SRC_ADDR    = (0x31), /** INT_GEN1_SRC address register */
        REG_GEN1_THR_ADDR    = (0x32), /** INT_GEN1_THS address register */
        REG_GEN1_DUR_ADDR    = (0x33), /** INT_GEN1_DUR address register */
        REG_GEN2_AXIS_ADDR   = (0x34), /** INT_GEN2_REG address register */
        INT_GEN2_SRC_ADDR    = (0x35), /** INT_GEN2_SRC address register */
        REG_GEN2_THR_ADDR    = (0x36), /** INT_GEN2_THS address register */
        REG_GEN2_DUR_ADDR    = (0x37), /** INT_GEN2_DUR address register */

        /* Sensitivity */
        SENSITIVITY_ACC_2G  = 60, /**   ug/LSB  */
        SENSITIVITY_ACC_4G  = 120, /**  ug/LSB  */
        SENSITIVITY_ACC_8G  = 240, /**  ug/LSB  */
        SENSITIVITY_ACC_16G = 730, /**  ug/LSB  */

        SENSITIVITY_MAG_2G  = 80, /**   ugauss/LSB  */
        SENSITIVITY_MAG_4G  = 160, /**  ugauss/LSB  */
        SENSITIVITY_MAG_8G  = 320, /**  ugauss/LSB  */
        SENSITIVITY_MAG_12G = 480, /**  ugauss/LSB  */

        /* ODR */
        ODR_ACC_MASK         = (0XF0), /* Mask for odr change on acc */
        LSM303D_ACC_ODR_OFF  = (0x00), /* Power down */
        LSM303D_ACC_ODR3_125 = (0x10), /* 3.25Hz output data rate */
        LSM303D_ACC_ODR6_25  = (0x20), /* 6.25Hz output data rate */
        LSM303D_ACC_ODR12_5  = (0x30), /* 12.5Hz output data rate */
        LSM303D_ACC_ODR25    = (0x40), /* 25Hz output data rate */
        LSM303D_ACC_ODR50    = (0x50), /* 50Hz output data rate */
        LSM303D_ACC_ODR100   = (0x60), /* 100Hz output data rate */
        LSM303D_ACC_ODR200   = (0x70), /* 200Hz output data rate */
        LSM303D_ACC_ODR400   = (0x80), /* 400Hz output data rate */
        LSM303D_ACC_ODR800   = (0x90), /* 800Hz output data rate */
        LSM303D_ACC_ODR1600  = (0xA0), /* 1600Hz output data rate */

        ODR_MAG_MASK         = (0X1C), /* Mask for odr change on mag */
        LSM303D_MAG_ODR3_125 = (0x00), /* 3.25Hz output data rate */
        LSM303D_MAG_ODR6_25  = (0x04), /* 6.25Hz output data rate */
        LSM303D_MAG_ODR12_5  = (0x08), /* 12.5Hz output data rate */
        LSM303D_MAG_ODR25    = (0x0C), /* 25Hz output data rate */
        LSM303D_MAG_ODR50    = (0x10), /* 50Hz output data rate */
        LSM303D_MAG_ODR100   = (0x14), /* 100Hz output data rate */

        /* Magnetic sensor mode */
        MSMS_MASK            = (0x03), /* Mask magnetic sensor mode */
        POWEROFF_MAG         = (0x02), /* Power Down */
        CONTINUOS_CONVERSION = (0x00), /* Continuos Conversion */

        /* Default values loaded in probe function */
        WHOIAM_VALUE                        = (0x49), /** Who Am I default value */
        REG_DEF_CNTRL0          = (0x00), /** CNTRL0 default value */
        REG_DEF_CNTRL1          = (0x0F), /** CNTRL1 default value */
        REG_DEF_CNTRL2          = (0x00), /** CNTRL2 default value */
        REG_DEF_CNTRL3          = (0x00), /** CNTRL3 default value */
        REG_DEF_CNTRL4          = (0x00), /** CNTRL4 default value */
        REG_DEF_CNTRL5          = (0x18), /** CNTRL5 default value */
        REG_DEF_CNTRL6          = (0x20), /** CNTRL6 default value */
        REG_DEF_CNTRL7          = (0x02), /** CNTRL7 default value */

        REG_DEF_INT_CNTRL_MAG   = (0x00), /** INT_CTRL_REG_M default value */
        REG_DEF_INT_GEN1        = (0x00), /** INT_GEN1_REG default value */
        REG_DEF_INT_GEN2        = (0x00), /** INT_GEN2_REG default value */
        REG_DEF_IIG1_DURATION   = (0x00), /** INT_GEN1_DUR default value */
        REG_DEF_IIG2_DURATION   = (0x00), /** INT_GEN2_DUR default value */
        REG_DEF_IIG1_THRESHOLD  = (0x00), /** INT_GEN1_THS default value */
        REG_DEF_IIG2_THRESHOLD  = (0x00), /** INT_GEN2_THS default value */
        REG_DEF_MIG_THRESHOLD_L = (0x00), /** INT_THS_L_M default value */
        REG_DEF_MIG_THRESHOLD_H = (0x00), /** INT_THS_H_M default value */

        REG_DEF_ALL_ZEROS       = (0x00),

        /* Accelerometer Filter */
        LSM303D_ACC_FILTER_MASK = (0xC0), /* Mask for filter band change on acc */
        FILTER_773              = 773, /* Anti-Aliasing 773 Hz */
        FILTER_362              = 362, /* Anti-Aliasing 362 Hz */
        FILTER_194              = 194, /* Anti-Aliasing 194 Hz */
        FILTER_50               = 50, /* Anti-Aliasing 50 Hz */

        /* Temperature */
        TEMP_MASK        = (0x80), /* Mask for temperature change */
        TEMP_ON          = (0x80), /* Enable temperature */
        TEMP_OFF         = (0x00), /* Disable temperature */
        TEMP_SENSITIVITY = 8, /* Sensitivity temperature */
        OFFSET_TEMP      = 25, /* Offset temperature */
        NDTEMP           = 1000, /* Not Available temperature */

        /* Interrupt */
        GEN1_PIN1_MASK    = (0x20),
        GEN1_PIN2_MASK    = (0x40),
        GEN2_PIN1_MASK    = (0x10),
        GEN2_PIN2_MASK    = (0x20),
        GEN_MAG_PIN1_MASK = (0x08),
        GEN_MAG_PIN2_MASK = (0x10),
        GEN_MAG_EN_MASK   = (0x01),
        MAX_DUR_TH        = 127,
        MAX_TH_MAG        = 131071,
        GEN_X_HIGH_MASK   = (0x02),
        GEN_X_LOW_MASK    = (0x01),
        GEN_Y_HIGH_MASK   = (0x08),
        GEN_Y_LOW_MASK    = (0x04),
        GEN_Z_HIGH_MASK   = (0x20),
        GEN_Z_LOW_MASK    = (0x10),
        GEN_X_MAG_MASK    = (0x80),
        GEN_Y_MAG_MASK    = (0x40),
        GEN_Z_MAG_MASK    = (0x20),

        GEN1_AND_OR_MASK  = (0x80),
        GEN2_AND_OR_MASK  = (0x83),

        INT_PIN_CONF_MASK = (0x10),
        INT_POLARITY_MASK = (0x80),
};

struct {
        unsigned int cutoff_us;
        uint8_t value;
} lsm303d_acc_odr_table[] = {
        {   1, LSM303D_ACC_ODR800  },
        {   2, LSM303D_ACC_ODR400  },
        {   5, LSM303D_ACC_ODR200  },
        {  10, LSM303D_ACC_ODR100  },
        {  20, LSM303D_ACC_ODR50   },
        {  40, LSM303D_ACC_ODR25   },
        {  80, LSM303D_ACC_ODR12_5 },
        { 160, LSM303D_ACC_ODR6_25 },
        { 320, LSM303D_ACC_ODR3_125},
};

struct {
        unsigned int cutoff_us;
        uint8_t value;
} lsm303d_mag_odr_table[] = {
        {  10, LSM303D_MAG_ODR100  },
        {  20, LSM303D_MAG_ODR50   },
        {  40, LSM303D_MAG_ODR25   },
        {  80, LSM303D_MAG_ODR12_5 },
        { 160, LSM303D_MAG_ODR6_25 },
        { 320, LSM303D_MAG_ODR3_125},
};

struct interrupt_enable {
        uint8_t address;
        uint8_t mask;
};

struct interrupt_value {
        int value;
        uint8_t address;
};

struct lsm303d_interrupt {
        struct interrupt_enable gen1_pin1;
        struct interrupt_enable gen1_pin2;
        struct interrupt_enable gen2_pin1;
        struct interrupt_enable gen2_pin2;
        struct interrupt_value gen1_threshold;
        struct interrupt_value gen2_threshold;
        struct interrupt_value gen1_duration;
        struct interrupt_value gen2_duration;
        struct interrupt_enable gen_mag_pin1;
        struct interrupt_enable gen_mag_pin2;
        struct interrupt_enable gen_mag;
        struct interrupt_value gen_mag_threshold;
        struct interrupt_enable gen1_axis[6];
        struct interrupt_enable gen2_axis[6];
        struct interrupt_enable gen_mag_axis[3];
        struct interrupt_enable gen1_and_or;
        struct interrupt_enable gen2_and_or;
        struct interrupt_enable interrupt_pin_conf;
        struct interrupt_enable interrupt_polarity;
};

struct lsm303d_interrupt lsm303d_int;

uint16_t lsm303d_sensitivity_acc, lsm303d_sensitivity_mag;

struct lsm303d_acc_platform_data {

        unsigned int poll_interval;
        unsigned int min_interval;

        uint8_t fs_range;

        uint8_t aa_filter_bandwidth;

};

const struct lsm303d_acc_platform_data default_lsm303d_acc_pdata = {
        .fs_range = LSM303D_ACC_FS_2G,
        .poll_interval = 100,
        .min_interval = LSM303D_ACC_MIN_POLL_PERIOD_MS,
        .aa_filter_bandwidth = ANTI_ALIASING_773,
};

struct lsm303d_mag_platform_data {
        unsigned int poll_interval;
        unsigned int min_interval;

        uint8_t fs_range;
};


const struct lsm303d_mag_platform_data default_lsm303d_mag_pdata = {
        .poll_interval = 100,
        .min_interval = LSM303D_MAG_MIN_POLL_PERIOD_MS,
        .fs_range = LSM303D_MAG_FS_2G,
};

struct reg_rw {
        uint8_t address;
        uint8_t default_value;
        uint8_t resume_value;
};

struct reg_r {
        uint8_t address;
        uint8_t value;
};

static struct status_registers {
        struct reg_r who_am_i;
        struct reg_rw cntrl0;
        struct reg_rw cntrl1;
        struct reg_rw cntrl2;
        struct reg_rw cntrl3;
        struct reg_rw cntrl4;
        struct reg_rw cntrl5;
        struct reg_rw cntrl6;
        struct reg_rw cntrl7;
        struct reg_rw int_ctrl_reg_m;
        struct reg_rw int_mag_threshold_low;
        struct reg_rw int_mag_threshold_high;
        struct reg_rw int_gen1_reg;
        struct reg_rw int_gen2_reg;
        struct reg_rw int_gen1_duration;
        struct reg_rw int_gen2_duration;
        struct reg_rw int_gen1_threshold;
        struct reg_rw int_gen2_threshold;
        struct reg_r int_src_reg_m;
        struct reg_r int_gen1_src;
        struct reg_r int_gen2_src;
        struct reg_r int_gen_mag_src;
} status_registers = {
        .who_am_i.address=REG_WHOAMI_ADDR, .who_am_i.value=WHOIAM_VALUE,
        .cntrl0.address=REG_CNTRL0_ADDR, .cntrl0.default_value=REG_DEF_CNTRL0,
        .cntrl1.address=REG_CNTRL1_ADDR, .cntrl1.default_value=REG_DEF_CNTRL1,
        .cntrl2.address=REG_CNTRL2_ADDR, .cntrl2.default_value=REG_DEF_CNTRL2,
        .cntrl3.address=REG_CNTRL3_ADDR, .cntrl3.default_value=REG_DEF_CNTRL3,
        .cntrl4.address=REG_CNTRL4_ADDR, .cntrl4.default_value=REG_DEF_CNTRL4,
        .cntrl5.address=REG_CNTRL5_ADDR, .cntrl5.default_value=REG_DEF_CNTRL5,
        .cntrl6.address=REG_CNTRL6_ADDR, .cntrl6.default_value=REG_DEF_CNTRL6,
        .cntrl7.address=REG_CNTRL7_ADDR, .cntrl7.default_value=REG_DEF_CNTRL7,
        .int_ctrl_reg_m.address=REG_GEN_MAG_ADDR,
        .int_ctrl_reg_m.default_value=REG_DEF_INT_CNTRL_MAG,
        .int_mag_threshold_low.address=REG_GEN_MAG_THR_ADDR,
        .int_mag_threshold_low.default_value=REG_DEF_MIG_THRESHOLD_L,
        .int_mag_threshold_low.address=MIG_THRESHOLD_ADDR_H,
        .int_mag_threshold_low.default_value=REG_DEF_MIG_THRESHOLD_H,
        .int_gen1_reg.address=REG_GEN1_AXIS_ADDR,
        .int_gen1_reg.default_value=REG_DEF_INT_GEN1,
        .int_gen2_reg.address=REG_GEN2_AXIS_ADDR,
        .int_gen2_reg.default_value=REG_DEF_INT_GEN2,
        .int_gen1_duration.address=REG_GEN1_DUR_ADDR,
        .int_gen1_duration.default_value=REG_DEF_IIG1_DURATION,
        .int_gen2_duration.address=REG_GEN2_DUR_ADDR,
        .int_gen2_duration.default_value=REG_DEF_IIG2_DURATION,
        .int_gen1_threshold.address=REG_GEN1_THR_ADDR,
        .int_gen1_threshold.default_value=REG_DEF_IIG1_THRESHOLD,
        .int_gen2_threshold.address=REG_GEN2_THR_ADDR,
        .int_gen2_threshold.default_value=REG_DEF_IIG2_THRESHOLD,
        .int_src_reg_m.address = INT_SRC_REG_M_ADDR,
        .int_src_reg_m.value = REG_DEF_ALL_ZEROS,
        .int_gen1_src.address = INT_GEN1_SRC_ADDR,
        .int_gen1_src.value = REG_DEF_ALL_ZEROS,
        .int_gen2_src.address = INT_GEN2_SRC_ADDR,
        .int_gen2_src.value = REG_DEF_ALL_ZEROS,
        .int_gen_mag_src.address = INT_SRC_REG_M_ADDR,
        .int_gen_mag_src.value = REG_DEF_ALL_ZEROS,
};

bool accel_initted = false, accel_pin_initted = false;
// gpio_t lsm303d_cs_pin, lsm303d_int1_pin, lsm303d_int2_pin;
gpio_t lsm303d_scl_pin, lsm303d_sda_pin, lsm303d_int1_pin, lsm303d_int2_pin;
gpio_irq_t lsm303d_int1_irq, lsm303d_int2_irq;

bool lsm303d_initted = false, accel_is_on = false, mag_is_on = false;
void lsm303d_isr1(uintptr_t context, gpio_irq_event event);
void lsm303d_isr2(gpio_irq_event event);

void lsm303d_mag_update_fs_range(uint8_t new_fs_range);
void lsm303d_acc_update_fs_range(uint8_t new_fs_range);

void lsm303d_acc_update_odr(unsigned int poll_interval_ms);
void lsm303d_mag_update_odr(unsigned int poll_interval_ms);

void
lsm303d_read_command(uint8_t cmd, uint8_t *buf, int len)
{
        // spi_lock(&spi1_bus);
        // gpio_write(&lsm303d_cs_pin, 0);
        // spi_master_write(&spi1_bus, cmd | 0x80);
        // spi_master_block_read(&spi1_bus, buf, len);
        // gpio_write(&lsm303d_cs_pin, 1);
        // spi_unlock(&spi1_bus);
        // TODO Ian: Replace with I2C interface
}

void
lsm303d_write_command(uint8_t *buf, int len)
{
        // spi_lock(&spi1_bus);
        // gpio_write(&lsm303d_cs_pin, 0);
        // spi_master_block_write(&spi1_bus, buf, len);
        // gpio_write(&lsm303d_cs_pin, 1);
        // spi_unlock(&spi1_bus);
        // TODO Ian: Replace with I2C interface
}

void
lsm303d_write_reg(uint8_t reg, uint8_t val)
{
        uint8_t buf[2] = { reg, val };

        lsm303d_write_command(buf, 2);
}

void
accel_factory_provision()
{
#ifdef HAVE_V1
        if (accel_initted)
                device_power_off(POWER_ACCEL);
#else
        if (accel_init()) {
                accel_power_off();
                mag_power_off();
        }
#endif
}

void
accel_pin_setup()
{
        if (accel_pin_initted)
                return;

        // TODO Ian: Init IMU pins appropriately - no CS b/c no SPI

#ifndef HAVE_V1
        // for v1 this is already done in device_power_init()
        gpio_init_out_ex(&lsm303d_cs_pin, IMU_CS, 1);
#endif
        accel_pin_initted = true;
}

void
lsm303d_setup()
{

        status_registers.cntrl1.resume_value =
                status_registers.cntrl1.default_value;
        status_registers.cntrl2.resume_value =
                status_registers.cntrl2.default_value;
        status_registers.cntrl3.resume_value =
                status_registers.cntrl3.default_value;
        status_registers.cntrl4.resume_value =
                status_registers.cntrl4.default_value;
        status_registers.cntrl5.resume_value =
                status_registers.cntrl5.default_value;
        status_registers.cntrl6.resume_value =
                status_registers.cntrl6.default_value;
        status_registers.cntrl7.resume_value =
                status_registers.cntrl7.default_value;

        status_registers.int_ctrl_reg_m.resume_value =
                status_registers.int_ctrl_reg_m.default_value;
        status_registers.int_mag_threshold_low.resume_value =
                status_registers.int_mag_threshold_low.default_value;
        status_registers.int_mag_threshold_high.resume_value =
                status_registers.int_mag_threshold_high.default_value;
        status_registers.int_gen1_reg.resume_value =
                status_registers.int_gen1_reg.default_value;
        status_registers.int_gen2_reg.resume_value =
                status_registers.int_gen2_reg.default_value;
        status_registers.int_gen1_duration.resume_value =
                status_registers.int_gen1_duration.default_value;
        status_registers.int_gen2_duration.resume_value =
                status_registers.int_gen2_duration.default_value;
        status_registers.int_gen1_threshold.resume_value =
                status_registers.int_gen1_threshold.default_value;
        status_registers.int_gen2_threshold.resume_value =
                status_registers.int_gen2_threshold.default_value;


        lsm303d_int.gen1_pin1.address = REG_CNTRL3_ADDR;
        lsm303d_int.gen1_pin2.address = REG_CNTRL4_ADDR;
        lsm303d_int.gen2_pin1.address = REG_CNTRL3_ADDR;
        lsm303d_int.gen2_pin2.address = REG_CNTRL4_ADDR;
        lsm303d_int.gen_mag_pin1.address = REG_CNTRL3_ADDR;
        lsm303d_int.gen_mag_pin2.address = REG_CNTRL4_ADDR;
        lsm303d_int.gen_mag.address = REG_GEN_MAG_ADDR;
        lsm303d_int.gen1_duration.address = REG_GEN1_DUR_ADDR;
        lsm303d_int.gen2_duration.address = REG_GEN2_DUR_ADDR;
        lsm303d_int.gen1_threshold.address = REG_GEN1_THR_ADDR;
        lsm303d_int.gen2_threshold.address = REG_GEN2_THR_ADDR;
        lsm303d_int.gen_mag_threshold.address = REG_GEN_MAG_THR_ADDR;

        lsm303d_int.gen1_pin1.mask = GEN1_PIN1_MASK;
        lsm303d_int.gen1_pin2.mask = GEN1_PIN2_MASK;
        lsm303d_int.gen2_pin1.mask = GEN2_PIN1_MASK;
        lsm303d_int.gen2_pin2.mask = GEN2_PIN2_MASK;
        lsm303d_int.gen_mag_pin1.mask = GEN_MAG_PIN1_MASK;
        lsm303d_int.gen_mag_pin2.mask = GEN_MAG_PIN2_MASK;
        lsm303d_int.gen_mag.mask = GEN_MAG_EN_MASK;

        lsm303d_int.gen1_threshold.value = 0;
        lsm303d_int.gen2_threshold.value = 0;
        lsm303d_int.gen1_duration.value = 0;
        lsm303d_int.gen2_duration.value = 0;
        lsm303d_int.gen_mag_threshold.value = 0;

        for(int i = 0; i < 6; i++) {
                lsm303d_int.gen1_axis[i].address = REG_GEN1_AXIS_ADDR;
                lsm303d_int.gen2_axis[i].address = REG_GEN2_AXIS_ADDR;

        }

        for(int i = 0; i < 3; i++) {
                lsm303d_int.gen_mag_axis[i].address = REG_GEN_MAG_ADDR;
        }

        lsm303d_int.gen1_axis[0].mask = GEN_X_LOW_MASK;
        lsm303d_int.gen1_axis[1].mask = GEN_Y_LOW_MASK;
        lsm303d_int.gen1_axis[2].mask = GEN_Z_LOW_MASK;
        lsm303d_int.gen1_axis[3].mask = GEN_X_HIGH_MASK;
        lsm303d_int.gen1_axis[4].mask = GEN_Y_HIGH_MASK;
        lsm303d_int.gen1_axis[5].mask = GEN_Z_HIGH_MASK;

        lsm303d_int.gen2_axis[0].mask = GEN_X_LOW_MASK;
        lsm303d_int.gen2_axis[1].mask = GEN_Y_LOW_MASK;
        lsm303d_int.gen2_axis[2].mask = GEN_Z_LOW_MASK;
        lsm303d_int.gen2_axis[3].mask = GEN_X_HIGH_MASK;
        lsm303d_int.gen2_axis[4].mask = GEN_Y_HIGH_MASK;
        lsm303d_int.gen2_axis[5].mask = GEN_Z_HIGH_MASK;

        lsm303d_int.gen_mag_axis[0].mask = GEN_X_MAG_MASK;
        lsm303d_int.gen_mag_axis[1].mask = GEN_Y_MAG_MASK;
        lsm303d_int.gen_mag_axis[2].mask = GEN_Z_MAG_MASK;

        lsm303d_int.gen1_and_or.address = REG_GEN1_AXIS_ADDR;
        lsm303d_int.gen1_and_or.mask = GEN1_AND_OR_MASK;

        lsm303d_int.gen2_and_or.address = REG_GEN1_DUR_ADDR;
        lsm303d_int.gen2_and_or.mask = GEN2_AND_OR_MASK;

        lsm303d_int.interrupt_pin_conf.address = REG_GEN_MAG_ADDR;
        lsm303d_int.interrupt_pin_conf.mask = INT_PIN_CONF_MASK;

        lsm303d_int.interrupt_polarity.address = REG_GEN_MAG_ADDR;
        lsm303d_int.interrupt_polarity.mask = INT_POLARITY_MASK;
}

bool
accel_init()
{
        uint8_t buf[1] = {0};

        if (accel_initted)
                return true;

        accel_pin_setup();

        device_power_on(POWER_ACCEL);
#ifdef HAVE_V1
        osDelay(200);
#endif

        lsm303d_read_command(status_registers.who_am_i.address, buf, 1);

        if (buf[0] != status_registers.who_am_i.value) {
                EVENT_LOG2(EVT_MEMS_NOT_PRESENT, "not present", "chip", EVT_STRCONST, "lsm303d", "result", EVT_8BIT | EVT_HEX, buf[0]);
                return false;
        }

        lsm303d_setup();


   gpio_init_in(&lsm303d_int1_pin, IMU_INT);
   gpio_irq_init(&lsm303d_int1_irq, IMU_INT, lsm303d_isr1, 0);
   gpio_irq_set(&lsm303d_int1_irq, IRQ_RISE, 1);

//    gpio_init_in(&lsm303d_int2_pin, ACCEL_INT2);
//    gpio_irq_init(&lsm303d_int2_irq, ACCEL_INT2, lsm303d_isr2);
//    gpio_irq_set(&lsm303d_int2_irq, IRQ_RISE, 1);

        accel_initted = 1;

        EVENT_LOG(EVT_MEMS_INIT, "init");

        return true;
}

void
lsm303d_isr1(uintptr_t context, gpio_irq_event event)
{
        gpio_irq_disable(&lsm303d_int1_irq);

        if (accel_tid) {
                osSignalSet(accel_tid, ACCEL_SIGNAL_IRQ1);
        }
}

void
lsm303d_isr2(gpio_irq_event event)
{
        gpio_irq_disable(&lsm303d_int2_irq);

        if (accel_tid) {
                osSignalSet(accel_tid, ACCEL_SIGNAL_IRQ2);
        }
}

void
lsm303d_process()
{
        uint8_t buf[1];

        lsm303d_read_command(status_registers.int_gen1_src.address, buf, 1);
        lsm303d_read_command(status_registers.int_gen_mag_src.address, buf, 1);
}

void
accel_power_off()
{
        if (!accel_initted)
                return;

        // kill the temp sensor
        lsm303d_write_reg(status_registers.cntrl5.address, status_registers.cntrl5.resume_value);
        // kill the accelerometer
        lsm303d_write_reg(status_registers.cntrl1.address, 0);

        pin_mode(IMU_INT, PullDown);
        // pin_mode(ACCEL_INT2, PullDown);      //! PORT: INT1/2 tied together ish I think; see schematic -- no pin for it
        accel_is_on = false;
}

void
mag_power_off()
{
        if (!accel_initted)
                return;

        lsm303d_write_reg(status_registers.cntrl7.address, POWEROFF_MAG);
        mag_is_on = false;
}

void
accel_power_on()
{
        uint8_t buf[6];

        if (!accel_initted)
                return;

        lsm303d_write_reg(status_registers.cntrl0.address, status_registers.cntrl0.resume_value);

        buf[0] = status_registers.cntrl1.address | 0x40;
        buf[1] = status_registers.cntrl1.resume_value;
        buf[2] = status_registers.cntrl2.resume_value;
        buf[3] = status_registers.cntrl3.resume_value;
        buf[4] = status_registers.cntrl4.resume_value;
        buf[5] = status_registers.cntrl5.resume_value | TEMP_ON;
        lsm303d_write_command(buf, 6);

        lsm303d_write_reg(status_registers.int_gen1_reg.address, status_registers.int_gen1_reg.resume_value);

        buf[0] = status_registers.int_gen1_threshold.address | 0x40;
        buf[1] = status_registers.int_gen1_threshold.resume_value;
        buf[2] = status_registers.int_gen1_duration.resume_value;
        lsm303d_write_command(buf, 3);

        lsm303d_write_reg(status_registers.int_gen2_reg.address, status_registers.int_gen2_reg.resume_value);

        buf[0] = status_registers.int_gen2_threshold.address | 0x40;
        buf[1] = status_registers.int_gen2_threshold.resume_value;
        buf[2] = status_registers.int_gen2_duration.resume_value;
        lsm303d_write_command(buf, 3);

        lsm303d_acc_update_fs_range(LSM303D_ACC_FS_16G);
        lsm303d_acc_update_odr(20);

        pin_mode(IMU_INT, PullNone);
        // pin_mode(ACCEL_INT2, PullNone);

        accel_is_on = true;
}

void
mag_power_on()
{
        uint8_t buf[5];

        if (!accel_initted)
                return;

        lsm303d_write_reg(status_registers.cntrl0.address, status_registers.cntrl0.resume_value);

        buf[0] = status_registers.cntrl3.address | 0x40;
        buf[1] = status_registers.cntrl3.resume_value;
        buf[2] = status_registers.cntrl4.resume_value;
        buf[3] = status_registers.cntrl5.resume_value;
        if (accel_is_on)
                buf[3] |= TEMP_ON;
        buf[4] = status_registers.cntrl6.resume_value;

        lsm303d_write_command(buf, 5);

        lsm303d_write_reg(status_registers.int_ctrl_reg_m.address, status_registers.int_ctrl_reg_m.resume_value);

        buf[0] = status_registers.int_mag_threshold_low.address | 0x40;
        buf[1] = status_registers.int_mag_threshold_low.resume_value;
        buf[2] = status_registers.int_mag_threshold_high.resume_value;
        lsm303d_write_command(buf, 3);

        lsm303d_write_reg(status_registers.cntrl7.address, ((MSMS_MASK & CONTINUOS_CONVERSION) |
                          ((~MSMS_MASK) & status_registers.cntrl7.resume_value)));

        lsm303d_mag_update_fs_range(LSM303D_MAG_FS_12G);
        lsm303d_mag_update_odr(20);
        mag_is_on = true;
}

void
lsm303d_acc_update_filter(uint8_t new_bandwidth)
{
        uint8_t updated_val;
        uint8_t buf[2] = {0};

        switch (new_bandwidth) {
        case ANTI_ALIASING_50:
                break;
        case ANTI_ALIASING_194:
                break;
        case ANTI_ALIASING_362:
                break;
        case ANTI_ALIASING_773:
                break;
        default:
                //uart_printf("accel: invalid bandwidth %d\n", new_bandwidth);
                return;
        }

        lsm303d_read_command(status_registers.cntrl2.address, buf, 1);

        status_registers.cntrl2.resume_value = buf[0];
        updated_val = ((LSM303D_ACC_FILTER_MASK & new_bandwidth) |
                       ((~LSM303D_ACC_FILTER_MASK) & buf[0]));

        lsm303d_write_reg(status_registers.cntrl2.address, updated_val);
        status_registers.cntrl2.resume_value = updated_val;
}

void
lsm303d_acc_update_fs_range(uint8_t new_fs_range)
{
        uint16_t sensitivity;
        uint8_t updated_val;
        uint8_t buf[1] = {0};

        switch (new_fs_range) {
        case LSM303D_ACC_FS_2G:
                sensitivity = SENSITIVITY_ACC_2G;
                break;
        case LSM303D_ACC_FS_4G:
                sensitivity = SENSITIVITY_ACC_4G;
                break;
        case LSM303D_ACC_FS_8G:
                sensitivity = SENSITIVITY_ACC_8G;
                break;
        case LSM303D_ACC_FS_16G:
                sensitivity = SENSITIVITY_ACC_16G;
                break;
        default:
                //uart_printf("accel: invalid fs range %d\n", new_fs_range);
                return;
        }

        lsm303d_read_command(status_registers.cntrl2.address, buf, 1);

        status_registers.cntrl2.resume_value = buf[0];
        updated_val = ((LSM303D_ACC_FS_MASK & new_fs_range) |
                       ((~LSM303D_ACC_FS_MASK) & buf[0]));
        lsm303d_write_reg(status_registers.cntrl2.address, updated_val);

        status_registers.cntrl2.resume_value = updated_val;
        lsm303d_sensitivity_acc = sensitivity;
}

void
lsm303d_mag_update_fs_range(uint8_t new_fs_range)
{
        uint16_t sensitivity;
        uint8_t updated_val;
        uint8_t buf[2];

        switch (new_fs_range) {
        case LSM303D_MAG_FS_2G:
                sensitivity = SENSITIVITY_MAG_2G;
                break;
        case LSM303D_MAG_FS_4G:
                sensitivity = SENSITIVITY_MAG_4G;
                break;
        case LSM303D_MAG_FS_8G:
                sensitivity = SENSITIVITY_MAG_8G;
                break;
        case LSM303D_MAG_FS_12G:
                sensitivity = SENSITIVITY_MAG_12G;
                break;
        default:
                //uart_printf("accel: invalid mag fs range %d\n", new_fs_range);
                return;
        }

        lsm303d_read_command(status_registers.cntrl6.address, buf, 1);

        status_registers.cntrl6.resume_value = buf[0];
        updated_val = (LSM303D_MAG_FS_MASK & new_fs_range);

        lsm303d_write_reg(status_registers.cntrl6.address, updated_val);

        status_registers.cntrl6.resume_value = updated_val;
        lsm303d_sensitivity_mag = sensitivity;
        return;
}

void
lsm303d_acc_update_odr(unsigned int poll_interval_ms)
{
        uint8_t config;
        int i;

        for (i = ARRAY_SIZE(lsm303d_acc_odr_table) - 1; i >= 0; i--) {
                if ((lsm303d_acc_odr_table[i].cutoff_us <= poll_interval_ms)
                    || (i == 0))
                        break;
        }

        config = ((ODR_ACC_MASK & lsm303d_acc_odr_table[i].value) |
                  ((~ODR_ACC_MASK) & status_registers.cntrl1.resume_value));

        lsm303d_write_reg(status_registers.cntrl1.address, config);
        status_registers.cntrl1.resume_value = config;
}

void
lsm303d_mag_update_odr(unsigned int poll_interval_ms)
{
        uint8_t config;
        int i;

        for (i = ARRAY_SIZE(lsm303d_mag_odr_table) - 1; i >= 0; i--) {
                if ((lsm303d_mag_odr_table[i].cutoff_us <= poll_interval_ms)
                    || (i == 0))
                        break;
        }

        config = ((ODR_MAG_MASK & lsm303d_mag_odr_table[i].value) |
                  ((~ODR_MAG_MASK) & status_registers.cntrl5.resume_value));

        if (accel_is_on)
                config |= TEMP_ON;

        lsm303d_write_reg(status_registers.cntrl5.address, config);
        status_registers.cntrl5.resume_value = config;
}

bool
accel_read_data(axis_t *data)
{
        uint8_t acc_data[6];

        if (!accel_initted || !accel_is_on)
                return false;

        lsm303d_read_command(REG_ACC_DATA_ADDR | 0x40, acc_data, 6);

        data->x = (((int16_t)((acc_data[1] << 8) | (acc_data[0])))) >> 4;
        data->y = (((int16_t)((acc_data[3] << 8) | (acc_data[2])))) >> 4;
        data->z = (((int16_t)((acc_data[5] << 8) | (acc_data[4])))) >> 4;

        return true;
}

bool
mag_read_data(axis_t *data)
{
        uint8_t mag_data[6];

        if (!mag_is_on)
                return false;

        lsm303d_read_command(REG_MAG_DATA_ADDR | 0x40, mag_data, 6);

        data->x = (( (int16_t)((mag_data[1] << 8) | (mag_data[0]))));
        data->y = (( (int16_t)((mag_data[3] << 8) | (mag_data[2]))));
        data->z = (( (int16_t)((mag_data[5] << 8) | (mag_data[4]))));

        return true;
}

int16_t
lsm303d_temp_read()
{
        uint8_t temp_data[2] = {0, 0};
        int16_t temp;

        if (!accel_is_on)
                return 0;

        // lsm303d return temperature as a two's complement 12-bit offset from 25 degrees C in units of 0.125C
        lsm303d_read_command(REG_TEMP_DATA_ADDR | 0x40, temp_data, 2);
        temp = (temp_data[1] << 8) | temp_data[0];

        if (temp > 2048)
                temp = 65536 - temp;

        temp += (TEMP_SENSITIVITY * OFFSET_TEMP);

        // Return 10 * celsius, including truncated decimal
        return ((temp / TEMP_SENSITIVITY) * 10) + ( ((10*(temp % TEMP_SENSITIVITY)) / TEMP_SENSITIVITY) % 10);
}

#ifdef HAVE_FACTORY_TEST
ft_result_t
accel_factory_test()
{
        axis_t data = { .x = 0, .y = 0, .z = 0};
        uint8_t buf[1] = {0};

        accel_pin_setup();
        device_power_on(POWER_ACCEL);

#ifdef HAVE_V1
        osDelay(1000);
#endif

        lsm303d_read_command(status_registers.who_am_i.address, buf, 1);

        if (buf[0] != status_registers.who_am_i.value) {
                ft_update("lsm303d: chipid err id=[0%x]", buf[0]);
                device_power_off(POWER_ACCEL);
                return FT_ACCEL_UNRESPONSIVE;
        }

        lsm303d_setup();
        accel_initted = true;

        accel_power_on();
        mag_power_on();

        for (int retries = 0; retries < 5; retries++) {
                osDelay(1000);
                accel_read_data(&data);
                ft_update("lsm303d: leveling %d", retries);
        }

        if (abs(data.x) > 40 || abs(data.y) > 40 || data.z < 200) {
                accel_power_off();
                mag_power_off();
                ft_update("lsm303d: uneven %d %d %d", data.x, data.y, data.z);
                device_power_off(POWER_ACCEL);
                return FT_ACCEL_UNEVEN;
        }

        ft_device_result("lsm303d-accel %d %d %d", data.x, data.y, data.z);
        mag_read_data(&data);
        ft_device_result("lsm303d-mag %d %d %d", data.x, data.y, data.z);
        ft_update("lsm303d: passed");

        accel_power_off();
        mag_power_off();

        device_power_off(POWER_ACCEL);
        return FT_SUCCESS;
}
#endif

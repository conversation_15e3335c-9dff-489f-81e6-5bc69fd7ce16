#include "pelagic.h"
#include "gpio_api.h"
#include "dma.h"
#include "winbond_flash.h"
#include "signals.h"
#include "factory_test.h"
#include "device_power.h"

typedef enum {
        CMD_W25_WRITE_STATUS	= 0x01,	/* Write Status Register */
        CMD_W25_PAGE_PROGRAM	= 0x02,	/* Page Program */
        CMD_W25_READ_DATA	= 0x03,	/* Read Data Bytes */
        CMD_W25_WRITE_DISABLE	= 0x04,	/* Write Disable */
        CMD_W25_READ_STATUS	= 0x05,	/* Read Status Register */
        CMD_W25_WRITE_ENABLE	= 0x06,	/* Write Enable */
        CMD_W25_FAST_READ_DATA	= 0x0b, /* fast read */
        CMD_W25_SECTOR_ERASE	= 0x20,	/* Sector (4K) Erase */
        CMD_W25_DEVICE_ID	= 0x90,	/* Device ID */
        CMD_W25_CHIP_ERASE      = 0xc7, /* Chip Erase */
        CMD_W25_POWER_DOWN      = 0xb9, /* Deep Power-down */
        CMD_W25_RELEASE         = 0xab  /* Release from DP, and Read Signature */
} winbond_command_t;

typedef enum {
        WINBOND_STATUS_BUSY	= 0x01,	/* Command busy */
        WINBOND_STATUS_WEL	= 0x02	/* write-enable latched */
} winbond_status_t;

enum {
        WINBOND_PAGE_SIZE	= 256,
        WINBOND_PAGE_SHIFT	= 8,

        WINBOND_SECTOR_SIZE	= 4096,
        WINBOND_SECTOR_SHIFT    = 12,

        WINBOND_DMA_TX_IRQ      = (DMA0_IRQn + DMA_SPI1_TX_CHANNEL),
        WINBOND_DMA_RX_IRQ      = (DMA0_IRQn + DMA_SPI1_RX_CHANNEL),

        WINBOND_CHIP_ID		= 0xef17
};

gpio_t winbond_cs_pin;
gpio_t winbond_wp_pin;

#ifdef TARGET_DEVBOARD
gpio_t winbond_hold_pin;
#endif

void winbond_spi_write(uint8_t *cmd, int cmdbytes, uint8_t *data, int bytes);
void winbond_spi_read(uint8_t *cmd, int cmdbytes, uint8_t *data, int bytes);

void winbond_dma_rx_irq(uint32_t param);
void winbond_dma_tx_irq(uint32_t param);

__IO uint8_t *winbond_dmamux_rx_reg, *winbond_dmamux_tx_reg;
struct DMA_Channel *winbond_dma_rx_regs, *winbond_dma_tx_regs;

osThreadId winbond_waiting_tid;

osMutexId winbond_flash_mutex;
osMutexDef(winbond_flash_mutex);

bool winbond_initted = false, winbond_pin_initted = false;
bool winbond_keep_on = false, winbond_is_on = false;

void
winbond_power_on(void)
{
        uint8_t power_up_cmd[] = { CMD_W25_RELEASE };

        if (winbond_is_on)
                return;

        winbond_spi_write(power_up_cmd, sizeof(power_up_cmd), NULL, 0);
        osDelay(1);
        winbond_is_on = true;
}

void
winbond_power_off(void)
{
        uint8_t power_down_cmd[] = { CMD_W25_POWER_DOWN };

//	if (!winbond_is_on)
//		return;

        winbond_spi_write(power_down_cmd, sizeof(power_down_cmd), NULL, 0);
        winbond_is_on = false;
}

void
winbond_lock(void)
{
        osMutexWait(winbond_flash_mutex, osWaitForever);

        winbond_power_on();
}

void
winbond_unlock(void)
{
        if (!winbond_keep_on)
                winbond_power_off();

        osMutexRelease(winbond_flash_mutex);
}

void
winbond_select(void)
{
        spi_lock(&spi1_bus);
        gpio_write(&winbond_cs_pin, 0);
}

void
winbond_unselect(void)
{
        gpio_write(&winbond_cs_pin, 1);
        spi_unlock(&spi1_bus);
}

void
winbond_factory_provision(void)
{
#ifndef TARGET_V1
        winbond_flash_init();
#endif
        device_power_off(POWER_WINBOND);
}

uint16_t
winbond_read_chip_id(void)
{
        uint16_t chip_id;
        uint8_t cmd[4] = { CMD_W25_DEVICE_ID, 0, 0, 0 }, data[2];
        uint8_t power_down_cmd[] = { CMD_W25_POWER_DOWN };
        uint8_t power_up_cmd[] = { CMD_W25_RELEASE };

        winbond_spi_write(power_up_cmd, sizeof(power_up_cmd), NULL, 0);
        osDelay(1);
        winbond_spi_read(cmd, sizeof(cmd), data, sizeof(data));
        winbond_spi_write(power_down_cmd, sizeof(power_down_cmd), NULL, 0);

        chip_id = (data[0] << 8) | data[1];
        return chip_id;
}

void
winbond_pin_setup(void)
{
        if (winbond_pin_initted)
                return;

#ifndef HAVE_V1
        // setup in device_power_init()
        gpio_init_out_ex(&winbond_cs_pin, WINBOND_CS, 1);
#endif
        gpio_init_out_ex(&winbond_wp_pin, WINBOND_WP, 0);

#ifdef TARGET_DEVBOARD
        gpio_init_out(&winbond_hold_pin, PTE30);
        gpio_write(&winbond_hold_pin, 1);
#endif

        winbond_pin_initted = true;
}

void
winbond_pins_init(void)
{
        if (winbond_initted)
                return;

        winbond_pin_setup();

        winbond_flash_mutex = osMutexCreate(osMutex(winbond_flash_mutex));

//    winbond_spi.spi->C3 |= SPI_C3_FIFOMODE_MASK;

        winbond_initted = true;
        osDelay(10);
}

bool
winbond_flash_init(void)
{
        uint16_t        chip_id;
        uint8_t         i;

        winbond_pins_init();
        device_power_on(POWER_WINBOND);
        osDelay(10);    // WAG: give power a chance to 'settle'

        for (i = 1; i <= 5; i++) {
                chip_id = winbond_read_chip_id();
                if (chip_id == WINBOND_CHIP_ID)
                        break;
                osDelay(15);
        }

        if (chip_id != WINBOND_CHIP_ID) {
                EVENT_LOG2(EVT_FLASH_NOT_PRESENT, "not present", "chip", EVT_STRCONST, "winbond", "result", EVT_16BIT, chip_id);
                return false;
        }

        EVENT_LOG1(EVT_FLASH_INIT, "init, got it in", "tries", EVT_8BIT, i);
        return true;
}

void
winbond_spi_write(uint8_t *cmd, int cmdbytes, uint8_t *data, int bytes)
{
        winbond_select();

        spi_master_block_write(&spi1_bus, cmd, cmdbytes);
        if (bytes)
                spi_master_block_write(&spi1_bus, data, bytes);
        winbond_unselect();
}

void
winbond_spi_read(uint8_t *cmd, int cmdbytes, uint8_t *data, int bytes)
{
        winbond_select();

        spi_master_block_write(&spi1_bus, cmd, cmdbytes);
        if (bytes)
                spi_master_block_read(&spi1_bus, data, bytes);

        winbond_unselect();
}

bool
winbond_wait_ready(void)
{
        uint8_t status;
        uint8_t cmd[] = { CMD_W25_READ_STATUS };

        for (int i = 0; i < 1000; i++) {
                osDelay(1);

                winbond_spi_read(cmd, sizeof(cmd), &status, 1);

                if ((status & WINBOND_STATUS_BUSY) == 0)
                        return true;
        }

        return false;
}

flash_result_t
winbond_flash_erase_sector(uint32_t address)
{
        bool success;
        uint8_t we_cmd[] = { CMD_W25_WRITE_ENABLE };
        // chip rounds down and ignores last bits.
        uint8_t cmd[] = { CMD_W25_SECTOR_ERASE, (address >> 16) & 0xff, (address >> 8) & 0xff, 0 };

        if (address & (WINBOND_SECTOR_SIZE - 1))
                return FLASH_ERR_ALIGNMENT;

        winbond_lock();
        winbond_spi_write(we_cmd, sizeof(we_cmd), NULL, 0);
        winbond_spi_write(cmd, sizeof(cmd), NULL, 0);

        success = winbond_wait_ready();
        winbond_unlock();

        return success ? FLASH_SUCCESS : FLASH_ERR_RUNTIME;
}

flash_result_t
winbond_flash_write(uint32_t address, void *param, int bytes)
{
        bool success;
        uint8_t *data = (uint8_t *) param;
        uint32_t count, remaining;
        uint8_t we_cmd[] = { CMD_W25_WRITE_ENABLE };
        uint8_t cmd[4];

        cmd[0] = CMD_W25_PAGE_PROGRAM;

        winbond_lock();

        while (bytes > 0) {
                cmd[1] = (address >> 16) & 0xff;
                cmd[2] = (address >> 8) & 0xff;
                cmd[3] = address & 0xff;

                // bytes will wrap around to the top of the page, and not go to
                // the next if the count spans pages

                remaining = WINBOND_PAGE_SIZE - (address & (WINBOND_PAGE_SIZE - 1));
                count = remaining < bytes ? remaining : bytes;

                gpio_write(&winbond_wp_pin, 1);
                winbond_spi_write(we_cmd, sizeof(we_cmd), NULL, 0);
                winbond_spi_write(cmd, sizeof(cmd), data, count);
                success = winbond_wait_ready();
                gpio_write(&winbond_wp_pin, 0);

                if (!success) {
                        winbond_unlock();
                        return FLASH_ERR_RUNTIME;
                }

                bytes -= count;
                address += count;
                data += count;
        }

        winbond_unlock();

        return FLASH_SUCCESS;
}

flash_result_t
winbond_flash_read(uint32_t address, void *data, int size)
{
        uint8_t cmd[] = { CMD_W25_FAST_READ_DATA, (address >> 16) & 0xff, (address >> 8) & 0xff, address & 0xff, 0 };

        // The Winbond chip will continue to pump out bytes until the CS line is released..
        winbond_lock();
        winbond_spi_read(cmd, sizeof(cmd), data, size);
        winbond_unlock();

        return FLASH_SUCCESS;
}

void
winbond_flash_keep_on(bool keep_on)
{
        osMutexWait(winbond_flash_mutex, osWaitForever);
        winbond_keep_on = keep_on;

        if (winbond_keep_on == false)
                winbond_power_off();

        osMutexRelease(winbond_flash_mutex);
}

flash_device_t winbond_flash_device = {
        .erase            = winbond_flash_erase_sector,
        .write            = winbond_flash_write,
        .read             = winbond_flash_read,
        .keep_on          = winbond_flash_keep_on,
        .page_size        = 256,
        .sector_size      = 4096,
        .pages_per_sector = 16
};

#ifdef HAVE_FACTORY_TEST

ft_result_t
winbond_run_test(void)
{
        flash_result_t result;
        uint16_t chip_id;
        uint8_t *data = the_buffer.test;

        chip_id = winbond_read_chip_id();

        if (chip_id != WINBOND_CHIP_ID) {
                ft_update("winbond: not present. got [0%x]", chip_id);
                return FT_WINBOND_UNRESPONSIVE;
        }

        ft_update("winbond: starting IO test.");

        memset(data, 0xcc, 256);

        result = winbond_flash_erase_sector(0);
        if (result != FLASH_SUCCESS) {
                ft_update("winbond: page erase err=[%d]", result);
                return FT_WINBOND_IO_FAIL;
        }

        result = winbond_flash_write(0, data, 256);
        if (result != FLASH_SUCCESS) {
                ft_update("winbond: write err=[%d]", result);
                return FT_WINBOND_IO_FAIL;
        }

        memset(data, 0, 256);
        result = winbond_flash_read(0, data, 256);
        if (result != FLASH_SUCCESS) {
                ft_update("winbond: read io err=[%d].", result);
                return FT_WINBOND_IO_FAIL;
        }

        for (int i = 0; i < 256; i++) {
                if (data[i] != 0xcc) {
                        ft_update("winbond: mismatch %d/0x%x", i, data[i]);
                        return FT_WINBOND_IO_FAIL;
                }
        }

        ft_update("winbond: all tests passed.");

        return FT_SUCCESS;
}

ft_result_t
winbond_factory_test(void)
{
        ft_result_t result;

        winbond_pins_init();
        device_power_on(POWER_WINBOND);

        result = winbond_run_test();

        device_power_off(POWER_WINBOND);

        return result;
}
#endif

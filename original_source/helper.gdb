#tar rem :3333
#mon arm semihosting enable

define lvms
echo Loading new vms image
monitor reset halt
file vms.elf
load
end

define lsen
echo Loading new sensor image
monitor reset halt
file sensor.elf
load
end

define reboot
echo Rebooting
monitor reset halt
continue
end

define serialsh
break main
commands
    silent
    echo "setting semihosting on"
    set serial_semihost = 1
    delete main
    cont
end
reboot
end

define gpson
    set gps_show_bytes = 1
end

define gpsoff
    set gps_show_bytes = 0
end

#include "pelagic.h"
#include "system_file.h"
#include "signals.h"
#include "mcu_sleep.h"
#include "shell.h"

void
reboot_command(int argc, char **argv)
{
        if (argc == 1) {
                printf("reboot now   - immediate reboot\n");
#ifdef TARGET_VMS
                printf("reboot wait  - wait until threads are idle before rebooting.\n");
                printf("reboot crit  - enter critical power state reboot (test)\n");
#endif
                return;
        }

        if (strcmp(argv[1], "now") == 0) {
                printf("rebooting\n");
                osDelay(250);

#ifdef TARGET_VMS
                rtc_save(0);
#endif

                SYS_REG_FILE->reboot_reason = REBOOT_NORMAL;
                // And good bye!
                NVIC_SystemReset();
        }

#ifdef TARGET_VMS
        if (strcmp(argv[1], "wait") == 0) {
                printf("signaling reboot thread\n");
                osSignalSet(reboot_tid, REBOOT_SIGNAL_NORMAL);
                return;
        }

        if (strcmp(argv[1], "crit") == 0) {
                printf("signalling critical power shutdown");
                osSignalSet(reboot_tid, REBOOT_SIGNAL_CRITICAL);
                return;
        }
#endif

        if (strcmp(argv[1], "test") == 0) {
                printf("testing mcu_sleep\n");
                osDelay(100);
                mcu_sleep(10);
        }

        unknown_command(argv[1]);
}

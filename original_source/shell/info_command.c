#include "pelagic.h"
#include "rtc_api.h"
#ifdef TARGET_VMS
#include "board_info.h"
#endif

extern const char build_tag[];
extern const uint32_t build_target_version;

void
info_command(int argc, char **argv)
{
        printf("buildstamp %d tag %s target %d",
               build_timestamp, build_tag, build_target_version);
#ifdef TARGET_VMS
        printf(" crc %x", board_info_read_build_crc());
#endif
        printf("\nuptime %d seconds\n", epoch_clock);
}

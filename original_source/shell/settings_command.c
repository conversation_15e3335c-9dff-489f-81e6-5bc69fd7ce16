#include "pelagic.h"
#include "console.h"

void settings_store();
int settings_execute(int argc, char **argv);

void
settings_command(int argc, char **argv)
{
        if (argc == 1) {
                int len = settings_dump(setting_buffer);
                if (len > 0) {
                        setting_buffer[len] = 0;
                        puts(setting_buffer);
                } else {
                        printf("No settings??\n");
                }
                return;
        }

        if (settings_execute(argc - 1, argv+1)) {
                settings_store();
                printf("set success.\n");
        } else {
                printf("set failure.\n");
        }
}

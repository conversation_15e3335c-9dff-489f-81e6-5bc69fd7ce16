#include "pelagic.h"
#include "temp.h"

static void
temp_display()
{
#ifdef TARGET_SENSOR
        printf("thermistor 0x%x\n", thermistor_read());
#else
        int16_t temp = lsm303d_temp_read();
        printf("temp %d.%dC\n", temp / 10, abs(temp % 10));
#endif
}

void
temp_command(int argc, char **argv)
{
        if (argc == 2 && strcmp(argv[1], "watch") == 0) {
                while (!have_esc()) {
                        temp_display();
                        osDelay(1000);
                }
                return;
        }

        temp_display();
}

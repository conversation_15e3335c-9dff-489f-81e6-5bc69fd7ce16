#include "pelagic.h"
#include "shell.h"

void
event_command(int argc, char **argv)
{
        char *command = argv[1];

        if (argc == 1) {
                printf("No command given.\n");
                printf("event dump  - print event log.\n");
                printf("event on    - enable console event printing\n");
                printf("event off   - disable console event printing\n");
                return;
        }

        if (strcmp(command, "dump") == 0) {
                printf("Event log dump:\n");
                event_dump();
                return;
        }

        if (strcmp(command, "on") == 0) {
                printf("event watching on\n");
                event_show = true;
                return;
        }

        if (strcmp(command, "off") == 0) {
                printf("event watching off\n");
                event_show = false;
                return;
        }

        unknown_command(command);
}

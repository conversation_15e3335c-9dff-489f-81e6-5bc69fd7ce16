#ifdef HAVE_RADIO
#include "pelagic.h"
#include "bnet.h"
#include "memmap.h"
#include "radio.h"
#include "shell.h"

#ifdef TARGET_SENSOR
#include "bnet_sensor.h"
#endif

#ifdef TARGET_SENSOR
void
net_temp_test()
{
        int result;

        uart_printf("hit <ESC> to stop - may take a few seconds to terminate.\n");

        bnet_packet_watch = 1;
        for (;;) {
                result = bnet_sensor_report(false);

                if (result == 0) {
                        uart_printf("no response");
                        continue;
                }

                if (result == 1) {
                        uart_printf(".");
                } else {
                        break;
                }
        }

        bnet_packet_watch = 0;
        uart_printf("\nstopped\n");
}
#endif

void
net_command(int argc, char **argv)
{
        char *command = argv[1];

        if (argc < 2) {
                printf("No net command given. Commands are:\n");
#ifdef TARGET_VMS
                printf("net sensor - watch sensor packet with no logging\n");
#endif
                printf("net sleep - put the radio to sleep\n");
                printf("net watch - display incoming packets\n");
#ifdef TARGET_SENSOR
                printf("net temp - temperature test\n");
                printf("net check - query for firmware update\n");
                printf("net update - download firmware update\n");
#endif
                return;
        }

#ifdef TARGET_VMS
        if (strcmp(command, "sensor") == 0) {
                bnet_sensor_watch = !bnet_sensor_watch;
                printf("sensor watch now %s\n", bnet_sensor_watch ? "on" : "off");
                return;
        }
#endif

#ifdef TARGET_SENSOR
        if (strcmp(command, "temp") == 0) {
                net_temp_test();
                return;
        }

        if (strcmp(command, "check") == 0) {
                if (bnet_sensor_firmware_check())
                        printf("firmware available\n");
                else
                        printf("no firwmare atm\n");
                return;
        }

        if (strcmp(command, "update") == 0) {
                if (bnet_sensor_firmware_download()) {
                        extern void firmware_update();
                        printf("firmware downloaded\n");
                        firmware_update();
                } else
                        printf("error during download\n");
                return;
        }
#endif

        if (strcmp(command, "status") == 0) {
                radio_stats();
                return;
        }

        if (strcmp(command, "chip") == 0) {
                radio_chip_status();
                return;
        }

        if (strcmp(command, "sleep") == 0) {
                radio_power_off();
                return;
        }

        if (strcmp(command, "watch") == 0) {
                bnet_packet_watch = !bnet_packet_watch;
                uart_printf("watch toggled %s\n", bnet_packet_watch ? "ON" : "OFF");
                return;
        }

        if (strcmp(command, "reset") == 0) {
                uart_printf("Reseting radio\n");
                radio_power_off();
                osDelay(1000);
                radio_power_on();
        }

        unknown_command(command);
}
#endif

#include "pelagic.h"
#include "accel.h"

void
park_command(int ac, char **av)
{
        char *command = av[1];

        if (ac == 1) {
                printf("park mode is %s\n", (enable_park == true) ? "enabled" : "disabled");
                return;
        }

        if (strncmp(command, "enable", 2) == 0 ||
            strcmp(command, "on") == 0||
            *command ==  '1') {
                enable_park = true;
        } else if (strncmp(command, "disable", 3) == 0 ||
                   strcmp(command, "off") == 0 ||
                   *command == '0') {
                enable_park = false;
        } else {
                printf("Invalid park command\n"
                       "park\n"
                       "park enable|on|1\n"
                       "park disable|off|0\n");
        }
}
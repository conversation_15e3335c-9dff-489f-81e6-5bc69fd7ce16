#include "pelagic.h"
#include "shell.h"
#include "semihost_api.h"
#include "firmware.h"

void
load_command(int argc, char **argv)
{
        int fd, bytes;

        if (argc == 1) {
                printf("missing filename\n");
                return;
        }

        fd = semihost_open(argv[1], 0);

        if (fd == -1) {
                printf("failed to open file\n");
                return;
        }

        printf("file %d\n", fd);
        printf("erasing partition\n");
        flash_store_erase(&firmware_sensor_partition);
        printf("erased - loading file\n");

        // semihost_read returns how many bytes LEFT.. seriously ARM?

        while ((bytes = semihost_read(fd, the_buffer.test, 512)) != 512) {
                printf("%d ", 512 - bytes);
                flash_store_write(&firmware_sensor_partition, the_buffer.test, 512 - bytes);
        }

        if (bytes == -1) {
                printf("error ");
        }
        printf("finished\n");
        semihost_close(fd);

        flash_store_flush(&firmware_sensor_partition);
        firmware_sensor_init();
        printf("loaded %s buildstamp %d\n", argv[1], firmware_sensor_header.buildstamp);
}

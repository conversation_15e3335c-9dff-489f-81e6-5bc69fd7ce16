#include "pelagic.h"
#include "winbond_flash.h"
#include "helm.h"
#include "shell.h"

uint8_t test_buffer[256];

void
winbond_command(int argc, char **argv)
{
        char *command = argv[1];
        int result;

        if (argc == 1) {
                printf("winbond read - read from flash\n");
                printf("winbond write - write to flash\n");
                return;
        }

        if (strcmp(command, "read") == 0) {
                if (argc != 3) {
                        printf("missing address argument\n");
                        return;
                }
                int address = atoi(argv[2]);

                result = winbond_flash_read(address, test_buffer, 256);
                if (result != FLASH_SUCCESS) {
                        printf("flash read failure [%d] address [%d]\n", result, address);
                        return;
                }

                for (int i = 0; i < 256; i += 32) {
                        if ((i%32) == 0) {
                                printf("\n%3.3d: ", i);
                        }

                        hex_dump(test_buffer + i, 32);
                }

                printf("\n");
                return;
        }

        if (strcmp(command, "erase") == 0) {
                if (argc != 3) {
                        printf("missing address argument\n");
                        return;
                }
                int address = atoi(argv[2]);

                result = winbond_flash_erase_sector(address);

                if (result != FLASH_SUCCESS) {
                        printf("flash erase failure\n");
                        return;
                }
                printf("flash erased success address [%d]\n", address);
                return;
        }

        if (strcmp(command, "write") == 0) {
                if (argc != 3) {
                        printf("missing address argument\n");
                        return;
                }
                int address = atoi(argv[2]);

                result = winbond_flash_erase_sector(address);

                if (result != FLASH_SUCCESS) {
                        printf("flash erase failure\n");
                        return;
                }

                for (int i = 0; i < 256; i++) {
                        test_buffer[i] = i + 1;
                }

                result = winbond_flash_write(address, test_buffer, 256);

                if (result != FLASH_SUCCESS) {
                        printf("flash write failure\n");
                        return;
                }

                printf("flash write success to address %d!\n", address);
                return;
        }

        if (strcmp(command, "stress") == 0) {
                extern osThreadId gps_tid;

                uint32_t start;
                printf("Terminating helm thread - reset the board after this\n");
                osThreadTerminate(helm_tid);
                osThreadTerminate(gps_tid);
                printf("Stress testing winbond\n");

                for (int i = 0; i < 20; i++) {
                        printf("Loop %d\n", i);
                        start = clock_read();
                        for (int sector = 0; sector < ((16*1024*1024)); sector += 4096) {
                                result = winbond_flash_erase_sector(sector);
                                printf(".");
                                if (result != FLASH_SUCCESS) {
                                        printf("Erase failure at address 0x%x, result %d\n", sector, result);
                                        return;
                                }

                                for (int page = 0; page < (4096/256); page++) {
                                        uint32_t address = sector + (page * 256);

                                        for (int c = 0; c < 256; c++)
                                                test_buffer[c] = c+1;

                                        result = winbond_flash_write(address, test_buffer, 256);

                                        if (result != FLASH_SUCCESS) {
                                                printf("Read failure at address 0x%x, result %d\n", address, result);
                                                return;
                                        }

                                        for (int c = 0; c < 256; c++)
                                                test_buffer[c] = 0xff;

                                        result = winbond_flash_read(address, test_buffer, 256);

                                        if (result != FLASH_SUCCESS) {
                                                printf("Read failure at address 0x%x, result %d\n", address, result);
                                                return;
                                        }

                                        for (int c = 0; c < 256; c++)
                                                if (test_buffer[c] != (uint8_t)(c+1)) {
                                                        printf("Pattern failure at address 0x%x, byte %d, received 0x%x\n", address, c, test_buffer[c]);
                                                }
                                }
                        }
                        printf("\nFinished time %d seconds\n", clock_read() - start);
                }

                return;
        }

        unknown_command(command);
}

#include "pelagic.h"
#include "boat_log.h"
#include "board_log.h"
#include "shell.h"


static flash_partition_t *partitions[] = {
        &boatlog_partition,
        &event_partition,
        &firmware_partition,
        &firmware_sensor_partition
};

void
store_command(int argc, char **argv)
{
        char *command = argv[1];
        flash_partition_t *part = NULL;

        if (argc == 1) {
                printf("No flash store command given. Type:\n");
                printf("store dump <name> - display the store\n");
                printf("store hex <name> - display the store in hex\n");
                printf("store flush <name> - commit the current sector out to flash\n");
                printf("store erase <name> - reset the flash store (erase)\n");
                printf("store info <name> - display stats about the partition\n");
                printf("store list  - list the partitions\n");
                return;
        }

        if (strcmp(command, "list") == 0) {
                for (int i = 0; i < ARRAY_SIZE(partitions); i++) {
                        printf("%s\n", partitions[i]->name);
                }

                return;
        }


        if (argc != 3) {
                printf("missing store name\n");
                return;
        }

        for (int i = 0; i < ARRAY_SIZE(partitions); i++) {
                if (strcmp(argv[2], partitions[i]->name) == 0) {
                        part = partitions[i];
                        break;
                }
        }

        if (part == NULL) {
                printf("store [%s] not found\n", argv[2]);
                return;
        }

        if (strcmp(command, "hex") == 0) {
                flash_store_dump_hex(part);
                return;
        }

        if (strcmp(command, "flush") == 0) {
                printf("flushing %s to flash\n", part->name);
                flash_store_flush(part);
                return;
        }

        if (strcmp(command, "erase") == 0) {
                printf("erasing %s\n", part->name);
                flash_store_lock(part, osWaitForever);
                flash_store_erase(part);

                if (part == &boatlog_partition)
                        boat_log_reset();
                else if (part == &event_partition)
                        event_log_reset();

                flash_store_unlock(part);
                return;
        }

        if (strcmp(command, "info") == 0) {
                uint32_t page_size = part->device->page_size;
                printf("store %s, page size %d bytes\n", part->name, page_size);
                printf("size      : %d (%dKB)\n", part->size, part->size / 1024);
                printf("stored bytes: %d (%d pages)\n", part->bytes_stored);
                printf("pages used  : %d\n", part->pages_used);
                printf("page read   : %d\n", part->page_read);
                printf("stage page : %d\n", part->staging_page);
                printf("sequence  : %u\n", part->sequence);
                printf("stage off.  : %d\n", part->staging_offset);
                printf("full    : %c\n", part->is_full ? 'Y' : 'N');
                printf("raw     : %c\n", part->is_raw ? 'Y' : 'N');
                printf("busy : %c\n", part->is_busy ? 'Y' : 'N');

                return;
        }

        unknown_command(command);
}

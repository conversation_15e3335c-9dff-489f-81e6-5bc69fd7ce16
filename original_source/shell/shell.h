/**
 * @ingroup     sys
 * @brief       Simple shell interpreter
 */

#ifndef __SHELL_H
#define __SHELL_H

/**
 * @brief           Protype of a shell callback handler.
 * @details         The functions supplied to shell_init() must use this signature.
 *                  The argument list is terminated with a NULL, i.e ``argv[argc] == NULL`.
 *                  ``argv[0]`` is the function name.
 *
 *                  Escape sequences are removed before the function is called.
 *
 *                  The called function may edit `argv` and the contained strings,
 *                  but it must be taken care of not to leave the boundaries of the array.
 *                  This functionality can be used by getopt() or a similar function.
 * @param[in]       argc   Number of arguments supplied to the function invocation.
 * @param[in]       argv   The supplied argument list.
 */
typedef void (*shell_command_handler_t)(int argc, char **argv);

/**
 * @brief           A single command in the list of the supported commands.
 * @details         The list of commands is NULL terminated,
 *                  i.e. the last element must be ``{ NULL, NULL, NULL }``.
 */
typedef struct shell_command_t {
        const char *name; /**< Name of the function */
        const char *desc; /**< Description to print in the "help" command. */
        shell_command_handler_t handler; /**< The callback function. */
} shell_command_t;

/**
 * @brief           The internal state of a shell session.
 * @details         Use shell_init() to initialize the datum,
 *                  and shell_run() to run the REPL session.
 */
typedef struct shell_t {
        const shell_command_t *command_list; /**< The commandlist supplied to shell_init(). */
        uint16_t shell_buffer_size;          /**< The maximum line length supplied to shell_init(). */
} shell_t;

/**
 * @brief           Initialize a shell session state.
 * @param[out]      shell               The datum to initialize.
 * @param[in]       shell_commands      Null-terminated list of commands to understand.
 *                                      Supply `NULL` to only feature the default commands.
 */

void shell_init(shell_t *shell, const shell_command_t *shell_commands);

/**
 * @brief           Start the shell session.
 * @param[in]       shell   The session that was previously initialized with shell_init().
 * @returns         This function does not return.
 */
void shell_run(shell_t *shell);

void unknown_command(const char *command);
int readline(char *prompt, char *buf, size_t size);

extern const shell_command_t shell_commands[];

#endif /* __SHELL_H */

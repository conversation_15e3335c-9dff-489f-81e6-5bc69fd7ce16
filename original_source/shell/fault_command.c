#include "pelagic.h"
#include "shell.h"

void os_error (uint32_t error_code);
void mbed_assert_internal(const char *expr, const char *file, int line);

void
fault_command(int argc, char **argv)
{
        char *command = argv[1];

        if (argc == 1) {
                printf("fault hard - hard fault the board\n");
                printf("fault oserror - cause oserror\n");
                printf("fault assert - cause an assert\n");
                return;
        }

        if (strcmp(command, "fault") == 0) {
                volatile uint32_t *addr = (uint32_t *)0x1241;

                printf("hard faulting\n");
                *addr = 1;
                osDelay(1000);
                printf("WTF? should have faulted.\n");
                return;
        }

        if (strcmp(command, "oserror") == 0) {
                printf("calling os_error\n");
                os_error(1);
                return;
        }

        if (strcmp(command, "assert") == 0) {
                printf("calling assert\n");
                mbed_assert_internal("animals != screem", "animal_screem.c", 1);
                return;
        }

        unknown_command(command);
}

#include "pelagic.h"
#include "system_file.h"

#ifdef TARGET_VMS
#include "sun.h"
#endif

void
date_command(int argc, char **argv)
{
        uint32_t utc_time = rtc_read();
        uint32_t clock_time = clock_read();

#if defined(TARGET_VMS)
        if (time_acquired) {
                printf("Now     ");
                datetime_display(utc_time);
                printf("\n");
                if (sunrise) {
                        printf("Sunrise ");
                        datetime_display(sunrise);
                        printf("\n");

                        printf("Sunset  ");
                        datetime_display(sunset);
                        printf("\nIt's currently %stime\n", is_nighttime() ? "night" : "day");
                } else {
                        printf("No sun position calculated yet.\n");
                }
        } else {
                printf("No time acquired yet.\n");
        }
#elif defined(TARGET_SENSOR)
        if (utc_time < 1000000) {
                printf("No time acquired yet.\n");
        } else {
                printf("Now ");
                datetime_display(utc_time);
                printf("\n");
        }
#endif
        printf("RTC %s (rtc clock %d, uptime %d)\n", (SYS_REG_FILE->rtc_status == RTC_STATUS_USE_SOFTWARE) ? "software" : "hardware", utc_time, clock_time);
}

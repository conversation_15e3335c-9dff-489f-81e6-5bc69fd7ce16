
#include "rt_TypeDef.h"
#include "RTX_Config.h"
#include "rt_System.h"
#include "rt_Task.h"
#include "rt_List.h"
#include "rt_MemBox.h"
#include "rt_Robin.h"
#include "rt_HAL_CM.h"
#include <stdint.h>

extern void main();
extern void gps_thread();
extern void helm_thread();
extern void bnet_thread();
extern void os_idle_demon();
extern void osTimerThread();
extern void reboot_thread();
extern void modem_thread();
extern void accel_thread();

struct thread_name {
        char    *name;
        void    *func;
} thread_names[] = {
        { "main", &main },
#ifdef TARGET_VMS
        { "gps", &gps_thread },
        { "helm", &helm_thread },
#ifdef HAVE_RADIO
        { "bnet", &bnet_thread },
#endif
#ifdef HAVE_ACCEL
        { "accel", &accel_thread },
#endif
        { "reboot", &reboot_thread },
        { "modem", &modem_thread },
#endif
        { "idle", &os_idle_demon },
        { "timer", &osTimerThread }
};

static char *thread_states[] = {
        "inactive",
        "ready",
        "run",
        "delay",
        "timer",
        "evtor",    // event or
        "evtand",    // event and
        "sema",
        "mbox",
        "mutex"
};

int printf(char *fmt, ...);

static void
find_thread(void *entry)
{

        for (int i = 0; i < sizeof(thread_names)/sizeof(thread_names[0]); i++) {
                if (thread_names[i].func == entry) {
                        printf("%-8s", thread_names[i].name);
                        return;
                }
        }

        printf("%8x", entry);
}

void
ps_command(int argc, char **argv)
{
        P_TCB tcb;

        printf("# Thread   State    Prio Stack\n");

        for (int i = 0; i < os_maxtaskrun; i ++) {
                if ((tcb = os_active_TCB[i]) == NULL)
                        continue;

                printf("%d ", tcb->task_id);
                find_thread(tcb->ptask);
                printf(" %-8s %4d %8.8x (+%d)\n", thread_states[tcb->state], tcb->prio, tcb->stack, ((uint32_t) tcb->tsk_stack - (uint32_t)tcb->stack));
        }
}

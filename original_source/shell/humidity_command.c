#include "pelagic.h"
#include "humidity.h"
#include "shell.h"

void
humidity_command(int argc, char **argv)
{
        char *command = argv[1];

        if (argc == 1) {
                printf("humidity %d\n", humidity_read());
                return;
        }

        if (strcmp(command, "watch") == 0) {
                printf("hit <ESC> to stop.\n");
                while (!have_esc()) {
                        printf("humidity %d\n", humidity_read());
                        osDelay(1000);
                }
                return;
        }

        unknown_command(command);
}

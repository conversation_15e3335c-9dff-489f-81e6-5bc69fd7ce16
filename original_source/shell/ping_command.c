#include "pelagic.h"
#include "bnet.h"
#include "radio.h"

void
ping_command(int argc, char **argv)
{
        uint8_t ping_data[sizeof(bnet_ping_t) + sizeof(imei_t)];
        bnet_ping_t *ping = (bnet_ping_t *) ping_data;
        int size;

        ping->type = BNET_TYPE_PING;
        ping->sequence = 0;
        uid_set_me(ping->from_uid);

        if (argc > 1) {
                ping->ping_type = BNET_TYPE_IMEI;
                imei_to_i(argv[1], ping->destination);
                size = sizeof(bnet_ping_t) + sizeof(imei_t);
        } else {
                ping->ping_type = BNET_BROADCAST;
                size = sizeof(bnet_ping_t);
        }

        printf("pinging %s\n", argc > 1 ? "device" : "network");

        for (;;) {
                if (have_esc()) {
                        printf("finished\n");
                        return;
                }
                radio_send(ping, size);
                osDelay(1000);
                ping->sequence++;
        }
}

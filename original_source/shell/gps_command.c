#include "pelagic.h"
#include "signals.h"
#include "shell.h"
#include "gps.h"

void gps_always_locate_mode();
void gps_full_mode();
void gps_backup_mode();

extern void gps_settings_mode(int argc, char **argv);
extern void gps_settings_periodic(int argc, char **argv);

extern osThreadId gps_tid;

typedef struct {
        char        *command;
        uint16_t    state;
} gps_state_command_t;

const gps_state_command_t gps_state_commands[] = {
        { .command = "off", .state = GPS_STATE_OFF },
        { .command = "stationary", .state = GPS_STATE_STATIONARY },
        { .command = "normal", .state = GPS_STATE_NORMAL },
};

static const char *gps_track_names[] = {
        "low-res",
        "high-res",
        "periodic",
};

static const char *gps_state_names[] = {
        "normal",
        "off",
        "stationary",
};

void
gps_command(int argc, char **argv)
{
        const char *command = argv[1];

        if (argc == 1) {
                printf("No command given.\n");
                printf("gps bytes   - show GPS received bytes.\n");
                printf("gps capture - to toggle capturing of gps data.\n");
                printf("gps current - show current location\n");
                printf("gps info   - show current state\n");
                printf("gps log     - show boat log\n");
                printf("gps watch   - show location updates.\n");
                printf("gps {normal,stationary} - put GPS into stationary, or stationary mode\n");
                printf("gps off     - turn GPS off\n");
                return;
        }

        if (strcmp(command, "log") == 0) {
                boat_log_dump();
                return;
        }

        if (strcmp(command, "capture") == 0) {
                gps_capture_location = !gps_capture_location;
                printf("GPS capturing is now %s\n", gps_capture_location ? "ON" : "OFF");
                return;
        }

        if (strcmp(command, "current") == 0) {
                gps_display_location();
                return;
        }

        if (strcmp(command, "bytes") == 0) {
                printf("Displaying received GPS bytes - hit <ESC> to stop.\n");
                gps_show_bytes = 1;

                while (!have_esc())
                        osDelay(200);

                gps_show_bytes = 0;
                return;
        }

        if (strcmp(command, "info") == 0) {
                printf("have fix? %c\n", gps_have_fix ? 'Y' : 'N');
                printf("state %s tracking %s\n", gps_state_names[gps_state], gps_track_names[gps_tracking]);
                printf("idle time %d\n", gps_idle_timestamp ? rtc_read()  - gps_idle_timestamp : 0);
                return;
        }

        if (strcmp(command, "watch") == 0) {
                printf("Displaying location - hit <ESC> to stop.\n");
                gps_watch_location = 1;
                while (!have_esc())
                        osDelay(200);
                gps_watch_location = 0;

                return;
        }

        const gps_state_command_t *cmd = gps_state_commands;
        for (int i = 0; i < ARRAY_SIZE(gps_state_commands); i++, cmd++) {
                if (strcmp(cmd->command, command) == 0) {
                        printf("switching to %s\n", command);
                        gps_state_requested = cmd->state;
                        osSignalSet(gps_tid, GPS_SIGNAL_STATE_CHANGE);
                        return;
                }
        }

        unknown_command(command);
}

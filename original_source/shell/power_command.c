#include "pelagic.h"
#include "power.h"
#include "shell.h"

void
power_display()
{
        printf("battery %d, solar %d, charge %c, fault %c\n", battery_read(), solar_read(),
               battery_charge_read() ? 'Y' : 'N', battery_fault_read() ? 'Y' : 'N');
}

void
power_command(int argc, char **argv)
{
        if (argc == 1) {
                power_display();
                return;
        }

        if (strcmp("watch", argv[1]) == 0) {
                printf("Hit <ESC> to cancel\n");
                while (!have_esc()) {
                        power_display();
                        osDelay(1000);
                }

                return;
        }

        unknown_command(argv[0]);
}

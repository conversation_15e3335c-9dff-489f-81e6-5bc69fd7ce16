#include "pelagic.h"
#include "provision.h"
#include "shell.h"

void
provision_command(int argc, char **argv)
{
        char *command = argv[1];

        if (argc == 1) {
                printf("provision factory  - force factory test mode\n");
                printf("provision shipping - force wait for deployment\n");
                printf("provision deploy   - force deployment\n");
                return;
        }

        if (strcmp(command, "factory") == 0) {
                uart_printf("forcing factory state\n");
                provision_factory();
                return;
        }

        if (strcmp(command, "deploy") == 0) {
                uart_printf("forcing deployed state\n");
                provision_deployed();
                return;
        }

        if (strcmp(command, "shipping") == 0) {
                uart_printf("forcing shipping state\n");
                provision_shipping();
                return;
        }

        unknown_command(command);
}

#include "pelagic.h"
#include "modem.h"
#include "signals.h"
#include "alarm.h"
#include "shell.h"
#include "modem_thread.h"
#include "modem_log.h"
#include "board_info.h"

//! Moved to modem_bodytrace.c
volatile bool gsm_enabled = true;

void
gsm_display_status(modem_status_t *status)
{
        printf("state 0x%2.2x voltage %4d ADC %4d RSSI %3d signal %3d temp %3dC\n",
               status->state,
               status->voltage,
               status->adc,
               status->rssi,
               status->signal_strength,
               status->temperature
              );
}

int
gsm_cmd_power_on()
{
        if (modem_thread_running) {
                printf("Modem thread is running -- try again later.\n");
                return 0;
        }

        modem_thread_hold = 1;
        printf("Powering up modem (5 secs)\n");
        modem_power_on();
        return 1;
}

void
gsm_cmd_power_off()
{
        printf("powering down modem\n");
        modem_power_off();
        modem_thread_hold = 0;
}

void
gsm_command(int argc, char **argv)
{
        char *command = argv[1];
        modem_status_t status;

        if (argc == 1) {
                printf("No gsm commands given.\n");
                printf("gsm cell    - display cell signal log\n");
                printf("gsm connect - send logs, check messages, record signal.\n");
                printf("gsm console - connect to modem directly\n");
                printf("gsm hold    - toggle modem activity\n");
                printf("gsm info    - display info\n");
                printf("gsm log     - display connect log\n");
                printf("gsm poll    - continuously display status\n");
                printf("gsm save    - toggle saving uploads to host fs (requires openocd)\n");
                printf("gsm status  - retrieve status\n");
                printf("gsm sendtest    - send modem TEST message (for firmware upgrade)\n");
                printf("gsm watch   - toggle debugging display\n");
                printf("gsm on      - turn the modem on\n"
                       "gsm off     - turn the modem off\n"
                       "gsm check   - do the check_for_network dance\n"
                       "gsm disable - do NOT power on the modem before doing things\n"
                       "gsm enable  - do power on the modem before doing things\n");
                return;
        }

        if (strcmp(command, "disable") == 0) {
                gsm_enabled = false;
                return;
        }

        if (strcmp(command, "enable") == 0) {
                gsm_enabled = true;
                return;
        }

        if (strcmp(command, "on") == 0) {
                gsm_cmd_power_on();
                return;
        }

        if (strcmp(command, "off") == 0) {
                gsm_cmd_power_off();
                return;
        }

        if (strcmp(command, "check") == 0) {
                modem_check_for_network();
                return;
        }

        if (strcmp(command, "watch") == 0) {
                modem_watch = !modem_watch;
                printf("Display modem debugging is now %s\n", modem_watch ? "ON" : "OFF");
                return;
        }


        if (strcmp(command, "status") == 0) {
                int result;

                if (!gsm_cmd_power_on())
                        return;

                result = modem_get_status(&status);
                if (result == MODEM_SUCCESS) {
                        gsm_display_status(&status);
                } else {
                        printf("Failed to get status result [%d]\n", result);
                }
                gsm_cmd_power_off();
                return;
        }

        if (strcmp(command, "poll") == 0) {
                if (!gsm_cmd_power_on())
                        return;

                printf("press <ESC> to stop.\n");
                while (!have_esc()) {
                        int result;
                        result = modem_get_status(&status);
                        if (result == MODEM_SUCCESS) {
                                gsm_display_status(&status);
                        } else {
                                printf("Failed to get status result [%d]\n", result);
                        }

                        osDelay(2000);
                }

                gsm_cmd_power_off();
                return;
        }

        if (strcmp(command, "connect") == 0) {
                printf("attempting to connect to network (signaling modem thread)\n");
                osSignalSet(modem_tid, MODEM_SIGNAL_FORCE);
                return;
        }

        if (strcmp(command, "info") == 0) {
                printf("modem %s\n", modem_thread_running ? "active" : "sleeping");

                if (board_info_have_imei) {
                        uint64_t imei = get_uint64(board_info_imei);
                        printf("IMEI %6.6d%9.9d\n", (uint32_t)(imei / 1000000000),
                               (uint32_t)(imei % 1000000000));
                } else {
                        printf("modem info has not been retrieved yet.\n");
                }

                uint32_t clock = clock_read();

                if (modem_last_power_up) {
                        printf("last power up: %d secs ago\n", clock - modem_last_power_up);
                } else {
                        printf("never powered up\n");
                }

                if (modem_last_contact) {
                        printf("last contact: %d secs ago\n", clock - modem_last_contact);
                } else {
                        printf("no successful contact\n");
                }

                return;
        }

        if (strcmp(command, "cell") == 0) {
                printf("cell signal log:\n");
                cell_signal_dump();
                return;
        }

        if (strcmp(command, "log") == 0) {
                modem_log_display();
                return;
        }

        if (strcmp(command, "hold") == 0) {
                modem_thread_hold = !modem_thread_hold;
                printf("modem activity %s\n", modem_thread_hold ? "holding" : "released");
                return;
        }

        if (strcmp(command, "sendtest") == 0) {
                uint8_t packet[] = { 0xaa };
                modem_result_t result;

                if (!gsm_cmd_power_on())
                        return;

                modem_watch = true;
                result = modem_send_test(packet, sizeof(packet));

                if (result != MODEM_SUCCESS) {
                        printf("send test failure = %d\n", result);
                } else {
                        printf("send test success\n");
                }
                modem_watch = false;
                gsm_cmd_power_off();

                return;
        }

        if (strcmp(command, "test") == 0) {
                modem_result_t result;
                uint32_t start;

                if (argc != 3) {
                        printf("usage: gsm test <message-count>\n");
                        return;
                }

                start = clock_read();
                if (!gsm_cmd_power_on())
                        return;

                int messages = atoi(argv[2]);
                uint32_t xmit_start = clock_read();
                bool fail = false;
                printf("Sending %d messages\n", messages);
                for (int i = 0; i < messages; i++) {
                        printf("message %d ", i);
                        memset(the_buffer.test, 0xff, 1000);
                        result = modem_send_data(the_buffer.test,1000, MODEM_WAIT_NONE);

                        if (result != MODEM_SUCCESS) {
                                printf("send test failure = %d\n", result);
                                fail = true;
                                break;
                        } else {
                                printf("ok\n");
                        }
                }

                if (fail == false) {
                        printf("waiting for xmit to finish\n");
                        modem_xmit_wait(MODEM_WAIT);
                }
                printf("done - xmit in %d seconds, total %d secs\n", clock_read() - xmit_start, clock_read() - start);
                gsm_cmd_power_off();

                return;
        }

        if (strcmp(command, "console") == 0) {
                int bytes;
                bool echo = false;
                char buf[10];

                modem_thread_hold = true;

                if (argc == 3)
                        echo = true;

                printf("powering up modem (10 secs)\n");
                modem_power_on();
                osDelay(1000);
                //modem_pkey_on();
                serial_flush(&modem_port);
                printf("start typing. hit <ESC> when finished.\n");

                for (;;) {
                        if (serial_available(&modem_port)) {
                                bytes = serial_read(&modem_port, buf, sizeof(buf));
                                serial_write(&console_uart, buf, bytes);
                        }

                        if (serial_available(&console_uart)) {
                                bytes = serial_read(&console_uart, buf, sizeof(buf));
                                for (int i = 0; i < bytes; i++) {
                                        if (buf[i] == '\033') {
                                                printf("\nfinished - powering down modem.\n");
                                                //modem_pkey_off();
                                                modem_power_off();
                                                modem_thread_hold = false;
                                                return;
                                        }
                                }

                                serial_write(&modem_port, buf, bytes);

                                if (echo)
                                        serial_write(&console_uart, buf, bytes);
                        }

                        osDelay(1);
                }
        }

        unknown_command(command);
}

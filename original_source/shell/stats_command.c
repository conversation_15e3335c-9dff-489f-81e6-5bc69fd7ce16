#include "pelagic.h"

void
stats_command(int argc, char **argv)
{
        printf("gps sentences %d crc errors %d\n", gps_stats.sentences, gps_stats.crc_errors);
        printf("boat log xmits %d event log xmits %d\n", log_stats.boat_log_transmits, log_stats.event_transmits);
        printf("\nuart ints       tx-bytes   tx-dma rx-bytes   rx-dma  rx-ovrfl\n");
        for (int i = 0 ; i < 3; i++) {
                printf("%4d %10d %7d %10d %7d %d\n",
                       i, serial_stats[i].interrupts,
                       serial_stats[i].tx_bytes, serial_stats[i].dma_tx_interrupts,
                       serial_stats[i].rx_bytes, serial_stats[i].dma_rx_interrupts, serial_stats[i].rx_overflow);
        }
}

#include "pelagic.h"
#include "accel.h"
#include <math.h>


void
accel_command(int argc, char **argv)
{
        while (!have_esc()) {
                printf("x=%4d, y=%4d, z=%4d\n", accel_current.x, accel_current.y, accel_current.z);
                printf("heading %3d, raw %3d pitch=%3d, roll=%3d\n",  heading_current, device_raw_heading, device_pitch, device_roll);
                printf("capsized? %d\n", device_capsized);
                osDelay(1000);
        }
}

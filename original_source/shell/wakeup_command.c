#include "pelagic.h"
#include "bnet.h"
#include "radio.h"

void
wakeup_command(int argc, char **argv)
{
        uint8_t wakeup_data[sizeof(bnet_sensor_wakeup_t) + sizeof(imei_t)];
        bnet_sensor_wakeup_t *wakeup = (bnet_sensor_wakeup_t *) wakeup_data;

        wakeup->type = BNET_TYPE_SENSOR_WAKEUP;
        wakeup->sensor_type = 0x0E;
        uid_set_me(wakeup->from_uid);

        uid_clear(wakeup->to_uid);
        if (argc > 1) {
                str_to_uid(argv[1], wakeup->to_uid);
        }


        printf("waking sensors\n");

        for (;;) {
                if (have_esc()) {
                        printf("finished\n");
                        return;
                }
                radio_send(wakeup, sizeof(bnet_sensor_wakeup_t));
                osDelay(1000);
        }
}

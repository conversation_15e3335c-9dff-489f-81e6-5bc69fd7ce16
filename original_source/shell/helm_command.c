#include "pelagic.h"
#include "helm.h"
#include "signals.h"
#include "shell.h"

extern void helm_info();

typedef struct {
        char        *name;
        char        *desc;
        uint16_t    action;
} signal_cmd_t;

static signal_cmd_t helm_signal_cmds[] = {
        { .name = "sun", .desc = "sunrise/sunset position", .action = HELM_ACTION_SUN_POS },
        { .name = "power", .desc = "power manage", .action = HELM_ACTION_POWER_MANAGE },
        { .name = "log", .desc = "board log", .action = HELM_ACTION_BOARD_LOG },
        { .name = "cycle", .desc = "radio power cycle", .action = HELM_ACTION_POWER_CYCLE  }
};

void
helm_command(int argc, char **argv)
{
        char *command = argv[1];
        int i;
        signal_cmd_t *cmd;

        if (argc == 1) {
                printf("helm jobs - display jobs\n");
                printf("helm watch - toggle display\n");
                for (i = 0, cmd = helm_signal_cmds; i < ARRAY_SIZE(helm_signal_cmds); i++, cmd++)
                        printf("helm %s - %s\n", cmd->name, cmd->desc);
                return;
        }

        if (strcmp(command, "jobs") == 0) {
                helm_info();
                return;
        }

        if (strcmp(command, "watch") == 0) {
                helm_watch = !helm_watch;
                printf("Display helm debug now %s\n", helm_watch ? "ON" : "OFF");
                return;
        }

        for (i = 0,  cmd = helm_signal_cmds; i < ARRAY_SIZE(helm_signal_cmds); i++, cmd++) {
                if (strcmp(command, cmd->name) == 0) {
                        printf("Signaling helm thread to %s\n", cmd->desc);
                        helm_actions |= cmd->action;
                        osSignalSet(helm_tid, HELM_SIGNAL_ACTION);
                        return;
                }
        }

        unknown_command(command);
}

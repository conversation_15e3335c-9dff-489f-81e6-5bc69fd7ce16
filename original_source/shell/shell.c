/**
 * @ingroup     shell
 * @{
 */

/**
 * @file
 * @brief       Implementation of a very simple command interpreter.
 *              For each command (i.e. "echo"), a handler can be specified.
 *              If the first word of a user-entered command line matches the
 *              name of a handler, the handler will be called with the whole
 *              command line as parameter.
 *
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 */

#include "pelagic.h"
#include "console.h"
#include "shell.h"

#ifdef TARGET_VMS
char shell_prompt[] = "vms>";
#elif defined(TARGET_SENSOR)
char shell_prompt[] = "sensor>";
#elif defined(TARGET_CONSOLE)
char shell_prompt[] = "console>";
#else
char shell_prompt[] = ">";
#endif

static shell_command_handler_t
find_handler(const shell_command_t *command_list, char *command)
{
        const shell_command_t *entry = command_list;

        while (entry->name != NULL) {
                if (strcmp(entry->name, command) == 0) {
                        return entry->handler;
                } else {
                        entry++;
                }
        }

        return NULL;
}

static void
print_help(const shell_command_t *command_list)
{
        const shell_command_t *entry = command_list;
        printf("%-20s %s\n", "Command", "Description");
        puts("---------------------------------------");


        while (entry->name != NULL) {
                printf("%-20s %s\n", entry->name, entry->desc);
                entry++;
        }
}

static const char INCORRECT_QUOTING[] = "shell: incorrect quoting";

static void
handle_input_line(shell_t *shell, char *line)
{

        /* first we need to calculate the number of arguments */
        unsigned argc = 0;
        char *pos = line;
        int contains_esc_seq = 0;
        while (1) {
                if ((unsigned char) *pos > ' ') {
                        /* found an argument */
                        if (*pos == '"' || *pos == '\'') {
                                /* it's a quoted argument */
                                const char quote_char = *pos;
                                do {
                                        ++pos;
                                        if (!*pos) {
                                                puts(INCORRECT_QUOTING);
                                                return;
                                        } else if (*pos == '\\') {
                                                /* skip over the next character */
                                                ++contains_esc_seq;
                                                ++pos;
                                                if (!*pos) {
                                                        puts(INCORRECT_QUOTING);
                                                        return;
                                                }
                                                continue;
                                        }
                                } while (*pos != quote_char);
                                if ((unsigned char) pos[1] > ' ') {
                                        puts(INCORRECT_QUOTING);
                                        return;
                                }
                        } else {
                                /* it's an unquoted argument */
                                do {
                                        if (*pos == '\\') {
                                                /* skip over the next character */
                                                ++contains_esc_seq;
                                                ++pos;
                                                if (!*pos) {
                                                        puts(INCORRECT_QUOTING);
                                                        return;
                                                }
                                        }
                                        ++pos;
                                        if (*pos == '"') {
                                                puts(INCORRECT_QUOTING);
                                                return;
                                        }
                                } while ((unsigned char) *pos > ' ');
                        }

                        /* count the number of arguments we got */
                        ++argc;
                }

                /* zero out the current position (space or quotation mark) and advance */
                if (*pos > 0) {
                        *pos = 0;
                        ++pos;
                } else {
                        break;
                }
        }
        if (!argc) {
                return;
        }

        /* then we fill the argv array */
        char *argv[argc + 1];
        argv[argc] = NULL;
        pos = line;
        for (unsigned i = 0; i < argc; ++i) {
                while (!*pos) {
                        ++pos;
                }
                if (*pos == '"' || *pos == '\'') {
                        ++pos;
                }
                argv[i] = pos;
                while (*pos) {
                        ++pos;
                }
        }
        for (char **arg = argv; contains_esc_seq && *arg; ++arg) {
                for (char *c = *arg; *c; ++c) {
                        if (*c != '\\') {
                                continue;
                        }
                        for (char *d = c; *d; ++d) {
                                *d = d[1];
                        }
                        if (--contains_esc_seq == 0) {
                                break;
                        }
                }
        }

        /* then we call the appropriate handler */
        shell_command_handler_t handler = find_handler(shell->command_list, argv[0]);
        if (handler != NULL) {
                handler(argc, argv);
        } else {
                if (strcmp("help", argv[0]) == 0) {
                        print_help(shell->command_list);
                } else {
                        unknown_command(argv[0]);
                }
        }
}

int
readline(char *prompt, char *buf, size_t size)
{
        char *line_buf_ptr = buf;

        printf("%s ", prompt);

        while (1) {
                if ((line_buf_ptr - buf) >= ((int) size) - 1) {
                        return -1;
                }

                char c = console_getchar();
                if (c == 0) {
                        return 1;
                }

                /* We allow Unix linebreaks (\n), DOS linebreaks (\r\n), and Mac linebreaks (\r). */
                /* QEMU transmits only a single '\r' == 13 on hitting enter ("-serial stdio"). */
                /* DOS newlines are handled like hitting enter twice, but empty lines are ignored. */
                if (c == '\r')
                        continue;

                if (c == '\n') {
                        printf("\n");
                        *line_buf_ptr = '\0';

                        if (line_buf_ptr == buf) {
                                printf("%s ", prompt);
                                /* The line is empty. */
                                continue;
                        }
                        return 0;
                }
                /* QEMU uses 0x7f (DEL) as backspace, while 0x08 (BS) is for most terminals */
                else if (c == 0x08 || c == 0x7f) {
                        if (line_buf_ptr == buf) {
                                /* The line is empty. */
                                continue;
                        }

                        *--line_buf_ptr = '\0';
                        /* white-tape the character */
                        printf("\b \b");
                } else if (c == 21) {
                        // Control U - erase the entire line.
                        while (line_buf_ptr != buf) {
                                printf("\b \b");
                                *--line_buf_ptr = '\0';
                        }
                } else if (c == 12) {   // Control-L, redisplay line
                        printf("\n%s", prompt);
                        console_write(buf, line_buf_ptr - buf);
                        console_flush();
                } else if (c == '\033') {
                        line_buf_ptr = buf;
                        printf("\n%s", prompt);
                } else {
                        *line_buf_ptr++ = c;
                        printf("%c", c);
                }
        }
}

void
shell_run(shell_t *shell)
{
        char line_buf[shell->shell_buffer_size];

        do {
                int res = readline(shell_prompt, line_buf, sizeof(line_buf));

                if (!res) {
                        handle_input_line(shell, line_buf);
                }
        } while (1);
}

void
shell_init(shell_t *shell, const shell_command_t *shell_commands)
{
        shell->command_list = shell_commands;
        shell->shell_buffer_size = 80;
}

void
unknown_command(const char *command)
{
        printf("unknown command %s\n", command);
}

/** @} */

#include "pelagic.h"
#include "shell.h"

void bnet_ping_command(int argc, char **argv);
void date_command(int argc, char **argv);
#ifdef HAVE_DIP
void dip_command(int argc, char **argv);
#endif
void event_command(int argc, char **argv);
void info_command(int argc, char **argv);
void net_command(int argc, char **argv);
void ps_command(int argc, char **argv);
void reboot_command(int argc, char **argv);
void temp_command(int argc, char **argv);

const shell_command_t shell_commands[] = {
    { "date", "display date, sunrise & set", date_command },
#ifdef HAVE_DIP
    { "dip", "show dip switches", dip_command },
#endif
    { "event", "display the most recent events", event_command },
    { "info", "buildstamp, and tag", info_command },
    { "net", "net commands", net_command },
    { "ps", "thread listing", ps_command },
    { "reboot", "reboot board", reboot_command },
    { "temp", "display temperature", temp_command },
    { NULL, NULL, NULL }
};

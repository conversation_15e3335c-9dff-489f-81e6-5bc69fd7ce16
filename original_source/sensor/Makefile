PROJECT=sensor
PROJECT_DIR=sensor
TARGET=SENSOR

BUILD_FLAGS += -DOS_TASKCNT=2

# main logic & support
SRC +=	main.c \
		bnet_common.c \
		bnet_sensor_common.c \
		bnet_sensor_work.c \
		bnet_sensor_firmware.c \
		bnet_sensor_bond.c \
		bnet_sensor_report.c \
		console.c \
		datetime.c \
		firmware_sensor.c \
		firmware_update.c \
		thermistor.c

# shell commands
SRC += 	sensor_shell.c \
		date_command.c \
		dip_command.c \
		event_command.c \
		info_command.c \
		net_command.c \
		ps_command.c \
		reboot_command.c \
		temp_command.c


-include ../Makefile.common

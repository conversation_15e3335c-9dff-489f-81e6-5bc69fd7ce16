#include "pelagic.h"

#include "gpio_api.h"
#include "gpio_irq_api.h"
#include "clk_freqs.h"
#include "shell.h"

#include "semihost_api.h"
#include "firmware.h"
#include "console.h"
#include "dip.h"
#include "led.h"
#include "bnet.h"
#include "bnet_sensor.h"
#include "temp.h"
#include "board_fault.h"
#include "us_ticker_api.h"
#include "system_file.h"
#include "radio.h"
#include "alarm.h"
#include "mcu_sleep.h"

enum {
    NORMAL_SENSOR_WAKEUP    = (5*60),
    FAST_SENSOR_WAKEUP      = 5,
};

// Used by multiple locations
shared_buffer_t the_buffer __attribute__ (( aligned (4) ));

shell_t shell;

void random_setup_seed();

uint16_t sensor_slept;

int main() {
    sys_file_init();

#ifdef HAVE_DIP
    uint32_t start_time = us_ticker_read();
#endif

    serial_console_init(false); // leave console powered off

    rtc_init();

    event_show = false;

#ifdef HAVE_DIP
    dip_init();
#endif

#ifdef HAVE_LED
    led_init();
#endif

    uid_init();

    bnet_packet_watch = false;


#ifdef HAVE_DIP
    if (dip_on(DIP1) || dip_on(DIP2) || dip_on(DIP3)) {
        event_show = true;
        bnet_packet_watch = true;
        serial_power_on(&console_uart);
        announce("Sensor");
    }
#endif

    board_fault_check();
    random_setup_seed();

    thermistor_init();

    radio_init(true);

#ifdef HAVE_DIP
    if (dip_on(DIP2)) {
        uart_printf("power down elapsed %d usec\n", us_ticker_read() - start_time);
        osDelay(50);
        sensor_slept = FAST_SENSOR_WAKEUP;
    } else
#endif
    sensor_slept = NORMAL_SENSOR_WAKEUP;

#ifdef HAVE_DIP
    if (dip_on(DIP1)) {
        shell_init(&shell, shell_commands);
        shell_run(&shell);
    }

    if (dip_on(DIP3)) {
        bnet_packet_watch = true;
        for (;;) {
            //uart_printf("-----\n");
            bnet_sensor_work();
            osDelay(500);
        }
    } else
#endif
    bnet_sensor_work();

    radio_power_off();

    SYS_REG_FILE->reboot_reason = REBOOT_NORMAL;
    mcu_sleep(sensor_slept);
    // never to be seen again!
}

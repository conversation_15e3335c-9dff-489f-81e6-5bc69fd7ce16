/** @{

    @ingroup    sensor
    @file

    @brief      Sensor firmware update
*/

#include "pelagic.h"
#include "memmap.h"
#include "firmware_sensor.h"

enum {
    FIRMWARE_CACHE_SIZE = 128
};

static uint32_t firmware_offset;
static uint8_t firmware_cache[FIRMWARE_CACHE_SIZE];
static volatile int firmware_cache_offset;

/**
    @brief  erase the download firmware area in flash
*/

void firmware_erase() {
    firmware_offset = 0;
    firmware_cache_offset = 0;

    for (int offset = 0; offset < FIRMWARE_SIZE; offset += KINETIS_SECTOR_SIZE)
        kinetis_flash_erase_sector(FIRMWARE_ADDR + offset);
}

/**
    @brief  store data into the download firmware area
    @param[in]  data    new firmware to store
    @param[in]  length  byte count
*/

void firmware_write(uint8_t *data, int length) {
    int bytes;

    while (length > 0) {
        bytes = FIRMWARE_CACHE_SIZE - firmware_cache_offset;
        if (bytes > length)
            bytes = length;

        memcpy(&firmware_cache[firmware_cache_offset], data, bytes);
        firmware_cache_offset += bytes;
        length -= bytes;
        data += bytes;

        if (firmware_cache_offset >= FIRMWARE_CACHE_SIZE) {
            firmware_flush();
        }
    }
}

/**
    @brief  flush the download firmware staging buffer to flash
*/

void firmware_flush() {
    if (firmware_cache_offset == 0)
        return;

    for (int i = firmware_cache_offset; i < FIRMWARE_CACHE_SIZE; i++)
        firmware_cache[i] = 0;

    kinetis_flash_write(FIRMWARE_ADDR + firmware_offset, firmware_cache, FIRMWARE_CACHE_SIZE);
    firmware_offset += FIRMWARE_CACHE_SIZE;
    firmware_cache_offset = 0;
}

/** @} */

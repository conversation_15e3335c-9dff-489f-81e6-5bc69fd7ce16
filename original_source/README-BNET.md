BNET - BoatOS Networking {#bnet-main}
========================

[TOC]

General Overview
================

The BNET protocol is a fairly simple synchronous star network implementation.

The Freescale MCU serial unique id, 10 bytes, is used a unique node id.

The reasoning to use the longer MCU id is no extra work is needed to assign and track network ids.

The first byte in the packet is always the packet type. The high bit set (0x80) indicates an acknowledgement.

The packet also consists of one of the following:

- From UID identifier - a broadcast packet from a node to the entire network
- To UID & From UID identifier pair - a node-to-node packet
- To UID identifier indicate an ack back from an anonymous node (Deprecated - only for BNET_TYPE_OLD_TEMP,
  first BNET version, and only used in PT Bali.)

Some packet may have a sequence number to ensure stream order. The remainder of the packet is the payload.

The cc1200 and other like radios use automatic CRC checksumming for packet validity. The upper layers
of BNET assume the packet was received without transmission errors.

Sensor Bonding And Report {#sensor-bond}
=========================

The sensor uses a bonding method to communicate with a VMS. The sensor will set the radio to a
low transmission power level, and begin broadcast a bond request to the network. Currently,
2 attempts are made and if no response is received, the radio is set to the next power level up,
and 2 more attempts are tried. This is repeated until a response is received or an upper limit
is received.

If no response is received at all, the sensor goes back to sleep for roughly 5
minutes and the cycle is repeated again.

If a response is received, the sensor "bonds" with the device. It stores the VMS ID and
radio transmission power level used in the System Register File (a persistent RAM area
retained across sleeps but not power cycles) and a time-to-live counter is set for 6 hours.

In theory, the sensor will bond with a VMS that is closest to it by using the
less amount of transmission power. More the power used, the more likely the sensor will contact
more VMSes that are close by.

The following happens after a successful bond:

- A sensor report is made (i.e. thermistor reading sent) and waits for an acknowledgement
- A firmware check is made. The sensor sends the firmware's buildstamp to the VMS,
  and waits for ack. The VMS will respond with a packet that indicates there is a newer
  firmware image available or not.
- If a new image is available, the sensor will start the firmware download, and upon successful
  download, verify the image contents, install it, and reboot. This will cause a new bond
  cycle to start again.
 - If no new image is found, the sensor will go to sleep for 5 minutes.

Nothing is tracked on the VMS side, its happy to log sensor data from any device.

Sensor Wakeup After Previous Successful Bond {#sensor-wakeup}
=============================================

After a 5 minute sleep, the sensor, if bonded successful in the previous wakeup, will
just simply send a sensor report to the VMS uid and use the power level stored in the system
register file. An ack is waited for. Up to 10 attempts are made.

When the sensor does not received an acknowledgement, it will start the bond cycle over again
repeating the sequence described previously.

When the sensor wakes up after 6 hours of the last successful bond, the bond will be "re-evaluated".
That is the sensor will NOT start a new bond progress again. Instead, the power level will be
set low, a sensor report is attempted for 10 tries, every 2 tries will increase the power level.
If an acknowledgement is received from the VMS at any power level, the new power level is stored,
another firmware check is requested, and the sensor will go back to sleep.

If no acknowledgement is received at any power level, the sensor will repeat the bond cycle.

By re-evaulating the power level, the sensor could in theory use less power in later attempts,
and maintain a connection with a specific VMS that is actually located on the same boat. The boat
might be in a noisy RF environment at dock where the sensor has to scream at high power to be heard,
but at sea the environment might be quiet and the sensor can use less power to whisper.

Network Ping {#ping}
============

BNET supports three type of pings to identify VMSes listening in a location. Any matching
VMS will respond will a "pong" packet.

- Broadcast Ping - all VMSes should respond that receive the packet
- Target MCU UID - the VMS that matches the UID should respond (not really used)
- Target IMEI - All VMS which matches a partial IMEI should respond. (used by the OTA Console and
    targeted factory test)

A partial IMEI number is one which is scanned from the right and going until the last
digit. For example, a partial IMEI of 1234 will match 99991234 but not 12349999.

The Pong response contains the initialing ping's UID, the board's UID, and an optional
IMEI if the board has a GSM.

Over The Air (OTA) Console {#ota-console}
==========================

The Over The Air (OTA) Console provides an interactive shell over the radio
interface to a targeted VMS.

The console has several  packets sub-types all under the BNET_TYPE_CONSOLE type.

All console packet addressing is UID based. A partial IMEI ping is used to
discover the target's UID.

The target VMS is send a BNET_CONSOLE_OPEN subtype packet and waits for an
ack response. Up to 10 attempts are made with a second delay between each
attempt.

The target VMS will respond with a BNET_CONSOLE_OPEN_ACK if it does not
already have a connection. Otherwise a BNET_CONSOLE_BUSY is sent.

Both the client and target will start sending BNET_CONSOLE_SEND packets.
A client send packet is keyboard input for the target VMS. A VMS send packet
is console text. Each packet contains an incrementing sequence number.

Each send packet is expected to be acknowledge with the sequence number echo'ed
back. All packets are synchronous.

There are 10 attempts made for a packet acknowledgement. If no acks are received,
the connection will be closed.

While the client has the connection open, a BNET_CONSOLE_KEEPALIVE packet is
sent every 30 seconds to maintain the connection. Up to 20 attempts, each
waiting up to 1 second for an ack, are made. A no response will close the
connection.

OTA Console Image {#ota-console-image}
=================

To compile a console image:

$ make VERS=10 console

Any board version is supported. Flash the image onto the board and on the serial
port a "console> " prompt should be displayed.

To connect to a target board - type "c IMEI" where IMEI is a partial or full IMEI
number.

<CTRL>-C is used to close the connection.

Factory Test {#factory-test}
============

The factory test (FT) is used by the factory (duh) to test a board. The hardware
setup is a testbox board (a board with a radio, LEDs, and serial debugging port)
running the testbox image, and a target VMS.

The FT protocol is very simplistic - it consists of a start test, update status,
and report results.

The VMS image loaded onto a board should be compiled with the FT code (make FACTORY_TEST=1).
The image will wait until solar is present and then start waiting for FT start packets to
be received.

A testbox will continuously send a BNET_TYPE_FACTORY_TEST_START for any VMS in the area
- OR -  BNET_TYPE_FACTORY_TEST_TARGET will be used target a specific VMS with a given
IMEI.

When the VMS receives a matching packet, it will respond with BNET_TYPE_FACTORY_BEGIN
and wait for an acknowledgement from the testbox. After acknowledgement, the
device will begin the tests.

The target VMS will send out periodic BNET_TYPE_FACTORY_UPDATE packets. This indicates
the device is still running the tests, and gives an ascii message for debugging purposes
on what's happening. The VMS should send out a packet within 30 seconds, otherwise the
testbox will consider the board to have failed the tests.

Each update packet should be acknowledged back by the testbox.

Regardless if all tests passed or a single test failed, the BNET_TYPE_FACTORY_FINISH
packet is sent by the VMS with the final test result code. An acknowledgement should
be sent by the testbox. The VMS will go back to sleep as indicated by the acknowledgement
packet, and the testbox will start the next test start packet cycle after a long delay
or by user intervention.

Future areas for research {#future}
==========================

- Move to 6LOWPAN as an underlying network protocol
    * Would allow to leverage some interesting possibilities including
      the ability to remotely connect to a VMS using an edge router
      that is at the dock and has access to a stable internet connection.
    * The IMEI number could be used to assign node ids. The 6LOWPAN addressing
      only uses the lower 64-bits of the 128-bit IPV6 ip addressing scheme
      (the lower 64-bits is the node id, the upper 64-bit is the network)
      The IMEI could be shifted up by several bits to provide around 10,000 unique
      ids for each VMS that it could hand out to sensors.
 - Channel hopping to split up the spectrum in docks with high number of VMSes & sensors
    * how does a sensor rendezvous with a VMS when multiple channels are involved that
      doesn't burn thru power?
    * how does a VMS decide which channel to land and stay on?

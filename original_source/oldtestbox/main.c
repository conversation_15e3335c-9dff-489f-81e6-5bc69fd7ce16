/** @{

    @ingroup    testbox
    @file

    @brief      Test Box main thread
*/

#include "pelagic.h"
#include "semihost_api.h"
#include "console.h"
#include "led.h"
#include "bnet.h"
#include "board_fault.h"
#include "system_file.h"
#include "radio.h"
#include "alarm.h"
#include "test_server.h"
#include "shell.h"

// Used by multiple locations
shared_buffer_t the_buffer __attribute__ (( aligned (4) ));

extern void random_setup_seed();

void abort_thread(void const *arg);
osThreadDef(abort_thread, osPriorityNormal, 1, 0);
osThreadId abort_tid;

osThreadId main_tid;

void test_server_loop();
void printf_init();

imei_t target_imei;

int main() {
    serial_console_init(true);

    char imei[16];

    rtc_init();
    uid_init();

    event_show = false;

    announce("Test Box");

    main_tid = osThreadGetId();

    abort_tid = osThreadCreate(osThread(abort_thread), NULL);

    random_setup_seed();

    radio_init(true);
    radio_set_rx_mode(RADIO_RX_MODE_HIGH);

#ifdef HAVE_LED
    led_init();
#endif

    serial_flush(&console_uart);

    for (;;) {
        readline("\nTarget IMEI?", imei, 16);
        if (!imei_valid_str(imei))
            continue;
        imei_to_i(imei, target_imei);
        printf("\n\nAttempting to start\n");

        test_server_loop();
    }
}

/** @} */

ARGS=DEVICE_VERSION=$(VERS)

# ifneq ($(RADIO),)
# ARGS+= RADIO=$(RADIO)
# endif

ifneq ($(FACTORY_TEST),)
ARGS+= FACTORY_TEST=1
endif

ifneq (,$(<PERSON><PERSON><PERSON><PERSON><PERSON>OA<PERSON>))
  ifeq (,$(filter $(MAKE<PERSON><PERSON><PERSON>OA<PERSON>),all clean clobber superclean test buildall doc deploy release_blurb))
    ifeq ($(VERS),)
      $(error please specify the hardware version you are building for. e.x.:  )
    endif
  endif
endif

# Buildstamp
export BUILDSTAMP = `date -u '+%y%m%d%H%M'`
BUILDSTAMPFILE = .buildstamp

.PHONY: all vms ft sensor testbox console doc buildall \
	clobber superclean clean \
	deploy deployable release_blurb \
	$(BUILDSTAMPFILE)

all:
	@echo "make VERS=x vms       - make VMS image"
	@echo "make VERS=x FACTORY_TEST=1 vms      - make VMS image with factory tests"
	@echo "make VERS=x STANDALONE=1 vms        - make VMS image with just factory tests"
	@echo "make VERS=x vms-flash - flash VMS image"
	@echo "make VERS=x vms-flash-erase - erase then flash VMS"
	@echo "make VERS=x vms-clean - remove image & objs"
	@echo "make VERS=x sensor     - make sensor image"
	@echo "make VERS=x sensor-flash  - flash sensor image"
	@echo "make VERS=x sensor-clean  - remove image & objs"
	@echo "make VERS=x testbox       - make testbox image"
	@echo "make VERS=x testbox-flash - flash testbox image"
	@echo "make VERS=x testbox-clean - remove image & objs"
	@echo "make VERS=x console       - make console image"
	@echo "make VERS=x console-flash - flash console image"
	@echo "make VERS=x console-clean - remove image & objs"
	@echo "where x is 04 = V.04, Jan '15, 05 = V.05, Apr '15, 10 = V1.0, May '15, 12 = V1.2, Nov '15, 125 = V1.25, Dec '16"
	@echo "options: QUIET=[0|1]"
	@echo "         STACK_USAGE=[0|1]"
	@echo "         FACTORY_TEST=[0|1]"

$(BUILDSTAMPFILE):
	@echo $(BUILDSTAMP) > $@

buildall: $(BUILDSTAMPFILE)
	for radio in cc1200; do \
		( make VERS=125 -j4 vms || exit 1) && \
		( make VERS=125 -j4 console || exit 1) && \
		( make VERS=125 -j4 testbox || exit 1) \
	done

deploy: deployable release_blurb $(BUILDSTAMPFILE)
	$(MAKE) clean
	@for version in 05 10 12 125 13; do \
		( $(MAKE) VERS=$$version -j4 vms || exit 1 ) \
	done
	tools/upload.sh

deployable:
	tools/deployable.sh

release_blurb:
	awk -v tag="$$(git tag --contains)" -f tools/relnotes.awk RELEASE_NOTES.md > $@
	@echo "Created relase_blurb, please check below..."
	@echo
	@cat $@
	@echo
	@echo "!!!"
	@echo "!!!  If the above is incorrect, Ctrl-C now!!!"
	@echo "!!!"
	@sleep 5

vms: $(BUILDSTAMPFILE)
	@cd vms; make $(ARGS) build

# e.g. `make vms-build` does `cd vms; make build`
vms-%: $(BUILDSTAMPFILE)
	@cd vms; make $(ARGS) $(subst vms-,,$@)

sensor: $(BUILDSTAMPFILE)
	@cd sensor; make $(ARGS) build

sensor-%: $(BUILDSTAMPFILE)
	@cd sensor; make $(ARGS) $(subst sensor-,,$@)

console: $(BUILDSTAMPFILE)
	@cd console; make $(ARGS) build

console-%: $(BUILDSTAMPFILE)
	@cd console; make $(ARGS) $(subst console-,,$@)

testbox: $(BUILDSTAMPFILE)
	@cd testbox; make $(ARGS) build

testbox-%: $(BUILDSTAMPFILE)
	@cd testbox; make $(ARGS) $(subst testbox-,,$@)

oldtestbox: $(BUILDSTAMPFILE)
	@cd oldtestbox; make $(ARGS) build

oldtestbox-%: $(BUILDSTAMPFILE)
	@cd oldtestbox; make $(ARGS) $(subst oldtestbox-,,$@)

test:
	@cd test; make test

doc:
	@cd doc; doxygen boatos.doxyfile

clean:
	rm -rf images

clobber superclean: clean
	rm -rf objs images $(BUILDSTAMPFILE)

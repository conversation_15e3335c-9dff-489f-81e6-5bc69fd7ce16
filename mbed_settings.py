"""
mbed SDK
Copyright (c) 2016 ARM Limited

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""

from os.path import join, abspath, dirname

#ROOT = abspath(join(dirname(__file__), "."))

##############################################################################
# Build System Settings
##############################################################################
#BUILD_DIR = abspath(join(ROOT, "build"))

# ARM
#ARM_PATH = "C:/Program Files/ARM"

# GCC ARM
#GCC_ARM_PATH = ""

# IAR
#IAR_PATH = "C:/Program Files (x86)/IAR Systems/Embedded Workbench 7.0/arm"

# Goanna static analyser. Please overload it in private_settings.py
#GOANNA_PATH = "c:/Program Files (x86)/RedLizards/Goanna Central 3.2.3/bin"

#BUILD_OPTIONS = []

# mbed.org username
#MBED_ORG_USER = ""

# Print compiler warnings and errors as link format
#PRINT_COMPILER_OUTPUT_AS_LINK = False
